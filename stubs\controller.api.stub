<?php

namespace {{ namespace }};

use {{ rootNamespace }}Http\Controllers\Controller;
use Illuminate\Http\Request;

use DB;
use PF;
use Illuminate\Http\Request;

/***
"功能名稱":"活動",
"資料表":"activity",
"建立時間":"2022-01-18 11:38:08 ",
***/
class {{ class }} extends Controller

    private $data;
    private $xmlDoc;

    /**
     *TODO 建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
     
        // FIXME 共用的hidden變數
        $this->data['hiddens'] = [];
        $this->data['displaynames'] = $this->activityRepo->getFieldTitleArray();
        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'activity');
    }

    /**
     * @OA\Post(
     *     path="/api/admin/activity",security={{"bearerAuth":{}}},operationId="",tags={"後台/活動"},summary="列表",description="",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *         @OA\Schema(@OA\Property(property="kind_id",description="種類ID",type="integer",example="10025",) ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),
     *      @OA\Property(property="data", type="array",@OA\Items(
     *          @OA\Property(property="id", type="integer",description="編號", ),
     *          @OA\Property(property="title", type="string",description="名稱", ),
     *          @OA\Property(property="kind_id", type="integer",description="種類ID", ),
     *          @OA\Property(property="kindtitle", type="string",description="種類名稱", ),

     *          @OA\Property(property="kind", type="string",description="活動類型[1:交流 ; 2:抽獎 ; 3:團康遊戲 ; 4:業務交流 ; 5:自由參加交流 ; 6:訓練 ; ]", ),
     *          @OA\Property(property="url", type="string",description="網址URL", example="https://www.google.com.tw"  ),*
     *          @OA\Property(property="session_id", type="integer",description="場次編號", example="1"  ),
     *          @OA\Property(property="sessiontitle", type="string",description="場次", example="test"  ),
     *          @OA\Property(property="activity_date", type="string",description="活動日期", example="2021-08-01"  ),
     *          @OA\Property(property="stewest_date", type="string",description="活動截步日期", example="2021-08-01"  ),
     *          @OA\Property(property="created_at", type="string",description="建立時間", example="2021-08-01"  ),
     *      ),
     * )),)
     *),)
     */

    /**
     * TODO 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $rows = $this->getRows($request);
        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $pagesize = (is_numeric($request->input('pagesize')) ? $request->input('pagesize') : 10);
        $rows = $rows->paginate($pagesize);
        foreach ($rows as $rs) {
            $rs->share_checkin_url = \config('line.life_line_url').'?live=1&eid='.$rs->session_id;
            $rs->share_checkin_qrcode_url = url('/').'/images/activitysession/'.$rs->session_id.'.svg';
        }
        // 顯示sqlcmd
        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * 資料Rows.
     *
     * @return \Illuminate\Http\Response
     */
    public function getRows($request)
    {
        $rows = $this->activityRepo->selectRaw('activity.id,activity.kind_id,activity.title,activity.kind,url,activitysession.id as session_id,activitysession.title as sessiontitle,stewest_date,activity.created_at,activity_date,kind.kindtitle');
        $rows->leftjoin('kind', 'kind.kindid', '=', 'activity.kind_id');
        $rows->leftjoin('activitysession', 'activitysession.activity_id', '=', 'activity.id');
        $rows->myWhere('activity.title', $request->input('search'), $this->data['displaynames'], 'N');
        $rows->myWhere('activity.kind_id|N', $request->input('kind_id'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(created_at,DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert(created_at,DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');
        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), 'desc' == $request->input('sorttype') ? $request->input('sorttype') : 'asc');
        } else {
            $rows->orderByRaw('activity.id desc');
        }
        //PF::dbSqlPrint($rows);

        return $rows;
    }

    /**
     * @OA\Post(
     *     path="/api/admin/activity/show",security={{"bearerAuth":{}}},operationId="",tags={"後台/活動"},summary="單筆顯示",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="id",description="編號",type="integer",example="1",),
     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/activity"),

     *         @OA\Schema(@OA\Property(property="share_url", type="string",description="報名網址", example="") ),
     *     }),
     *      @OA\Property(property="sessions", type="array",@OA\Items(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/activitysession"),

     *         @OA\Schema(@OA\Property(property="share_checkin_url", type="string",description="報到網址", example="") ),
     *     })


     *     )),
     *     ),)
     *),)
     */

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show($request)
    {
        $rows = $this->activityRepo->select('id,title,kind,body,unit,name,email,phone,memo,imagepc,imagemobile,additional_file,url,created_at,updated_at,otherjson');
        $rows->myWhere('id|N', $request->input('id'), 'id', 'Y');
        $rows = $rows->take(1);
        //  PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            $rs = $rows->first();
            $rs->share_url = \config('line.life_line_url').'?live=2&aid='.$rs->id;
            $rows = $this->activitysessionRepo->selectRaw('*');
            $rows->myWhere('activity_id|N', $request->input('id'), 'key', 'Y');
            //$rows->orderByRaw('id desc');
            $rows = $rows->get();

            $rs->sessions = $rows;
            //$rs->qrcodeurl= url('/').'/images/activity/'.$rs->id.".svg";
            $this->jsondata['data'] = $rs;
        } else {
            throw new \CustomException('no data');
        }

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * @OA\Post(
     *     path="/api/admin/activity/store",security={{"bearerAuth":{}}},operationId="",tags={"後台/活動"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="id",description="編號",type="integer",example="1",),
     *          @OA\Property(property="title", type="string",description="名稱", example="test"  ),
     *          @OA\Property(property="kind_id", type="integer",description="種類ID", example="10025" ),
     *          @OA\Property(property="kindtitle", type="string",description="種類名稱", ),

     *          @OA\Property(property="kind", type="string",description="活動類型[1:交流 ; 2:抽獎 ; 3:團康遊戲 ; 4:業務交流 ; 5:自由參加交流 ; 6:訓練 ; ]", ),
     *          @OA\Property(property="body", type="string",description="活動內容", example="test123"  ),
     *          @OA\Property(property="unit", type="string",description="活動單位", example=""  ),
     *          @OA\Property(property="name", type="string",description="聯絡人姓名", example=""  ),
     *          @OA\Property(property="email", type="string",description="email", example="<EMAIL>"  ),
     *          @OA\Property(property="phone", type="string",description="電話", example="0212345678"  ),
     *          @OA\Property(property="memo", type="string",description="備註", example=""  ),
     *          @OA\Property(property="imagepc", type="string",description="圖片_電腦_檔名", example="xx.jpg"  ),
     *          @OA\Property(property="imagemobile", type="string",description="圖片_手機_檔名", example="xx.jpg"  ),
     *          @OA\Property(property="url", type="string",description="網址URL", example="https://www.google.com.tw"  ),
     *          @OA\Property(property="otherjson", type="string",description="其他資料json", example="您要儲存的JSON內容"  ),*
     *      @OA\Property(property="session", type="array",@OA\Items(
     *          @OA\Property(property="id", type="integer",description="編號", example="1"),
     *          @OA\Property(property="title", type="string",description="活動", example="test"  ),
     *          @OA\Property(property="activity_date", type="string",description="活動日期", example="2021-08-01"  ),
     *          @OA\Property(property="start_date", type="string",description="活動開始日期", example="2021-08-01"  ),
     *          @OA\Property(property="end_date", type="string",description="活動結束日期", example="2021-08-01"  ),
     *          @OA\Property(property="stewest_date", type="string",description="活動截步日期", example="2021-08-01 08:00:01"  ),
     *          @OA\Property(property="location", type="string",description="地點", example="test"  ),
     *          @OA\Property(property="contact", type="string",description="聯絡人姓名", example="test"  ),
     *          @OA\Property(property="contact_email", type="string",description="聯絡人Email", example="<EMAIL>"  ),
     *          @OA\Property(property="contact_phone", type="string",description="聯絡人電話", example="0212345678"  ),
     *          @OA\Property(property="maxsign", type="integer",description="最多報名人數", example="1"  ),
     *          @OA\Property(property="isnotice", type="integer",description="報名通知", example="1"  ),
     *     )),
     *       ),
     *
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="id", type="integer",description="編號", ),
     *      )
     *
     *     ),)
     *),)
     */

    /**
     * TODO 資料新增編輯儲存.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $edit = $request->input('id');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        $validators['email'] = ['nullable', 'email']; //email
        $validators['title'] = ['required', new MyValidatorsUnique(
            'activity', ['id' => $edit]
            )];
        $validator = \Validator::make($this->data, $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $inputs = $request->all();

        //$inputs['title']=$this->data['title'];//產品名稱-
        //$inputs['kind']=$this->data['kind'];//種類-
        //$inputs['body']=$this->data['body'];//內容-
        //$inputs['unit']=$this->data['unit'];//單位-
        //$inputs['name']=$this->data['name'];//姓名-
        //$inputs['email']=$this->data['email'];//email-
        //$inputs['phone']=$this->data['phone'];//電話-
        //$inputs['memo']=$this->data['memo'];//備註-
        //$inputs['imagepc']=$this->data['imagepc'];//圖片_電腦-
        //$inputs['imagemobile']=$this->data['imagemobile'];//圖片_手機-
        //$inputs['url']=$this->data['url'];//網址URL-

        // try {
        //     //FIXME 檔案上傳取得值
        //     $upload = new \App\Libraries\UploadFile();
        //     $upload->request = $request;
        //     $upload->inputs = $inputs;
        //     $upload->folder = 'images/activity/';
        //     $upload->width = '800';
        //     $upload->height = '600';
        //     //$upload->limitext = config('app.FileLimit');
        //     $inputs = $upload->execute();
        // } catch (\Exception $e) {
        //     die($e->getMessage());
        // }

        if ('' == $edit) {
            $inputs['created_at'] = date('Y-m-d H:i:s'); //建立時間-
            //PF::printr($inputs);exit();
            $edit = $this->activityRepo->create($inputs)->id;
            $this->jsondata['resultmessage'] = '新增成功';
        } else {
            //PF::printr($inputs); exit();
            $this->activityRepo->update($inputs, $edit);
            $this->jsondata['resultmessage'] = '更新成功';
        }

        if ('' != $request->input('session')) {
            foreach ($request->input('session') as $k => $v) {
                $inputs = $v;

                $inputs['activity_id'] = $edit; //活動-
                //$inputs['title']=$this->data['title'];//產品名稱-
                //$inputs['activity_date']=$this->data['activity_date'];//活動日期-
                //$inputs['start_date']=$this->data['start_date'];//活動開始日期-
                //$inputs['end_date']=$this->data['end_date'];//活動結束日期-
                //$inputs['stewest_date']=$this->data['stewest_date'];//活動截步日期-
                //$inputs['location']=$this->data['location'];//地點-
                //$inputs['contact']=$this->data['contact'];//聯絡人姓名-
                //$inputs['contact_email']=$this->data['contact_email'];//聯絡人Email-
                //$inputs['contact_phone']=$this->data['contact_phone'];//聯絡人電話-
                //$inputs['maxsign']=$this->data['maxsign'];//最多報名人數-

                $inputs['otherjson'] = json_encode($v, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                //$inputs['isnotice'] = $this->data['isnotice']; //報名通知-
                $rows = $this->activitysessionRepo->updateOrCreate(['id' => $inputs['id']], $inputs);

                // if($rows->wasRecentlyCreated){
                //     $v['id']=>$rows->id;
                //     PF::printr($rows->id);
                //     //$this->jsondata['resultmessage'] = '已存在';
                // }
            }
        }
        $this->jsondata['data']['id'] = $edit;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * @OA\Post(
     *     path="/api/admin/activity/destroy",security={{"bearerAuth":{}}},operationId="",tags={"後台/活動"},summary="刪除",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="del",description="要刪除的編號",type="integer",example="1",description="多筆中間用逗號",),

     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *     ),)
     *),)
     */

    /**
     * TODO 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $this->activityRepo->deleteIds($this->data['del']);
        $this->jsondata['resultmessage'] = '刪除成功';

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
