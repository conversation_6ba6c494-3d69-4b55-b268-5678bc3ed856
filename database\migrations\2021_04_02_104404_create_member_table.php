<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/*會員*/

class CreatememberTable extends Migration {
    /**
     * Run the migrations.
     */
    public function up() {
        if (!Schema::hasTable('member')) {
            Schema::create('member', function (Blueprint $table) {
                $table->engine = 'MyISAM';
                $table->bigIncrements('id')->from(10000);
                $table->string('name')->comment('姓名');
                $table->string('lineid')->nullable()->comment('LINE ID');

                $table->string('password')->comment('密碼');
                $table->string('sex', 10)->nullable()->comment('性別');

                $table->string('mobile', 50)->unique()->comment('行動電話');
                $table->string('tel', 50)->nullable()->comment('市話');
                $table->string('email', 250)->unique()->comment('電子信箱');
                $table->string('patterns')->nullable()->comment('法拍屋種類');
                $table->string('totalupsets')->nullable()->comment('總底價');
                $table->string('postals')->nullable()->comment('區域');
                $table->mediumText('myproducts')->nullable()->comment('我的收藏');
                $table->mediumText('item_request')->nullable()->comment('細項需求');

                $table->tinyInteger('isepaper')->default(0)->nullable()->comment('是否訂閱電子報');

                $table->mediumText('memo')->nullable()->comment('備註');
                $table->string('api_token', 100)->nullable()->comment('api_token');
                $table->rememberToken()->nullable()->comment('remember_token');

                $table->string('lastlogin_ip')->nullable()->comment('登入IP');
                $table->dateTime('lastlogin_dt')->nullable()->comment('最後登入日期');
                $table->integer('logincount')->nullable()->comment('登入次數');

                $table->tinyInteger('online')->length(1)->nullable()->comment('會員狀態');


                $table->integer('adminuser_id')->nullable()->comment('編輯人員');
                $table->string('adminuser_account', 50)->nullable()->comment('編輯人員');
                //$table->string('alg', 5)->nullable()->comment('語系');
                $table->timestamp('created_at')->useCurrent()->comment('建立時間');
                $table->timestamp('updated_at')->useCurrent()->comment('編輯時間');
            });
            \DB::statement("ALTER TABLE member COMMENT '會員'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down() {
        Schema::dropIfExists('member');
    }
}
