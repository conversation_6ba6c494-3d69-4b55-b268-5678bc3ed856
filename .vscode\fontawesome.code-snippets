{
    "qrcode": {
        "prefix": "img qrcode",
        "scope": "php,html,blade",
        "body": [
            "<i class=\"fa fa-qrcode\" aria-hidden=\"true\"></i>"
        ]
    },
    "trash": {
        "prefix": "img trash 垃圾筒",
        "scope": "php,html,blade",
        "body": [
            "<i class=\"fa fa-trash\" aria-hidden=\"true\"></i>"
        ]
    },
    "plus": {
        "prefix": "img plus 新增",
        "scope": "php,html,blade",
        "body": [
            "<i class=\"fa fa-plus\" aria-hidden=\"true\"></i>"
        ]
    },
    "edit": {
        "prefix": "img edit 編輯",
        "scope": "php,html,blade",
        "body": [
            "<i class=\"fa fa-edit\" aria-hidden=\"true\"></i>"
        ]
    },
    "facebook": {
        "prefix": "img facebook",
        "scope": "php,html,blade",
        "body": [
            "<i class=\"fa fa-facebook\" aria-hidden=\"true\"></i>"
        ]
    },
    "file": {
        "prefix": "img file",
        "scope": "php,html,blade",
        "body": [
            "<i class=\"fa fa-file\" aria-hidden=\"true\"></i>"
        ]
    },





}