<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Config;
use DB;
use PF;
use App\Repositories\boardRepository;

class mediaController extends Controller
{
    private $data;
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->boardRepo = $boardRepo;
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        Config::set('config.title', '媒體專訪 | '.config('config.title'));

        $rows = $this->boardRepo->selectRaw('*');
        $rows->myWhere('kind|S', 'media', 'successid', 'Y');
        $rows->orderByRaw('boardsort');
        $rows = $rows->paginate(9);
        $this->data['rows'] = $rows;

        return view('media.index', [
            'data' => $this->data,
            ]
       );
    }

    public function show(Request $request, $successid)
    {
        $rows = DB::table('success');
        $rows->selectRaw('success.*');
        $rows->myWhere('successid|N', $this->data['success'], 'successid', 'Y');
        $rows->orderByRaw('successid desc');

        $rows = $rows->take(1)->get();
        //$rows = $rows->get();
        //dd($row);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('No data');
        }
        Config::set('config.title', $this->data['title'].' | '.config('config.title'));
        // Config::set('config.keyword', '');
        // Config::set('config.description', '');

        return view('success.show', [
            'data' => $this->data,
            ]
       );
    }
}
