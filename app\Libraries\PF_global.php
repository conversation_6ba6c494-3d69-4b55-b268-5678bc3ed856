<?php

namespace App\Libraries;

use Mail;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/***
"功能名稱":"共用類別-工具函式",
"建立時間":"2022-01-18 13:19:17",
 ***/
trait PF_global {
    /**
     * 處理例外狀況並返回錯誤訊息
     * @param Exception $e 例外物件
     * @return string 格式化後的錯誤訊息
     */
    public static function exception($e) {
        $msg = $e->getMessage();

        if ($e instanceof \Illuminate\Database\QueryException) {
            try {
                $msg = 'QueryException > ' . PF::errMsg($msg);
            } catch (\Exception $e1) {
            }
        }

        return $msg;
    }

    /**
     * 處理錯誤訊息並發送錯誤通知郵件
     * @param string $msg 錯誤訊息
     * @return string 處理後的錯誤訊息
     */
    public static function errMsg($msg) {
        try {
            if (PF::isEmpty($msg)) {
                return;
            }

            // if (substr_count(\Request::server('HTTP_HOST'), 'allennb') > 0 || '127.0.0.1' == \Request::server('HTTP_HOST')) {
            //     return $msg;
            // }
            if ('local' == \App::environment() || \Config::get('app.debug')) {
                return $msg;
            }

            $RequestForm = '';
            foreach (Request::all()  as $_key => $_value) {
                if ('' != $_value) {
                    if (is_array($_value)) {
                        $RequestForm .= $_key . '=' . implode(',', $_value) . '&';
                    } else {
                        $RequestForm .= $_key . '=' . $_value . '&';
                    }
                }
            }

            try {
                $ebody = view('email.errmsg', [
                    'RequestForm' => $RequestForm,
                    'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'],
                    'msg' => $msg,
                    'PATH_INFO' => \Request::getUri(),
                    'clientip' => \Request::ip(),
                    'date' => date('Y.m.d D H:i'),
                    'HTTP_REFERER' => $_SERVER['HTTP_REFERER'],
                ])->render();

                Mail::send([], [], function ($message) use ($ebody) {
                    $message->from(config('mail.from.address'), config('mail.from.name'));
                    $message->to('<EMAIL>', '管理者');
                    $message->subject($_SERVER['SERVER_NAME'] . '錯誤回報');
                    $message->setBody($ebody, 'text/html');
                });
            } catch (\Exception $e) {
            }

            if (substr_count($msg, 'cannot be null') > 0) {
                $str = __('部份資料未填,請回上一頁重新填寫');
            } else {
                $str = $_SERVER['REMOTE_ADDR'] . '使用此功能出現錯誤,我們已經將你的錯誤訊息EMAIL給管理者,將儘速為您處理!';
            }

            return $str;
        } catch (Exception $e) {
        }
    }

    /**
     * 記錄錯誤日誌
     * @param string $body 錯誤內容
     * @return string 錯誤內容
     */
    public static function log($body) {
        try {
            Log::error($_SERVER['REQUEST_URI'] . ' | ' . $_SERVER['REQUEST_METHOD'] . ' | ' . $body . "\n");
        } catch (\Exception $e) {
            //throw $e;
        }

        return $body;
    }

    /**
     * 處理請求參數，包含安全性檢查和日期格式轉換
     * @param array $data 原始資料
     * @return array 處理後的資料
     */
    public static function requestAll($data) {
        try {
            $tmp = $data;
            $denys = ['<iframe', '<script', '<object', '<?', ',', '?>']; //接收拒絕的字串
            foreach (Request::all()  as $_key => $_value) {
                if ('' != $_value) {
                    // if (false == \Auth::guard('admin')->check()) {
                    //     if(is_numeric($_value)==false){
                    //     if (in_array($_value, $denys)) {
                    //         PF::errMsg($_key.'='.$_value.'值錯誤');
                    //         throw new Exception($_key.'='.$_value.'值錯誤');
                    //     }
                    //     }
                    // }

                    if (is_array($_value)) {
                        $tmp[$_key] = implode(',', $_value);
                    } else {
                        $tmp[$_key] = $_value;
                    }
                    if ('_yy' == PF::right($_key, 3)) {
                        $name = substr($_key, 0, strlen($_key) - 3);
                        $yy = $_value;
                        $mm = Request::get($name . '_mm');
                        $dd = Request::get($name . '_dd');
                        if (false == PF::isEmpty($yy) && false == PF::isEmpty($mm) && false == PF::isEmpty($dd)) {
                            if (strlen($yy) < 4) {
                                $yy = strval(intval($yy) + 1911);
                            }
                            $ymd = $yy . '-' . $mm . '-' . $dd;
                            if (PF::isDate($ymd)) {
                                $tmp[$name] = $ymd;
                            }
                        }
                    }
                }
            }
            if ($tmp == null) {
                return [];
            }
            return $tmp;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 獲取特定請求參數值
     * @param string $_key 參數名稱
     * @return mixed 參數值
     */
    public static function request($_key) {
        try {
            $_value = Request::all();

            $_value = $_value[$_key];

            if (is_array($_value)) {
                $_value = implode(',', $_value);
            }

            return $_value;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 檢查字串是否為有效日期格式
     * @param string $v 日期字串
     * @return bool 是否為有效日期
     */
    public static function isDate($v) {
        try {
            if (isset($GLOBALS['lg']) == false || ('' == $GLOBALS['lg'] || 'zh' == $GLOBALS['lg'])) {

                return preg_match("/^[0-9]{4}(\-|\/)[0-9]{1,2}(\\1)[0-9]{1,2}(|\s+[0-9]{1,2}(|:[0-9]{1,2}(|:[0-9]{1,2})))$/", $v);
            } else {

                $v = str_replace('-', '/', $v);
                $d = explode('/', $v);
                if (count($d) < 2) {
                    return false;
                }
                if (4 == strlen($d[0])) {
                    return preg_match("/^[0-9]{4}(\-|\/)[0-9]{1,2}(\\1)[0-9]{1,2}(|\s+[0-9]{1,2}(|:[0-9]{1,2}(|:[0-9]{1,2})))$/", $v);
                } elseif (2 == strlen($d[0]) || strlen(4 == $d[2])) {
                    if (checkdate($d[0], $d[1], $d[2])) { //checkdate bool checkdate ( int $month , int $day , int $year )
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 驗證電子郵件格式
     * @param string $email 電子郵件地址
     * @return bool 是否為有效的電子郵件格式
     */
    public static function isEmail($email) {
        try {
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 檢查變數是否為空
     * @param mixed $v 要檢查的變數
     * @return bool 是否為空
     */
    public static function isEmpty($v) {
        if ((false == isset($v) || empty($v)) && '0' != $v) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 取得字串左側指定長度的子字串
     * @param string $str 原始字串
     * @param int $len 要取得的長度
     * @return string 子字串
     */
    public static function left($str, $len) {
        return mb_substr($str, 0, $len, 'UTF-8');
    }

    /**
     * 取得字串右側指定長度的子字串
     * @param string $str 原始字串
     * @param int $len 要取得的長度
     * @return string 子字串
     */
    public static function right($str, $len) {
        return substr($str, -$len);
    }

    /**
     * 移除HTML標籤並清理字串
     * @param string $str 含HTML的字串
     * @return string 清理後的字串
     */
    public static function noHtml($str) {
        try {
            $str = str_replace("&nbsp;", "", $str);
            if (0 == substr_count($str, '<') || 0 == substr_count($str, '>')) {
                return trim($str);
            } else {
                $str = trim($str);
                $str = preg_replace('@<!--(.*?)-->@is', '', $str);
                $str = preg_replace('@<script(.*?)</script>@is', '', $str);
                $str = preg_replace('@<iframe(.*?)</iframe>@is', '', $str);
                $str = preg_replace('@<style(.*?)</style>@is', '', $str);
                $str = preg_replace('@<(.*?)>@is', '', $str);
                $str = preg_replace('/\s(?=\s)/', '', $str);
                $str = preg_replace('/[\n\r\t]/', ' ', $str);
                $str = str_replace("'", '', $str);

                return $str;
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 格式化日期
     * @param string $value 日期字串
     * @param string $formatString 格式化字串
     * @return string 格式化後的日期
     */
    public static function formatDate($value, $formatString = '') {
        try {
            if (PF::isEmpty($value)) {
                return '';
            }

            if (PF::IsDate($value)) {
                if ('YY-MM-DD' == $formatString) {
                    //$items=explode("chr(13).chr(10)",$s);
                    $dates = explode('-', $value);

                    return ($dates[0] - 1911) . '-' . date('m-d', strtotime(date($value)));
                } else {
                    return date('Y-m-d', strtotime(date($value)));
                }
            } else {
                if (\Str::contains($value, ['T'])) {
                    $dt = \Carbon::parse($value);
                    return $dt->toDateString(); // 2020-03-18
                }
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 將換行符轉換為HTML換行標籤
     * @param string $v 原始字串
     * @return string 轉換後的字串
     */
    public static function vbcrlf($v) {
        return str_replace("\n", '<br>', $v);
    }

    /**
     * 開發環境下的變數輸出函式
     * @param mixed $n 要輸出的變數
     */
    public static function printr($n) {


        if (\Str::contains(\Request::getRequestUri(), ['/api']) == false && (env('APP_ENV') == 'dev' || in_array(Request::ip(), ['***************', '*************']))) {
            if ($GLOBALS['codelive'] == "") {
                $body = "<html>";
                $body .= "<script type='text/javascript' src='" . url('/') . "/Scripts/jquery.js'></script>" . PHP_EOL;
                $body .= "<script type='text/javascript' src='https://allennb.com.tw:442/codelive/index.js?d=" . date('YmdHis') . "'></script>" . PHP_EOL;
                $body .= "</html>";
                $GLOBALS['codelive'] = true;
                echo $body;
            }
        }
        print_r('<hr>');
        print_r('<pre>');
        print_r($n);
        print_r('</pre>');
        //print_r(chr(13).chr(10));
        print_r(PHP_EOL);
    }

    /**
     * 格式化數字
     * @param mixed $v1 數值
     * @param int $v2 小數位數
     * @return string 格式化後的數字
     */
    public static function formatNumber($v1, $v2) {
        $v1 = trim($v1);
        if (PF::IsEmpty($v1)) {
            return 0;
        }
        if (is_numeric($v1)) {
            if (-1 == $v2) {
                $v2 = 0;
                $v1arr = explode('.', $v1);
                if (count($v1arr) > 1) {
                    for ($i = 0; $i < strlen($v1arr[1]); ++$i) {
                        if (substr($v1arr[1], $i, 1) > 0) {
                            $v2 = $i + 1;
                        }
                    }
                }
            }

            return number_format($v1, $v2);
        } else {
            return $v1;
        }
    }

    /**
     * 檢查字串是否為UTF-8編碼
     * @param string $string 要檢查的字串
     * @return bool 是否為UTF-8編碼
     */
    public static function isUTF8($string) {
        return $string == mb_convert_encoding(mb_convert_encoding($string, 'UTF-32', 'UTF-8'), 'UTF-8', ' UTF-32');
    }

    /**
     * 計算分頁序號
     * @param int $page 頁碼
     * @param int $pagesize 每頁筆數
     * @param int $s 序號
     * @return int 計算後的序號
     */
    public static function SN($page, $pagesize, $s) {
        if (is_numeric($page)) {
            if (1 == $page) {
                return $s;
            } else {
                $page = $page - 1;

                return $page * $pagesize + $s;
            }
        } else {
            return $s;
        }
    }

    /**
     * 將十六進制轉換為UTF-8編碼
     * @param array $ar 十六進制陣列
     * @return string UTF-8編碼字串
     */
    public static function toUtf8($ar) {
        $c = '';
        foreach ($ar as $val) {
            $val = intval(substr($val, 2), 16);
            if ($val < 0x7F) {
                // 0000-007F
                $c .= chr($val);
            } elseif ($val < 0x800) {
                // 0080-0800
                $c .= chr(0xC0 | ($val / 64));
                $c .= chr(0x80 | ($val % 64));
            } else {
                // 0800-FFFF
                $c .= chr(0xE0 | (($val / 64) / 64));
                $c .= chr(0x80 | (($val / 64) % 64));
                $c .= chr(0x80 | ($val % 64));
            }
        }

        return $c;
    }

    /**
     * 記錄表單查詢日誌
     * @param string $pagename 頁面名稱
     * @param Request $request 請求物件
     */
    public static function setFormQuerylog($pagename, $request) {
        $formbody = '';
        foreach ($_POST as $_key => $_value) {
            if ('' != $_value) {
                $value = '';
                if (is_array($_value)) {
                    $value = implode(',', $_value);
                } else {
                    $value .= $_value;
                }
                $formbody .= $_key . '=' . $value;
                $formbody .= '&';
            }
        }

        $inputs = null;
        $inputs['pathinfo'] = $request->path();
        $inputs['formbody'] = $formbody;
        $inputs['querybody'] = $request->getQueryString();
        $inputs['pagename'] = $pagename;

        \App\Models\formquerylog::create($inputs);
    }

    /**
     * 比較分隔值是否包含指定值
     * @param string $SplitValue 分隔值字串
     * @param string $CompareValue 比較值
     * @return bool 是否包含
     */
    public static function splitCompare($SplitValue, $CompareValue) {
        if (false == isset($CompareValue) || false == isset($SplitValue)) {
            return false;
        }

        $CompareValue = strtolower($CompareValue);
        $SplitValue = strtolower($SplitValue);

        $SplitValue = str_replace(';', ',', $SplitValue);
        $CompareValue = str_replace(';', ',', $CompareValue);

        $CompareValuesplit = explode(',', $CompareValue);
        $SplitValuesplit = explode(',', $SplitValue);

        for ($x = 0; $x < count($CompareValuesplit); ++$x) {
            for ($y = 0; $y < count($SplitValuesplit); ++$y) {
                if (trim($CompareValuesplit[$x]) == trim($SplitValuesplit[$y])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 取得字串中兩個標記之間的內容
     * @param string $str 原始字串
     * @param string $g1 起始標記
     * @param string $g2 結束標記
     * @param string $flag 標記
     * @return string|null 擷取的內容
     */
    public static function getStr($str, $g1, $g2, $flag) {
        try {
            if ('' != $str) {
                if ('' == $g1) {
                    $instr1 = 0;
                } else {
                    $instr1 = strpos($str, $g1) + strlen($g1);
                }

                if ('' != $g2) {
                    if ('' != $g1) {
                        $instr2 = strpos($str, $g2, $instr1) - strpos($str, $g1) - strlen($g1);
                    } else {
                        $instr2 = strpos($str, $g2, $instr1) - strlen($g1);
                    }
                } else {
                    $instr2 = strlen($str);
                }

                if ($instr2 > 0) {
                    $result = substr($str, $instr1, $instr2);
                    if ('' != $result) {
                        if ('1' == $flag) {
                            $GLOBALS['str'] = substr($str, $instr1 + strlen($result), strlen($str));
                        }

                        return $result;
                    }
                }
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 計算兩個日期之間的差異
     * @param string $date1 日期1
     * @param string $date2 日期2
     * @param string $unit 單位(s:秒,i:分,h:時,d:天)
     * @return float|bool 差異值或false
     */
    public static function dateDiff($date1, $date2, $unit = '') {
        try {
            switch ($unit) {
                case 's':
                    $dividend = 1;
                    break;
                case 'i':
                    $dividend = 60;
                    break;
                case 'h':
                    $dividend = 3600;
                    break;
                case 'd':
                    $dividend = 86400;
                    break;
                default:
                    $dividend = 86400;
            }
            $time1 = strtotime($date1);
            $time2 = strtotime($date2);
            if ($time1 && $time2) {
                return (float) ($time1 - $time2) / $dividend;
            }

            return false;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 將字串中指定位置的字元替換為特定字元
     * @param string $str 原始字串
     * @param array $locations 位置陣列
     * @param string $strx 替換字元
     * @return string 處理後的字串
     */
    public static function getStrX($str, $locations = [4, 7, 9], $strx = '*') {
        $temp = '';

        for ($i = 0; $i < mb_strlen($str, 'UTF-8'); ++$i) {
            if (in_array($i, $locations)) {
                $temp .= $strx;
            } else {
                $o = mb_substr($str, $i, 1, 'UTF-8');
                $temp .= $o;
            }
        }

        return $temp;
    }

    /**
     * 語言轉換函式
     * @param string $v 要轉換的文字
     * @return string 轉換後的文字
     */
    public static function LG($v) {
        return $v;
    }

    /**
     * 生成GUID
     * @return string GUID字串
     */
    public static function getGuid() {
        mt_srand((float) microtime() * 10000); //optional for php 4.2.0 and up.
        $charid = strtoupper(md5(uniqid(rand(), true)));
        $uuid = substr($charid, 0, 8)
            . substr($charid, 8, 4)
            . substr($charid, 12, 4)
            . substr($charid, 16, 4)
            . substr($charid, 20, 12);

        return $uuid;
    }

    /**
     * 獲取設定值
     * @param string $key 設定鍵值
     * @return mixed 設定值
     */
    public static function getConfig($key) {
        if ('' != config('config.' . request()->getHost() . '.' . $key)) {
            return config('config.' . request()->getHost() . '.' . $key);
        } elseif ('' != config('config.' . app()->getLocale() . '.' . $key)) {
            return config('config.' . app()->getLocale() . '.' . $key);
        }

        return config('config.' . $key);
    }

    /**
     * 設定設定值
     * @param string $key 設定鍵值
     * @param mixed $value 設定值
     */
    public static function setConfig($key, $value = '') {
        if ('' != config('config.' . request()->getHost() . '.' . $key)) {
            \Config::set('config.' . request()->getHost() . '.' . $key, $value);
        } elseif ('' != config('config.' . app()->getLocale() . '.' . $key)) {
            \Config::set('config.' . app()->getLocale() . '.' . $key, $value);
        } else {
            \Config::set('config.' . $key, $value);
        }
    }

    /**
     * 獲取寄件者信箱
     * @return string 寄件者信箱
     */
    public static function getFromEmail() {
        $email = PF::getConfig('email');
        $emails = explode(';', $email);

        return $emails[0];
    }

    /**
     * 將字串轉換為視圖
     * @param string|null $view 視圖內容
     * @param array $data 資料陣列
     * @param array $mergeData 合併資料陣列
     * @return \Illuminate\View\View
     */
    public static function getStringToView($view = null, $data = [], $mergeData = []) {
        /* Create a file with name as hash of the passed string */
        $filename = hash('sha1', $view);

        $file_location = storage_path('framework/views/');
        $filepath = storage_path('framework/views/' . $filename . '.blade.php');

        if (!file_exists($filepath)) {
            file_put_contents($filepath, $view);
        }

        view()->addLocation($file_location);

        return view($filename, $data, $mergeData);
    }

    /**
     * 格式化台灣日期格式
     * @param string $d 日期字串
     * @return string|null 格式化後的日期
     */
    public static function formatTWDate($d) {
        if (PF::isDate($d)) {
            $ds = str_replace('-', '/', $d);
            $items = explode('/', $ds);
            if (3 == count($items)) {
                return (intval($items[0]) - 1911) . '-' . $items[1] . '-' . $items[2];
            }
        }

        return null;
    }

    /**
     * JSON解碼並進行錯誤檢查
     * @param string $json JSON字串
     * @param bool $flag 是否轉換為陣列
     * @return mixed 解碼後的資料
     * @throws \CustomException
     */
    public static function json_decode($json, $flag = true) {
        if (PF::isEmpty($json)) {
            throw new \CustomException('json data is empty');
        }
        $json1 = json_decode($json, $flag);

        if (JSON_ERROR_NONE != json_last_error()) {
            $msg = "JSON 錯誤在第 " . json_last_error() . " 行\n";
            $msg .= "錯誤訊息: " . json_last_error_msg() . "\n";
            throw new \CustomException($json . ">" . $msg . ' json 解析錯誤');
        }

        return $json1;
    }

    /**
     * 檢查是否為行動裝置
     * @return bool 是否為行動裝置
     */
    public static function isMobile() {
        return preg_match("/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i", $_SERVER['HTTP_USER_AGENT']);
    }

    /**
     * 檢查字串是否為有效的JSON格式
     * @param string $string 要檢查的字串
     * @return bool 是否為有效的JSON
     */
    public static function isJson($string) {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    /**
     * 將民國年日期轉換為西元年日期
     * @param string $rocDate 民國年日期
     * @return string|null 西元年日期
     */
    public static function rocDateToWestDate($rocDate) {
        try {
            if (PF::isEmpty($rocDate)) {
                return null;
            }

            // 將日期中的 / 轉換為 -
            $rocDate = str_replace('/', '-', $rocDate);

            // 分割日期字串
            $dateParts = explode('-', $rocDate);

            // 檢查日期格式是否正確
            if (count($dateParts) !== 3) {
                return null;
            }

            // 取得年月日
            $year = (int)$dateParts[0];
            $month = (int)$dateParts[1];
            $day = (int)$dateParts[2];

            // 檢查月份和日期是否有效
            if ($month < 1 || $month > 12 || $day < 1 || $day > 31) {
                return null;
            }

            // 民國年轉西元年
            $westYear = $year + 1911;

            // 格式化日期
            $westDate = sprintf('%04d-%02d-%02d', $westYear, $month, $day);

            // 驗證日期是否有效
            if (!PF::isDate($westDate)) {
                return null;
            }

            return $westDate;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 處理中文檔名
     * @param string $filename 檔名
     * @return string 處理後的檔名
     */
    public static function getChineseFileName($filename) {
        if (DIRECTORY_SEPARATOR === '/') {
            $filename = iconv('big5', 'utf-8', $filename);
        }

        return $filename;
    }

    /**
     * 檢查是否為Apple裝置
     * @return bool 是否為Apple裝置
     */
    public static function isAppleDevice() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'];

        return false !== strpos($userAgent, 'iPhone') || false !== strpos($userAgent, 'iPad') || false !== strpos($userAgent, 'iPod');
    }

    /**
     * 檢查是否為Android裝置
     * @return bool 是否為Android裝置
     */
    public static function isAndroidDevice() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'];

        return false !== strpos($userAgent, 'Android');
    }

    /**
     * 將JSON資料轉換為資料集
     * @param mixed $rs 資料集物件
     * @param string $jsonbody JSON字串
     * @param bool $isover 是否覆蓋
     * @return mixed 轉換後的資料集
     */
    public static function jsonToRs($rs, $jsonbody, $isover = false) {
        if ($jsonbody != "") {
            $json = \PF::json_decode($jsonbody, true); //ture=>可以用$json['yyy'];false=>可以直接update
            if (method_exists($rs, 'getAttributes') && $isover == false) {
                $rsKeys = array_keys($rs->getAttributes());
                // 從 $json 中刪除所有存在於 $rs 中的鍵
                $json = array_diff_key($json, array_flip($rsKeys));
            }

            foreach ($json as $k => $v) {
                $rs->{$k} = $v;
            }
        }

        return $rs;
    }

    /**
     * 將stdClass物件轉換為陣列
     * @param array|null $data 原始資料
     * @param object $rs stdClass物件
     * @return array 轉換後的陣列
     */
    public static function stdClassToArray($data, $rs) {
        if ($data != null) {
            $data = array_merge($data, $rs->toArray());
        } else {
            $data = $rs->toArray();
        }
        foreach ($rs->getRelations()  as $k => $v) {
            $data = array_merge($data, [$k => $v]);
        };

        return $data;
    }

    /**
     * 加密資料
     * @param mixed $data 要加密的資料
     * @param string|null $iv 初始向量
     * @return string 加密後的字串
     */
    public static function encryptData($data, $iv = null) {
        $key = \config('encrypted.key');
        $iv = $iv ?: \config('encrypted.iv');

        $jsonData = json_encode($data);
        $encrypted = openssl_encrypt(
            $jsonData,
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        return base64_encode($encrypted);
    }
    /**
     * 創建 Excel 下載回應並設定適當的標頭
     *
     * @param mixed $export Export 實例
     * @param string $filename 檔案名稱
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public static function createExcelDownloadResponse($export, string $filename) {
        // 生成 Excel 檔案
        $response = \Excel::download($export, $filename, \Maatwebsite\Excel\Excel::XLSX);

        // 添加 CORS 標頭以支援前端跨域讀取標頭
        $corsHeaders = [
            'Access-Control-Expose-Headers' => 'Content-Disposition, Content-Type, Content-Length',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'POST, GET, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization',
        ];

        foreach ($corsHeaders as $key => $value) {
            $response->headers->set($key, $value);
        }

        // 設定正確的 Content-Disposition 標頭以支援中文檔名
        // 確保同時支援舊版和新版瀏覽器的檔案名稱顯示
        $response->headers->set(
            'Content-Disposition',
            'attachment; filename="' . urlencode($filename) . '"; filename*=UTF-8\'\'' . rawurlencode($filename)
        );

        return $response;
    }
}
