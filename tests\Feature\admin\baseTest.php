<?php

namespace Tests\Feature\admin;



class baseTest extends \Tests\Feature\baseTest {

    public $adminuser;


    public function setUp(): void {
        /*
     * @runInSeparateProcess
     */
        parent::setUp();


        $this->adminuser = \App\Models\adminuser::factory()->create([]);


        $this->actingAs($this->adminuser, "admin");
        $response = $this->get('/');
    }

    // public function tearDown()
    // {

    // }
}
