<?php

namespace App\Libraries;

use DB;
use Illuminate\Http\Request;

/***
"功能名稱":"共用類別-資料庫函式",
"建立時間":"2022-01-18 13:17:06",
 ***/
trait PF_db {
    /**
     * 刪除指定資料夾中的檔案
     *
     * @param string $folders 資料夾路徑，多個資料夾用逗號分隔
     * @param string $files 檔案名稱，多個檔案用逗號分隔
     * @return void
     */
    public static function dbDeleteFile($folders, $files) {
        $arrayfolder = explode(',', $folders);
        $arrayfile = explode(',', $files);

        for ($x = 0; $x < count($arrayfolder); ++$x) {
            for ($y = 0; $y < count($arrayfile); ++$y) {
                if ('' != $arrayfile[$y]) {
                    $filename = public_path($arrayfolder[$x] . '/' . $arrayfile[$y]);

                    if (\File::exists($filename)) {
                        \File::delete($filename);
                    }
                }
            }
        }
    }

    /**
     * 執行SQL查詢並返回第一個欄位的值
     *
     * @param string $sql SQL查詢語句
     * @param mixed $arr 查詢參數，可以是字串或陣列
     * @return string 查詢結果，多個結果以逗號分隔
     */
    public static function dbGetValue($sql, $arr) {
        $tempvalue = '';
        $sqlarray = [];
        if (is_string($arr)) {
            $sqlarray[] = $arr;
        } else {
            $sqlarray = $arr;
        }
        $rows = DB::select($sql, $sqlarray);

        $cc = '';
        foreach ($rows as $rs) {
            $rs = get_object_vars($rs);
            $arrkey = array_keys($rs);
            $tempvalue = $cc . $rs[$arrkey[0]];
            $cc = ',';
        }

        return $tempvalue;
    }

    /**
     * 檢查資料表中指定欄位的值是否唯一
     *
     * @param object $validator Laravel驗證器物件
     * @param mixed $data 要驗證的資料，可以是字串或陣列
     * @param string $tablename 資料表名稱
     * @param string $fieldname 欄位名稱，多個欄位用^分隔
     * @param string $fieldtitle 欄位標題
     * @return void
     */
    public static function dbIsUnique(&$validator, $data, $tablename, $fieldname, $fieldtitle) {
        try {
            $fieldvalue = '';
            if (is_string($data)) {
                $fieldvalue = $data;
            } else {
                $fieldvalue = $data[$fieldname];
            }
            if (false == isset($fieldvalue)) {
                return;
            }
            if (false == is_string($data)) {
                if ($data[$fieldname . '1'] == $data[$fieldname]) {
                    return;
                }
            }
            //DB::enableQueryLog();

            $fieldnamesplit = explode('^', $fieldname);
            $fieldvaluesplit = explode('^', $fieldvalue);
            $rows = DB::table($tablename)->selectRaw('null');
            for ($i = 0; $i < count($fieldnamesplit); ++$i) {
                $rows->where($fieldnamesplit[$i], $fieldvaluesplit[$i]);
            }
            $rows->limit(1);
            $rows = $rows->get();
            if ($rows->count() > 0) {
                $validator->errors()->add($fieldname, 'This ' . __($fieldtitle) . '  has already been registered');
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 將查詢建構器轉換為SQL語句
     *
     * @param object $rows 查詢建構器物件
     * @return string SQL語句
     * @throws CustomException 當沒有SQL命令字串時拋出異常
     */
    public static function dbToSql($rows) {
        if (null == $rows) {
            throw new \CustomException('dbToSql: no sql command string');
        }

        return PF::dbToSqlStr($rows->toSql(), $rows->getBindings());
    }

    /**
     * 將SQL語句和綁定參數轉換為完整的SQL字串
     *
     * @param string $sql SQL語句
     * @param array $bindings 綁定參數陣列
     * @return string 格式化後的SQL字串
     * @throws CustomException 當沒有SQL命令字串時拋出異常
     */
    public static function dbToSqlStr($sql, $bindings) {
        if ('' == $sql) {
            throw new \CustomException('dbToSql: no sql command string');
        }

        // $bindings = $rows->getBindings();
        $sql = str_replace('%', '@@@@', $sql);
        $sql = str_replace('?', '\'%s\'', $sql);
        $sql = vsprintf($sql, $bindings);
        $sql = str_replace('@@@@', '%', $sql);

        $sql = str_replace('where', PHP_EOL . 'where' . PHP_EOL, $sql);
        //$items=explode("chr(13).chr(10)",$s);
        $items = [' and ', ' left join ', ' inner join ', ' order '];
        foreach ($items as $k => $v) {
            $sql = str_replace($v, PHP_EOL . $v, $sql);
        }

        return $sql;
    }

    /**
     * 輸出格式化的SQL語句
     *
     * @param object $rows 查詢建構器物件
     * @param bool $isEOL 是否包含換行符，預設為true
     * @return void
     */
    public static function dbSqlPrint($rows, $isEOL = true) {
        $sql = PF::dbToSqlStr($rows->toSql(), $rows->getBindings());
        // if ($isEOL) {
        //     $sql = str_replace(PHP_EOL, '\r\n', $sql);
        // }

        print_r('<hr>');
        print_r('<pre>');
        print_r($sql);
        print_r('</pre>');
        //print_r(chr(13).chr(10));
        print_r(PHP_EOL);
    }

    /**
     * 檢查資料表中的多個欄位值是否唯一
     *
     * @param string $table_name 資料表名稱
     * @param array $displaynames 欄位顯示名稱陣列
     * @param array $wheres 條件陣列
     * @param array $values 要檢查的值陣列
     * @return void
     * @throws CustomException 當發現重複值時拋出異常
     */
    public static function dbUnique($table_name, $displaynames, $wheres, $values) {
        // PF::dbUnique('member', $this->data['displaynames'], ['id' => $edit], [
        //     'lineid' => $this->data['lineid'],
        //     'email' => $email,
        // ]);
        $isvalue = false;
        foreach ($wheres as $k => $v) {
            if ('' != $v) {
                $isvalue = true;
            }
        }
        $old_rs = [];
        if ($isvalue) {
            $rows = \DB::table($table_name)->where($wheres)->get();
            if ($rows->count() > 0) {
                $rs = $rows->first();
                $old_rs = get_object_vars($rs);
            }
        }
        foreach ($values as $k => $v) {
            $old_value = $old_rs[$k];
            if ($old_value != $v) {
                $rows = \DB::table($table_name)->selectRaw('1');
                $rows->where($k, $v);
                $rows->where($k, '<>', $old_value);
                $rows = $rows->get();
                if ($rows->count() > 0) {
                    throw new \CustomException($displaynames[$k] . __(' 請勿重覆'));
                }
            }
        }
    }

    /**
     * 生成不重複的隨機代碼
     *
     * @param string $tableName 資料表名稱
     * @param string $fieldName 欄位名稱
     * @param int $length 代碼長度，預設為5
     * @param int $count 最大嘗試次數，預設為5
     * @return string 生成的隨機代碼
     */
    public static function dbRndCode($tableName, $fieldName, $length = 5, $count = 5) {
        $code = \Str::random($length);
        for ($i = 0; $i < $count; ++$i) {
            if (\DB::table($tableName)->where($fieldName, $code)->exists()) {
                $code = \Str::random($length);
            } else {
                break;
            }
        }

        return $code;
    }
    public static function dynamicConditions(Request $request, $query) {
        if ($request->has('dynamicConditions')) {
            $conditions = $request->input('dynamicConditions');

            foreach ($conditions as $index => $condition) {
                // 決定使用的查詢方法
                $method = $index === 0 ? 'where' : (strtolower($condition['operator']) === 'or' ? 'orWhere' : 'where');

                // 解析欄位名稱和類型標記
                $fieldWithType = $condition['field'];
                $field = $fieldWithType;
                $fieldDataType = null;

                // 檢查是否有類型標記 (例如: flowerorder.moneystatus|INT)
                if (strpos($fieldWithType, '|') !== false) {
                    list($field, $fieldDataType) = explode('|', $fieldWithType, 2);
                    $fieldDataType = strtoupper($fieldDataType);
                }

                $comparison = $condition['comparison'];
                $value = $condition['value'];

                // 根據欄位數據類型處理值
                if ($fieldDataType === 'INT') {
                    // 數字類型：轉換為整數
                    $value = (int) $value;
                } elseif ($fieldDataType === 'FLOAT' || $fieldDataType === 'DECIMAL') {
                    // 浮點數類型：轉換為浮點數
                    $value = (float) $value;
                }

                if ($condition['fieldType'] === 'date') {
                    if ($comparison === 'BETWEEN') {
                        $query = $query->$method($field, '>=', $condition['startDate'])
                            ->where($field, '<=', $condition['endDate']);
                    } else {
                        $query = $query->$method($field, $comparison, $value);
                    }
                } elseif ($condition['fieldType'] === 'select') {
                    // select 欄位直接使用 value，但仍需考慮數據類型
                    $query = $query->$method($field, $comparison, $value);
                } else {
                    // text 欄位處理
                    if ($comparison === 'LIKE' && $fieldDataType !== 'INT' && $fieldDataType !== 'FLOAT' && $fieldDataType !== 'DECIMAL') {
                        // 數字類型不使用 LIKE 查詢
                        $query = $query->$method($field, 'LIKE', "%{$value}%");
                    } else {
                        $query = $query->$method($field, $comparison, $value);
                    }
                }
            }
        }
        return $query;
    }
}
