<?php

namespace App\Libraries;

use PF;
use Exception;
use Intervention\Image\Facades\Image;

/***
"功能名稱":"共用類別-檔案上傳函式",
"建立時間":"2022-01-18 13:17:50",
 ***/
class UploadFile {
    public $request;
    public $inputs;
    public $folder;
    public $basepathfolder;
    public $width;
    public $height;
    public $limitext;
    public $size;
    public $sourcefile;

    public function __construct() {
    }
    public function init() {
        if (empty($this->limitext)) {
            $this->limitext = config('config.uploadfilelimit');
            $this->limitext = str_replace(';', ',', $this->limitext);
        }

        if (empty($this->size)) {
            $this->size = config('config.uploadfilesize');
        }

        if (\Str::contains($this->folder, ['storage'])) {
            $this->basepathfolder = $this->folder;
        } else {
            $storage = base_path('public/');
            $this->basepathfolder = $storage . iconv('utf-8', 'big5', str_replace('\\', '/', $this->folder));
        }
        //$storage = base_path('storage/app/public/');

        //  echo base_path("public");
        //  exit();

        if ('/' != substr($this->basepathfolder, -1)) {
            $this->basepathfolder .= '/';
        }

        if (false == file_exists($this->basepathfolder)) {
            mkdir($this->basepathfolder, 777);
            //chmod(base_path("public").$uploadpath, 0777);
        }

        if (@chmod($this->basepathfolder, 0777)) {
        }
        if (false == is_writeable($this->basepathfolder)) {
            PF::errmsg('抱歉你沒有寫入的權限(請通知網管人員打開目錄(' . $this->folder . ')的寫入權限)');
        }
    }
    public function execute() {
        try {
            $this->init();
            // if (0 == substr_count($this->request->server('CONTENT_TYPE'), 'multipart/form-data')) {
            //     throw new \CustomException('post type not multipart/form-data');

            //     return false;
            // }


            $names = [];
            foreach ($this->request->all()  as $key => $item) {
                if (substr_count($key, 'hiddenfile') > 0) {
                    $name = str_replace('hiddenfile', '', $key);
                    $names[$name] = '';
                    //  print_r($name);
                    if (is_array($item)) {
                        foreach ($item as $k => $v) {
                            if ('' != $v) {
                                $files[$name][$k] = $v;
                            }
                        }
                        //$this->inputs[$name] = $value;
                    } else {
                        if ('' != $item) {
                            $files[$name][0] = $item;
                        }
                        //$this->inputs[$name] = $item;
                    }
                    if (null != $files[$name] && 0 == count($files[$name]) && '' == $this->request->file()[$name]) {
                        $this->inputs[$name] = '';
                    }
                } else if (is_string($item) && PF::left($item, 5) == "data:") {
                    $this->inputs[$key] = $this->parseImageFileName($item);
                } else {
                    if (PF::left($key, 4) == "json") {
                        if ($item != "") {
                            if (PF::isJson($item)) {

                                $json = \PF::json_decode($item, true); //ture=>可以用$json['yyy'];false=>可以直接update
                                foreach ($json as $k2 => $v2) {
                                    if (is_array($v2)) {
                                        foreach ($v2 as $k3 => $v3) {
                                            foreach ($v3 as $k4 => $v4) {
                                                $json[$k2][$k3][$k4] = $this->parseImageFileName($v4);
                                            }
                                        }
                                    } else {
                                        $json[$k2] = $this->parseImageFileName($v2);
                                    }
                                }

                                $jsonbody = json_encode($json, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                                $this->inputs[$key] = $jsonbody;
                            }
                        }
                    }
                }
            }

            if (count($this->request->file()) > 0) {
                foreach ($this->request->file() as $name => $v) {
                    $names[$name] = '';
                    if (is_array($v)) {
                        foreach ($v as $key => $v1) {
                            $file = self::uploadonefile($name, $v1);
                            $files[$name][$key] = $file;
                        }
                    } else {
                        $files[$name][0] = self::uploadonefile($name, $v);
                    }

                    //PF::printr($value);
                }
            }

            foreach ($names as $key => $value) {
                if ('' != $files[$key]) {
                    $this->inputs[$key] = '';
                    foreach ($files[$key] as $k => $v) {
                        if ('' != $v) {
                            $this->inputs[$key] .= $v . ',';
                        }
                    }

                    $this->inputs[$key] = ltrim(rtrim($this->inputs[$key], ','), ',');
                } else {
                    $this->inputs[$key] = '';
                }
            }
        } catch (Exception $e) {
            throw $e;
        }

        return $this->inputs;
    }
    public function executejson() {
        $this->init();
        foreach ($this->request->all()  as $k => $v) {
            if (is_array($v)) {
                foreach ($v as $k2 => $v2) {
                    $this->inputs[$k] = $this->parseImageFileName($v2);
                }
            }
        }
        return $this->inputs;
    }
    public function parseImageFileName($v2) {
        if (PF::left($v2, 5) == "data:") {
            $filename_Mname = md5(date('YmdHis') . floor(microtime() * 100000));
            //$ext = $msg = \Str::between($v2, 'data:image/', ';base64,');

            $filename = $filename_Mname . '.png';
            $data = $v2;
            list($type, $data) = explode(';', $data);
            list(, $data)      = explode(',', $data);
            $data = base64_decode($data);
            $folder = $this->basepathfolder;

            if (false == \File::isDirectory($folder)) {
                \File::makeDirectory($folder);
            }
            $filename1 = $folder . $filename;
            $success = file_put_contents($filename1, $data);
            if (\Str::contains($v2, ['data:image']) && ($this->width != "" || $this->height != "")) {
                \Image::make($filename1)->resize($this->width, $this->height, function ($constraint) {
                    $constraint->upsize();
                    $constraint->aspectRatio();
                })->save(
                    $filename1,
                    100
                );
            }
            return $filename;
        } else {
            return $v2;
        }
    }
    public function uploadonefile($name, $v) {
        if (null == $v) {
            return;
        }
        $tmpsize = 0;
        $tmperror = UPLOAD_ERR_OK;
        $tmpname = '';

        try {
            $tmpsize = $v->getClientSize();
        } catch (\Exception $e) {
            try {
                $tmpsize = $v->getSize();
            } catch (\Exception $e) {
            }
        }
        if (0 == $tmpsize) {
            return null;
        }

        $tmperror = $v->getError();
        $tmpname = $v->getClientOriginalName();
        $this->sourcefile = $tmpname;
        if (UPLOAD_ERR_OK != $tmperror && 4 != $tmperror) {
            throw new Exception($this->uploaderror($tmperror));
        }
        if ('' == $this->limitext) {
            $this->limitext = PF::getConfig('uploadfilelimit');
        }
        if ($tmpsize > 0) {
            $ext = substr($tmpname, strrpos($tmpname, '.') + 1);

            $this->limitext = str_replace(';', ',', $this->limitext);
            if (false == in_array(strtolower($ext), explode(',', strtolower($this->limitext)))) {
                throw new \CustomException(__('格式不符'));
            }

            if ('' == config('config.uploadfilesize')) {
                config(['config.uploadfilesize' => 100]);
            }

            if ($tmpsize > config('config.uploadfilesize') * 1024 * 1024) {
                throw new \CustomException(__('檔案大小請勿超過') . ':' . config('config.uploadfilesize') . 'M');
            }
            if ($tmperror > 0) {
                throw new Exception('upload Error Code: ' . $tmperror);
            } else {
                if (null != $this->isfix && ('Y' == $this->isfix || true == $this->isfix)) {
                    $filename_name = trim($tmpname);
                } else {
                    $filename_Mname = $tmpname;

                    $filename_name = $filename_Mname;
                    //if ('1' == $filenameNameMethod) {
                    $filename_Mname = md5(date('YmdHis') . floor(microtime() * 100000));
                    // 擷取副檔名

                    // 合成並顯示新的檔名
                    $filename_name = $filename_Mname . '.' . $ext;
                }
                // }
                //echo public_path();
                //echo iconv('utf-8', 'big5', $filename_name);
                $isimg = false;
                if (in_array($ext, ['jpg', 'gif', 'png', 'jpeg'])) {
                    $isimg = true;
                    $imagedetails = getimagesize($v);

                    $orgwidth = $imagedetails[0];
                    $orgheight = $imagedetails[1];
                }
                // $filename_name = str_replace('.', '_', $filename_name);
                // $filename_name = str_replace('(', '_', $filename_name);
                // $filename_name = str_replace(')', '_', $filename_name);
                $filename_name = str_replace(' ', '_', $filename_name);
                if (DIRECTORY_SEPARATOR === '/') {
                    $upload_success = $v->move($this->basepathfolder, iconv('utf-8', 'big5', $filename_name));
                } else {
                    $upload_success = $v->move($this->basepathfolder, $filename_name);
                }

                if ($isimg) {
                    $imgname = $this->basepathfolder . '/' . iconv('utf-8', 'big5', $filename_name);
                    //$this->correctImageOrientation($imgname);
                    //print_r($imgname);
                    //$imgthumbname = base_path('public/images/thumb/').$this->width.'_'.$this->height.'_'.$filename_name;
                    //print_r($imgthumbname);

                    if ('' == $this->width) {
                        $this->width = $this->request->input($name . '_width');
                    }
                    if ('' == $this->height) {
                        $this->height = $this->request->input($name . '_height');
                    }


                    if (false == PF::isEmpty($this->width) || false == PF::isEmpty($this->height)) {
                        if ('' == $this->width) {
                            $this->width = 9999;
                        }
                        if ('' == $this->height) {
                            $this->height = 9999;
                        }
                        // PF::printr($this->width);
                        // PF::printr($orgwidth);
                        // PF::printr($this->height);
                        // PF::printr($orgheight);
                        if ($this->width < $orgwidth && $this->height < $orgheight) {
                            try {
                                $img = Image::make($imgname);
                                $img->orientate();

                                $img->resize($this->width, $this->height, function ($constraint) {
                                    $constraint->upsize();
                                    $constraint->aspectRatio();
                                });
                                $img->save($imgname, 100);
                            } catch (\Exception $e) {
                                \Log::error($e->getMessage());
                                //echo $e->getMessage(); //throw e;
                            } finally {
                            }
                        }
                    } else {
                        try {
                            Image::make($imgname)->save($imgname, 100);
                        } catch (\Exception $e) {
                            \Log::error($e->getMessage());
                            //echo $e->getMessage(); //throw e;
                        } finally {
                        }
                    }
                }
            }
        }

        return $filename_name;
    }

    public function correctImageOrientation($filename) {
        if (function_exists('exif_read_data')) {
            $exif = exif_read_data($filename);

            if ($exif && isset($exif['Orientation'])) {
                $orientation = $exif['Orientation'];
                if (1 != $orientation) {
                    $img = imagecreatefromjpeg($filename);
                    $deg = 0;
                    switch ($orientation) {
                        case 3:
                            $deg = 180;
                            break;
                        case 6:
                            $deg = 270;
                            break;
                        case 8:
                            $deg = 90;
                            break;

                            if ($deg) {
                                $img = imagerotate($img, $deg, 0);
                                imagejpeg($img, $filename, 100);
                            }
                    }

                    // then rewrite the rotated image back to the disk as $filename
                } // if there is some rotation necessary
            } // if have the exif orientation info
        } // if function exists
    }

    public function uploaderror($code) {
        switch ($code) {
            case UPLOAD_ERR_INI_SIZE:
                $message = 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
                break;
            case UPLOAD_ERR_FORM_SIZE:
                $message = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
                break;
            case UPLOAD_ERR_PARTIAL:
                $message = 'The uploaded file was only partially uploaded';
                break;
            case UPLOAD_ERR_NO_FILE:
                $message = 'No file was uploaded';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $message = 'Missing a temporary folder';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $message = 'Failed to write file to disk';
                break;
            case UPLOAD_ERR_EXTENSION:
                $message = 'File upload stopped by extension';
                break;

            default:
                $message = 'Unknown upload error';
                break;
        }

        return $message;
    }
}
