<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

//訊息公告
class board extends baseModel
{
    public $tabletitle = '訊息公告';
    public $table = 'board';
    public $primaryKey = 'id';

    //欄位必填
    public $rules = [
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(11)'],//
'kind'=>['title'=>'種類','type'=>'varchar(200)'],// //法拍屋[1],中古屋[2],土地專區[3],
'kindid'=>['title'=>'次種類編號','type'=>'int(11)'],//
'title'=>['title'=>'標題','type'=>'varchar(400)'],//
'memo'=>['title'=>'備註','type'=>'varchar(400)'],//
'body'=>['title'=>'本文','type'=>'mediumtext'],//
'begindate'=>['title'=>'開始時間','type'=>'date'],//
'closedate'=>['title'=>'結束時間','type'=>'date'],//
'created_at'=>['title'=>'建立時間','type'=>'datetime'],//
'updated_at'=>['title'=>'異動時間','type'=>'datetime'],//
'boardsort'=>['title'=>'排序號碼','type'=>'float(5,2)'],//
'location'=>['title'=>'','type'=>'varchar(200)'],//
'field1'=>['title'=>'','type'=>'varchar(400)'],//
'field2'=>['title'=>'','type'=>'varchar(400)'],//
'field3'=>['title'=>'','type'=>'varchar(400)'],//
'field4'=>['title'=>'','type'=>'varchar(400)'],//
'field5'=>['title'=>'','type'=>'varchar(400)'],//
'field6'=>['title'=>'','type'=>'varchar(400)'],//
'field7'=>['title'=>'','type'=>'varchar(400)'],//
'field8'=>['title'=>'','type'=>'varchar(400)'],//
'field9'=>['title'=>'','type'=>'varchar(400)'],//
'hits'=>['title'=>'點閱次數','type'=>'int(11)'],//
'userid'=>['title'=>'操作者編號','type'=>'int(11)'],//
'online'=>['title'=>'上下架','type'=>'int(11)'],// //上架[1],下架[0],
'useraccount'=>['title'=>'操作者帳號','type'=>'varchar(50)'],//
'alg'=>['title'=>'語系','type'=>'varchar(5)'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';

    protected $fillable = ['kind','kindid','title','memo','body','begindate','closedate','created_at','updated_at','boardsort','location','field1','field2','field3','field4','field5','field6','field7','field8','field9','hits','userid','online','useraccount','alg'];
    protected $guarded = [];
    protected $dates = ['begindate','closedate','created_at','updated_at'];

    //public function kind()
    //{
    //  return $this->belongsTo(kind::class,'kindid');
    //}
/*
    public function __construct($attr = array(), $exists = false)
    {
        $this->fillable = parent::getfillables();//接受$request->all();
        parent::__construct($attr, $exists);

        parent::setFieldInfo($this->fieldInfo);
    }
*/
    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
        });
        static::deleted(function ($model) {
        });
    }
}
