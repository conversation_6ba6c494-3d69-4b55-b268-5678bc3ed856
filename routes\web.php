<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/



// Route::group(['prefix'=>'news'], function(){
//     Route::get('/', 'newsController@index');
//     Route::get('create', 'newsController@create');
//     Route::get('show/{id}', 'newsController@show');
//     Route::get('edit/{id}', 'newsController@edit');
//     Route::get('update/{id}', 'newsController@update');
// });

//會自動產生index,create,store,show,update,destory,edit
//Route::resource('photos', 'PhotoController');
// Route::resource('admin/news', 'admin/newsController');
//except除了create', 'edit
//Route::resource('crud', 'Api\CRUDController')->except('create', 'edit');
// 只充許
// Route::resource('photos', 'PhotoController')->only([
//     'index', 'show'
// ]);
//多筆
// Route::resources([
//     'photos' => 'PhotoController',
//     'posts' => 'PostController',
// ]);



Route::group(['middleware' => 'AuthAdmin', 'prefix' => 'admin', 'namespace' => 'admin'], function () {

    Route::match(['get', 'post'], '/', '\App\Http\Controllers\admin\adminloginController@index');
    Route::any(
        '/{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {

            return \App::Make('App\\Http\\Controllers\admin\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});

Route::group(['middleware' => 'Front', 'prefix' => 'member', 'namespace' => 'member'], function () {
    Route::any('show/{id?}', 'showController@index')->where('id', '[0-9]+');
    Route::any('show/{id?}/store', 'showController@store')->where('id', '[0-9]+');

    Route::any(
        '/{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {
            return \App::Make('App\\Http\\Controllers\member\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
Route::group(['middleware' => 'AuthMember', 'prefix' => 'membercenter/', 'namespace' => 'membercenter'], function () {
    Route::any(
        '/{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {
            return \App::Make('App\\Http\\Controllers\membercenter\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});



Route::any('about/{id?}', 'aboutController@index')->defaults('id', 'about');
Route::any('news/show/{id?}', 'newsController@show')->where('id', '[0-9]+');
Route::any('news/{kindid?}', 'newsController@index')->where('kindid', '[0-9]+')->defaults('kindid', '');
Route::any('house/show/{id?}', 'houseController@show');
Route::any('message/show/{id?}', 'messageController@show');
Route::any('message/success/{id?}', 'successController@show');
Route::group(['middleware' => 'Front', 'prefix' => '/'], function () {

    Route::any(
        '/{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {

            if (str_contains($action, ".")) {
                return $action . " - not found.";
            }
            return \App::Make('App\\Http\\Controllers\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
