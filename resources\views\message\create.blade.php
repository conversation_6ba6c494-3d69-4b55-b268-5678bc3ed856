@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')
<div class="px-3">
    <div class="theme-container">
        <div class="row">
            <nav aria-label="breadcrumb" class="col-xs-12">
                <ol class="breadcrumb end-xs">
                    <li class="breadcrumb-item"><a href="{{ url('/') }}/">首頁</a></li>
                    <li class="breadcrumb-item active" aria-current="page">我要留言</li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<div class="px-3">
    <div class="theme-container">
        <div class="mdc-card main-content-header mb-5">
            <div class="row around-xs">
                <h3 class="w-100 text-center py-3">我要留言</h3>
                <script language=JavaScript>
                    function oForm_onsubmit(form) {
                        if (PF_FormMultiAll(form) == false) { return false };
                        PF_FieldDisabled(form)
                        return true;
                    }
                </script>

                <form action="{{ url('/') }}/message/store" class="contact-form row p-5" method="post" onsubmit="return oForm_onsubmit(this);">
                    <div class="col-xs-12 col-sm-12 col-md-6 p-2">
                        {{Form::myUIXml([
                                                    'xmldoc' => $data['xmldoc'],
                                                    'type' =>'select',
                                                    'title' =>'留言類別',
                                                    'node' => '//參數設定檔/留言類別/KIND',
                                                    'name' => 'kind',
                                                    'value' => $data['kind'],
                                                    'linecount' => 4,
                                                    'firsttxt'=>'請選擇',
                                                    'requiredclass' => 'required[1,TEXT]',
                                                    'class'=>'form-control form-control-lg'
                                                ])
                        }}

                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 p-2">
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 p-2">
                        <div class="mdc-text-field mdc-text-field--outlined w-100">
                            <input class="mdc-text-field__input" placeholder="姓名 is required" name="name" required title="姓名" requiredclass="required[1,TEXT]">
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">姓名</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 p-2">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="sex" id="inlineRadio1" value="先生">
                            <label class="form-check-label" for="inlineRadio1">先生</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" value="女士" name="sex" id="inlineRadio2">
                            <label class="form-check-label" for="inlineRadio2">女士</label>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 p-2">
                        <div class="mdc-text-field mdc-text-field--outlined w-100">
                            <input class="mdc-text-field__input" type="email" placeholder="Email is required" required title="Email" requiredclass="required[1,EMAIL]" name="email">
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">Email</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 p-2">
                        <div class="mdc-text-field mdc-text-field--outlined w-100">
                            <input class="mdc-text-field__input" name="title" required title="留言標題" requiredclass="required[1,TEXT]" placeholder="聯絡電話 is required">
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">留言標題</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 p-2">
                        <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--textarea w-100">
                            <textarea class="mdc-text-field__input" rows="5" placeholder="留言 is required" name="body" required title="留言內容" requiredclass="required[1,TEXT]"></textarea>
                            <div class="mdc-notched-outline mdc-notched-outline--upgraded">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">留言內容</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 w-100 py-3 text-center">
                        <script src="https://www.google.com/recaptcha/api.js?render={{config('recaptcha.id')}}"></script>
                        <script>
                            $("button").each(function(index) {
                                $(this).prop('disabled', true);
                            });
                            grecaptcha.ready(function() {
                                grecaptcha.execute("{{config('recaptcha.id')}}", { action: 'homepage' }).then(function(token) {
                                    var recaptchaResponse = document.getElementById('recaptchaResponse');
                                    recaptchaResponse.value = token;
                                    $("button").each(function(index) {
                                        $(this).prop('disabled', false);
                                    });
                                });
                            });
                        </script> <input type="hidden" value="" name="google_recaptcha_token" id="recaptchaResponse">
                        <button class="mdc-button mdc-button--raised" type="submit">
                            <span class="mdc-button__ripple"></span>
                            <span class="mdc-button__label">送出表單</span>
                        </button>
                    </div>

                    <input type="hidden" name="gobackurl" value="{{ url('/') }}/message">
                </form>
            </div>
            <!--<div class="mt-5">
                    <div id="contact-map"></div>
                </div> -->
        </div>
    </div>
</div>
@endsection