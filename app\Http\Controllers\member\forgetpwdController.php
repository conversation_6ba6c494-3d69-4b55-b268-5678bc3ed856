<?php

namespace App\Http\Controllers\member;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Config;
use DB;
use PF;
use App\Repositories\memberRepository;
class forgetpwdController extends Controller
{
    private $data;

    private $memberRepo;
    public function __construct(memberRepository $memberRepo)
    {
        
        
        
        
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->memberRepo=$memberRepo;
        $this->data['displaynames'] =$this->memberRepo->getFieldTitleArray();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        Config::set('config.description', '');

        return view('member.forgetpwd.index', [
            'data' => $this->data,
            ]
       );
    }

    public function store(Request $request)
    {
        $memberService = new \App\Services\memberService();
        $resultmessage=$memberService->forgetPassword($request,$this->data);
        //return redirect(app()->getLocale().'/member/login')->with('js', '_alert("'.$resultmessage.'")');
        return redirect('member/login')->with('js', '_alert("'.$resultmessage.'")');
    }
}
