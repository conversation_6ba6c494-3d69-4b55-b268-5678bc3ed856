@extends('admin.layouts.master')
@section('css')
    <style>
        body {

            overflow-x: hidden;
            /*刪除水平捲軸*/
            overflow-y: show;
            background: #343a40;
        }
    </style>
@endsection

@section('js')
    <script type="text/javascript">
        document.addEventListener("DOMContentLoaded", () => {
            //console.log(jQuery("nav.mt-2 > ul > li.nav-item[level=1]").length);
            jQuery("nav.mt-2 > ul > li.nav-item[level=1]").each(function(i, item1) {
                // console.log($(this).find('li.nav-item[level=2]').html());
                // console.log($(this).find('li.nav-item').find('a').length);

                if ($(this).find('li.nav-item a').length == 0) {
                    //console.log($(this).html());
                    $(this).eq(0).hide();
                }
                $(item1).find('li.nav-item[level=2]').each(function(i, item2) {
                    if ($(item2).find('li.nav-item a').length == 0) {
                        //console.log($(this).html());
                        $(item2).eq(0).hide();
                    }

                });
            });
        });

        $(document).ready(function() {
            $("a").click(function(event) {

                if ($(this).attr('href') != '#') {
                    var width = $(window.top).width();
                    if (width < 768) {
                        $('#frameset', window.top.document).attr('cols', '0,*');
                    }
                }
            });
        });
    </script>
@endsection



@section('content')
    <div class="wrapper" class="hold-transition layout-fixed" data-panel-auto-height-mode="height">
        <aside class="sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="#" class="brand-link">

                <span class="brand-text font-weight-light">{{ PF::getConfig('name') }}</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu"
                        data-accordion="false">
                        {!! $data['menu'] !!}
                    </ul>
                </nav>
            </div>
        </aside>
    </div>

    <script src="{{ url('/') }}/assets/adminlte/js/adminlte.min.js?d=4"></script>
@endsection
