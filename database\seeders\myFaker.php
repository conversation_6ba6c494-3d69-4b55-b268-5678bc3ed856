<?php

namespace Database\Seeders;

use PF;

class myFaker {
    public $city1title;
    public function __construct() {
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        $this->faker = \Faker\Factory::create('zh_TW');
    }

    /**
     * Run the database seeds.
     */
    public function getImage($folder, $w = 100, $h = 100) {
        \File::copy(public_path('images/no-picture.gif'), public_path($folder . "/no-picture.gif"));
        return 'no-picture.gif';
    }
    public function getHttpImage($folder, $w = 100, $h = 100) {

        $img = \Image::make('https://picsum.photos/' . $w . '/' . $h)->encode('png');
        $filename = date('YmdHis') . '.png';

        $img->save(public_path($folder) . '/' . $filename);


        return $filename;
    }
    public function getImageName() {
        return date('YmdHis') . floor(microtime() * 1000) . ".png";
    }
    public function getDocumentName() {
        return date('YmdHis') . floor(microtime() * 1000) . ".doc";
    }

    /**
     * Run the database seeds.
     */
    public function getName() {
        $items = ['王小明', '蔡伊林', '林俊傑', '江蕙', '郭雪芙', '張鈞甯', '田馥甄', '林志玲', '張景嵐', '徐熙娣', '林依晨', '陳意涵', '周渝民', '王陽明', '劉以豪', '張孝全', '李國毅', '炎亞綸', '鄭元暢', '賀軍翔', '李程彬', '霍建華'];

        return $items[rand(0, count($items) - 1)] . \Str::random(2);;
    }

    public function getTel() {
        return '02-250' . rand(100000, 999999);
    }

    public function getMobile() {
        return '0912' . rand(100000, 999999);
    }
    public function getAccount() {
        return 'test' . rand(100000, 999999);
    }

    public function getCity1() {



        return "台北市";
    }

    public function getCity2() {

        return "中正區";
    }

    public function getAddress() {
        //$items = ['中山北路', '忠孝路', '信義路', '仁愛路', '和平東路', '和平西路', '博愛路', '敦化北路', '敦化南部', '重慶北路', '重慶南路', '重慶南路'];

        //return $items[rand(0, count($items)-1)].rand(1, 3)."段".rand(1, 10)."鄰".rand(1, 10)."巷".rand(1, 10)."弄".rand(1, 199)."號".rand(1, 10)."樓";
        return $this->faker->streetName . $this->faker->streetAddress;
    }

    public function getCity2_Postal($city1title) {
        return "中正區100";
    }
    public function getRndNumber($start = 100000, $end = 99999) {
        return rand($start, $end);
    }

    public function getDbData($sqlraw) {
        $rows = \DB::select(\DB::raw($sqlraw));
        //$rows = $rows->get();
        $temp = '';
        foreach ($rows as $rs) {
            $rs = get_object_vars($rs);
            $arrkey = array_keys($rs);
            $temp .= $rs[$arrkey[0]] . ',';
        }
        $temp = ltrim(rtrim($temp, ','), ',');

        return $temp;
    }

    public function getDate() {
        return date('Y-m') . '-' . rand(1, 28);
    }
    public function getUrl() {
        $items = ['https://www.yahoo.com.tw', 'https://www.google.com.tw', 'https://www.pchome.com.tw', 'https://www.mobile01.com.tw', 'https://shopee.tw/', 'https://udn.com/news/index', 'https://www.facebook.com.tw', 'https://tw.bid.yahoo.com/', 'https://www.setn.com', 'https://www.momoshop.com.tw', 'https://www.setn.com', 'https://ptt.cc', 'https://www.ettoday.net', 'https://www.ltn.com.tw', 'https://www.gamer.com.tw'];

        return $items[rand(0, count($items) - 1)];
    }

    public function getXml($title) {
        $items = [];

        foreach ($this->data['xmldoc']->xpath('//參數設定檔/' . $title . '/KIND') as $v) {
            if ('' != $v->傳回值) {
                $items[] = strval($v->傳回值);
            } else {
                $items[] = strval($v->資料);
            }
        }

        return $items[rand(0, count($items) - 1)];
    }

    public function getUid() {
        $items = ['A123456789', 'B259633782', 'A163036127', 'B159633780', 'B242625109', 'F269391731', 'F139372020', 'S118717501', 'S238484354', 'U298835315', 'U187601507', 'D128951095', 'D276715785', 'A232467158', 'V204783949', 'T216213138', 'E232320636', 'B114461997', 'B182065210'];

        return $items[rand(0, count($items) - 1)];
    }
    public function getEmail() {
        $items = ['allen', 'jason', 'kevin', 'eric', 'david', 'james', 'alex', 'jerry', 'andy', 'jack', 'vincent', 'sam', 'ken', 'chris', 'tony', 'leo', 'peter', 'steven', 'ryan', 'daniel', 'emily', 'amy', 'alice', 'grace', 'tian', 'joyce', 'vivian', 'ivy', 'cindy', 'jenny', 'annie', 'vicky', 'jessica', 'peggy', 'sandy', 'irene'];

        return $items[rand(0, count($items) - 1)] . rand(1, 100) . "@gmail.com";
    }
}
