@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')

<div class="px-3">
    <div class="theme-container">
        <div class="row agents-wrapper">
            @foreach ($data['rows'] as $rs)   

            
            
            <div class="row item col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 mb-5">
                <div class="mdc-card property-item list-item column-1">
                    <div class="pthumbnail-section pr-3">
                        
                        {{
                                Html::myUIImage([
                                    'folder' => "images/place",
                                    'filename' => $rs->field1,
                                    'alt' => $data['title'],
                                    
                                    'class' => 'mw-100',
                                ])
                        }}


                    </div>
                    <div class="pthumbnail-section">
                        <div style="max-height: 100px;">
                            
                            <iframe
                                src="https://maps.google.com.tw/maps?f=q&source=s_q&hl=zh-TW&geocode=&q={{urlencode($rs->memo)}}&ie=UTF8&z=16&output=embed"
                                width="444" height="333" frameborder="0" style="border:0;" allowfullscreen=""
                                aria-hidden="false" tabindex="0"></iframe>
                        </div>
                    </div>

                    <div class="pproperty-content-wrapper">
                        <div class="property-content">
                            <div class="content">
                                <h1 class="title"><a href="#">{{$rs->title}}</a></h1>
                                <p class="row date mb-3">
                                    <i class="material-icons text-muted">location_on</i>
                                    <span class="mx-2">{{$rs->memo}}</span>
                                </p>
                                <hr class="my-3">
                                <p>TEL：
                                    {{$rs->field2}}
                                    <br>FAX：
                                    {{$rs->field3}}
                                </p>
                            </div>
                            <div class="grow"></div>
                        </div>
                    </div>
                </div>
            </div>

            @endforeach

        </div>
        @if (count($data['rows'] )==0)
                    No Data
            @endif
                    {{ $data['rows']!=null ? $data['rows']->links('layouts.paginate') :"" }}
    </div>
</div>


@endsection