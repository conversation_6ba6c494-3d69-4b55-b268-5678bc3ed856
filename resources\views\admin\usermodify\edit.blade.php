@extends('admin.layouts.master')

@section('css')

@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
function oForm_onsubmit(form) {
    if (PF_FormMultiAll(form) == false) {
        return false
    };
    
    if (form.elements['password'].value !=form.elements['pwpassword1'].value ) {_alert('密碼與確認密碼不符');form.elements['pwd1'].focus();return false;}
    PF_FieldDisabled(form) //將全部button Disabled
    return true;
}
</SCRIPT>
<div class="card">
    <div class="card-body">

        <!--// TODO : 前端資料填寫-->
        <form name="oForm" id="oForm" method="post" language="javascript"
            action="{{ URL::to('admin/usermodify/store') }}" onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->

            <div class="form-group row">
                <label class="col-md-2">帳號：<font class="text-danger"></font></label>
                <div class="col-md-10">
                    {{$data['account']}}

                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">密碼：<font class="text-danger"></font></label>
                <div class="col-md-10">


                    <input type="password" class="form-control" name="password" autocomplete="off"
                        requiredclass="required[0,PASSWORD]" value="" size="40" maxlength="15" data-toggle="tooltip"
                        data-placement="top" autocomplete="off"
                        title="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *" />

                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">確認密碼：<font class="text-danger"></font></label>
                <div class="col-md-10">
                    <input type="password" class="form-control" name="password1" autocomplete="off"
                        requiredclass="required[0,PASSWORD]" autocomplete="off" title="密碼" value="" size="40"
                        maxlength="15" />

                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">姓名：<font class="text-danger">*</font></label>
                <div class="col-md-10">
                    <input type="text" name="name" class="form-control" title="姓名" required
                        requiredclass="required[1,TEXT]" value="{{$data['name']}}" size="40" />

                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">E-Mail：<font class="text-danger"></font></label>
                <div class="col-md-10">
                    <input type="email" name="email" class="form-control" title="E-Mail"
                        requiredclass="required[0,EMAIL]" value="{{$data['email']}}" size="40"
                        placeholder="ex <EMAIL>" />

                </div>
            </div>







            <div align="center">
                <button type="submit" class="btn btn-success">確定</button>

                {!!Session::get('success')!!}
            </div>
            @include('admin.layouts.hiddencommon')

            {!! Form::hidden("edit", $data['edit']) !!}
            {!! Form::hidden("gobackurl", $data['gobackurl'] ) !!}
        </form>

    </div>
</div>


@stop