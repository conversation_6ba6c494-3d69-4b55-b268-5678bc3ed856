<?php

namespace App\Libraries;

use DB;
use Form;
use Exception;
use Illuminate\Support\HtmlString;

/***
"功能名稱":"透過描述檔產生UI",
"建立時間":"2022-01-18 13:18:28",
 ***/
class PM {
    protected $request;
    protected $xmlspec;
    protected $xmldoc;

    public function __construct($request, $xmldoc, $xmlspec) {
        $this->request = $request;
        $this->xmldoc = $xmldoc;
        $this->xmlspec = $xmlspec;
    }

    public function getSqlInfo($sql) {
        $array = explode(' ', $sql);
        $table = $array[3];
        $array = explode(',', $array[1]);
        $fieldid = $array[0];
        $fieldname = $array[1];

        return [$table, $fieldid, $fieldname];
    }

    public function search() {
        $array = array();
        try {
            $result = $this->xmlspec->xpath('//table'); // returns an array with one element
            $tablename = strval($result[0]['name']);

            $xPath = "//table/Field[@search='Y' and @type!='date']";

            $items = $this->xmlspec->xpath($xPath);
            //PF::printr($v);

            $array[''] = '請選擇';
            foreach ($items as $k => $v) {
                //PF::printr($v);
                $Field = strval($v['name']);
                $title = strval($v['title']);
                switch (strtolower($v['method'])) {
                    case 'db':

                        if ('id' == PF::right($Field, '2')) {
                            $array1 = $this->getSqlInfo($v['sql']);
                            $table = $array1[0];
                            $fieldid = $array1[1];
                            $fieldname = $array1[2];
                            $array[$table . '.' . $fieldname] = $title;
                        }

                        break;

                    default:
                        if (strtolower('int' == $v['type'])) {
                            $array[$tablename . '.' . $Field . '|N'] = $title;
                        } else {
                            $array[$tablename . '.' . $Field] = $title;
                        }
                        break;
                }
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }

        return $array;
    }

    public function searchdate() {
        $array = [];
        try {
            $result = $this->xmlspec->xpath('//table'); // returns an array with one element
            $tablename = strval($result[0]['name']);
            $xPath = "//table/Field[@search='Y' and @type='date' ]";
            $items = $this->xmlspec->xpath($xPath);
            foreach ($items as $k => $v) {
                $array[$tablename . '.' . strval($v['name'])] = strval($v['title']);
            }
        } catch (\Exception $e) {
            //var_dump($e);
            throw new Exception($e->getMessage());
        }

        return $array;
    }

    public function modify_item($v, $data) {


        $html = '';
        $Field = strval($v['name']);

        $Value = $data[$Field];
        //PF::printr($Field." > ".$Value);

        $title = strval($v['title']);
        $required = '';
        $method = strtolower(strval($v['method']));
        if ('Y' == $v['必填']) {
            $required = 'required';
            //$RequiredClass = 'required[1,'.strtoupper($method).']';
        } else {
            //$RequiredClass = 'required[0,'.strtoupper($method).']';
        }
        switch ($method) {
            case 'span':
                $html .= '<span id="' . $Field . '"></span>' . PHP_EOL;
                break;
            case 'vue-uiuploadfile':
            case 'vue-uiupload':

                $html .= '<input type="file" @change="selectFile($event,inputs)" class="form-control" name="' . $Field . '" requiredclass="' . $RequiredClass . '"   title="' . $v['title'] . '">';
                $html .= '<a :href="\'' . url('/') . '/' . $v['上傳目錄'] . '/\'+inputs.' . $Field . '"><div v-show="inputs.' . $Field . '!=null &&　(inputs.' . $Field . ').substring(0,5)!=\'data:\'" v-text="inputs.' . $Field . '"></div></a>' . PHP_EOL;
                break;
            case 'uiuploadfile':
            case 'uiupload':

                $html .= Form::myUIUpload([
                    'name' => $Field,
                    'folder' => $v['上傳目錄'],
                    'filename' => $Value,
                    'title' => $title,
                    'width' => $v['Width'],
                    'height' => $v['Height'],
                    //'requiredclass' => $RequiredClass,
                    'accept' => strval($v['可上傳格式']),
                ]);

                break;
            case 'uiuploadmutlifile':

                $html .= Form::myUIMultiUpload([
                    'name' => $Field,
                    'folder' => $v['上傳目錄'],
                    'filename' => $Value,
                    'title' => $title,
                    'width' => $v['Width'],
                    'height' => $v['Height'],
                    'accept' => strval($v['可上傳格式']),
                ]);
                break;

            case 'city1':
                //  $GLOBALS['city1field'] = $Field;
                // $GLOBALS['city1value'] = $Value;
                break;

                break;
            case 'date':
            case 'datetime':
            case 'pj_inputdate':
                if ('pj_inputdate' == $method) {
                    $method = 'date';
                }

                $html .= Form::myUISelectDate([
                    'name' => $Field,
                    'type' => $method,
                    'title' => $title,
                    'value' => $Value,
                    'requiredclass' => $RequiredClass,
                ]);

                break;
            case 'textarea':
                $html .= '<textarea class="form-control" v-model="inputs.' . $Field . '" name="' . $Field . '" cols="50" rows="10"  requiredclass="' . $RequiredClass . '" ' . $required . ' title="' . $v['title'] . '">' . $Value . '</textarea>';

                break;
            case 'int':
                $html .= '<input type="number" v-model="inputs.' . $Field . '" class="form-control" name="' . $Field . '" value="' . htmlspecialchars($Value) . '" requiredclass="' . $RequiredClass . '"  ' . $required . '  size="10" maxlength="10"  title="' . $v['title'] . '">';
                break;
            case 'checkbox':
                $html .= '<div class="form-check form-check-inline">';

                $html .= '<input v-model="inputs.' . $Field . '" type="checkbox" class="form-check-input" value="1" name="' . $Field . '" ';
                if ('1' == $Value) {
                    $html .= ' checked';
                }
                $html .= ' value="' . htmlspecialchars($Value) . '" requiredclass="' . $RequiredClass . '"  ' . $required . '  size="10" maxlength="10"  title="' . $v['title'] . '">';
                $html .= '</div>';
                break;
            case 'xml':
                $xtitle = $title;
                $xtitle = str_replace('/', '', $xtitle);
                $xtitle = str_replace("'", '', $xtitle);
                $xtitle = str_replace(' ', '', $xtitle);
                $xtitle = str_replace('?', '', $xtitle);
                $html .= Form::myUIXml([
                    'xmldoc' => $this->xmldoc,
                    'type' => $v['methodtype'],
                    'title' => $title,
                    'node' => '//參數設定檔/' . $xtitle . '/KIND',
                    'name' => $Field,
                    'value' => $Value,
                    'linecount' => 4,
                    'requiredclass' => $RequiredClass,
                ]);

                break;
            case 'db':
            case 'select2':
                $html .= Form::myUIDb([
                    'type' => $v['methodtype'],
                    'title' => $title,
                    'sql' => $v['sql'],
                    'name' => $Field,
                    'value' => $Value,
                    'linecount' => 4,
                    'requiredclass' => $RequiredClass,
                ]);
                break;
            case 'html':
                $html .= '<textarea  v-model="inputs.' . $Field . '" class="form-control" name="' . $Field . '" cols="50" rows="10" title="' . $v['title'] . '">' . $Value . '</textarea>';
                $html .= '<script src="' . asset('ckeditor/ckeditor.js') . '"></script>' . PHP_EOL;
                $html .= '<script>' . PHP_EOL;
                $html .= "document.addEventListener(\"DOMContentLoaded\", () => {" . PHP_EOL;
                $html .= '   CKEDITOR.replace("' . $Field . '")' . PHP_EOL;
                $html .= "})" . PHP_EOL;
                $html .= '</script>' . PHP_EOL;
                break;
            case 'label':
                $html .= '<div v-text="inputs.' . $Field . '">' . htmlspecialchars($Value) . '</div>' . PHP_EOL;
                break;
            default:
                $html .= '<input class="form-control" placeholder="' . $v['placeholder'] . '" v-model="inputs.' . $Field . '"  type="' . $v['method'] . '" name="' . $Field . '" value="' . htmlspecialchars($Value) . '" requiredclass="' . $RequiredClass . '"  ' . $required . ' title="' . PF::LG($v['title']) . '">' . PHP_EOL;

                break;
        }

        return new HtmlString($html);
    }

    public function modify($data) {
        $html = '';
        $xPath = "//table/Field[@edit='Y']";
        $items = $this->xmlspec->xpath($xPath);

        foreach ($items as $k => $v) {
            $Field = strval($v['name']);
            $title = strval($v['title']);
            $required = '';
            $method = strval($v['method']);
            if ('uiuploadfile' == $v['method']) {
                $method = str_replace(',', ';', $v['可上傳格式']);
            }
            if ('Y' == $v['必填']) {
                $required = 'required';
                $RequiredClass = 'required[1,' . strtoupper($method) . ']';
            } else {
                $RequiredClass = 'required[0,' . strtoupper($method) . ']';
            }

            $html .= '<div class="form-group row">';
            if ('th' == $v['method']) {
                $html .= '<div>';
                $html .= '<h3>';
                $html .= $v['title'];
                $html .= '</h3>';
            } else {
                if ('city1' != strtolower($v[$i]['method'])) {
                    $html .= '<label class="col-md-2">' . $v['title'];
                    if ('Y' == $v['必填']) {
                        $html .= "<font class='text-danger p-1'>*</font>";
                    }
                    $html .= '：</label>' . PHP_EOL;
                    $html .= '<div class="col-md-10">';
                    $html .= PHP_EOL;
                }
                $html .= $this->modify_item($v, $data);
                if ('' != $v['Memo']) {
                    $html .= $v['Memo'];
                    $html .= PHP_EOL;
                }
                if ('' != $v->Memo) {
                    $html .= $v->Memo;
                    $html .= PHP_EOL;
                }
                $html .= '</div>';
            }

            $html .= PHP_EOL;
            $html .= '</div>';
            $html .= PHP_EOL;
        }

        return new HtmlString($html);
    }

    public function th() {
        $html = '';
        try {
            $xPath = "//table/Field[@list='Y']";
            $items = $this->xmlspec->xpath($xPath);
            foreach ($items as $k => $v) {
                switch ($v['type']) {
                    case 'date':
                    case 'int':
                        $html .= '<th id="' . $v['name'] . '" >' . $v['title'] . '</th>' . PHP_EOL;
                        break;
                    default:
                        $html .= '<th width="" id="' . $v['name'] . '" >' . $v['title'] . '</th>' . PHP_EOL;
                }
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }

        return new HtmlString($html);
    }

    public function show_item($v, $value) {
        $Field = strval($v['name']);

        switch (strtolower($v['method'])) {
            case 'db':
                if ('id' == PF::right($Field, '2')) {
                    // if (strval($Field)=="kindid"){
                    $array = $this->getSqlInfo($v['sql']);
                    $table = $array[0];

                    $fieldid = $array[1];
                    $fieldname = $array[2];
                    $rows1 = DB::table($table);
                    $rows1->selectRaw($fieldname);
                    $rows1->myWhere($fieldid . '|S', $value, 'kind', 'N');

                    $rows1 = $rows1->limit(1);

                    if ($rows1->count() > 0) {
                        $rs1 = $rows1->first();
                        $rs1 = get_object_vars($rs1);
                        $arrkey = array_keys($rs1);
                        $title = $rs1[$fieldname];
                    }
                    $html .= $title;
                } else {
                    $html .= $value;
                }

                break;
            case 'xml':
                $xtitle = $v['title'];
                $xtitle = str_replace('/', '', $xtitle);
                $xtitle = str_replace("'", '', $xtitle);
                $xtitle = str_replace(' ', '', $xtitle);
                $xtitle = str_replace('?', '', $xtitle);
                //$UI->Node="//參數設定檔/".$xtitle."/KIND";

                $html .= PF::xmlSearch($xmldoc, '//參數設定檔/' . $xtitle . '/KIND/傳回值', '資料', $value);
                break;
            case 'text':
                switch (strtolower($v['type'])) {
                    case 'int':
                        $html .= PF::formatNumber($value, 0);
                        break;
                    case 'date':
                        $html .= PF::formatDate($value);
                        break;
                    default:
                        $html .= $value;
                        break;
                }
                break;
            case 'int':
                $html .= PF::formatNumber($value, 0);
                break;
            case 'uiuploadfile':
                // $UI = new DisplayObject();
                // $UI->Folder = $v['上傳目錄'];
                // $UI->FileName = $value;
                // $UI->Width = '100';
                // $UI->Height = '100';
                // $UI->Noimg = 'no-picture.gif';
                // $UI->CreateHtml();
                break;
            case 'pj_inputdate':
            case 'date':
                $html .= PF::formatDate($value);
                break;
            default:
                $html .= $value;
                break;
        }

        return $html;
    }

    public function list($rs) {
        $rs = get_object_vars($rs);

        $html = '';
        try {
            $xPath = "//table/Field[@list='Y']";
            $items = $this->xmlspec->xpath($xPath);

            foreach ($items as $k => $v) {
                $Field = strval($v['name']);
                $html .= '<td>' . PHP_EOL;
                $value = htmlspecialchars($rs[$Field]);
                $html .= $this->show_item($v, $value);
                $html .= '</td>' . PHP_EOL;
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }

        return new HtmlString($html);
    }
}
