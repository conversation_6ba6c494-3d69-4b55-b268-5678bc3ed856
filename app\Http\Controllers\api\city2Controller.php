<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
//use Illuminate\Support\Facades\DB;
use App\Repositories\city2Repository;

class city2Controller extends Controller {
    private $data;
    private $city2Repo;

    /**
     *建構子.
     */
    public function __construct(city2Repository $city2Repo) {
        //$this->limit="xx";
        parent::__construct();

        $this->city2Repo = $city2Repo;
    }

    /**
     * @OA\Post(
     *     path="/api/city2",operationId="index",tags={"前台/鄉鎮"},summary="列表",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="city1title",description="縣市",type="string",example="zh",),
     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="array",@OA\Items(
     *          @OA\Property(property="city1title", type="integer",description="縣市", ),
     *          @OA\Property(property="city2title", type="string",description="鄉鎮", ),
     *
     * )),
     *     )
     *   ),
     * )
     */
    public function index(Request $request) {
        //\Cache::flush();

        $rows = \Cache::remember('city2', 5 * 60, function () {
            $rows = $this->city2Repo->selectRaw('city2.city1title,city2title,postal');
            //$rows->where('city1title', $request->input('city1title'));
            $rows = $rows->orderByRaw('city2title');
            $rows = $rows->get();
            //$echo $ebody;
            return $rows;
        });
        $rows = collect($rows)->where('city1title', $request->input('city1title'))->all();


        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
