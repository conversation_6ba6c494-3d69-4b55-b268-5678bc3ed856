@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
    {!! $data['nav'] !!}
@endsection

@section('content')

    <SCRIPT language=JavaScript>
        function oForm_onsubmit(form) {
            if (PF_FormMultiAll(form) == false) {
                return false
            };
            PF_FieldDisabled(true)
            return true;
        }
    </SCRIPT>
    <div class="card">
        <div class="card-body">
            <!--// TODO : 前端資料填寫-->

            <form name="oForm" id="oForm" method="post" language="javascript"
                action="{{ request()->url() }}/../excelimportstore" ENCTYPE="multipart/form-data"
                onsubmit="return oForm_onsubmit(this);">
                <div class="form-group row">
                    <label class="col-md-2">EXCEL檔案：<font class="text-danger">*</font></label>
                    <div class="col-md-10">
                        <div class="input-group">
                            <div class="custom-file">
                                <input type="file" accept=".xlsx" name="file1" required class="custom-file-input">
                                <label class="custom-file-label" for="exampleInputFile">選擇檔案</label>
                            </div>
                            <div class="input-group-append">
                                <button class="input-group-text">上傳</button>
                            </div>
                        </div>
                    </div>
                </div>

                @include('admin.layouts.hiddenall')



            </form>
            <form method="post" language="javascript" action="{{ request()->url() }}/../excelsample">
                <button class="btn btn-info">範本</button>

                @include('admin.layouts.hiddenall')
            </form>
        </div>
    </div>

@stop
