<?php

namespace App\Repositories;

use App\Models\message1;
use DB;
use PF;

class message1Repository extends Repository
{
    public $model;
    public $data;

    public function __construct(message1 $model)
    {
        $this->model = $model;
    }
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    public function view($rows)
    {
        $rows->join('diversionurl', 'diversionurl.id', '=', 'diversionlog.diversionurl_id');
        return $rows;
    }


    public function create($inputs)
    {

        return parent::create($inputs);
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        return  parent::update($inputs,$id,'id') ;      
    }

    public function deleteIds($ids)
    {
    //     $this->message1xxRepo = app(\App\Repositories\message1xxRepository::class);
    //     $rows = $this->message1xxRepo->select(null);
    //     $rows->myWhere('message1_id|ININT', $ids, 'message1_id', 'Y');
    //     $rows->delete();

        
    //    \DB::delete('delete message1sign from  message1sign INNER JOIN message1xx ON (message1xx.id=message1sign.message1xx_id) where message1xx.message1_id in (?)',[$ids]);
        
        parent::deleteIds($ids);
      

        // $rows=$this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // $rows->get()->each(function ($rs) {
        //     $path=storage_path($this->model->table.'/'.$rs->img.".json");            
        //     //$path=public_path('images/'.$this->model->table.'/'.$rs->img);
        //     if (\File::exists($path)) {
        //        \File::delete($path);
        //     }            
        //     $rs->delete();
        // });
        
    }
}
