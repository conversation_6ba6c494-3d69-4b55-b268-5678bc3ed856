{"adminuser": {"prefix": "with adminuser ", "scope": "php", "body": ["\\$rows = \\App\\Models\\adminuser::selectRaw('*')", "    ->get();"]}, "adminuserloginlog": {"prefix": "with adminuserloginlog ", "scope": "php", "body": ["\\$rows = \\App\\Models\\adminuserloginlog::selectRaw('*')", "    ->get();"]}, "board": {"prefix": "with board 訊息公告", "scope": "php", "body": ["\\$rows = \\App\\Models\\board::selectRaw('*')", "    ->get();"]}, "city1": {"prefix": "with city1 ", "scope": "php", "body": ["\\$rows = \\App\\Models\\city1::selectRaw('*')", "    ->get();"]}, "city2": {"prefix": "with city2 ", "scope": "php", "body": ["\\$rows = \\App\\Models\\city2::selectRaw('*')", "    ->get();"]}, "epost": {"prefix": "with epost 上稿系統", "scope": "php", "body": ["\\$rows = \\App\\Models\\epost::selectRaw('*')", "    ->get();"]}, "feedback": {"prefix": "with feedback 聯絡我們", "scope": "php", "body": ["\\$rows = \\App\\Models\\feedback::selectRaw('*')", "    ->get();"]}, "formquerylog": {"prefix": "with formquerylog ", "scope": "php", "body": ["\\$rows = \\App\\Models\\formquerylog::selectRaw('*')", "    ->get();"]}, "kind": {"prefix": "with kind ", "scope": "php", "body": ["\\$rows = \\App\\Models\\kind::selectRaw('*')", "    ->get();"]}, "kindhead": {"prefix": "with kindhead ", "scope": "php", "body": ["\\$rows = \\App\\Models\\kindhead::selectRaw('*')", "    ->get();"]}, "kindmain": {"prefix": "with kindmain ", "scope": "php", "body": ["\\$rows = \\App\\Models\\kindmain::selectRaw('*')", "    ->get();"]}, "member": {"prefix": "with member 會員", "scope": "php", "body": ["\\$rows = \\App\\Models\\member::selectRaw('*')", "    ->get();"]}, "member_adminuser": {"prefix": "with member adminuser 會員 ", "scope": "php", "body": ["\\$rows = \\App\\Models\\member::selectRaw('*')", "    ->with(['adminuser' => function (\\$query) {", "        \\$query->selectRaw('id,name');", "    }])", "    ->get();"]}, "message": {"prefix": "with message 留言版", "scope": "php", "body": ["\\$rows = \\App\\Models\\message::selectRaw('*')", "    ->get();"]}, "myproduct": {"prefix": "with myproduct 我的物件", "scope": "php", "body": ["\\$rows = \\App\\Models\\myproduct::selectRaw('*')", "    ->get();"]}, "myproduct_member": {"prefix": "with myproduct member 我的物件 會員", "scope": "php", "body": ["\\$rows = \\App\\Models\\myproduct::selectRaw('*')", "    ->with(['member' => function (\\$query) {", "        \\$query->selectRaw('id,name');", "    }])", "    ->get();"]}, "myproduct_product": {"prefix": "with myproduct product 我的物件 物件", "scope": "php", "body": ["\\$rows = \\App\\Models\\myproduct::selectRaw('*')", "    ->with(['product' => function (\\$query) {", "        \\$query->selectRaw('id');", "    }])", "    ->get();"]}, "product": {"prefix": "with product 物件", "scope": "php", "body": ["\\$rows = \\App\\Models\\product::selectRaw('*')", "    ->get();"]}, "viewcount": {"prefix": "with viewcount ", "scope": "php", "body": ["\\$rows = \\App\\Models\\viewcount::selectRaw('*')", "    ->get();"]}}