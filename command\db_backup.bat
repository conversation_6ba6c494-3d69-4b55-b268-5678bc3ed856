chcp 65001
REM cd..
for /f "skip=1" %%x in ('wmic os get localdatetime') do if not defined MyDate set MyDate=%%x
for /f %%x in ('wmic path win32_localtime get /format:list ^| findstr "="') do set %%x
set fmonth=00%Month%
set fday=00%Day%
set today=%Year%%fmonth:~-2%%fday:~-2%


set "current_dir=%~dp0"
echo Current directory: %current_dir%
set db=%current_dir:C:\AppServ\laravel\=%
set db=%db:\command\=%
REM echo %db%
RME C:\xampp82\mysql\bin\mysqldump %db% -u root -p123456 > C:/Users/<USER>/downloads/db_%db%_%today%.sql
C:\xampp82\mysql\bin\mysqldump %db% -u root -p123456 > C:/Users/<USER>/downloads/db_%db%.sql
cmd /k
