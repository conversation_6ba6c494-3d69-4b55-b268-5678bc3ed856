{
    "carbon toDateString": {
        "prefix": "carbon toDateString yyy-MM-dd",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::now(); ",
            "\\$dt->toDateString(); // 2020-03-18",
        ]
    },
    "carbon toTimeString": {
        "prefix": "carbon toDateString HH-mm-ss",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::now(); ",
            "\\$dt->toTimeString(); // 20:25:00",
        ]
    },
    "carbon toDateTimeString": {
        "prefix": "carbon toDateTimeString yyy-MM-dd HH-mm-ss",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::now(); ",
            "\\$dt->toDateTimeString(); // 2020-03-18 20:25:00",
        ]
    },
    "carbon tomorrow": {
        "prefix": "carbon tomorrow 明天",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::tomorrow(); ",
            "\\$dt->toDateString(); // 2020-03-18",
        ]
    },
    "carbon yesterday": {
        "prefix": "carbon yesterday 昨天",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::yesterday(); ",
            "\\$dt->toDateString(); // 2020-03-18",
        ]
    },
    "carbon parse": {
        "prefix": "carbon parse 文字->日期",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::parse('2016-10-15');",
            "\\$dt->toDateString(); // 2020-03-18",
        ]
    },
    "carbon createFromFormat": {
        "prefix": "carbon createFromFormat 日期格式化Ymd->Y-m-d",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::createFromFormat('Ymd', $v)->format('Y-m-d');",
            "\\$dt->toDateString(); // 2020-03-18",
        ]
    },
    "carbon subDays": {
        "prefix": "carbon subDays 減幾天",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::now()->subDays(5)->diffForHumans(); ",
            "\\$dt->toDateString(); // 2020-03-18",
        ]
    },
    "carbon tomsubDays+": {
        "prefix": "carbon subDays 加幾天",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::now()->diffForHumans(\\Carbon::now()->subDays(5)); ",
            "\\$dt->toDateString(); // 2020-03-18",
        ]
    },
    "carbon parse dayOfWeek": {
        "prefix": "carbon parse dayOfWeek 星期幾",
        "scope": "php",
        "body": [
            "\\$weekdayMap = [",
            "    0 => '日',",
            "    1 => '一',",
            "    2 => '二',",
            "    3 => '三',",
            "    4 => '四',",
            "    5 => '五',",
            "    6 => '六',",
            "];",
            "\\$dt = \\Carbon::parse('2016-10-15');",
            "\\$weekday=$dt->dayOfWeek(); //",
            "\\$weekdayInChinese = \\$weekdayMap[$weekday];",
        ]
    },
    "carbon diff": {
        "prefix": "carbon diff 差幾天 ",
        "scope": "php",
        "body": [
            "\\$dt = \\Carbon::parse('2016-10-15');",
            "\\$diff = $dt->diff(\\Carbon::parse(now()))->format('%d天 %h小時');",
        ]
    },



}