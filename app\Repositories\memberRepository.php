<?php

namespace App\Repositories;

use DB;
use PF;
use App\Models\member;

class memberRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(member $model)
    {
        $this->model = $model;
    }
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    public function view($rows)
    {
        $rows->join('diversionurl', 'diversionurl.id', '=', 'diversionlog.diversionurl_id');
        return $rows;
    }


    public function create($inputs)
    {
        //\Cache::forget($this->model->table);
        $rows = parent::create($inputs);
        $this->cache();
        return $rows;
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        //\Cache::forget($this->model->table);
        $rows = parent::update($inputs, $id, 'id');
        $this->cache();
        return  $rows;
    }
    public function cache()
    {
        //if (\Cache::has($this->model->table)) {
        // \Cache::forget($this->model->table);
        // \Cache::rememberForever($this->model->table, function () {
        //     $rows = DB::table($this->model->table)->selectRaw('*');
        //     $rows = $rows->get();
        //     return $rows;
        // });
        //}
    }


    public function deleteIds($ids)
    {

    //     $this->memberxxRepo = app(\App\Repositories\memberxxRepository::class);
    //     $rows = $this->memberxxRepo->select(null);
    //     $rows->myWhere('member_id|ININT', $ids, 'member_id', 'Y');
    //     $rows->delete();

        // $parenttable = $this->model->table;
        // $childtable = 'votelog';
        // $sql = 'delete '.$childtable.' from '.$childtable.' INNER JOIN '.$parenttable.' ON ('.$parenttable.'.id='.$childtable.'.'.$parenttable.'_id) where '.$childtable.'.'.$parenttable.'_id in (:ids)';
        // \DB::delete($sql, ['ids' => $ids]);
           //\Cache::forget($this->model->table);

           parent::deleteIds($ids);
           $this->cache();
        // $rows=$this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // $rows->get()->each(function ($rs) {
        //     \DB::update('update member set vote =(select count(*) from memberlog where member_id=?) where id = ?', [$rs->member_id,$rs->member_id]);
        //     $path=storage_path($this->model->table.'/'.$rs->img.".json");
        //     //$path=public_path('images/'.$this->model->table.'/'.$rs->img);
        //     if (\File::exists($path)) {
        //        \File::delete($path);
        //     }
        //     $rs->delete();
        // });

    }
}
