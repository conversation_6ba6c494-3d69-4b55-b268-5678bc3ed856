<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Repositories\boardRepository;

class boardbannerSeeder extends Seeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        //\DB::table('member')->truncate();
        $this->boardRepo->select()
        ->myWhere('kind|S', 'banner', 'del', 'Y')
        ->delete();

        $this->faker = \Faker\Factory::create('zh_TW');
        $this->myfaker = new myFaker();
        for ($i = 0; $i < 3; ++$i) {
            $data['kind'] = 'banner';
            //'title' =>  $faker->sentence;
            $data['title'] = $this->faker->sentence;
            $data['field1'] = $this->myfaker->getImage('images/banner', 1020, 800);
            $data['memo'] = $this->faker->safeEmailDomain; //返回安全的郵件域名,
            $data['begindate'] = date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month'))));

            $this->boardRepo->create($data);
        }

        // $this->call(UsersTableSeeder::class);
    }
}
