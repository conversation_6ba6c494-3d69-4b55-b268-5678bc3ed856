<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/*formquerylog*/
class CreateformquerylogTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        if (!Schema::hasTable('formquerylog')) {
            Schema::create('formquerylog', function (Blueprint $table) {
                $table->increments('formquerylogid');
                $table->string('pagename', 4000)->nullable()->comment('功能名稱');
                $table->string('pathinfo', 4000)->nullable()->comment('程式位置');
                $table->string('formbody')->nullable()->comment('FORM欄位值');
                $table->string('querybody', 8000)->nullable()->comment('get內容');

                $table->timestamps();

                /*
                $table->timestamps();
                $table->string('kind')->index()->comment('種類');
                $table->dateTime('begindate')->nullable()->comment('開始時間');
                $table->dateTime('created_at')->nullable()->comment('建立時間');
                $table->dateTime('updated_at')->nullable()->comment('更新時間');
                $table->integer('hits')->default(0)->comment('點率次數');
                $table->float('boardsort', 5, 3)->nullable()->comment('排序號碼');
                $table->integer('userid')->nullable()->comment('編輯人員');
                $table->string('useraccount', 50)->nullable()->comment('編輯人員');
                $table->string('account',50)->unique();;
                $table->unique(array('kind', 'kindid'));
                $table->index(array('kind', 'kindid'));
                */
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('formquerylog');
    }
}
