<div class="row justify-content-center">

    <div class="row msearch">
        <!--側選及狀態 start-->
        <div class="mdc-card main-content-header col-md-2 col-xs-12" style="border-top: 5px solid #313583; display: none;">
            <!--側選單 start-->
            <ul class="mdc-list py-0">
                <li>
                    <a href="https://www.1house.com.tw/" class="mdc-list-item" target="_blank">
                        <img src="{{ url('/') }}/assets/images/logos/oh_logo.svg" class="h-100 mw-100">
                    </a>
                </li>
                <li>
                    <a href="https://www.nra.com.tw/aa007/" target="_blank" class="mdc-list-item">
                        <img src="{{ url('/') }}/assets/images/logos/nr_logo.svg" class="h-100 mw-100">
                    </a>
                </li>
                <li class="mt-2">
                    <div class="mdc-menu-surface--anchor">
                        <button class="mdc-button mdc-ripple-surface text-muted mutable">
                            <span class="mdc-button__ripple"></span>
                            <span class="mdc-list-item__graphic material-icons mx-2" style="color:red; font-size: 36px; width: 36px; height: 36px;">favorite</span>
                            <span class="mdc-button__label"><span class="mutable">便利小工具</span></span>
                            <i class="material-icons mdc-button__icon m-0">arrow_drop_down</i>
                        </button>
                        <div class="mdc-menu mdc-menu-surface px-2">
                            <div class="mdc-list">
                                @foreach ($data['rowslink'] as $rs)

                                <div>
                                    <a href="{{$rs->memo}}" target="_blank" class="mdc-button">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="mdc-button__label text-muted">{{$rs->title}}</span>
                                    </a>
                                </div>
                                @endforeach

                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <!--側選單 end-->
        </div>
        <div class="barSearch">
            <div class="mdc-card text-center col-md-12 col-xs-12 p-0 bgsearch">
                {!!$data['viewcount']!!}
            </div>
            <!--側選及狀態 end-->
            <div class="topSearch">
                <div class="searchRow">
                    {{-- <h2>特殊搜尋</h2> --}}
                    <ul>
                        <li>
                            <a href="{{ url('/') }}/house?location=TaipeiMRT" title="捷運增值">
                                <img src="/images/icon_mrt.png" width="20" alt="">
                                捷運增值
                            </a>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('/') }}/house?location=beattime" title="低價必賣">
                                <img src="/images/icon_money.png" width="20" alt="">
                                低價必賣
                            </a>
                            </a>
                        </li>
                        {{-- <li>
                            <a href="#">
                                明星學區宅
                            </a>
                        </li> --}}
                        <li>
                            <a href="#" title="地圖搜尋">
                                <img src="/images/icon_search0.png" width="15" alt="">
                                地圖搜尋
                            </a>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="searchRow">
                    <a href=""><i class="fa fa-home"></i>房仲買賣</a>
                    <a href=""><i class="fa fa-user"></i>屋主自售</a>
                    <a href="{{ url('/') }}/house?pattern=土地"><i class="fa fa-search"></i>土地搜尋</a>
                </div>
            </div>
            <!--搜尋 start-->
            <form name="oForm" action="{{ url('/') }}/house" method="get" id="filters" class="search-wrapper">
                {{-- {{ Form::hidden("city1title", $data["city1title"] ) }}
                {{ Form::hidden("city2title", $data["city2title"] ) }}
                {{ Form::hidden("pattern", $data["pattern"] ) }}
                {{ Form::hidden("totalupset", $data["totalupset"] ) }}
                {{ Form::hidden("houseage", $data["houseage"] ) }} --}}
                {{ Form::hidden("mainlawnestablishment", $data["mainlawnestablishment"] ) }}
                <div class="inputRow">
                    <h4 class="title">選擇縣市</h4>
                    <div class="inputContent selectContainer" id="city1">
                        @foreach ($data['rowscity1'] as $rs)
                        <div class="items" data-value="{{$rs->city1title}}">
                            {{$rs->city1title}}
                        </div>
                        @endforeach
                        <input type="hidden" name="city1title">
                    </div>
                </div>
                <div class="inputRow">
                    <h4 class="title">區域</h4>
                    <div class="inputContent selectContainer" id="city2">
                        <div class="items">不拘</div>
                        <input type="hidden" name="city2title">
                    </div>
                </div>
                <div class="inputRow">
                    <h4 class="title">房屋型態</h4>
                    <div class="inputContent selectContainer">
                        @foreach ($data['xmldoc']->xpath("//參數設定檔/房屋類別/KIND") as $v)
                        <div class="items" data-value="{{$v->資料}}">{{$v->資料}}</div>
                        @endforeach
                        <input type="hidden" name="pattern">
                    </div>
                </div>
                <div class="inputRow">
                    <h4 class="title">權狀坪數</h4>
                    <div class="inputContent selectContainer">
                        @foreach ($data['xmldoc']->xpath("//參數設定檔/坪數/KIND") as $v)
                        <div class="items" data-value="{{$v->傳回值}}">{{$v->資料}}</div>
                        @endforeach
                        <input type="hidden" name="pingtotalnumberof">
                    </div>
                </div>
                <div class="inputRow">
                    <h4 class="title">購屋預算</h4>
                    <div class="inputContent selectContainer">
                        @foreach ($data['xmldoc']->xpath("//參數設定檔/購屋預算/KIND") as $v)
                        <div class="items" data-value="{{$v->傳回值}}">{{$v->資料}}</div>
                        @endforeach
                        <input type="hidden" name="totalupset">
                    </div>
                </div>
                <div class="inputRow">
                    <h4 class="title">屋齡</h4>
                    <div class="inputContent selectContainer">
                        @foreach ($data['xmldoc']->xpath("//參數設定檔/屋齡/KIND") as $v)
                        <div class="items" data-value="{{$v->傳回值}}">{{$v->資料}}</div>
                        @endforeach
                        <input type="hidden" name="houseage">
                    </div>
                </div>
                <div class="inputRow">
                    <h4 class="title">拍次</h4>
                    <div class="inputContent selectContainer">
                        @foreach ($data['xmldoc']->xpath("//參數設定檔/拍次/KIND") as $v)
                        <div class="items" data-value="{{$v->資料}}">{{$v->資料}}</div>
                        @endforeach
                        <input type="hidden" name="beattime">
                    </div>


                </div>
                <div class="inputRow">
                    <h4 class="title">捷運站、學區</h4>
                    <div class="inputContent">
                        <input type="text" name="search" class="form-control" placeholder="捷運站、學區">
                    </div>
                </div>
                <button class="mdc-button mdc-button--raised" type="submit" style="height: 42px;">
                    <span class="mdc-button__ripple"></span>
                    <i class="material-icons mdc-button__icon">search</i>
                    <span class="mdc-button__label">搜尋</span>
                </button>

                {{-- <div class="row ml-2">
                <div class="col-xs-12 col-sm-6 col-md p-2">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <i class="mdc-select__dropdown-icon"></i>
                            <div class="mdc-select__selected-text"></div>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch">
                                    <label class="mdc-floating-label">不拘</label>
                                </div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface">


                            <ul class="mdc-list">


                                <li class="mdc-list-item" data-value="不拘" onclick="document.forms['oForm'].elements['city1title'].value='';">不拘</li>
                                @foreach ($data['rowscity1'] as $rs)
                                <li class="mdc-list-item @if ($data['city1title']==$rs->city1title) mdc-list-item--selected @endif" data-value="{{$rs->city1title}}" onclick="document.forms['oForm'].elements['city1title'].value=$(this).attr('data-value');$('#city2').html('loading..');$('#city2').load('{{ url('/') }}/api/db/city2?city1title='+$(this).attr('data-value'));">{{$rs->city1title}}</li>
                @endforeach

                </ul>
        </div>
    </div>
</div>
<div class="col-xs-12 col-sm-6 col-md p-2">
    <div class="mdc-select mdc-select--outlined">
        <div class="mdc-select__anchor">
            <i class="mdc-select__dropdown-icon"></i>
            <div class="mdc-select__selected-text"></div>
            <div class="mdc-notched-outline">
                <div class="mdc-notched-outline__leading"></div>
                <div class="mdc-notched-outline__notch">
                    <label class="mdc-floating-label">區域</label>
                </div>
                <div class="mdc-notched-outline__trailing"></div>
            </div>
        </div>
        <div class="mdc-select__menu mdc-menu mdc-menu-surface">
            <ul class="mdc-list" id="city2">
                <li class="mdc-list-item" data-value="不拘" onclick="document.forms['oForm'].elements['city2title'].value='';">不拘</li>
                @if ($data['rowscity2']!=null)
                @foreach ($data['rowscity2'] as $rs)
                <li class="mdc-list-item @if ($data['city2title']==$rs->city2title) mdc-list-item--selected @endif" data-value="{{$rs->city2title}}" onclick="document.forms['oForm'].elements['city2title'].value=$(this).attr('data-value');">{{$rs->city2title}}</li>
                @endforeach
                @endif

            </ul>
        </div>
    </div>
</div>
<div class="col-xs-12 col-sm-6 col-md p-2">
    <div class="mdc-select mdc-select--outlined">
        <div class="mdc-select__anchor">
            <i class="mdc-select__dropdown-icon"></i>
            <div class="mdc-select__selected-text"></div>
            <div class="mdc-notched-outline">
                <div class="mdc-notched-outline__leading"></div>
                <div class="mdc-notched-outline__notch">
                    <label class="mdc-floating-label">房屋型態</label>
                </div>
                <div class="mdc-notched-outline__trailing"></div>
            </div>
        </div>
        <div class="mdc-select__menu mdc-menu mdc-menu-surface">
            <ul class="mdc-list">
                <li class="mdc-list-item" data-value="不拘" onclick="document.forms['oForm'].elements['pattern'].value='';">不拘</li>
                @foreach ($data['xmldoc']->xpath("//參數設定檔/房屋類別/KIND") as $v)

                <li class="mdc-list-item @if ($data['pattern']==$v->資料) mdc-list-item--selected @endif" data-value="{{$v->資料}}" onclick="document.forms['oForm'].elements['pattern'].value=$(this).attr('data-value');;">{{$v->資料}}</li>
                @endforeach
            </ul>
        </div>
    </div>
</div>
<div class="col-xs-12 col-sm-6 col-md p-2">
    <div class="mdc-select mdc-select--outlined">
        <div class="mdc-select__anchor">
            <i class="mdc-select__dropdown-icon"></i>
            <div class="mdc-select__selected-text"></div>
            <div class="mdc-notched-outline">
                <div class="mdc-notched-outline__leading"></div>
                <div class="mdc-notched-outline__notch">
                    <label class="mdc-floating-label">權狀坪數</label>
                </div>
                <div class="mdc-notched-outline__trailing"></div>
            </div>
        </div>
        <div class="mdc-select__menu mdc-menu mdc-menu-surface">
            <ul class="mdc-list">
                <li class="mdc-list-item" data-value="不限" onclick="document.forms['oForm'].elements['pingtotalnumberof'].value='';">不拘</li>
                @foreach ($data['xmldoc']->xpath("//參數設定檔/坪數/KIND") as $v)
                <li class="mdc-list-item @if ($data['pingtotalnumberof']==$v->傳回值) mdc-list-item--selected @endif" data-value="{{$v->傳回值}}" onclick="document.forms['oForm'].elements['pingtotalnumberof'].value=$(this).attr('data-value');;">{{$v->資料}}</li>
                @endforeach
            </ul>
        </div>
    </div>
</div>
<div class="col-xs-12 col-sm-6 col-md p-2">
    <div class="mdc-select mdc-select--outlined">
        <div class="mdc-select__anchor">
            <i class="mdc-select__dropdown-icon"></i>
            <div class="mdc-select__selected-text"></div>
            <div class="mdc-notched-outline">
                <div class="mdc-notched-outline__leading"></div>
                <div class="mdc-notched-outline__notch">
                    <label class="mdc-floating-label">購屋預算</label>
                </div>
                <div class="mdc-notched-outline__trailing"></div>
            </div>
        </div>
        <div class="mdc-select__menu mdc-menu mdc-menu-surface">
            <ul class="mdc-list">
                <li class="mdc-list-item" data-value="不限" onclick="document.forms['oForm'].elements['pingtotalnumberof'].value='';">不拘</li>
                @foreach ($data['xmldoc']->xpath("//參數設定檔/購屋預算/KIND") as $v)
                <li class="mdc-list-item @if ($data['totalupset']==$v->傳回值) mdc-list-item--selected @endif" data-value="{{$v->傳回值}}" onclick="document.forms['oForm'].elements['totalupset'].value=$(this).attr('data-value');;">{{$v->資料}}</li>
                @endforeach
            </ul>
        </div>
    </div>
</div>
<div class="col-xs-12 col-sm-6 col-md p-2">
    <div class="mdc-select mdc-select--outlined">
        <div class="mdc-select__anchor">
            <i class="mdc-select__dropdown-icon"></i>
            <div class="mdc-select__selected-text"></div>
            <div class="mdc-notched-outline">
                <div class="mdc-notched-outline__leading"></div>
                <div class="mdc-notched-outline__notch">
                    <label class="mdc-floating-label">屋齡</label>
                </div>
                <div class="mdc-notched-outline__trailing"></div>
            </div>
        </div>

        <div class="mdc-select__menu mdc-menu mdc-menu-surface">
            <ul class="mdc-list">
                <li class="mdc-list-item" data-value="" onclick="document.forms['oForm'].elements['houseage'].value='';">不拘</li>
                @foreach ($data['xmldoc']->xpath("//參數設定檔/屋齡/KIND") as $v)
                <li class="mdc-list-item @if ($data['houseage']==$v->傳回值) mdc-list-item--selected @endif" data-value="{{$v->傳回值}}" onclick="document.forms['oForm'].elements['houseage'].value=$(this).attr('data-value');;">{{$v->資料}}</li>
                @endforeach
            </ul>
        </div>
    </div>
</div>

<div class="col-xs-12 col-sm-6 col-md p-2">
    <div class="mdc-text-field mdc-text-field--outlined">
        <input class="mdc-text-field__input" name="search" value="{{$data['search']}}" placeholder="捷運站、學區、街道、商圈">
        <div class="mdc-notched-outline">
            <div class="mdc-notched-outline__leading"></div>
            <div class="mdc-notched-outline__notch">
                <label class="mdc-floating-label">捷運站、學區、街道、商圈</label>
            </div>
            <div class="mdc-notched-outline__trailing"></div>
        </div>
    </div>
</div>

<div class="col-xs-12 col-sm-6 col-md center-xs middle-xs p-2">
    <!--<button class="mdc-button mdc-button--raised bg-warn" type="button" id="clear-filter">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">重設</span>
            </button>-->
    <button class="mdc-button mdc-button--raised" type="submit" style="height: 50px;">
        <span class="mdc-button__ripple"></span>
        <i class="material-icons mdc-button__icon">search</i>
        <span class="mdc-button__label">搜尋</span>
    </button>
</div>
</div> --}}
</form>
</div>
<!--搜尋 end-->
</div>
</div>

<script>
    (function() {
        try {
            $('body').on('click', e => {
                $('.inputRow').removeClass('active')
            })
            $('.inputRow').on('click', e => {
                e.stopPropagation()
                var el = $(e.currentTarget)
                // if (el.hasClass('active')) {
                //     return el.removeClass('active')
                // }
                // el.parent().find('.inputRow').removeClass('active')
                el.addClass('active')
                if (el.find('.form-control').length > 0) {
                    el.find('.form-control').focus()
                }
            })
            $('.inputRow input').on('change', e => {
                var el = $(e.currentTarget)
                if (el.val() !== '') {
                    return el.parent().parent('.inputRow').addClass('hasVal')
                }
            })
            $('.inputContent:not([id]) .items').on('click', e => {
                var el = e.currentTarget
                $(el).parent().find('.items').removeClass('active')
                $(el).addClass('active')
                const data = $(el).data()
                setVal(
                    el,
                    (typeof data.value !== 'undefined' ? data.value : el.innerText),
                    el.innerText
                )
            })
            $('#city1 .items').on('click', e => {
                var el = e.currentTarget
                $('#city2').parent().attr('data-val', null)
                $('#city2 input').val('')
                $('#city2 input').prevAll().remove()
                $.get("{{ url('/') }}/api/db/city2?city1title=" + el.innerText, res => {
                    var r = res.match(/(?:>)(.*?)(?:<\/)/g)
                    var html = r.reduce((h, i) => {
                        var iv = i.replace(/>|<\//g, '')
                        return h + '<div class="items"><i class="fa"></i>' + iv + '</div>'
                    }, '')
                    $('#city2 input').before(html)
                })
                $(el).parent().find('.items').removeClass('active')
                $(el).addClass('active')
                const data = $(el).data()
                setVal(
                    el, (typeof data.value !== 'undefined' ? data.value : el.innerText),
                    el.innerText
                )
            })

            $(document).find('#city2').on('click', '.items', e => {
                var el = e.currentTarget
                var itemData = $(el).parent().find('.items')
                var inputVal = $(el).parent().find('input').val()
                var ivData = inputVal.split(',')
                if (el.innerText == '不限') {
                    $(el).parent().find('.items').removeClass('active')
                    return setVal(el, el.innerText)
                }
                var newVal = ivData
                var filterVal = newVal.filter(r => r == el.innerText)
                if (filterVal.length <= 0) {
                    newVal.push(el.innerText)
                    $(el).addClass('active')
                } else {
                    newVal = newVal.filter(r => r !== el.innerText)
                    itemData.each((k, r) => {
                        if (r.innerText !== el.innerText) return
                        $(r).removeClass('active')
                    })
                }
                // console.log([el, inputVal, ivData, newVal])
                newVal = newVal.filter(f => f != '' && f != '不限')
                if (newVal.length > 3) {
                    $(el).removeClass('active')
                    Swal.fire('', '最多只能選三個', 'warning')
                    return false
                }
                setVal(el, newVal.join(','))
            })

            function setVal(el, text = '', innerText = '') {
                $(el).parent().parent('.inputRow').attr(
                    'data-val', (innerText == '' ? (text == '' ? null : text) : innerText)
                )
                $(el).parent().find('input').val(text)
            }
        } catch (error) {}
    })()
</script>