<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PF;

//use Illuminate\Support\Facades\DB;

class aboutController extends Controller
{
    private $data;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        if ('' == $id) {
            $id = 'about';
        }

        $title = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/傳回值', '資料', $id);
        if ($title == $id) {
            $title = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/KIND/傳回值', '資料', $id);
        }
        if ($title == $id) {
            $title = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/KIND/KIND/傳回值', '資料', $id);
        }
        $this->data['title'] = $title;
        $this->data['body'] = \PT::getEpostBody($id);
        //PF::printr($this->data['body']);
        \Config::set('config.title', $title.' | '.config('config.title'));
        $this->data['id'] = $id;

        return view('about.index', [
            'data' => $this->data,
            ]
       );
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
    }
}
