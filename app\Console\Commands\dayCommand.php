<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class dayCommand extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'day:run';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'day Command ';

    public function __construct() {

        parent::__construct();
    }

    public function handle() {
        /* try {
            echo "dayService start\n";
            $cl = new \App\Services\dayService();
            echo $cl->day();
            echo "\ndaySmsService end\n";

        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            echo $e->getMessage();
        }*/
        try {
            $validCommands = array('route:clear', 'config:clear', 'cache:clear', 'view:clear', 'clear-compiled', 'config:cache');
            foreach ($validCommands as $cmd) {
                try {
                    $this->call('' . $cmd . '');
                } catch (\Exception $e) {
                    \Log::error($cmd . ' > ' . $e->getMessage());
                }
            }
        } catch (\CustomException $e) {
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
        \Log::info('day:clear OK');
    }
}
