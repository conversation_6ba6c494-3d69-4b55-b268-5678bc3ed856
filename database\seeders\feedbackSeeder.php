<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use DB;
use App\Repositories\boardRepository;
use Faker\Generator as Faker;

class feedbackSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tablename="feedback";
        //DB::table('board')->delete();
        DB::table($tablename)->truncate();
        
        
        $faker = \Faker\Factory::create('zh_TW');
        
        for ($i = 0; $i < 100; $i++) {
            $data[] = [
                'name'      => $faker->name,
                'company'      => $faker->company,
                'email'      => $faker->email,
                'title' => sha1('foo'),
                'memo'=>mb_substr($faker->city, 0, 3, 'UTF-8'),
                //postcode                            
                //'memo'=>$faker->numberBetween($min = 10000, $max = 50000),
                'adddate'       => date('Y-m-d H:i:s'),
            ];
        }

        DB::table($tablename)->insert($data);

         
        // $this->call(UsersTableSeeder::class);
    }
}
