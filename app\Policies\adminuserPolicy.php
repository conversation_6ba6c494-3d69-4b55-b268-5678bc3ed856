<?php

namespace App\Policies;

use Illuminate\Http\Request;
use App\Models\adminuser;
use App\Models\member;
use Illuminate\Auth\Access\HandlesAuthorization;
use PF;
use App\User;

class adminuserPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     */
    public function __construct()
    {
    }
    public function destroy(?member $member)
    {
        
        if ('999' == \Auth::guard('admin')->user()->status) {
            return true;
        }

        return false;
        //return $adminuser->userid == $user->id;
    }
}
