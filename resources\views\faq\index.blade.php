@extends('layouts.master')
@section('css')
@endsection

@section('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ 展開/收合功能
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                const header = item.querySelector('.faq-header');
                const body = item.querySelector('.faq-body');
                const icon = item.querySelector('.faq-icon');

                header.addEventListener('click', function() {
                    const isOpen = item.classList.contains('faq-open');

                    // 關閉其他展開的項目
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('faq-open');
                            const otherBody = otherItem.querySelector('.faq-body');
                            const otherIcon = otherItem.querySelector('.faq-icon');
                            otherBody.style.maxHeight = '0';
                            otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });

                    // 切換目前項目
                    if (isOpen) {
                        item.classList.remove('faq-open');
                        body.style.maxHeight = '0';
                        icon.style.transform = 'rotate(0deg)';
                    } else {
                        item.classList.add('faq-open');
                        body.style.maxHeight = body.scrollHeight + 'px';
                        icon.style.transform = 'rotate(180deg)';
                    }
                });
            });
        });
    </script>
@endsection

@section('content')
    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">常見問題</h2>
    <!-- 麵包屑導航 -->


    <!-- 主要內容區域 -->
    <div class="bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

            <!-- 頁面標題區域 -->
            <div class="text-center mb-12">

                <p class="text-xl text-gray-600 font-medium">
                    Q & A
                </p>
                <div class="mt-6 w-24 h-1 bg-primary mx-auto rounded-full"></div>
            </div>

            <!-- FAQ 項目列表 -->
            <div class="space-y-4">
                @foreach ($data['rows'] as $index => $rs)
                    <div
                        class="faq-item bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">

                        <!-- FAQ 標題區域 -->
                        <div
                            class="faq-header cursor-pointer p-6 flex items-center justify-between hover:bg-gray-50 transition-colors duration-200">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 pr-4">
                                    {{ $rs->title }}
                                </h3>
                                @if ($rs->memo)
                                    <p class="text-sm text-gray-500 mt-2 hidden md:block">
                                        {{ $rs->memo }}
                                    </p>
                                @endif
                            </div>

                            <!-- 展開/收合圖標 -->
                            <div class="faq-icon flex-shrink-0 transition-transform duration-300">
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- FAQ 內容區域 -->
                        <div class="faq-body overflow-hidden transition-all duration-300" style="max-height: 0;">
                            <div class="px-6 pb-6">
                                <!-- 分隔線 -->
                                <div class="border-t border-gray-200 mb-4"></div>

                                <!-- FAQ 內容 -->
                                <div class="prose prose-gray max-w-none">
                                    <div class="text-gray-700 leading-relaxed">
                                        {!! $rs->body !!}
                                    </div>
                                </div>

                                <!-- 可選：有用性反饋 (註解掉的部分) -->
                                <!--
                                        <div class="mt-6 pt-4 border-t border-gray-100">
                                            <div class="flex items-center space-x-4">
                                                <span class="text-sm text-gray-600">這個回答對您有幫助嗎？</span>
                                                <button class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 transition-colors duration-200">
                                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    有用
                                                </button>
                                                <button class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    沒用
                                                </button>
                                            </div>
                                        </div>
                                        -->
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- 如果沒有 FAQ 資料 -->
            @if (count($data['rows']) === 0)
                <div class="text-center py-12">
                    <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">目前沒有常見問題</h3>
                    <p class="text-gray-500">常見問題正在整理中，請稍後再來查看。</p>
                </div>
            @endif

            <!-- 聯絡我們區域 -->
            <div class="mt-16 bg-gray-50 rounded-xl p-8 text-center">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                    找不到您要的答案？
                </h3>
                <p class="text-gray-600 mb-6">
                    我們的專業團隊隨時為您服務，歡迎與我們聯絡
                </p>
                <a href="{{ url('/contact') }}"
                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-red-700 transition-colors duration-200 shadow-sm">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                        </path>
                    </svg>
                    聯絡我們
                </a>
            </div>
        </div>
    </div>
@endsection
