<?php

namespace App\Macros;

use PF;
use CustomException;

class MyBuilderDB
{
    protected $builder;
    protected $pdo;
    protected $searchname;
    protected $codition;
    protected $search;
    protected $title;

    public function __construct($builder)
    {
        $this->builder = $builder;
    }

    public function sql($searchname, $search, $nicknamearray, $isrequired)
    {
        //PF::printr($searchname);
        //$sSQLCmd .= PF_dbSqlNsearch('signupid|N', $signupid, 'signupid', 'Y');

        if ('' == $searchname) {
            return $this->builder;
        }
        if ('0' == $isrequired || 'N' == $isrequired || '' == $isrequired) {
            if ('' == $search) {
                return $this->builder;
            }
        }
        if (substr_count($searchname, '&') > 0 || substr_count($searchname, ':') > 0) {
            return $this->builder;
        }

        $this->searchname = $searchname;

        $this->search = $search;

        if ('1' == $isrequired || 'Y' == $isrequired) {
            if ('' == $this->search) {
                if ('string' == gettype($nicknamearray)) {
                    $this->title = $nicknamearray;
                } else {
                    $this->title = $nicknamearray[$searchname];
                }
                if ('' == $this->title) {
                    $this->title = $this->name;
                }

                throw new CustomException(PF::LG($this->title).' is empty');
            }
        }

        //PF::printr(2);
        $this->sqlcommand();

        return  $this->builder;
    }

    /**
     * Prepare the values.
     */
    private function sqlcommand()
    {
        $x = 0;
        $isEloquent=false;
        if ($this->builder instanceof \Illuminate\Database\Eloquent\Builder){
            $isEloquent=true;
        }

        $search = trim($this->search);

        $searchnameSplit = explode('^', $this->searchname);

        $searchSplit = explode('^', $search);
        $andor = 'and';

        if (count($searchnameSplit) > 1) {
            $andor = 'or';
        }

        for ($i = 0; $i < count($searchnameSplit); ++$i) {
            //PF::printr("1".$searchnameSplit[$i]);
            $searchtempvalue = $search;
            if (count($searchnameSplit) == count($searchSplit)) {
                $searchtempvalue = $searchSplit[$i];
            }

            for ($j = 0; $j < count($searchSplit); ++$j) {
                $array = explode('|', $searchnameSplit[$i]);
                $sn1 = $array[0];
                $sn11 = '';
                $sn12 = '';
                if (substr_count($sn1, '.') > 0) {
                    $sn1array = explode('.', $sn1);
                    $sn11 = $sn1array[0];
                    
                    $sn12 = $sn1array[1];
                    
                }
                $sn2 = '';
                if (isset($array[1])) {
                    $sn2 = $array[1];
                }
                $sn2 = strtoupper($sn2);
                switch ($sn2) {
                        case 'INT':
                        case 'N':
                            if (false == is_numeric($searchtempvalue)) {
                                return;
                            }
                            if ('and' == $andor) {
                                if ('' != $sn11 && $isEloquent) {
                                    $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                        $query->where($sn1, '=', $searchtempvalue);
                                    });
                                } else {
                                    $this->builder->where($sn1, '=', $searchtempvalue);
                                }

                                
                            } else {
                                if ('' != $sn11 && $isEloquent) {
                                    $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                        $query->orWhere($sn1, '=', $searchtempvalue);
                                    });
                                } else {
                                    $this->builder->orWhere($sn1, '=', $searchtempvalue);
                                }
                                
                            }
                            break;
                        case 'D':
                            if (false == PF::isDate($searchtempvalue)) {
                                return;
                                throw new Exception($searchtempvalue.'not date type');
                            }
                            if ('and' == $andor) {
                                if ('' != $sn11 && $isEloquent) {
                                    $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                        $query->where($sn1, '=', $searchtempvalue);
                                    });
                                } else {
                                    $this->builder->where($sn1, '=', $searchtempvalue);
                                }
                                
                            } else {
                                if ('' != $sn11 && $isEloquent) {
                                    $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                        $query->orWhere($sn1, '=', $searchtempvalue);
                                    });
                                } else {
                                    $this->builder->orWhere($sn1, '=', $searchtempvalue);
                                }
                                
                            }
                            break;
                        case 'S':
                        case '=':

                            if ('and' == $andor) {
                                if ('' != $sn11 && $isEloquent) {
                                    $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                        $query->where($sn12, '=', $searchtempvalue);
                                    });
                                } else {
                                    $this->builder->where($sn1, '=', $searchtempvalue);
                                }
                            } else {
                                if ('' != $sn11 && $isEloquent) {
                                    $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                        $query->orwhere($sn12, '=', $searchtempvalue);
                                    });
                                } else {
                                    $this->builder->orwhere($sn1, '=', $searchtempvalue);
                                }
                            }
                        break;
                        case 'ININT':
                        case 'INS':
                        if ('and' == $andor) {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                    $query->whereIn($sn12, explode(',', $searchtempvalue));
                                });
                            } else {
                                $this->builder->whereIn($sn1, explode(',', $searchtempvalue));
                            }

                            
                        } else {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                    $query->orWhereI($sn12, explode(',', $searchtempvalue));
                                });
                            } else {
                                $this->builder->orWhereIn($sn1, explode(',', $searchtempvalue));
                            }
                            
                        }
                        break;
                        case 'NOTIN':
                        case 'NOTINT':
                        if ('and' == $andor) {
                            $this->builder->whereNotIn($sn1, explode(',', $searchtempvalue));
                        } else {
                            $this->builder->orWhereNotIn($sn1, explode(',', $searchtempvalue));
                        }
                        break;
                        case '<':
                        case '<=':
                        case '>':
                        case '>=':
                        case '<>':
                        if ('and' == $andor) {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12,$sn2,$searchtempvalue) {
                                    $query->where($sn12, $sn2, $searchtempvalue);
                                });
                            } else {
                                $this->builder->where($sn1, $sn2, $searchtempvalue);
                            }
                            
                        } else {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12,$sn2,$searchtempvalue) {
                                    $query->where($sn12, $sn2, $searchtempvalue);
                                });
                            } else {
                                $this->builder->orWhere($sn1, $sn2, $searchtempvalue);
                            }
                            
                        }
                        break;
                        case 'NULL':
                        break;
                        if ('and' == $andor) {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12) {
                                    $query->whereNull($sn12);
                                });
                            } else {
                                $this->builder->whereNull($sn1);
                            }
                            
                        } else {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12) {
                                    $query->orWhereNull($sn12);
                                });
                            } else {
                                $this->builder->orWhereNull($sn1);
                            }
                            
                        }
                        case 'NOTNULL':
                        if ('and' == $andor) {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12) {
                                    $query->whereNotNull($sn12);
                                });
                            } else {
                                $this->builder->whereNotNull($sn1);
                            }
                            
                        } else {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12) {
                                    $query->orWhereNotNull($sn12);
                                });
                            } else {
                                $this->builder->orWhereNotNull($sn1);
                            }
                            
                        }
                        break;
                        default:

                        if ('and' == $andor) {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                    $query->where($sn12, 'like', '%'.$searchtempvalue.'%');
                                });
                            } else {
                                $this->builder->where($sn1, 'like', '%'.$searchtempvalue.'%');
                            }
                        } else {
                            if ('' != $sn11 && $isEloquent) {
                                $this->builder->whereHas($sn11, function ($query) use ($sn12,$searchtempvalue) {
                                    $query->orWhere($sn12, 'like', '%'.$searchtempvalue.'%');
                                });
                            } else {
                                $this->builder->orWhere($sn1, 'like', '%'.$searchtempvalue.'%');
                            }
                            
                        }
                    }
            }
        }
    }
}