@if ($paginator->hasPages())
<div class="container">

    <script language=JavaScript>
    function PF_pagesubmit(page) {
        console.log(page);
        document.forms['pageform'].elements['page'].value = page
        document.forms['pageform'].submit();
    }
    </script>

    <form name="pageform" id="pageform" method="post" language="javascript" action="{{request()->getRequestUri()}}">
        @include('admin.layouts.hiddenall', ['method'=>'pageform','data'=>$data])
        <input type="hidden" name="page" value="{{$paginator->currentPage()}}" />
        {{__('頁數')}}：{{$paginator->currentPage()}} / {{$paginator->lastPage()}}　{{__('總筆數')}}：<label id="paginatortotal"> {{$paginator->total()}}</label>


        <nav aria-label="...">

            <ul class="pagination justify-content-end" style="margin:0;">


                @php
                $showpage = 5;//$paginator->perPage();

                $temppage = intval(($paginator->currentPage() - 1) / $showpage) + 1;
                $backpage = ($temppage - 1) * $showpage;
                $nextpage = $temppage * $showpage + 1;
                @endphp

                @if ($paginator->currentPage()<>1)
                    <li class="page-item">
                        <a href="#" class="page-link" onkeypress="javascript:PF_pagesubmit(1);return false;"
                            onclick="javascript:PF_pagesubmit(1);return false;" title="1"> « </a>
                    </li>
                    @endif
                    @if ($paginator->currentPage()>$showpage)
                    <li class="page-item">
                        <a href="#" class="page-link" onkeypress="PF_pagesubmit({{$backpage}});return false;"
                            onclick="PF_pagesubmit({{$backpage}});return false;" title="{{$backpage}}"> -10 </a>
                    </li>
                    @endif

                    @if ($paginator->lastPage()>=$paginator->currentPage() && 1!=$paginator->currentPage())
                    <li class="page-item">
                        <a href="#" class="page-link"
                            onkeypress="PF_pagesubmit({{ $paginator->currentPage()-1 }});return false;"
                            onclick="PF_pagesubmit({{ $paginator->currentPage()-1 }});return false;"
                            title="{{ ($paginator->currentPage()-1) }}"> ‹ </a>
                    </li>
                    @endif

                    @for ($i = ($temppage-1) * $showpage; $i < ($temppage*$showpage); ++$i) @if ($paginator->
                        currentPage()== ($i+1))
                        <li class="page-item active"><a href="#" class="page-link">{{($i+1)}}</a></li>
                        @else
                        @if ($i == ($paginator->currentPage()-1))
                        <li class="page-item active"><a href="#">{{($i+1)}}</a></li>
                        @else
                        <li class="page-item">
                            <a href="#" class="page-link" onkeypress="PF_pagesubmit({{ ($i+1) }});return false;"
                                onclick="PF_pagesubmit({{ ($i+1) }});return false;" title="{{ ($i+1) }}">{{($i+1)}}</a>
                        </li>
                        @endif
                        @endif

                        @if (($i+1) >= $paginator->lastPage())
                        @break
                        @endif

                        @endfor

                        @if ($paginator->lastPage() > $paginator->currentPage())
                        <li class="page-item">
                            <a href="#" class="page-link" onkeypress="PF_pagesubmit({{ $paginator->currentPage()+1 }})"
                                onclick="PF_pagesubmit({{ $paginator->currentPage()+1 }});return false;"
                                title="{{ $paginator->currentPage()+1 }}"> › </a>
                        </li>
                        @endif

                        @if ($nextpage <= $paginator->lastPage())
                            <li class="page-item">
                                <a href="#" class="page-link" onkeypress="PF_pagesubmit({{ $nextpage }});return false;"
                                    onclick="PF_pagesubmit({{ $nextpage }})" title="{{ $nextpage }};return false;">
                                    +{{$nextpage}} </a>
                            </li>
                            <li class="page-item">
                                <a href="#" class="page-link"
                                    onkeypress="PF_pagesubmit({{ $paginator->lastPage() }});return false;"
                                    onclick="PF_pagesubmit({{ $paginator->lastPage() }});return false;"
                                    title="{{ $paginator->lastPage() }}"> » </a>
                            </li>
                            @endif

            </ul>
        </nav>



    </form>
</div>
@endif