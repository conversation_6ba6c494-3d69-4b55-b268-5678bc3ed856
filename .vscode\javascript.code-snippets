{
  "input_required": {
    "prefix": "js required",
    "scope": "html,blade",
    "body": [
      " required "
    ]
  },
  "js_if_undefined": {
    "prefix": "js if undefined",
    "scope": "js,javascript,vue,typescript",
    "body": [
      "if ( typeof(form.${1:SYMBOL}) != \"undefined\" ){",
      "",
      "}"
    ]
  },
  "js_console_log": {
    "prefix": "pp",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "console.log([\"${1:${CLIPBOARD}}\",${1:${CLIPBOARD}}]);"
    ]
  },
  "js_console_error": {
    "prefix": "error",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "console.error(${1:});"
    ]
  },
  "js_console_echo ": {
    "prefix": "echo",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "console.log('${1:<PERSON><PERSON>MB<PERSON>}');"
    ]
  },
  "js_try_catch": {
    "prefix": "js try catch throw ",
    "scope": "js,javascript,vue,typescript",
    "body": [
      "try {",
      "",
      "}catch(ex) {",
      " //throw 'xxx';",
      " _alert(ex.message);",
      " console.log(ex);",
      "} finally {}"
    ]
  },
  "toast": {
    "prefix": "toast alert success",
    "scope": "js,javascript,vue",
    "body": [
      "_toast('${1:SYMBOL}',500);"
    ]
  },
  "toast warning": {
    "prefix": "toast alert warning",
    "scope": "js,javascript,vue",
    "body": [
      "_toast('${1:SYMBOL}',500,'warning');"
    ]
  },
  "alert": {
    "prefix": "alert",
    "scope": "js,javascript,vue",
    "body": [
      "_alert('${1:SYMBOL}','success');"
    ]
  },
  "javascript,typescript": {
    "prefix": "js 純<script></script>",
    "scope": "html,blade",
    "body": [
      "<script type=\"text/javascript\">",
      "${1}",
      "</script>"
    ]
  },
  "js_oForm": {
    "prefix": "js oForm needs-validation",
    "scope": "html,blade",
    "body": [
      "<form name=\"oForm\" class=\"needs-validation\" id=\"oForm\" method=\"post\" language=\"javascript\"",
      " action=\"{{request()->url()}}/store\" novalidate>",
      "",
      "</form>"
    ]
  },
  "js_oForm_onsubmit": {
    "prefix": "js oForm_onsubmit",
    "scope": "html,blade",
    "body": [
      "<script type=\"text/javascript\">",
      "function oForm_onsubmit(form) {",
      "        if (!form.reportValidity()) {",
      "            checkValidity(window.event, form);",
      "            return false;",
      "        } ",
      "        PF_FieldDisabled(form)",
      "        return true;",
      "}",
      " </script>",
      "<form name=\"oForm\"  id=\"oForm\" method=\"post\" language=\"javascript\"",
      " action=\"{{request()->url()}}/store\" ",
      " onsubmit=\"return oForm_onsubmit(this);\" novalidate>",
      "</form>"
    ]
  },
  "jsform_elements": {
    "prefix": "dom 單筆 form  name",
    "scope": "javascript,vue,blade,html",
    "body": [
      "form.elements['${1:id}'].value"
    ]
  },
  "js_form_elements": {
    "prefix": "dom frame window.top.document",
    "scope": "javascript,vue,blade,html",
    "body": [
      "\\$('#frameset', window.top.document).attr('', '');"
    ]
  },
  "js_document_getElementById": {
    "prefix": "dom 單筆 id getElementById",
    "scope": "javascript,vue,html,blade,typescript",
    "body": [
      "document.getElementById('${1:id}')"
    ]
  },
  "js rel  querySelector": {
    "prefix": "doms 單筆 rel",
    "scope": "javascript,vue,html,blade,typescript",
    "body": [
      "    document.querySelector(\"[rel=\"abc\"]\")"
    ]
  },
  "js class name": {
    "prefix": "dom 單筆 style",
    "scope": "javascript,vue,html,blade,typescript",
    "body": [
        "textElement=document.querySelector('.message-input')",
        "if (textElement) {",
         "textElement.style.bottom = '10px'",
        "}",
    ]
  },

  "js class querySelector": {
    "prefix": "dom 單筆 any rel querySelector",
    "scope": "javascript,vue,html,blade,typescript",
    "body": [
      "    document.querySelector(\".x-y\")"
    ]
  },
  "js querySelectorAll": {
    "prefix": "dom 多筆 querySelector",
    "scope": "javascript,vue,html,blade,typescript",
    "body": [
      "document.querySelectorAll(\"[rel=\"abc\"]\")[0]"
    ]
  },
  "js_document_getElementsByName": {
    "prefix": "dom 多筆 name",
    "scope": "javascript,vue,html,blade,typescript",
    "body": [
      "document.getElementsByName('${1:id}')[0].value"
    ]
  },
  "js_jquery_input_name": {
    "prefix": "dom jquery name",
    "scope": "javascript,vue,blade,html",
    "body": [
      "\\$(\"#oForm input[name='${1:id}']\").val()"
    ]
  },
  "js_jquery_input_id": {
    "prefix": "dom jquery id",
    "scope": "javascript,vue,blade,html",
    "body": [
      "\\$('#${1:id}').val()"
    ]
  },
  "js_document_forms": {
    "prefix": "dom document.forms ",
    "scope": "javascript,vue,html,blade",
    "body": [
      "document.forms['oForm'].elements['${1:db}'].value"
    ]
  },
  "ajax多筆": {
    "prefix": "jquery ajax 包裝版 多筆",
    "scope": "html,blade,js,javascript,vue",
    "body": [
      "<script type=\"text/javascript\">",
      "function sleep(n){   ",
      "    var start=new Date().getTime();   ",
      "    while(true) if(new Date().getTime()-start>n) break;   ",
      "}   ",
      "var sendcount=0;",
      "var sendcount0=0;",
      "var sendcount1=0;",
      "var tcount=0;",
      "document.addEventListener(\"DOMContentLoaded\", () => {",
      "    tcount=jQuery(\"[rel='resend']\").length;",
      "        \\$(\"#tcount\").html(tcount);",
      "        i=0;",
      "       ",
      "            jQuery(\"[rel='resend']\").each(function (i,item) {",
      "                setTimeout(function () {",
      "                i++;",
      "                var id=\\$(item).attr(\"id\");",
      "                ",
      "                var dict={",
      "                url:\"{{ url('/') }}/api/admin/zoomline/store\",",
      "                data:{",
      "                    'id': id",
      "                },",
      "                //data:jQuery(\"#oForm\").serialize(),",
      "                //data:JSON.stringify(this.inputs),    ",
      "                dataType:'json',",
      "                noloading:true,",
      "                //debug:true, ",
      "                }",
      "                ",
      "                PF_ajax(dict).done(function(obj) {                    ",
      "                    $('#ajaxloading').hide(1000);",
      "                    if (obj.resultcode == 0) {               ",
      "                        sendcount1++;",
      "                        \\$(\"#message_\"+id).addClass('badge bg-success');                                                        ",
      "                    } else {",
      "                        sendcount0++;",
      "                        \\$(\"#message_\"+id).addClass('badge bg-danger');                                                        ",
      "                    }",
      "                    ",
      "                }).fail(function (resp) {",
      "                    sendcount0++;",
      "                        try {",
      "                            obj = JSON.parse(resp.responseText);",
      "                            _alert(obj.resultmessage);",
      "                        } catch (e) {",
      "                            _alert(resp.responseText);",
      "                        }",
      "                }).then(function () {",
      "                    sendcount=sendcount1+sendcount0;",
      "                    if(tcount==sendcount){",
      "                        _alert(\"已完成 : \"+sendcount+\" 筆\",'success');",
      "                    }",
      "                });        ",
      "                }.bind(this), i*1000);",
      "            });",
      "        ",
      "      ",
      "});",
      " </script>",
      "",
      "",
      "<SCRIPT type=\"text/javascript\">",
      "    function oForm_onsubmit(form)",
      "    {",
      "          if (PF_FormMultiAll(form) == false) {return false;}",
      "                                                                                                                            ",
      "          PF_FieldDisabled(form);//將全部button Disabled",
      "          return true;",
      "    }",
      "    </SCRIPT>",
      "<div class=\"card\">",
      "    <div class=\"card-body\">",
      "        <h2>預計執行筆數:",
      "",
      "            <span id=\"sendcount\">0</span>",
      "            /<span id=\"tcount\">0</span>",
      "            (",
      "            成功",
      "            <span id=\"sendcount1\" class=\"badge bg-success\">0</span>",
      "            /",
      "            失敗:<span id=\"sendcount0\" class=\"badge bg-danger\">0</span>",
      "            )",
      "        </h2>",
      "        <div class=\"table-responsive\">",
      "            <table class=\"table table-striped table-hover  table-bordered table-fixed\">",
      "                <!--排序的參數-->",
      "                <form name=\"SortoForm\" method=\"post\">",
      "                    @include('admin.layouts.hidden', ['method'=>'SortoForm','data'=>\\$data])",
      "                    <thead>",
      "                        <tr valign=\"top\" align=\"left\">",
      "                            <th width=\"\" id=\"name\">姓名</th>",
      "                            <th width=\"\" id=\"lineid\">Line ID</th>",
      "",
      "                            <th width=\"\" id=\"message\">異常訊息</th>",
      "",
      "                            <th width=\"\" id=\"created_at\">建立時間</th>",
      "                        </tr>",
      "                    </thead>",
      "",
      "                </form>",
      "",
      "",
      "                <tbody>",
      "",
      "                    @foreach(\\$data['rows'] as \\$rs)",
      "                    <tr rel=\"resend\" id=\"{{\\$rs->id}}\">",
      "",
      "                        <td>",
      "                            {{\\$rs->name}}",
      "                        </td>",
      "                        <td>",
      "                            {{\\$rs->lineid}}",
      "                        </td>",
      "",
      "                        <td>",
      "                            <span id=\"message_{{\\$rs->id}}\">",
      "                            </span>",
      "                        </td>",
      "",
      "                        <td>",
      "                            {{\\$rs->created_at}}",
      "                        </td>",
      "                    </tr>",
      "                    @endforeach",
      "",
      "                </tbody>",
      "",
      "",
      "            </table>",
      "        </div>",
      "    </div>",
      "</div>",
      "",
      ""
    ]
  },
  "ajax客製版": {
    "prefix": "jquery PF_ajax 客製版",
    "scope": "js,javascript,vue",
    "body": [
      "var dict={",
      "    url:\"{{ \\request()->middlewareurlapi }}store/\",",
      "    data:{",
      "        'unit': 'a'",
      "    },",
      "    //data:jQuery(\"#oForm\").serialize(),",
      "    //data:JSON.stringify(this.inputs),    ",
      "     dataType: 'json',",
      "     noloading:true,",
      "    //debug:true, ",
      "}",
      "PF_ajax(dict).done(function(obj) {",
      "    //<div class = \"spinner-border\" role=\"status\" id=\"ajaxloading_smsloghistory\"></div>",
      "    $('#ajaxloading').hide(1000);",
      "    if (obj.resultcode == 0) {",
      "       //\\$.each(obj.data, function(key,item){",
      "       //vm[key]=item;",
      "       //});",
      "      _toast(obj.resultmessage,800)",
      "    } else {",
      "       _alert(obj.resultmessage);",
      "    }",
      "}).fail(function (resp) {",
      "                        try {",
      "                            obj = JSON.parse(resp.responseText);",
      "                            _alert(obj.resultmessage);",
      "                        } catch (e) {",
      "                            _alert(resp.responseText);",
      "                        }",
      "}).then(function () {",
      "",
      "});"
    ]
  },
  "ajax完整版": {
    "prefix": "jquery ajax 原生",
    "scope": "js,javascript",
    "description": "ajax error beforesend success",
    "body": [
      "var requestURL=\"{{ url('/') }}/admin/ec/\";",
      "     jQuery.ajax({",
      "      type: \"post\",",
      "      url: requestURL,",
      "      dataType: \"json\",",
      "     // data: pars,",
      "      data: jQuery(\"#oForm\").serialize(),",
      "      error: function(resp) {",
      "                        try {",
      "                            obj = JSON.parse(resp.responseText);",
      "                            _alert(obj.resultmessage);",
      "                        } catch (e) {",
      "                            _alert(resp.responseText);",
      "                        }",
      "      },",
      "      beforeSend: function() {",
      "          //jQuery(\"#ajaxloading\").toggle();",
      "      },",
      "      success: function(obj) {",
      "          $('#ajaxloading').hide(1000);",
      "          if (obj.resultcode == 0) {",
      "              //\\$.each(obj.data, function(name, value) {",
      "              //    \\$(\"#\" + name).html(value);",
      "              //});",
      "              //vm.inputs = obj.data;",
      "              _toast(obj.resultmessage,800);",
      "              return;",
      "          } else {",
      "              _alert(obj.resultmessage);",
      "          }",
      "      }",
      "  });"
    ]
  },
  "ajax 純post版本": {
    "prefix": "ajax post",
    "scope": "js,javascript",
    "description": "ajax .post",
    "body": [
      "let vm = this;",
      "\\$.post(\"{{ url('order/totalprice') }}\",",
      "      { ",
      "          discountcode:vm.inputs.discountcode, ",
      "          product_id:{{\\$data['product_id']}} ",
      "      }",
      "   ).done(function( obj ) {",
      "     $('#ajaxloading').hide(1000);",
      "     if (obj.resultcode == 0) {",
      "     \\$.each(obj.data, function(i,item){",
      "      vm[i]=item;",
      "     });",
      "    _toast(obj.resultmessage,800)",
      "  } else {      ",
      "      _alert(obj.resultmessage);",
      "  }",
      "                });"
    ]
  },
  "jquery_for": {
    "prefix": "jquery dom for each",
    "scope": "js,javascript",
    "body": [
      "\\$(\"input[type='text'],input[type='number']\").each((key, item) => {",
      "  \\$(item).attr('time1')",
      "});"
    ]
  },
  "get getAttribute": {
    "prefix": "dom get getAttribute",
    "scope": "js,javascript",
    "body": [
      "this.getAttribute('${1:SYMBOL}');"
    ]
  },
  "set getAttribute": {
    "prefix": "dom set getAttribute",
    "scope": "js,javascript,typescript",
    "body": [
      "document.getElementById('${1:SYMBOL}').setAttribute('scrolling', 'no');"
    ]
  },
  "back_html": {
    "prefix": "back javascript,typescript",
    "scope": "html,blade,js,javascript,typescript",
    "body": [
      " onClick=\"javascript:window.history.go(-1);return false;\" "
    ]
  },
  "select onChange": {
    "prefix": "select onChange",
    "scope": "html,blade",
    "body": [
      "onChange=\"location.href='{{ url('/') }}/Go?s=' + this.options[this.selectedIndex].value;\" "
    ]
  },
  "jquery_rel": {
    "prefix": "jquery dom foreach rel",
    "scope": "javascript,vue,js",
    "description": "herf search name",
    "body": [
      "jQuery(\"[rel='${1:startdate}']\").each(function (i,item) {",
      "       \\$(this).text();",
      "       \\$(this).html(${2:startdate});",
      "});"
    ]
  },
  "jquery_foreach": {
    "prefix": "jquery dom foreach ",
    "scope": "javascript,vue,js,typescript",
    "description": "herf search name",
    "body": [
      ".each(function (i,item) {",
      "       \\$(this).text();",
      "       \\$(this).html(${2:startdate});",
      "});"
    ]
  },
  "checkbox ajax": {
    "prefix": "api checkbox ajax",
    "scope": "blade,html",
    "description": "checkbox ajax",
    "body": [
      "",
      "{!!Form::checkbox('online', \\$rs->id,(\\$rs->online),  ",
      " ['onClick' => \"PF_AjaxblockUI('\".url('/').\"/admin/${1:member}/setchecked?id=' + this.value + '&value=' + this.checked,null);\"]",
      ")!!}"
    ]
  },
  "checkbox prop": {
    "prefix": "checkbox jquery if checked 判斷是否勾取",
    "scope": "javascript,vue,js",
    "body": [
      "",
      "if ($(\"input[name='${1:member}']\").prop(\"checked\")) {",
      "",
      "}"
    ]
  },
  "checkbox get value": {
    "prefix": "checkbox jquery get value 取值",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "",
      "var cbxvalue=(jQuery(form).find(\"input:checkbox:checked[name='${1:member}[]']\").map(function() { return $(this).val(); }).get()).toString();"
    ]
  },
  "checkbox 不打勾": {
    "prefix": "checkbox 不打勾",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"input[name='${1:member}']\").prop(\"checked\",'');"
    ]
  },
  "checkbox 打勾": {
    "prefix": "checkbox 打勾",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"input[name='${1:member}']\").prop(\"checked\",true);"
    ]
  },
  "checkbox change": {
    "prefix": "checkbox change",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "$(\":input[name='${1:member}']\").change(function () {",
      "    jQuery('input:checkbox:checked').each(function (i) {   ",
      "",
      "    }",
      "}"
    ]
  },
  "confirm": {
    "prefix": "alert confirm 一個選單",
    "scope": "javascript,vue,js",
    "description": "Swal.fire confirm",
    "body": [
      "Swal.fire({",
      "          title: '確定要?',",
      "          //text: \"You won't be able to revert this!\",",
      "          icon: 'warning',//success",
      "          showCancelButton: true,",
      "          confirmButtonColor: '#3085d6',",
      "          cancelButtonColor: '#d33',",
      "          confirmButtonText: '確定'",
      "     }).then((result) => {",
      "     if (result.value) {",
      "     }else{",
      "     }",
      "};"
    ]
  },
  "confirm_two": {
    "prefix": "alert confirm 二個選單",
    "scope": "javascript,vue,js",
    "description": "Swal.fire confirm",
    "body": [
      "        Swal.fire({",
      "                title:'確定要取消?',",
      "                icon: 'warning',",
      "                showCancelButton: true,",
      "                confirmButtonColor: '#3085d6',",
      "                cancelButtonColor: '#d33',",
      "                confirmButtonText: '確定',",
      "                cancelButtonText: '取消',",
      "            }).then((result) => {",
      "                if (typeof(result) != \"undefined\" && result.value) {",
      "                   document.forms['oForm'].submit();          ",
      "                   flag = true;",
      "                }else{",
      "                  flag=false;",
      "                  return false; ",
      "                }",
      "            });"
    ]
  },
  "html後執行javascript,typescript": {
    "prefix": "load",
    "scope": "javascript,vue,js",
    "body": [
      "document.addEventListener(\"DOMContentLoaded\", () => {",
      "",
      "});"
    ]
  },
  "html後執行javascript html": {
    "prefix": "js load",
    "scope": "html,blade",
    "body": [
      "<script type=\"text/javascript\">",
      "document.addEventListener(\"DOMContentLoaded\", () => {",
      " ",
      " ",
      "});",
      "</script>"
    ]
  },
  "jquery load": {
    "prefix": "ajax load",
    "scope": "javascript,vue,js",
    "body": [
      "\\$('#${1:city2}').load('{{ url('/') }}/api/db/${1:online}?${1:online}=');"
    ]
  },
  "json解析": {
    "prefix": "json parse 文字轉物件",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "JSON.parse(${1:SYMBOL})"
    ]
  },
  "json物件轉字串": {
    "prefix": "json stringify 物件轉字串",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "JSON.stringify(${1:SYMBOL})"
    ]
  },
  "str ": {
    "prefix": "str 產生數字亂數1~50",
    "scope": "javascript,typescript",
    "body": [
      "Math.floor(Math.random()*50);"
    ]
  },
  "string trim去除前後,": {
    "prefix": "str trim replace 去除頭尾,",
    "scope": "html,blade,javascript,vue,js,typescript",
    "body": [
      "filelists = filelists.replace(/^,|,$/g, \"\");"
    ]
  },
  "foreach array key": {
    "prefix": "array foreach 只抓key",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "Object.keys(${1:SYMBOL}).forEach(function(value, index){",
      "console.log(index,value );",
      "});"
    ]
  },
  "foreach array value": {
    "prefix": "array foreach 只抓value",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "Object.values(${1:SYMBOL}).forEach(function(value, index){",
      "console.log(index,value );",
      "});"
    ]
  },
  "array entries ": {
    "prefix": "array foreach entries 一階只有key沒有value",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "for (let [index, elem] of [ a ,  b ].entries()) {  ",
      "",
      "};"
    ]
  },
  "array keys ": {
    "prefix": "array for 只有key",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "for (let index of [ a ,  b ].keys()) {   ",
      "",
      "};"
    ]
  },
  "array values ": {
    "prefix": "array for 只有value",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "for (let index of [ a ,  b ].values()) {   ",
      "",
      "};"
    ]
  },
  "array get key ": {
    "prefix": "array 取某一筆key Object.keys",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "Object.keys(props.searchDateNames)[0]"
    ]
  },
  "array add": {
    "prefix": "array add 一開始就有資料",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "var ${1:arr}s = ['frame/left', 'frame/top']",
      "${1:arr}s.forEach((rs, index) => {",
      "",
      "}"
    ]
  },
  "foreach array add ": {
    "prefix": "array add",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "${1:SYMBOL}s=[];",
      "${1:SYMBOL}s.push('aa');",
      "${1:SYMBOL}s.forEach((rs, index) => {",
      "rs",
      "",
      "});"
    ]
  },
  "array exist multi": {
    "prefix": "array exists 是否存在(多找多)",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "var ${1:name}s = ${1:name}.split(\",\");",
      "if (['a','b'].some(i => ${1:name}s.includes(i))) {",
      "      ",
      "};",
      "console.log([\"${1:name}\", ${1:name}]);"
    ]
  },
  "array exist": {
    "prefix": "str 模糊比對(多筆)",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "if (['/vue', '/layout'].some(item => ${1:str}.includes(tiem))) {",
      "      ",
      "};"
    ]
  },
  "str includes": {
    "prefix": "str 模糊比對(一對)",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "if(${1:str}.includes('${2:str}')) {",
      "      ",
      "};"
    ]
  },
  "array exist del": {
    "prefix": "array splice 刪除",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "${1:name}.splice(${1:name}.findIndex(item => item === val),1,)"
    ]
  },
  "foreach array rs ": {
    "prefix": "array foreach ",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "for (const rs of data.data) {",
      " console.log(rs);",
      " rs.number;   ",
      "}"
    ]
  },
  "array where filter": {
    "prefix": "array collection where filter 進階",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "inputs.${1:lists} = store.setup['支出固定項目']",
      "inputs.${1:lists}.forEach(function (rs, index) {",
      "   const result = rep.data.${1:lists}.filter(rs1 => rs1.value == rs.value)",
      "     if (result.length > 0) {",
      "         rs = result[0]",
      "   }",
      "})"
    ]
  },
  "array where dictionary filter": {
    "prefix": "array collection filter 會回多筆",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "const result = json['fields'].filter((rs) => rs.name == 'a');",
      "if (result.length > 0) {",
      "",
      "}"
    ]
  },
  "array where dictionary filter search" : {
    "prefix": "array collection filter 模糊搜尋 會回多筆",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "const result = oldData.data.filter((rs) => rs.name.includes(state.search));",
      "if (result.length > 0) {",
      "data.data = result",
      "}"
    ]
  },
  "array  find": {
    "prefix": "array collection find 只回一筆",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "const result= store.data?.${1:arr1}?.find(rs1 => rs1.id == props.${2:name})",
      "if (result != null) {",
      "",
      "}"
    ]
  },
  "array to string": {
    "prefix": "array to string",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "${1:name} = ${1:name}.join(',');"
    ]
  },
  "array sample": {
    "prefix": "array sample key value",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "arrays: [",
      "        { id: '1', title: 'x' },",
      "        { id: '2', title: 'y' },",
      "        { id: '3', title: 'z' }",
      "    ]"
    ]
  },
  "jquery click trigger": {
    "prefix": "jquery click trigger 自動觸發",
    "scope": "js,javascript",
    "body": [
      "jQuery(\"[rel='${1:item}']\").eq(0).trigger(\"click\");"
    ]
  },
  "jquery click": {
    "prefix": "jquery click",
    "scope": "js,javascript",
    "body": [
      "jQuery(\"[rel='${1:item}']\").click(function() { ",
      " document.forms['oForm'].submit(); ",
      "}); "
    ]
  },
  "jquery change": {
    "prefix": "jquery change on",
    "scope": "js,javascript",
    "body": [
      "jQuery(\"form :input,select\").change(function() { ",
      "  ",
      "}); "
    ]
  },
  "jquery blur": {
    "prefix": "blur 文字框離開",
    "scope": "js,javascript",
    "body": [
      "jQuery(\"form :input,select,textarea\").blur(function() { ",
      "  ",
      "}); "
    ]
  },
  "jquery focus": {
    "prefix": "jquery focus 文字框點進來",
    "scope": "js,javascript,typescript",
    "body": [
      "jQuery(\"input[type=text]\").focus(function() { ",
      "  ",
      "}); "
    ]
  },
  "jquery removeClass": {
    "prefix": "jquery class removeClass 移除 name",
    "scope": "js,javascript",
    "body": [
      "jQuery(\"label[class='county active']\").removeClass('active');"
    ]
  },
  "jquery addClass": {
    "prefix": "class addClass 加 name",
    "scope": "js,javascript",
    "body": [
      "jQuery(\"[rel='item']\").addClass('item active')"
    ]
  },
  "jquery css background-color": {
    "prefix": "jquery css 改 background-color",
    "scope": "js,javascript,typescript",
    "body": [
      "jQuery(\"#aa\").css({\"background-color\": \"#FFCCCC\"})"
    ]
  },
  "parseFloat": {
    "prefix": "float parseFloat",
    "scope": "js,javascript,typescript",
    "body": [
      "parseFloat(\"${1:datas}\",10);"
    ]
  },
  "parseInt": {
    "prefix": "int parseInt",
    "scope": "js,javascript,typescript",
    "body": [
      "parseInt(\"${1:datas}\",10);"
    ]
  },
  "setTimeout": {
    "prefix": "time setTimeout 定時只執行一次",
    "scope": "js,javascript,typescript",
    "body": [
      "let mytimer= setTimeout(async function () {//定時只執行一次",
      "",
      "}.bind(this), 1000);"
    ]
  },
  "setInterval": {
    "prefix": "time setInterval 定時重覆執行",
    "scope": "js,javascript,typescript",
    "body": [
      "let mytimer = setInterval(async function () {//定時重覆執行",
      "",
      "}.bind(this), 1000);",
      "clearInterval(mytimer);"
    ]
  },
  "clearInterval": {
    "prefix": "time clearInterval 清除setInterval計時器",
    "scope": "js,javascript,typescript",
    "body": [
      "clearInterval(mytimer);//清除setInterval計時器"
    ]
  },
  "clearTimeout": {
    "prefix": "time clearTimeout 清除setTimeout計時器",
    "scope": "js,javascript,typescript",
    "body": [
      "clearTimeout(mytimer);//清除setTimeout計時器"
    ]
  },
  "event.currentTarget.name": {
    "prefix": "event name",
    "scope": "js,javascript,typescript",
    "body": [
      "event.currentTarget.name"
    ]
  },
  "referrer": {
    "prefix": "env referrer 上一頁網址",
    "scope": "js,javascript,typescript",
    "body": [
      "document.referrer"
    ]
  },
  "setCookie": {
    "prefix": "cookie setCookie",
    "scope": "js,javascript",
    "body": [
      "setCookie('${1:SYMBOL}', xx, {expires: 30});"
    ]
  },
  "getCookie": {
    "prefix": "cookie getCookie",
    "scope": "js,javascript",
    "body": [
      "getCookie('${1:SYMBOL}')"
    ]
  },
  "event.target": {
    "prefix": "event target = this",
    "scope": "js,javascript,typescript",
    "body": [
      "event.target"
    ]
  },
  "citydependent-dropdown": {
    "prefix": "db select city 一個是 myUIDb 一個是HTML",
    "scope": "html,blade",
    "body": [
      "<div class=\"form-group row\">",
      "                <div class=\"col-md-6\">",
      "                    {{",
      "Form::myUIDb([",
      "    'type' => 'select',",
      "    'title' =>'縣市',",
      "    'sql' =>'select city1title, city1title from city1 order by sortnum desc',",
      "    'name' => 'city1',",
      "    'value' => \\$data['city1'],",
      "    'firsttxt'=>'請選擇',",
      "",
      "",
      " ])",
      "}}",
      "                </div>",
      "                <div class=\"col-md-6\">",
      "",
      "                    <link href=\"{{ url('/') }}/assets/dependent-dropdown/css/dependent-dropdown.min.css\" rel=\"stylesheet\">",
      "                    <script src=\"{{ url('/') }}/assets/dependent-dropdown/js/dependent-dropdown.min.js\" type=\"text/javascript\"></script>",
      "                    <select name=\"city2\" id=\"city2\" class=\"form-control\" title=\"鄉鎮\">",
      "                        <option value=\"\">請選擇..</option>",
      "                    </select>",
      "                    <script>",
      "                        document.addEventListener(\"DOMContentLoaded\", () => {",
      "                            \\$(\"#oForm select[name='rcity2']\").depdrop({",
      "                                url: \"{{ url('/') }}/api/dependentdropdown/city2?selected={{\\$data['city2']}}\",",
      "                                depends: ['city1'],",
      "                                initDepends: ['city1'],",
      "                                initialize: true,",
      "                            });",
      "                        });",
      "                    </script>",
      "                    <div class=\"invalid-tooltip\" style=\"position: relative\">請選擇</div>",
      "                </div>",
      "            </div>"
    ]
  },
  "city2": {
    "prefix": "db select city",
    "scope": "html,blade",
    "body": [
      "<div class=\"input-group\">",
      "      {{Form::myUISelectMulti([",
      "                        [",
      "                        'formname' =>'oForm',",
      "                        'title' =>'縣市',",
      "                        'sql' =>'select city1title, city1title from city1 order by sortnum desc',",
      "                        'name' => 'city1',",
      "                        'value' => \\$data['city1'],",
      "                        'required' => false,",
      "                        ],",
      "                        [",
      "                        'formname' =>'oForm',",
      "                        'title' =>'鄉鎮',",
      "                        'url' =>url('/api/dependentdropdown/city2'),",
      "                        'name' => 'city2',",
      "                        'value' => \\$data['city2'],",
      "                        'required' => false,",
      "                        ]",
      "           ])",
      "     }}",
      "</div>"
    ]
  },
  "button confirm": {
    "prefix": "confirm",
    "scope": "html,blade",
    "body": [
      "<button type=\"button\" class=\"btn btn-danger btn-sm\"",
      "onclick=\"(!_confirm('您確定?',function (confirmed) {if (confirmed) {form.action='{{request()->url()}}/clear?edit={{\\$rs->id}}';form.submit();}else{return false;}}));\"",
      ">刪除</button> "
    ]
  },
  "encodeURIComponent": {
    "prefix": "url encodeURIComponent",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "encodeURIComponent(${1:kindhead_id})"
    ]
  },
  "reload": {
    "prefix": "reload",
    "scope": "javascript,vue,js,html,blade,typescript",
    "body": [
      "window.location.reload();"
    ]
  },
  "split": {
    "prefix": "split to array",
    "scope": "js,javascript,typescript",
    "body": [
      " const files = newValue",
      "            .split(',')",
      "             .filter(file => file !== 'undefined')",
      "              .map(rs => `<li>\\${rs}</li>`)"
    ]
  },
  "array map": {
    "prefix": "array map join",
    "scope": "js,javascript,typescript",
    "body": [
      "body.value = props.messages",
      "        .map(rs => {",
      "            const displayName = rs.displayName || 'No Name'",
      "            let text = rs.text || 'No Data'",
      "            text = text.replace('\n', '')",
      "            return `\\${displayName},\\${text}`",
      "        })",
      "        .join('\n')//轉转换为字符串"
    ]
  },
  "split to string": {
    "prefix": "split to string",
    "scope": "js,javascript,typescript",
    "body": [
      " const files = newValue",
      "            .split(',')",
      "             .filter(file => file !== 'undefined')",
      "              .map(item => `<li>${item}</li>`)",
      "      .join('')"
    ]
  },
  "addClass": {
    "prefix": "addClass",
    "scope": "js,javascript,vue",
    "body": [
      "$(\"[rel='${1:news}']\").addClass('${2:active}');"
    ]
  },
  "get Type": {
    "prefix": "get type",
    "scope": "js,javascript,vue",
    "body": [
      "console.log(typeof ${1:news})"
    ]
  },
  "get ckeditor body": {
    "prefix": "get ckeditor",
    "scope": "js,javascript",
    "body": [
      "CKEDITOR.instances['body'].getData()"
    ]
  },
  "moment formate": {
    "prefix": "date moment formate",
    "scope": "js,javascript,vue",
    "body": [
      "moment(event.start).format('YYYY-MM-DD');"
    ]
  },
  "moment string to datetime": {
    "prefix": "date parse string to datetime",
    "scope": "js,javascript,vue,typescript",
    "body": [
      "${1:start_date}=new Date(${1:start_date} + \"T\" + ${2:start_time});"
    ]
  },
  "xx": {
    "prefix": "timestamp to date YYYY-MM-DD HH:mm:ss",
    "scope": "js,javascript,vue,typescript",
    "body": [
      "const date = new Date(data.timestamp)",
      "                    //date.setHours(date.getHours() + 8)",
      "                    const formattedDate = `\\${date.getFullYear()}-\\${String(date.getMonth() + 1).padStart(2, '0')}-\\${String(",
      "                        date.getDate()",
      "                    ).padStart(2, '0')} \\${String(date.getHours()).padStart(2, '0')}:\\${String(date.getMinutes()).padStart(2, '0')}:\\${String(",
      "                        date.getSeconds()",
      "                    ).padStart(2, '0')}`",
    ]
  },
  "date timestampNow": {
    "prefix": "date timestampNow",
    "scope": "js,javascript,vue,typescript",
    "body": [
      "Math.floor(new Date().getTime() / 1000)"
    ]
  },
  "開始與結束時間內": {
    "prefix": "date 開始與結束時間內",
    "scope": "js,javascript,vue",
    "body": [
      "if(v['start']<seconds && seconds<v['end']){",
      "    return true;",
      "}"
    ]
  },
  "now": {
    "prefix": "date now yyyy/mm/dd HH:mm:ss",
    "scope": "js,javascript,vue,typescript",
    "body": [
      "d = new Date();",
      "    var at = d.getFullYear() + \"/\" +",
      "        (\"00\" + (d.getMonth() + 1)).slice(-2) + \"/\" +",
      "        (\"00\" + d.getDate()).slice(-2) + \" \" +",
      "        (\"00\" + d.getHours()).slice(-2) + \":\" +",
      "        (\"00\" + d.getMinutes()).slice(-2) + \":\" +",
      "        (\"00\" + d.getSeconds()).slice(-2);"
    ]
  },
  "today": {
    "prefix": "date now yyyy-mm-dd",
    "scope": "js,javascript,vue,typescript",
    "body": [
      "const today = new Date()",
      "today.toISOString().split('T')[0]"
    ]
  },


  "modal_href js": {
    "prefix": "modal js open 使用同頁的HTML下的modal",
    "scope": "js,javascript,vue",
    "description": "herf",
    "body": [
      "\\$('#exampleModalCenter').modal({",
      "            backdrop: 'static', // 禁用點擊背景關閉對話框",
      "            keyboard: false // 禁用ESC鍵關閉對話框",
      "}).on(\"hidden.bs.modal\", function(e) {",
      "            PF_FieldDisabled(form)",
      "            form.submit();",
      "});"
    ]
  },
  "input_button": {
    "prefix": "button onclick=location(url)",
    "scope": "html,blade,vue,typescript",
    "body": [
      " onclick=\"location.href='${1:SYMBOL}.php';\" "
    ]
  },
  "js_modal_close": {
    "prefix": "modal close",
    "scope": "javascript,vue,js",
    "body": [
      "$('[data-dismiss=\\'modal\\']',parent.document).trigger('click');"
    ]
  },
  "button innerText": {
    "prefix": "button innerText",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "s.innerText"
    ]
  },
  "str toUpperCase": {
    "prefix": "str toUpperCase 大寫",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "${1:name}.toUpperCase();"
    ]
  },
  "str toLowerCase": {
    "prefix": "str toLowerCase 小寫",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "${1:name}.toLowerCase();"
    ]
  },
  "str replaceAll": {
    "prefix": "str replaceAll",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "${1:name}=${1:name}.replaceAll(\"${2:name}\",\"${3:}\");"
    ]
  },
  "url encodeURIComponent": {
    "prefix": "url encodeURIComponent",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "encodeURIComponent('${1:name}');"
    ]
  },
  " typeof": {
    "prefix": "type 是什麼型態",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "console.log(\"${1:name} type:\",typeof(${1:name}))"
    ]
  },
  "array instanceof": {
    "prefix": "type array 判斷是否為陣列",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "if ((Array.isArray(${1:name})) {",
      "",
      "}"
    ]
  },
  "String instanceof": {
    "prefix": "type String 判斷是否為字串",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "if (typeof (data) == \"string\") {",
      "",
      "}"
    ]
  },
  "number instanceof": {
    "prefix": "type String 判斷是否為數字",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "if (typeof (data) == \"number\") {",
      "",
      "}"
    ]
  },
  "switch type": {
    "prefix": "type switch 什麼型態",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "switch(typeof value) {",
      "    case \"string\":",
      "        console.log(\"這是字串\");",
      "        break;            ",
      "    case \"number\":",
      "        console.log(\"這是數字\");",
      "        break;            ",
      "    case \"boolean\":",
      "        console.log(\"這是布林值\");",
      "        break;            ",
      "    case \"object\":",
      "        // 由於 null 也是 \"object\"，需要特別處理",
      "        if (value === null) {",
      "            console.log(\"這是 null\");",
      "        } ",
      "        // 判斷陣列",
      "        else if (Array.isArray(value)) {",
      "            console.log(\"這是陣列\");",
      "        }",
      "        else {",
      "            console.log(\"這是物件\");",
      "        }",
      "        break;",
      "        ",
      "    case \"undefined\":",
      "        console.log(\"這是 undefined\");",
      "        break;",
      "        ",
      "    case \"function\":",
      "        console.log(\"這是函式\");",
      "        break;",
      "        ",
      "    default:",
      "        console.log(\"未知型態\");",
      "}"
    ]
  },
  "ato2": {
    "prefix": "array a to inputs Object.assign",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "Object.assign(inputs, searchdatas);"
    ]
  },
  "array includes": {
    "prefix": "array exists 是否存在(一維陣列)",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "if (dicts.includes(value)) {",
      "return false;",
      "}"
    ]
  },
  "array push": {
    "prefix": "array 一維陣列 新增到最後一個",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "var arrays=[]",
      "arrays.push(value);"
    ]
  },
  "array sort": {
    "prefix": "array sort 排序",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "arrays.sort();"
    ]
  },
  "array sort field": {
    "prefix": "array sort 欄位",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "datas.value = rep.data.sort((a, b) => b.t - a.t)"
    ]
  },
  "array reverse": {
    "prefix": "array reverse 倒排序",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "arrays.reverse();"
    ]
  },
  "isarray": {
    "prefix": "array is array",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "if (xx instanceof Array) {",
      "",
      "}"
    ]
  },
  "array dict push": {
    "prefix": "array dict push",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "var array = [];",
      "",
      "var obj1 = { name: \"John\", age: 30 };",
      "var obj2 = { name: \"Jane\", age: 25 };",
      "",
      "array.push(obj1);",
      "array.push(obj2);",
      "",
      "console.log(array);"
    ]
  },
  "select value": {
    "prefix": "select get value",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"select[name='${1:name}']\").val()"
    ]
  },
  "select length": {
    "prefix": "select length",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"select[name='${1:name}'] option\").length"
    ]
  },
  "select selectedIndex": {
    "prefix": "select selectedIndex",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"select[name='${1:name}'] option\").get(0).selectedIndex = 1;"
    ]
  },
  "jquery next": {
    "prefix": "dom jquery next 控制項之後的控制項",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"#${1:name}\").next('tr')"
    ]
  },
  "jquery find": {
    "prefix": "dom jquery find 原控制項內的控制項",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"#${1:name}\").find('tr')"
    ]
  },
  "jquery closest": {
    "prefix": "dom jquery closest 原控制項上方的控制項",
    "scope": "javascript,vue,js",
    "body": [
      "$(\"#${1:name}\").closest('tr')"
    ]
  },
  "jquery onclick": {
    "prefix": "dom jquery onclick trigger",
    "scope": "javascript,vue,js",
    "body": [
      ".trigger(\"click\");"
    ]
  },
  "jquery 模糊相似": {
    "prefix": "dom jquery 模糊相似",
    "scope": "javascript,vue,js",
    "body": [
      "$('[name*=\"value\"]')"
    ]
  },
  "jquery 後方": {
    "prefix": "dom jquery 後方",
    "scope": "javascript,vue,js",
    "body": [
      "$('[name~=\"value\"]')"
    ]
  },
  "jquery 前面": {
    "prefix": "dom jquery 前面",
    "scope": "javascript,vue,js",
    "body": [
      "$('[name^=\"value\"]')"
    ]
  },
  "jquery 不等於": {
    "prefix": "dom jquery 不等於",
    "scope": "javascript,vue,js",
    "body": [
      "$('[name~=\"value\"]')"
    ]
  },
  "jquery parent": {
    "prefix": "dom jquery 上一層 parent",
    "scope": "javascript,vue,js",
    "body": [
      "jQuery(\"#sign\", window.parent.document).html('<img src=' + data + '>');"
    ]
  },
  "jquery top": {
    "prefix": "dom jquery 最上層 top",
    "scope": "javascript,vue,js",
    "body": [
      "jQuery(\"#sign\", window.top.document).html('<img src=' + data + '>');"
    ]
  },
  "jquery Offset": {
    "prefix": "dom jquery Offset location top left",
    "scope": "javascript,vue,js",
    "body": [
      "var offset = $('#${1:img}').offset();",
      "var top = offset.top;",
      "var left = offset.left;"
    ]
  },
  "jquery width height1": {
    "prefix": "width height dom",
    "scope": "javascript,vue,js",
    "body": [
      "var width = ${1:img}.offsetWidth;",
      "var height = ${1:img}.offsetHeight;",
      "//實際寬高",
      "var natural_width =$('#${1:img}').get(0).naturalWidth;",
      "var natural_height =$('#${1:img}').get(0).naturalHeight;"
    ]
  },
  "jquery width height": {
    "prefix": "width height window",
    "scope": "javascript,vue,js",
    "body": [
      "var width = \\$(window).width();",
      "var height = = \\$(window).height();"
    ]
  },
  "set height": {
    "prefix": "set width height",
    "scope": "javascript,vue,js",
    "body": [
      "$('#${1:img}').height(50)",
      "$('#${1:img}').width(50)"
    ]
  },
  "jquery css": {
    "prefix": "jquery 改 css",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "$(this).css({",
      "            'background-color': 'gray',",
      "            'position': 'absolute',",
      "            'top': 0,",
      "            'left': 0,",
      "            'width': '100%',",
      "            'height': '100%',",
      "            'opacity': 0.5 // 可调整透明度",
      "});"
    ]
  },
  "append": {
    "prefix": "jquery append",
    "scope": "javascript,vue,js",
    "body": [
      "\\$('.image-container').append('<div class=\"overlay\"></div>');"
    ]
  },
  "function resolve非同步": {
    "prefix": "fun function Promise 非同步",
    "scope": "typescript,javascript",
    "body": [
      "async checkUser() {",
      "",
      "        try {",
      "            const user = await new Promise((resolve, reject) => {",
      "                    if (user) {",
      "                        console.log(\"User is signed in:\", user);",
      "                        resolve(user); ",
      "                    } else {",
      "                        console.log('No user is signed in');",
      "                        resolve(null); ",
      "                    }",
      "            })",
      "            return user;",
      "        } catch (error) {",
      "            console.error('Error checking user:', error.message);",
      "            throw error; // 捕獲錯誤並拋出",
      "        }",
      "    }"
    ]
  },
  "debugger": {
    "prefix": "debugger",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "debugger;"
    ]
  },
  "iframe": {
    "prefix": "iframe",
    "scope": "html,blade",
    "body": [
      "<iframe id=\"${1:SYMBOL}\" src=\"\" style=\"width: 100%; height: 100%;\"></iframe>",
      "<script>",
      "            var height = \\$(window).height();",
      "            \\$('#${1:SYMBOL}').height(height);",
      "        });",
      "    </script>"
    ]
  },
  "iframe1": {
    "prefix": "iframe",
    "scope": "javascript,vue,js,typescript",
    "body": [
      "String(${1:a});"
    ]
  },
  "checkjson": {
    "prefix": "json check",
    "scope": "html,blade",
    "body": [
      "<button type=\"button\" onclick=\"checkJson('${1:SYMBOL}')\"",
      "class=\"btn btn-info btn-sm\">檢查JSON內容</button>"
    ]
  },
  "resize": {
    "prefix": "resize",
    "scope": "javascript,typescript",
    "body": [
      "window.addEventListener('resize', () => {",
      "        if (window.innerWidth >= 992) {",
      "        }",
      "    })"
    ]
  }
}