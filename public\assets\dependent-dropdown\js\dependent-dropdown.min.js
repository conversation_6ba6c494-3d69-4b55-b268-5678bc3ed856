/*!
 * dependent-dropdown v1.4.9
 * http://plugins.krajee.com/dependent-dropdown
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2014 - 2019, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD-3-Clause
 * https://github.com/kartik-v/dependent-dropdown/blob/master/LICENSE.md
 */!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(window.jQuery)}(function(e){"use strict";e.fn.depdropLocales={};var t,a;t={isEmpty:function(t,a){return null===t||void 0===t||0===t.length||a&&""===e.trim(t)},setParams:function(t,a){var n={};return 0===t.length?{}:(e.each(t,function(e,t){n[t]=a[e]}),n)},toStr:function(e){return t.isEmpty(e)?e:e.toString()}},a=function(t,a){var n=this;n.$element=e(t),e.each(a,function(e,t){n[e]=t}),n.initData(),n.init()},a.prototype={constructor:a,initData:function(){var e=this,t=e.$element;e.initVal=t.val(),e.initDisabled=t.attr("disabled"),t.data("url",e.url).data("placeholder",e.placeholder).data("loading",e.loading).data("loadingClass",e.loadingClass).data("loadingText",e.loadingText).data("emptyMsg",e.emptyMsg).data("params",e.params)},init:function(){var t,a=this,n=a.depends,i=a.$element,o=n.length,r=i.find("option").length,d=a.initDepends||a.depends;for(0!==r&&i.find('option[value=""]').length!==r||i.attr("disabled","disabled"),t=0;o>t;t++)a.listen(t,n,o);if(a.initialize===!0)for(t=0;t<d.length;t++)e("#"+d[t]).trigger("depdrop:change");i.trigger("depdrop:init")},listen:function(a,n,i){var o=this;e("#"+n[a]).on("depdrop:change change select2:select krajeeselect2:cleared",function(a){var r=e(this);(t.isEmpty(r.data("select2"))||"change"!==a.type)&&o.setDep(r,n,i)})},setDep:function(t,a,n){var i,o,r,d=this,p={};for(r=0;n>r;r++)if(i=e("#"+a[r]),o=i.attr("type"),p[r]="checkbox"===o||"radio"===o?i.prop("checked"):i.val(),d.skipDep&&(p[r]===d.loadingText||""===p[r]))return void d.$element.html('<option id="">'+d.emptyMsg+"</option>");d.processDep(d.$element,t.attr("id"),p,a)},processDep:function(a,n,i,o){var r,d,p,l,s,c=this,u=0,f={},g={},m=a.data("url"),h=t.setParams(o,i),v={},y=a.data("placeholder"),x=a.data("loading"),b=a.data("loadingClass"),j=a.data("loadingText"),P=a.data("emptyMsg"),D=a.data("params");if(c.ajaxResults={},g[c.parentParam]=i,!t.isEmpty(D)){for(p=0;p<D.length;p++)l=D[p],s=e("#"+D[p]).val(),f[p]=s,v[l]=s;g[c.otherParam]=f}g[c.allParam]=e.extend(!0,{},h,v),d={url:m,type:"post",data:g,dataType:"json",beforeSend:function(t){a.trigger("depdrop:beforeChange",[n,e("#"+n).val(),c.initVal,t]),a.find("option[selected]").removeAttr("selected"),a.val("").attr("disabled","disabled").html(""),x&&a.removeClass(b).addClass(b).html('<option id="">'+j+"</option>")},success:function(i,o,d){c.ajaxResults=i,r=t.isEmpty(i.selected)?c.initVal===!1?null:c.initVal:i.selected,t.isEmpty(i)?c.createOption(a,"",P,""):(c.renderSelect(i.output,y,r,a),a.find("optgroup").length>0&&a.find('option[value=""]').attr("disabled","disabled"),i.output&&!c.initDisabled&&a.removeAttr("disabled")),u=a.find("option").length,a.find('option[value=""]').length>0&&(u-=1),a.trigger("depdrop:change",[n,e("#"+n).val(),u,c.initVal,o,d])},error:function(t,i,o){a.trigger("depdrop:error",[n,e("#"+n).val(),c.initVal,t,i,o])},complete:function(t,i){x&&a.removeClass(b),a.trigger("depdrop:afterChange",[n,e("#"+n).val(),c.initVal,t,i])}},e.extend(!0,d,c.ajaxSettings),e.ajax(d)},createOption:function(a,n,i,o,r){var d=this,p={value:n,text:i},l=[],s=o,c=d.idParam,u=function(e){var a=t.toStr(e);a&&l.push(a)};s&&(s instanceof Array||s instanceof Object)?e.each(s,function(e,t){u(t instanceof Object?t[c]:t)}):u(s),e.extend(!0,p,r||{}),l.length&&e.inArray(t.toStr(n),l)>-1&&(p.selected="selected"),e("<option/>",p).appendTo(a)},renderSelect:function(a,n,i,o){var r,d=this,p=d.idParam,l=d.nameParam;o.empty(),n!==!1&&d.createOption(o,"",n,i),t.isEmpty(a)&&(a={}),e.each(a,function(a,n){if(t.isEmpty(n[p])){var s=e("<optgroup>",{label:a});e.each(n,function(e,t){r=t[d.optionsParam]||{},d.createOption(s,t[p],t[l],i,r)}),s.appendTo(o)}else r=n[d.optionsParam]||{},d.createOption(o,n[p],n[l],i,r)})},getAjaxResults:function(){var e=this;return e.ajaxResults}},e.fn.depdrop=function(n){var i=Array.apply(null,arguments),o=[];switch(i.shift(),this.each(function(){var r=e(this),d=r.data("depdrop"),p="object"==typeof n&&n,l=p.language||r.data("language")||"en",s={},c={};d||("en"===l||t.isEmpty(e.fn.depdropLocales[l])||(s=e.fn.depdropLocales[l]),e.extend(!0,c,e.fn.depdrop.defaults,e.fn.depdropLocales.en,s,p,r.data()),d=new a(this,c),r.data("depdrop",d)),"string"==typeof n&&o.push(d[n].apply(d,i))}),o.length){case 0:return this;case 1:return o[0];default:return o}},e.fn.depdrop.defaults={language:"en",depends:"",initDepends:"",url:"",params:{},ajaxSettings:{},ajaxResults:{},initialize:!1,skipDep:!1,loading:!0,loadingClass:"kv-loading",idParam:"id",nameParam:"name",optionsParam:"options",parentParam:"depdrop_parents",otherParam:"depdrop_params",allParam:"depdrop_all_params"},e.fn.depdropLocales.en={loadingText:"Loading ...",placeholder:"Select ...",emptyMsg:"No data found"},e.fn.depdrop.Constructor=a,e(function(){e("select.depdrop").depdrop()})});