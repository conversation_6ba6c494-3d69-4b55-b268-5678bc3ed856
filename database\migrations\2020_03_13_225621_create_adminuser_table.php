<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAdminuserTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        if (!Schema::hasTable('adminuser')) {
            Schema::create('adminuser', function (Blueprint $table) {
                $table->increments('id')->from(10000);
                $table->string('account', 50)->unique()->comment('帳號');
                $table->string('name', 50)->unique()->comment('姓名');
                $table->string('password', 100)->comment('密碼');
                $table->string('api_token', 100)->nullable()->unique()->comment('APITOKEN');
                $table->rememberToken()->nullable()->comment('忘記身份TOKEN'); //加入 remember_token 使用 VARCHAR(100) NULL
                //$table->string('userlimit', 500)->nullable()->comment('權限');
                $table->string('email', 255)->nullable()->comment('EMAIL');
                $table->integer('role')->nullable()->comment('角色');
                $table->dateTime('lastlogin_dt')->nullable()->comment('最後登入時間');
                $table->string('lastlogin_ip', 255)->nullable()->comment('最後登入IP');
                $table->string('linenotify_token')->nullable()->comment('linenotify token');

                $table->tinyInteger('online')->nullable()->default('1')->comment('開啟');

                $table->timestamp('created_at')->default(\DB::raw('CURRENT_TIMESTAMP'));
                $table->timestamp('updated_at')->default(\DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));

                //$table->timestamps();
            });
        }
        \DB::statement('ALTER TABLE adminuser AUTO_INCREMENT = 10000;');
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('adminuser');
    }
}
