{"vue_root": {"prefix": "vue app update root data", "scope": "js,javascript", "body": ["app.\\$root.\\$data.inputs.${1:SYMBOL}='C'\""]}, "function": {"prefix": "vue function", "scope": "js,javascript,vue-html", "body": ["on${1:XX}: function(event) {", "event.target.value", "},"]}, "vue_v_show": {"prefix": "vue v-show", "scope": "html,blade,vue-html", "body": ["v-show=\"inputs.${1:SYMBOL}=='C'\""]}, "vue_v_show indexOf": {"prefix": "vue v-show string like indexOf", "scope": "html,blade,vue", "body": ["v-show=\"inputs.${1:SYMB<PERSON>}.indexOf('${2:SYMBOL}') !== -1\""]}, "vue_v_show indexOf1": {"prefix": "vue v-show arraytostring like indexOf", "scope": "html,blade,vue", "body": ["v-show=\"JSON.stringify(inputs.${1:paykind}).indexOf('${2:ecpay}')>-1\""]}, "vue_v_show array": {"prefix": "vue v-show array like ", "scope": "html,blade,vue-html", "body": [" v-show=\"['11', '12', '13'].includes('' + inputs.type)\""]}, "str like array": {"prefix": "vue v-show string like ", "scope": "html,blade,vue-html", "body": [" v-show=\"inputs.type.includes('xx')\""]}, "vue_click": {"prefix": "vue click", "scope": "html,blade,vue-html", "body": [" @click=\"on${1:Admin}(\\$event)\">"]}, "vue_v_model": {"prefix": "vue v-model", "scope": "html,blade,vue-html", "body": [" v-model=\"inputs.${1:SYMBOL}\""]}, "vue_required": {"prefix": "vue required", "scope": "html,blade,vue-html", "body": [" :required=\"inputs.${1:SYMBOL}=='C'\""]}, "vue v-for": {"prefix": "vue v-for for", "scope": "html,blade,vue-html", "body": [" v-for=\"(rs, index) in data.${1:data}\" :key=\"index\""]}, "vue keyup": {"prefix": "vue keyup 打字觸發", "scope": "html,blade,vue-html", "body": ["@keyup=\"onFormat(\\$event.target.value)\""]}, "vue change": {"prefix": "vue change 值異動", "scope": "html,blade,vue-html", "body": ["@change=\"onChange${1:name}(\\$event.target.value)\""]}, "vue blur": {"prefix": "vue blur 離開", "scope": "html,blade,vue-html", "body": ["@blur=\"onFormat(\\$event.target.value)\""]}, "vue click": {"prefix": "vue click", "scope": "html,blade,vue-html", "body": ["@click=\"onClick(\\$event.target.value)\""]}, "vue submit prevent": {"prefix": "vue html submit prevent", "scope": "html,blade,vue-html", "body": ["@submit=\"onSubmit(this)\""]}, "vue onSubmit ": {"prefix": "vue onSubmit", "scope": "js,javascript", "body": ["onSubmit: function(event) {", "     form = document.forms['oForm'];", "     if (!form.reportValidity()) {", "      checkValidity(window.event, form);", "      return false;", "     }", "                json = JSON.stringify(this.inputs)", "                //console.log([\"json \", json]);", "                form.jsonbody.value = json;", "                //event.preventDefault();不要送出", "                return true;", "            }"]}, "vue onClick ": {"prefix": "vue onClick", "scope": "js,javascript", "body": ["onClick: function(event) {", "  event.currentTarget.id", "  event.currentTarget.name", "  console.log(['event.target.value',event.target.value,event.target.getAttribute('rel')]);", "}"]}, "vue onChange": {"prefix": "vue onChange", "scope": "js,javascript,vue-html,typescript", "body": ["onChange: function(event) {", "  console.log(['event.target.value',event.target.value]);", "  console.log(event.target.options[event.target.selectedIndex].title,event.target.getAttribute('rel'))", "}"]}, "vue rel1": {"prefix": "vue event rel", "scope": "js,javascript,vue-html,typescript", "body": ["event.target.getAttribute('rel')"]}, "vue rel2": {"prefix": "vue event rado get label,", "scope": "js,javascript,vue-html", "body": ["\\$(event.currentTarget).next('label:first').html();"]}, "vue data update value": {"prefix": "vue root data update", "scope": "js,javascript,vue", "body": ["app.\\$root.\\$data"]}, "if str count": {"prefix": "if str count", "scope": "javascript,js,vue-html,typescript", "body": ["if (${1:datas}.indexOf('${2:datas}')>-1){", "", "}"]}, "template": {"prefix": "vue template if else", "scope": "html,blade,vue-html", "body": ["<template v-if=\"step=='1'\">", "", "</template>", "<template v-else-if=\"step=='2'\">", "", "</template>", "<template v-else>", "", "</template>"]}, "template v-for": {"prefix": "vue template v-for", "scope": "html,blade,vue-html", "body": ["<template v-for=\"(rs, index) in data.${1:data}\" :key=\"index\">", "", "</template>"]}, "vue_text": {"prefix": "vue text", "scope": "html,blade", "body": ["<input type=\"text\" v-model=\"${1:inputs}.${2:key}\" class=\"form-control\"", ":value=\"'@{{item.name}}'\"", "required >"]}, "vue_text number": {"prefix": "vue text number", "scope": "html,blade", "body": ["<input type=\"number\" v-model.number=\"${1:inputs}.${2:key}\"  pattern=\"\\d*\" class=\"form-control\"", ":value=\"'@{{item.name}}'\"", "required >"]}, "vue_text float": {"prefix": "vue text float", "scope": "html,blade", "body": ["<input type=\"number\" v-model.number=\"${1:inputs}.${2:key}\"  step=\"0.01\" inputmode=\"decimal\" class=\"form-control\"", ":value=\"'@{{item.name}}'\"", "required >"]}, "vue xml select": {"prefix": "vue xml select", "scope": "html,blade", "body": ["{{Form::myUIXml([", "     'xmldoc' => \\$data['xmldoc'],", "     'type' =>'select',", "     'node' => '//參數設定檔/${1:index}/KIND',", "     'v-model' => '${2:inputs}.${3:name}',", "     'linecount' => 4,", "     'required'=>true,", "   //  '@change'=>\"onChange(event)", " ])", "}}"]}, "vue xml radio": {"prefix": "vue xml radio", "scope": "html,blade", "body": ["{{Form::myUIXml([", "     'xmldoc' => \\$data['xmldoc'],", "     'type' =>'radio',", "     'node' => '//參數設定檔/${1:index}/KIND',", "     'v-model' => '${2:inputs}.${3:name}',", "     'linecount' => 4,", "     'required'=>true,", "    // '@click'=>\"onClick(\\$event)", " ])", "}}"]}, "vue xml checkbox": {"prefix": "vue xml checkbox", "scope": "html,blade", "body": ["{{Form::myUIXml([", "     'xmldoc' => \\$data['xmldoc'],", "     'type' =>'checkbox',", "     'node' => '//參數設定檔/${1:index}/KIND',", "     'v-model' => '${2:inputs}.${3:name}',", "     'linecount' => 4,", "     'required'=>true,", "    // '@click'=>\"onClick(\\$event)", " ])", "}}"]}, "db_checkbox": {"prefix": "vue checkbox db", "scope": "html,blade,vue", "body": ["{{ ", "Form::myUIDb([", " 'type' => 'checkbox',", " 'sql' => \"select kindid,kindtitle from kind where kind='${1:index}kind' order by kindsortnum\",", " 'v-model' => '${1:inputs}.${2:name}',", " 'linecount' => 4,", " 'required'=>true,", " //'@click'=>\"onClick(\\$event)", "])", "}}"]}, "vue db radio": {"prefix": "vue radio db", "scope": "html,blade", "body": ["{{ ", "Form::myUIDb([", "  'type' => 'radio',", "  'sql' => \"select kindid,kindtitle from kind where kind='${1:index}kind' order by kindsortnum\",", "  'v-model' => '${1:inputs}.${2:name}',", "  'linecount' => 4,", "  'required'=>true,", "    // '@click'=>\"onClick(\\$event)", "])", "}}"]}, "vue db select": {"prefix": "vue select db", "scope": "html,blade", "body": ["{{ ", "Form::myUIDb([", "  'type' => 'select',", "  'sql' => \"select kindid,kindtitle from kind where kind='${1:index}kind' order by kindsortnum\",", "  'v-model' => '${1:inputs}.${2:name}',", "  'linecount' => 4,", "  'required'=>true,", "  //'@change'=>\"onChange(\\$event)", "])", "}}"]}, "push": {"prefix": "vue push", "scope": "javascript,vue", "body": ["this.inputs.${1:datas}.push(JSON.parse(JSON.stringify(this.templetedata)));"]}, "event.target.value": {"prefix": "vue target 取得client value", "scope": "javascript,vue", "body": ["event.target.value"]}, "vue radio": {"prefix": "vue radio value label", "scope": "javascript,vue", "body": ["console.log(event.target.value)", "console.log(event.currentTarget.nextElementSibling.textContent);// get label"]}, "vue select": {"prefix": "vue select value title", "scope": "javascript,vue", "body": ["console.log(event.target.value)", "console.log(event.target.options[event.target.selectedIndex].title)"]}, "event.currentTarget.name": {"prefix": "vue currentTarget 取得client name", "scope": "javascript,vue", "body": ["e.currentTarget.name"]}, "checkbox": {"prefix": "vue checkbox", "scope": "html,blade", "body": ["<input type=\"checkbox\"  @click=\"onSame($event)\" v-model=\"rs.online\" :true-value=\"1\" :false-value=\"0\" />"]}, "submit": {"prefix": "vue submit", "scope": "html,blade", "body": ["@submit=\"onSubmit\""]}, "localStorage": {"prefix": "vue localStorage", "scope": "javascript,vue", "body": ["localStorage.${1:api_token}"]}, "vue_load2": {"prefix": "vue load2", "scope": "html,blade,vue", "body": ["<SCRIPT language=JavaScript>", "function oForm_onsubmit(form)", "{", "", "    if (PF_FormMultiAll(form)==false){return false};", "  PF_FieldDisabled(form)//將全部button Disabled", "  ", "   json=JSON.stringify(app.\\$root.\\$data.inputs)", "console.log(json);", "   form.json.value=json;", "//    console.log(form.json.value);", "   return true;", "}", "</SCRIPT>", " {{ Form::hidden(\"jsonbody\", \\$data[\"jsonbody\"] ) }}", "", "<script type=\"text/javascript\" src=\"{{ asset('Scripts/vue.min.js') }}\"></script>", "<script type=\"text/javascript\" src=\"{{ asset('Scripts/PJSFunVue.js') }}\"></script>", "<script language=JavaScript> ", "Vue.config.devtools = true;", "var app = new Vue({", "    el: '#app',", "    data: {", "      ${1:datas}: [],", "      inputs:{},", "      templetedata:{},", "    },", "    methods: {", "      loadData: function () {", "        let vm = this;", "        jsonbody=document.forms['oForm'].elements['jsonbody'].value;", "        if(jsonbody!=\"\"){", "            //this.inputs.${1:datas}=JSON.parse(jsonbody).${1:datas};", "            json = JSON.parse(jsonbody);", "            Object.keys(json).forEach(key => {", "               vm.inputs[key] = json[key];", "            });", "       } else {", "         //this.onAdd(vm.inputs.${1:datas}, vm.templetedata)", "       }", "       this.\\$nextTick(() => {//DOM完畢後", "          ", "       });", "        ", "      },", "    },", "    created: function () { /** 程式己預備 **/", "           this.loadData();", "    },", "    /*", "    watch: {", "            'inputs.qty2s': {", "                handler(val) {", "                    total = 0;", "                    val.forEach(rs => {", "                        total += parseInt(rs.qty, 10);", "                    });", "                    this.qty2count = total;", "                },", "                deep: true", "            }", "    },", "    computed:{", "         total () {", "              var t=0;", "              t= this.${1:datas}.reduce((total, p) => {", "                  return total + p.price * p.qty;", "              }, 0);", "              console.log(t);", "              return t;", "        }", "    },", "    ", "    mounted() {", "        document.addEventListener(\"DOMContentLoaded\", () => {", " ", "        });", "    }", "    */", "});", " </script>"]}, "vue load3": {"prefix": "vue2 load3", "scope": "html,php,blade,vue", "body": ["", "<SCRIPT language=JavaScript>", "function oForm_onsubmit(form)", "{", "", "    if (PF_FormMultiAll(form)==false){return false};", "  PF_FieldDisabled(form)//將全部button Disabled", "  ", "   json=JSON.stringify(app.\\$root.\\$data.inputs)", "console.log(json);", "   form.json.value=json;", "//    console.log(form.json.value);", "   return true;", "}", "</SCRIPT>", " {{ Form::hidden(\"jsonbody\", \\$data[\"jsonbody\"] ) }}", "", "<script type=\"text/javascript\" src=\"{{ asset('Scripts/vueprod3.js') }}\"></script>", "<script type=\"text/javascript\" src=\"{{ asset('Scripts/vue-components.js') }}\"></script>", "<script type=\"text/javascript\" src=\"{{ asset('Scripts/PJSFunVue3.js') }}\"></script>", "<script language=JavaScript>", "const app = Vue.createApp({", "   mixins: [myMixin],", "   data() {", "      return {", "        ${1:datas}: [],", "        inputs:{${1:datas}:[]},", "        templetedata:{},", "       };", "    },", "    methods: {", "      loadData: function () {", "        let vm = this;", "        jsonbody=document.forms['oForm'].elements['jsonbody'].value;", "        if(jsonbody!=\"\"){", "            //this.inputs.${1:datas}=JSON.parse(jsonbody).${1:datas};", "            json = JSON.parse(jsonbody);", "            Object.keys(json).forEach(key => {", "               vm.inputs[key] = json[key];", "            });", "        } else {", "         //this.onAdd(vm.inputs.${1:datas}, vm.templetedata)", "        }", "       this.\\$nextTick(() => {//DOM完畢後", "          ", "       });", "        ", "      },", "      onSubmit: function(event) {", "         form = document.forms['oForm'];", "         if (!form.reportValidity()) {", "           checkValidity(window.event, form);", "            return false;", "          }", "                json = JSON.stringify(this.inputs)", "                //console.log([\"json \", json]);", "                form.jsonbody.value = json;", "                //event.preventDefault();不要送出", "                return true;", "       }", "    },", "    mounted: function() { /** 初始化掛載完成 **/", "        document.addEventListener(\"DOMContentLoaded\", () => {", "           this.loadData();", "        });", "    },", "    /*", "    watch: {", "            'inputs.qty2s': {", "                handler(val) {", "                    total = 0;", "                    val.forEach(rs => {", "                        total += parseInt(rs.qty, 10);", "                    });", "                    this.qty2count = total;", "                },", "                deep: true", "            }", "    },", "    computed:{", "         total () {", "              var t = 0;", "              this.inputs.datas.forEach(function(rs, index) {", "                  t += parseInt(rs.qty, 10);", "              });", "              console.log(t);", "              return t;", "        }", "    },", "    ", "    async mounted() {", "        //console.log(1);", "    }", "    */", "}).use(myComponentsPlugin).mount('#app');", "</script>"]}, "vue load3 簡易版": {"prefix": "vue2 load3 簡易版", "scope": "html,php,blade,vue", "body": ["", "<script type=\"text/javascript\" src=\"{{ asset('Scripts/vueprod3.js') }}\"></script>", "<script language=JavaScript>", "const app = Vue.createApp({", "   data() {", "      return {", "        inputs:{},", "       };", "    },", "    methods: {", "", "    },", "    mounted: function() { /** 初始化掛載完成 **/", "       document.addEventListener(\"DOMContentLoaded\", () => {", "       this.\\$nextTick(() => {//DOM完畢後", "          ", "       });", "        });", "    },", "}).mount('#app');", "</script>"]}, "vue add move remove table": {"prefix": "vue add move remove table ", "scope": "html,php,blade,vue", "body": ["", "                   <div class=\"table-responsive-md\">", "                        <table class=\"table table-striped table-hover table-bordered table-fixed\">", "                            <tr>", "                                <th>", "                                    關係", "", "                                </th>", "                                <th width=\"100\">", "                                    編輯", "                                </th>", "                            </tr>", "                            <tr v-for=\"(rs, index) in inputs.${1:datas}\" :key=\"index\">", "                                <td><input type=\"text\" v-model=\"rs.relation\" class=\"form-control\"></td>", "                                <td>", "                                    <button type=\"button\" class=\"btn btn-tool\" data-card-widget=\"remove\" title=\"Remove\" @click=\"onDelRow(inputs.${1:datas},rs)\">", "                                        <i class=\"fas fa-times\"></i>", "                                    </button>", "                                    <a href=\"javascript:;\" @click=\"onMoveRow(inputs.${1:datas},'up',index);\">▲ </a>", "                                    <a href=\"javascript:;\" @click=\"onMoveRow(inputs.${1:datas},'down',index);\">▼</a>", "                                </td>", "                            </tr>", "                        </table>", "                    </div>", "    <div align=\"center\">", "        <button type=\"button\" class=\" btn btn-primary\" @click=\"onAdd(inputs.${1:datas},templetedata)\">新增</button>", "    </div>"]}, "vue add move remove card": {"prefix": "vue add move remove card ", "scope": "html,php,blade", "body": ["", "    <template v-for=\"(rs, index) in inputs.${1:datas}\" :key=\"index\">", "", "        <div class=\"card\">", "            <div class=\"card-header\">", "                @{{index+1}}", "                <div class=\"card-tools\">", "                    <button type=\"button\" class=\"btn btn-tool\" data-card-widget=\"remove\" title=\"Remove\"", "                        @click=\"onDelRow(inputs.${1:datas},rs)\">", "                        <i class=\"fas fa-times\"></i>", "                    </button>", "                    <a href=\"javascript:;\" @click=\"onMoveRow(inputs.${1:datas},'up',index);\">▲ </a>", "                    <a href=\"javascript:;\" @click=\"onMoveRow(inputs.${1:datas},'down',index);\">▼</a>", "                    <a href=\"javascript:;\" @click=\"onMoveRow(inputs.${1:datas},'top',index);\">Top</a>", "                    <a href=\"javascript:;\" @click=\"onMoveRow(inputs.${1:datas},'bottom',index);\">Bottom</a>", "                </div>", "                <div class=\"card-body\" id=\"vmodels\">", "                  <input type=\"text\" v-model=\"rs.title\" class=\"form-control\">", "", "                </div>", "            </div>", "        </div>", "    </template>", "", "    <div align=\"center\">", "        <button type=\"button\" class=\" btn btn-primary\" @click=\"onAdd(inputs.${1:datas},templetedata)\">新增</button>", "        <button class=\"btn btn-success\">確定</button>", "        <button type=\"reset\" class=\"btn btn-secondary\">取消</button>", "    </div>"]}, "vue_radio_html": {"prefix": "vue radio", "scope": "html,blade", "body": ["<input type=\"radio\" ", "        required v-model=\"inputs.${1:status}\" >"]}, "vue checkbox_html": {"prefix": "vue checbox + hidden", "scope": "html,blade", "body": ["<input type=\"checkbox\" ", "        required v-model=\"inputs.${1:status}\" >"]}, "vue textarea": {"prefix": "vue textarea text", "scope": "html,blade", "body": ["<textarea v-model=\"inputs.${1:status}\" class=\"form-control\" cols=\"37\" rows=\"5\" style='width:90%;height:200px'></textarea>"]}, "vue text": {"prefix": "vue text input", "scope": "html,blade", "body": ["<input type=\"text\" class=\"form-control\"", " required v-model=\"inputs.${1:status}\" placeholder=\"\" />"]}, "vue class": {"prefix": "vue font class", "scope": "html,blade,vue-html", "body": ["<font :class=\"{'text-danger':(lowcount-qty1count>0)}\"></font>"]}, "vue style1": {"prefix": "vue font style", "scope": "html,blade,vue-html", "body": ["<font :style=\"{ color: (rs.isdeal == '1') ? 'red' : (rs.ischeckin == '1') ? 'blue' : '' }\">"]}, "vue style": {"prefix": "vue style", "scope": "html,blade,vue-html", "body": [" :style=\"{'background':inputs.body3.color}\">"]}, "vue html": {"prefix": "vue html 純文字", "scope": "html,blade,vue-html", "body": [" <span v-html=\"inputs.body\"></span>"]}, "vue upload": {"prefix": "vue2 upload file image", "scope": "html,blade", "body": [" <vue-upload folder=\"{{ url('/')}}/images/${1:product}/\" width=800 height=800 v-model=\"inputs.${2:img}\" :value=\"inputs.${2:img}\" accept=\"image /*\"></vue-upload>"]}, "foreach array del key ": {"prefix": "array vue del inputs.key", "scope": "javascript,vue,js", "body": ["Vue.delete(vm.inputs, key);"]}, "vue nextTick": {"prefix": "vue next", "scope": "js,javascript,vue", "body": [" this.\\$nextTick(() => {", "${1:}", " });"]}}