<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="share",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="sNO", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sTitle", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sAddr", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sFg", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sFg1", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPrice", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPaid", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic1", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic2", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic3", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic4", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic5", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic6", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic7", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sPic8", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sNote1", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sNote2", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sNote3", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="sKeyin", type="",description="", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class share extends baseModel
{
    use HasFactory;
    
    public $tabletitle = '';
    public $table = 'share';
    public $primaryKey = '';
    //public $incrementing = false;//取消自動編號
      //欄位必填
      public $rules = [
		'id' => 'required',

    ];
    public $fieldInfo = [
'sNO'=>['title'=>'','type'=>'varchar(255)'],//
'sTitle'=>['title'=>'','type'=>'varchar(255)'],//
'sAddr'=>['title'=>'','type'=>'varchar(255)'],//
'sFg'=>['title'=>'','type'=>'varchar(255)'],//
'sFg1'=>['title'=>'','type'=>'varchar(255)'],//
'sPrice'=>['title'=>'','type'=>'varchar(255)'],//
'sPaid'=>['title'=>'','type'=>'varchar(255)'],//
'sPic1'=>['title'=>'','type'=>'varchar(255)'],//
'sPic2'=>['title'=>'','type'=>'varchar(255)'],//
'sPic3'=>['title'=>'','type'=>'varchar(255)'],//
'sPic4'=>['title'=>'','type'=>'varchar(255)'],//
'sPic5'=>['title'=>'','type'=>'varchar(255)'],//
'sPic6'=>['title'=>'','type'=>'varchar(255)'],//
'sPic7'=>['title'=>'','type'=>'varchar(255)'],//
'sPic8'=>['title'=>'','type'=>'varchar(255)'],//
'sNote1'=>['title'=>'','type'=>'varchar(255)'],//
'sNote2'=>['title'=>'','type'=>'varchar(255)'],//
'sNote3'=>['title'=>'','type'=>'varchar(255)'],//
'sKeyin'=>['title'=>'','type'=>'varchar(255)'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['sNO','sTitle','sAddr','sFg','sFg1','sPrice','sPaid','sPic1','sPic2','sPic3','sPic4','sPic5','sPic6','sPic7','sPic8','sNote1','sNote2','sNote3','sKeyin']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    
    protected $dates = [];
  
//   public function __construct() {
//         $this->fillable = parent::getfillables();//接受$request->all();
//         //$this->fillable =array_keys($this->fieldInfo);
//         parent::__construct();        
//         parent::setFieldInfo($this->fieldInfo);
//   }         
    
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          
        });
         static::deleting(function ($model) {

          
        });
        static::deleted(function ($model) {
            
        });
    }	

}