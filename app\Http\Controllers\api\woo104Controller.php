<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\api\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use App\Models\Product;
use PF;
use Str;

class woo104Controller extends Controller {

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);



        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }
    /**
     * 處理 104woo 網站資料
     */
    public function store(Request $request) {

        //try {
        $woo104Service = new \App\Services\woo104Service();
        $woo104Service->parse($request);
        // } catch (\Exception $e) {
        //     $this->jsondata['resultcode'] = 999;【
        //     $this->jsondata['resultmessage'] = $e->getMessage();
        //     //throw $e;
        // } finally {
        // }


        return $this->apiResponse($this->jsondata);
    }
}
