

/*----------------------------------*\
  # team-title                     
\*----------------------------------*/
.team-title { 
  position: relative;
  font-size: 1.8rem;
  font-weight: bold;  
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 1.8rem 0;
}
.team-title::before,
.team-title::after {
  content: "";
  flex: 1;
  height: 1px;
  background: #ccc;
  margin: 0 1rem;
}

/*----------------------------------*\
  # team-grid                   
\*----------------------------------*/
.team-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  width: 63rem;
  max-width: 90%; 
  margin: 0 auto 2.7rem;
}
.team-grid a,
.team-grid a:visited,
.team-grid a:hover  {
  text-decoration:none; 
  color:inherit;  
}

@media (min-width: 768px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .team-grid {
    grid-template-columns: repeat(4, 1fr);
    margin: 0 auto 4.5rem;
    width: 72vw;
  }
}
.team-grid .member {
  background-color: #fff;
  padding: 16px; padding: .81rem; 
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(255, 128, 0, 0.2);
  box-shadow: var(--box-sd);
  text-align: center;
  transition: transform 0.3s ease;
}
.team-grid .member:hover {
  transform: translateY(-5px);
}
.team-grid .member img {
  width: 100%;      
  border-radius: .45rem;
  margin-bottom: .6rem;     
}
.team-grid .name {
  font-weight: bold;
  font-size: 1.1em;
  color: #333;
}
.team-grid .tel {
  color: #FF8000;
  font-weight: bold;
  margin: 6px 0; margin: .3rem 0;
}
.team-grid .desc {
  font-size: 0.9em;
  color: #555;
  line-height: 1.5;
}

/*----------------------------------*\
  # features                   
\*----------------------------------*/
.features { 
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 33px;gap:1.65rem;
  width: 72vw;   
  flex-wrap: nowrap;
  margin: 2.7rem auto 5.4rem;
}
.feature-item {
  position: relative;
  background: rgba(255, 255, 255, 0.6); /* 半透明白 */
  border-radius: 10px;
  padding: 2rem 1rem 1rem;
  flex: 0 0 31%;
  box-shadow: 0 .45rem 1rem rgba(0,0,0,0.15);  
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-align: center;
}
/* 半圓形偽元素也跟著有玻璃感 */
.feature-item::before {
  content: "";
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  z-index: 0;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
/* 半圓形偽元素 */
.feature-item::before {
  content: "";
  position: absolute;
  top: -1.8rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4.5rem;
  height: 4.5rem;
  background: #fff;
  border-radius: 50%;
  z-index: 0;
}
.feature-item i {
  position: absolute;
  top: -22px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2.7rem;
  color: #ff8000;
  z-index: 1;
  background: #fff;
  padding: 5px;
  border-radius: 50%;
}
.feature-item h3 {  
  font-size: var(--keyword);
  margin: 1.2rem 0;
  font-weight: 800;
}
.feature-item p {  
  font-size: var(--tiny);
  opacity: .9; 
}
@media (max-width: 1199px) {
  .features {       
    margin: 2.7rem auto ;
  }
}
@media (max-width: 768px) {
  .features {
    flex-direction: column;
    flex-wrap: wrap;
    width: 54rem;
    max-width: 90%;
  }
  .feature-item {
    flex: 1 1 100%;
    margin-bottom:2.7rem;
  }
}
/*----------------------------------*\
  # services                   
\*----------------------------------*/
/*.services {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 33px;
  max-width: 54rem;
  margin: 0 auto 5.4rem;
  flex-wrap: nowrap; 
}
.service-item {
  background: #fff;
  border-radius: .54rem;
  padding: 1rem;
  flex: 0 0 30%; 
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  box-shadow: var(--box-sd);
  text-align: center;  
}
.service-item i {
  font-size: 2.5rem;
  color: #ff8000;
  margin-bottom: 10px;
}
.service-item h3 {
  font-size: 1.2rem;
  font-size: var(--keyword);
  margin: 1.2rem 0;
  font-weight: 800;  
}
.service-item p {
  font-size: 1rem;
  font-size: var(--tiny);  
}
@media (max-width: 768px) {
  .services {
    flex-direction: column;
    flex-wrap: wrap;
    margin: 0 auto 2.7rem;
  }
  .service-item {
    flex: 1 1 90%;
  }
}*/

