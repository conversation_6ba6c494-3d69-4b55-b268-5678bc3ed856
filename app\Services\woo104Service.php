<?php

namespace App\Services;

use Illuminate\Http\Request;
use DB;
use PF;
use Str;
use File;
use Hamcrest\Type\IsNumeric;

/**
 * Class JsonTranslator.
 */
class woo104Service {

    public function __construct() {

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->data['displaynames'] = \App\Models\product::getFieldTitleArray();
    }
    public function getBetween($body, $str) {
        if (preg_match('/' . $str . '([0-9.]+)/u', $body, $matches)) {
            return  $matches[1];
        } else {
            return  '';
        }
    }
    public function allproduct() {
        $rows = \DB::table('product')->selectRaw('productid');
        //$rows->where('productid', 888);
        //$rows = $rows->limit(10);
        $rows->orderByRaw('productid desc');
        $rows = $rows->get();
        echo count($rows);
        $count = 0;
        foreach ($rows  as $key => $rs) {
            try {

                if (\File::exists(public_path('images/json/' . $rs->productid . ".json"))) {
                    $count++;
                    $jsonContent = \File::get(public_path('images/json/' . $rs->productid . ".json"));
                    $request = new Request(json_decode($jsonContent, true));
                    $this->parse($request, 1);
                }
            } catch (\Exception $e) {
                echo $e->getMessage();
                //throw $e;
            } finally {
            }
        }
        echo $count;
    }
    public function parse(Request $request, $isbug = false) {

        // 驗證輸入資料
        $validators = [
            'case_number' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'price' => 'required',

            'url' => 'required|url',
            'image_urls' => 'nullable|array',
            'image_urls.*' => 'nullable|url',
        ];

        $validator = \Validator::make($request->all(), $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        try {

            $data = $request->all();
            $inputs = [];
            // 下載圖片
            $imageNames = [];
            // if ('其它拍賣' == $court) {
            //     $producttitle = $number;
            // } else {
            //     $producttitle = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/物件標題對照/KIND/資料', '傳回值', $pattern);
            // }
            // $inputs['producttitle'] = $producttitle;
            $inputs['productkind'] = '1';
            if (\Str::contains($request->input('address'), ['地號'])) {
                $inputs['productkind'] = '3';
                $inputs['pattern'] = '土地';
            }
            $case_number = trim($request->input('case_number'));
            if (\Str::contains($request->input('case_number'), ['【'])) {
                $case_number = trim(\Str::between($request->input('case_number'), '【', ' '));

                $inputs['debtor'] = \Str::after(\Str::between($request->input('case_number'), '【', '】'), ' ');
                $inputs['court'] = trim(\Str::before($request->input('case_number'), '【'));
            }

            $inputs['number'] = $case_number;
            $address = $request->input('address');
            if (\Str::contains($address, ['地址：'])) {
                $address = \Str::between($address, '地址：', '<');
            }
            $inputs['city1title'] = mb_substr($address, 0, 3);
            $inputs['city2title'] = mb_substr($address, 3, 3);
            $inputs['address'] = \Str::after($address, $inputs['city2title']);


            $rows = \App\Models\product::selectRaw('productid,online');
            $rows->where('number', '=', $inputs['number']);
            $rows->where('city1title', '=', $inputs['city1title']);
            $rs = $rows->first();
            if ($rs != null) {
                if ($rs->online == 0) {
                    throw new \CustomException("已下架不更新");
                }
            }



            $totalupset = $request->input('price');
            //總底價
            $totalupset = trim(str_replace('萬', '', $totalupset));
            $totalupset = trim(str_replace(',', '', $totalupset));
            $inputs['totalupset'] = $totalupset; // 轉換為萬元
            $inputs['beattime'] = $data['auction_round'];

            $inputs['tenderdate'] = $request->input('bid_datetime');
            if (\Str::contains($request->input('bid_datetime'), ['('])) {
                $inputs['tenderdate'] = Str::before($request->input('bid_datetime'), '(');
                $inputs['tenderdate'] = preg_replace_callback('/(\d{3})\/(\d{1,2})\/(\d{1,2})/', function ($matches) {
                    return sprintf('%s/%02d/%02d', $matches[1], $matches[2], $matches[3]);
                }, $inputs['tenderdate']);
            }

            $inputs['floorprice'] = trim(str_replace('萬', '', $request->input('building_unit_price')));

            //保證金
            $inputs['margin'] = trim(str_replace('萬', '', $request->input('deposit')));
            $auction_result = str_replace(",", "", $data['auction_result']);
            $auction_result = str_replace("萬", "", $auction_result);

            if (is_numeric($auction_result)) {
                $inputs['auctions'] = '拍定';
                $inputs['thebidprice'] = $auction_result;
            } else {
                $inputs['auctions'] = $auction_result;
            }
            // //現況
            // $auctionssortnum = 9;
            // if ('應買' == $inputs['beattime']) {
            //     $auctionssortnum = 1.1;
            // } else {
            //     foreach ($this->data['xmldoc']->xpath('//參數設定檔/拍賣情況/KIND') as $v) {
            //         if (substr_count($inputs['auctions'], $v->資料) > 0) {
            //             $auctionssortnum = strval($v->傳回值);
            //             break;
            //         }
            //     }
            // }
            // $inputs['auctionssortnum'] = $auctionssortnum;


            $inputs['currentvalues'] = $data['announced_value'];

            $inputs['aftermakingvalue_added'] = trim(str_replace('萬', '', $request->input('post_auction_appreciation')));
            if (\Str::contains($request->input('address'), ['持分'])) {
                $inputs['online'] = 0;
                $inputs['houseage'] = "50";
            } else {
                $inputs['houseage'] = $data['building_age'] ? (int) $data['building_age'] : 0;
            }
            $inputs['buildname'] = $data['community_name'];
            $inputs['landaddress'] = $data['land'];
            $inputs['buildings'] = $data['building'];
            $inputs['transcriptinformation'] = $data['seizure_record'];
            $inputs['bidsrecords'] = $data['auction_record'];
            $inputs['url'] = $data['url'];

            //$total_area = \Str::between($request->input('total_area'), '總坪數', '坪');
            //持分地坪

            $inputs['stakeholdersfloor'] = explode("坪", $request->input('land_area'))[0];
            // $inputs['floorprice'] = 0;




            //公設比
            $postulatemorethan = trim(\Str::between($request->input('public_area'), '公設比 ', ' '));
            $inputs['postulatemorethan'] = str_replace("%", '', $postulatemorethan);

            $seizure_record = $request->input('seizure_record');
            if (\Str::contains($seizure_record, ['部份不點交', '部份點交'])) {
                $inputs['nocross_point'] = "部分點交";
            } else if (\Str::contains($seizure_record, ['拍定後點交'])) {
                $inputs['nocross_point'] = "拍定後點交";
            } else if (\Str::contains($seizure_record, ['拍定後不點交'])) {
                $inputs['nocross_point'] = "拍定後不點交";
            } else if (\Str::contains($seizure_record, ['不點交'])) {
                $inputs['nocross_point'] = "不點交";
            }
            if ($request->input('transcript_data') != "") {
                $transcript_data = $request->input('transcript_data');

                //總坪數、主建坪、附屬坪、公設坪、車位坪及增建坪
                $inputs['pingtotalnumberof'] = $this->getBetween($transcript_data, "總坪數");

                if ($inputs['pingtotalnumberof'] == "") {
                    $inputs['pingtotalnumberof'] = (float)\Str::between($request->input('total_area'), '總坪數', '坪');
                }
                if ($inputs['pingtotalnumberof'] == "") {
                    $inputs['pingtotalnumberof'] = 0;
                }
                $inputs['mainlawnestablishment'] = $this->getBetween($transcript_data, "主建坪");

                //附建坪
                $inputs['attachedtolawnestablishment'] = $this->getBetween($transcript_data, "附屬坪");
                $inputs['postulateping'] =  $this->getBetween($transcript_data, "公設坪");
                //車位坪
                $inputs['carping'] =  $this->getBetween($transcript_data, "車位坪");
                //增建坪
                $inputs['additionalping'] =  $this->getBetween($transcript_data, "增建坪");

                //if ($inputs['pingtotalnumberof'] != "" && $inputs['additionalping'] != '') {
                //公告建坪公告建坪就如同昨天我們討論的，是總坪述加增建坪
                //$inputs['noticelawnestablishment'] = $inputs['pingtotalnumberof'] + $inputs['additionalping'];
                //}
            }
            //公告建坪
            $inputs['noticelawnestablishment'] = trim(explode("坪", $request->input('building_area'))[0]);


            //土地公現


            $inputs['landprice'] = \Str::before($request->input('announced_value'), ' ');
            //可以爬蟲”建物”的關鍵字:鋼筋混凝土、加強磚造、磚造等這三種

            $building = $request->input('building');
            if (\Str::contains($building, ['鋼筋混凝土'])) {
                $inputs['architecture'] = "鋼筋混凝土";
            } else if (\Str::contains($building, ['加強磚造'])) {
                $inputs['architecture'] = "加強磚造";
            } else if (\Str::contains($building, ['磚造'])) {
                $inputs['architecture'] = "磚造";
            }

            // if (\Str::contains($request->input('address'), ['號']) && \Str::contains($request->input('address'), ['樓'])) {
            //     $inputs['storey'] = \Str::between($request->input('address'), '號', '樓');
            //大樓名稱
            $inputs['buildname'] = $request->input('community_name');
            $inputs['storey'] = "";
            if (\Str::contains($request->input('address'), ['<']) && \Str::contains($request->input('address'), ['>'])) {

                $inputs['storey'] = \Str::between($request->input('address'), '<', '>');
            }
            //房屋型態只要區分公寓、華廈、電梯大樓、透天、別墅這五樣，其他的套房、店面、辦公室及其他這四樣可以留給我們後端去勾選就好
            if (is_numeric($inputs['storey'])) {
                if ($inputs['storey'] >= 8) {
                    $inputs['pattern'] = '大樓';
                } else if ($inputs['storey'] >= 6) {
                    $inputs['pattern'] = '華廈';
                } else if ($inputs['storey'] <= 5) {
                    $inputs['pattern'] = '公寓';
                }
            } else  if ($inputs['storey'] == '三層樓' || \Str::contains($request->input('address'), ['層樓'])) {
                if ($inputs['buildname'] != "") {
                    $inputs['pattern'] = '別墅';
                } else {
                    $inputs['pattern'] = '透天';
                }
            }

            //
            //坪單價 = 總底價 / 公告建坪
            //如果總坪數為0，則坪單價=總底價/公告坪數
            // if (is_numeric($inputs['totalupset']) && is_numeric($inputs['noticelawnestablishment'])) {
            //     if ($inputs['pingtotalnumberof'] == 0 && $inputs['noticelawnestablishment'] > 0) {
            //         $inputs['floorprice'] = round($inputs['totalupset'] / $inputs['noticelawnestablishment'], 2);
            //     }
            // }

            // if ($inputs['floorprice'] == 0 && is_numeric($inputs['totalupset']) && is_numeric($inputs['pingtotalnumberof'])) {
            //     if ($inputs['pingtotalnumberof'] > 0) {
            //         $inputs['floorprice'] = round($inputs['totalupset'] / $inputs['pingtotalnumberof'], 2);
            //     }
            // }



            //        }



            if ($data['image_links'] != "") {
                foreach ($data['image_links'] as $index => $imageUrl) {
                    try {

                        $ext = pathinfo($imageUrl, PATHINFO_EXTENSION);
                        if (\Str::contains($ext, ['png', 'gif', 'jpg'])) {

                            $fileName = md5(end(explode("/", $imageUrl))) . "." . $ext;
                            $imageNames[] = $fileName;
                            if (\File::exists(public_path('images/product/' . $fileName))) {
                                continue;
                            }
                            $savePath = public_path('images/product/' . $fileName);

                            // 下載圖片
                            $encodedUrl = str_replace(' ', '%20', $imageUrl); // 將空格替換為%20
                            $encodedUrl = preg_replace_callback('/[^\x20-\x7f]/', function ($matches) {
                                return rawurlencode($matches[0]); // 將非ASCII字符進行URL編碼
                            }, $encodedUrl);
                            $ch = curl_init($encodedUrl);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 設定30秒超時
                            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允許重導向
                            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                'Upgrade-Insecure-Requests: 1',
                                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                                'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                                'sec-ch-ua-mobile: ?0',
                                'sec-ch-ua-platform: "Windows"'
                            ]);
                            $imageContent = curl_exec($ch);

                            // 檢查HTTP狀態碼
                            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                            if ($httpCode !== 200) {
                                throw new \Exception('下載圖片失敗: ' . $imageUrl . '，原因: HTTP狀態碼 ' . $httpCode);
                            }                // 檢查圖片內容
                            curl_close($ch);
                            if (empty($imageContent)) {
                                throw new \Exception('下載的圖片內容為空: ' . $imageUrl);
                            }
                            File::put($savePath, $imageContent);
                            $imageNames[] = $fileName;
                        }
                    } catch (\Exception $e) {
                        // 記錄錯誤但繼續執行
                        \Log::error('圖片下載失敗: ' . $e->getMessage());
                        continue;
                    }
                }
            }
            $inputs['img'] = implode(',', $imageNames);

            $original_announcement_link = $request->input('original_announcement_link');
            if ($original_announcement_link != "") {
                try {

                    $ext = pathinfo($original_announcement_link, PATHINFO_EXTENSION);
                    if ($ext == "pdf") {

                        $fileName = md5(end(explode("/", $original_announcement_link))) . "." . $ext;

                        $savePath = public_path('images/product/' . $fileName);
                        if (!\File::exists($savePath)) {
                            // 下載圖片
                            $encodedUrl = str_replace(' ', '%20', $original_announcement_link); // 將空格替換為%20
                            $encodedUrl = preg_replace_callback('/[^\x20-\x7f]/', function ($matches) {
                                return rawurlencode($matches[0]); // 將非ASCII字符進行URL編碼
                            }, $encodedUrl);
                            $ch = curl_init($encodedUrl);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 設定30秒超時
                            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允許重導向
                            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                'Upgrade-Insecure-Requests: 1',
                                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                                'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                                'sec-ch-ua-mobile: ?0',
                                'sec-ch-ua-platform: "Windows"'
                            ]);
                            $imageContent = curl_exec($ch);

                            // 檢查HTTP狀態碼
                            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                            if ($httpCode !== 200) {
                                throw new \Exception('下載圖片失敗: ' . $imageUrl . '，原因: HTTP狀態碼 ' . $httpCode);
                            }                // 檢查圖片內容
                            curl_close($ch);
                            if (empty($imageContent)) {
                                throw new \Exception('下載的圖片內容為空: ' . $imageUrl);
                            }

                            File::put($savePath, $imageContent);
                        }
                        $inputs['pdf'] = $fileName;
                    }
                } catch (\Exception $e) {
                    // 記錄錯誤但繼續執行
                    \Log::error('圖片下載失敗: ' . $e->getMessage());
                }
            }
            if ($rs != null) {
                $rs->update($inputs);
                $this->jsondata['resultmessage'] = '更新成功';
            } else {
                $rs = \App\Models\product::create($inputs);
                $this->jsondata['resultmessage'] = '新增成功';
            }



            if ($isbug == false) {
                \File::put(public_path('images/json/' . $rs->productid . ".json"), json_encode($request->all(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            }


            return $inputs;
        } catch (\Exception $e) {
            $errmsgs = [$e->getMessage(), json_encode($request->all(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)];

            $this->jsondata['resultmessage'] = $e->getMessage();
            \Log::error($errmsgs);
            throw $e;
        } finally {
        }
    }
}
