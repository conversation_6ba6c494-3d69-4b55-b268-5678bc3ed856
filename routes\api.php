<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::middleware('auth:api')->get('/user', function (Request $request) {
//     return $request->user();
// });
//Route::any('botbonnie/{id?}', 'api\botbonnieController@index')->defaults('id', '[0-9]+');
Route::group(['middleware' => 'ApiAuthAdmin', 'prefix' => 'admin/', 'namespace' => 'admin'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {
            return \App::Make('App\\Http\\Controllers\\api\\admin\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
Route::group(['middleware' => 'ApiFront', 'prefix' => 'member/', 'namespace' => 'member'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {
            return \App::Make('App\\Http\\Controllers\\api\\member\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
Route::group(['middleware' => 'ApiAuthMember', 'prefix' => 'membercenter/', 'namespace' => 'membercenter'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {
            return \App::Make('App\\Http\\Controllers\\api\\membercenter\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
Route::group(['middleware' => 'Front', 'prefix' => '/'], function () {
    Route::any(
        '{controller?}/{action?}/{arg?}',
        function ($controller = 'index', $action = 'index', $arg = null, Request $request) {
            if (str_contains($action, '.')) {
                return $action . ' - not found.';
            }

            return \App::Make('App\\Http\\Controllers\\api\\' . $controller . 'Controller')->callAction($action, array($arg ?: $request, $request));
        }
    );
});
