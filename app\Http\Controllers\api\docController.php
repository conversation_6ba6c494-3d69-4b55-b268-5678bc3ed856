<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;


//use Illuminate\Support\Facades\DB;
/***
"功能名稱":"swagger doc",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 17:27:28",
 ***/
class docController extends Controller {
    public function index(Request $request) {
        /*
        $openapi = \OpenApi\Generator::scan([

            base_path('app/Http/Controllers/api/Controller.php'),
            base_path('app/Http/Controllers/api/roomController.php'),
            base_path('app/Http/Controllers/api/schedulingController.php'),
            base_path(
                'app/Models'
            )
        ]);

*/
        $openapi = \OpenApi\Generator::scan([base_path('app/Http/Controllers/api'), base_path('app/Models')]);





        //$swagger = \OpenApi\scan($appDir, ['exclude' => $excludeDirs]);
        return response()->json($openapi, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
