<?php

namespace Tests\Feature\api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class productTest extends baseTest {
    use RefreshDatabase;
    use WithFaker;

    public function setUp(): void {
        parent::setUp();

        // 建立測試用的管理員使用者
        $this->adminuser = User::factory()->create([
            'name' => '測試管理員',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'api_token' => 'test-api-token-for-admin',
            'role' => 'admin'
        ]);

        // 建立測試用產品資料
        $this->product = Product::factory()->create([
            'productid' => 1001,
            'title' => '測試產品',
            'online' => 1,
            'city1title' => '台北市',
            'city2title' => '大安區',
            'totalupset' => 1000,
            'address' => '台北市大安區仁愛路',
            'number' => 'T123456789'
        ]);
    }

    /**
     * 測試取得產品列表
     */
    public function test_取得產品列表_index() {
        // 建立請求
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
            'Accept' => 'application/json'
        ])->get('/api/product');

        // 檢查回應
        $response->assertStatus(200);
        $this->checkJson($response);
        $response->assertJsonPath('status', 'success');
        $response->assertJsonStructure([
            'status',
            'data' => [
                '*' => [
                    'productid',
                    'title',
                    'online',
                    'city1title',
                    'city2title'
                ]
            ]
        ]);
    }

    /**
     * 測試取得單一產品資料
     */
    public function test_取得單一產品資料_show() {
        // 建立請求
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
            'Accept' => 'application/json'
        ])->get('/api/product/' . $this->product->productid);

        // 檢查回應
        $response->assertStatus(200);
        $this->checkJson($response);
        $response->assertJsonPath('data.productid', $this->product->productid);
        $response->assertJsonPath('data.title', $this->product->title);
    }

    /**
     * 測試建立新產品
     */
    public function test_建立新產品_store() {
        // 建立測試資料
        $productData = [
            'title' => '新測試產品',
            'online' => 1,
            'city1title' => '台中市',
            'city2title' => '西區',
            'address' => '台中市西區精誠路',
            'number' => 'T987654321',
            'totalupset' => 2000,
            'pattern' => '公寓',
            'houseage' => 15
        ];

        // 發送請求
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
            'Accept' => 'application/json'
        ])->post('/api/product', $productData);

        // 檢查回應
        $response->assertStatus(201);
        $this->checkJson($response);
        $response->assertJsonPath('status', 'success');

        // 檢查資料庫
        $this->assertDatabaseHas('product', [
            'title' => '新測試產品',
            'city1title' => '台中市',
            'city2title' => '西區'
        ]);
    }

    /**
     * 測試更新產品資料
     */
    public function test_更新產品資料_update() {
        // 更新資料
        $updateData = [
            'title' => '已更新的測試產品',
            'city1title' => '高雄市',
            'city2title' => '前金區'
        ];

        // 發送請求
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
            'Accept' => 'application/json'
        ])->put('/api/product/' . $this->product->productid, $updateData);

        // 檢查回應
        $response->assertStatus(200);
        $this->checkJson($response);
        $response->assertJsonPath('status', 'success');

        // 檢查資料庫
        $this->assertDatabaseHas('product', [
            'productid' => $this->product->productid,
            'title' => '已更新的測試產品',
            'city1title' => '高雄市',
            'city2title' => '前金區'
        ]);
    }

    /**
     * 測試刪除產品
     */
    public function test_刪除產品_destroy() {
        // 發送請求
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
            'Accept' => 'application/json'
        ])->delete('/api/product/' . $this->product->productid);

        // 檢查回應
        $response->assertStatus(200);
        $this->checkJson($response);
        $response->assertJsonPath('status', 'success');

        // 檢查資料庫，根據實際情況（軟刪除或硬刪除）
        // 如果是軟刪除
        $this->assertDatabaseHas('product', [
            'productid' => $this->product->productid,
            'deleted_at' => now()
        ]);

        // 如果是硬刪除
        // $this->assertDatabaseMissing('product', [
        //     'productid' => $this->product->productid
        // ]);
    }

    /**
     * 測試儲存產品資料
     */
    public function test_儲存產品資料_store() {
        $testData = [
            'body' => '<div>測試產品內容</div>',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
            'Accept' => 'application/json'
        ])->post('/api/product', $testData);

        $this->checkJson($response);

        $response->assertStatus(200)
            ->assertJsonPath('resultcode', 0)
            ->assertJsonStructure([
                'resultcode',
                'resultmessage',
                'productid'
            ]);

        // 確認檔案是否被儲存
        $this->assertTrue(
            file_exists(storage_path('product/' . $response->json('productid') . '.html'))
        );
    }
}
