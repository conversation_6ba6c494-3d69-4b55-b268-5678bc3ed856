<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use App\Libraries\UploadFile;

class ckeditorController extends adminController
{
    private $fieldnicknames;
    private $data;
    private $xmlDoc;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function upload(Request $request)
    {
        if ($request->hasFile('upload')) {
            $inputs = null;
            try {
                /*檔案上傳*/
                $upload = new UploadFile();
                $upload->request = $request;
                $upload->inputs = $inputs;
                $upload->folder = 'images/epost/';
                $upload->width = '800';
                $upload->height = '99900';
                //$upload->limitext = config('app.FileLimit');
                $inputs = $upload->execute();

                // Render HTML output
                @header('Content-type: text/html; charset=utf-8');
                $url = str_replace(asset('/'), '', '../../'.asset('images/epost/'.$inputs['upload']));
                $url = str_replace('../../', '/', $url);
                $CKEditorFuncNum = $request->input('CKEditorFuncNum');

                return response('<script type="text/javascript">window.parent.CKEDITOR.tools.callFunction('.$CKEditorFuncNum.", '".$url."', '');</script>");
            } catch (\Exception $e) {
                return response($e->getMessage());
            }
        }
    }
}
