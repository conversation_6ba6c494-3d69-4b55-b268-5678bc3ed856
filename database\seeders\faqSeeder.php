<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use DB;
use App\Repositories\boardRepository;

class faqSeeder extends Seeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        
        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->boardRepo->select()
        ->myWhere('kind|S', 'faq', "kind", 'Y')
        ->delete();
        
        $faker = \Faker\Factory::create('zh_TW');

        for ($i = 0; $i < 10; ++$i) {
            $data = [
                'kind' => 'faq',
                //'title' =>  $faker->sentence,
                'title' => '何謂兇宅-'.$i,
                'memo' => '一般問題',
                'body' => '根據內政部，第0970048190號函釋，「建築改良物曾發生兇殺或自殺致死之情況」，係指產權持有期間，其建築改良物之專有部分(包括主建物及附屬建物)，曾發生凶殺或自殺而死亡(不包括自然死亡)，及在專有部分有尋死行為致死之事實。<br><br>
                所以一般房屋俗稱凶宅意指，房屋曾經發生過「非自然死亡｣或是「曾發生凶殺案情況｣都認定為兇宅。',

                //'field1' => $faker->file('C:\AppServ\laravel\1\Pictures\banner.jpg', 'C:\AppServ\laravel\e\storage\app\public\images\news', false),
                //kindheadSeeder.php'field1' => $faker->image(public_path('images/place'), 800, 800, 'cats',false),

                'begindate' => date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month')))),
            ];
            $this->boardRepo->updateOrCreate($data);
        }
        
        

        

        // $this->call(UsersTableSeeder::class);
    }

}
