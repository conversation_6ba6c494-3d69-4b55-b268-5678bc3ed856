<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="myproduct",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="integer",description="自動編號", example="1"  )),
*         @OA\Schema( @OA\Property(property="member_id", type="integer",description="會員", example="1"  )),
*         @OA\Schema( @OA\Property(property="product_id", type="integer",description="物件", example="1"  )),
*         @OA\Schema( @OA\Property(property="created_at", type="string",description="建立日期", example="2021-08-12"  )),
*         @OA\Schema( @OA\Property(property="updated_at", type="string",description="編輯日期", example="2021-08-01"  )),

 *      }
 *)
 */
/*swagger api document end*/
class myproduct extends baseModel
{
    use HasFactory;
    public $table = 'myproduct';
    public $primaryKey = 'id';
    //public $incrementing = false;//取消自動編號
      //欄位必填
      public $rules = [
		'id' => 'required',

    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'member_id'=>['title'=>'會員','type'=>'int(11)'],//
'product_id'=>['title'=>'物件','type'=>'int(11)'],//
'created_at'=>['title'=>'建立日期','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯日期','type'=>'timestamp'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['member_id','product_id','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = ['created_at','updated_at'];

//   public function __construct() {
//         $this->fillable = parent::getfillables();//接受$request->all();
//         //$this->fillable =array_keys($this->fieldInfo);
//         parent::__construct();
//         parent::setFieldInfo($this->fieldInfo);
//   }
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
         static::deleting(function ($model) {


        });
        static::deleted(function ($model) {
        });
    }

}