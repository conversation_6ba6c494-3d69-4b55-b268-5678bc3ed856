@extends('layouts.master')
@section('css')

@stop

@section('js')
<script language=JavaScript>
    $(function() {
        //$("div[rel='today']:first").addClass('item active');
    });
</script>


<script type="application/ld+json">
    [{
            "@context": "http://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [{
                    "@type": "ListItem",
                    "position": 1,
                    "item": {
                        "@id": "{{ asset('/') }}",
                        "name": "首頁"
                    }
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "item": {
                        "@id": "{{ url('/') }}/news",
                        "name": "新聞"
                    }
                },
                {
                    "@type": "ListItem",
                    "position": 43,
                    "item": {
                        "@id": "{{ url('/') }}/news/show/{{$data['id']}}",
                        "name": "{{$data['title']}}"
                    }
                }
            ]
        },
        {
            "@context": "https://schema.org",
            "@type": "NewsArticle",
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": "{{ url('/') }}/news/show/{{$data['id']}}"
            },
            "headline": "{{$data['title']}}",
            "image": ["{{ url('/') }}/images/news/{{$data['field1']}}"],
            "datePublished": "{{PF::formatDate($data['created_at'])}}T08:00:00+08:00",
            "dateModified": "{{PF::formatDate($data['created_at'])}}T09:20:00+08:00",
            "author": {
                "@type": "Person",
                "name": "{{config('config.name')}}"
            },
            "publisher": {
                "@type": "Organization",
                "name": "{{config('config.name')}}",
                "logo": {
                    "@type": "ImageObject",
                    "url": "{{ url('/') }}/assets/images/logo.png"
                }
            },

            "description": "{{PF::noHtml($data['body'])}}",
            "keywords": "{{config('config.keyword')}}"
        }
    ]
</script>

@stop

@section('content')

<div class="px-3">
    <div class="theme-container">
        <div class="row">
            <nav aria-label="breadcrumb" class="col-xs-12">
                <ol class="breadcrumb end-xs">
                    <li class="breadcrumb-item"><a href="index.php">首頁</a></li>
                    <li class="breadcrumb-item active" aria-current="page">房訊新聞</li>
                </ol>
            </nav>
        </div>
    </div>
</div>

<div class="px-3">
    <div class="theme-container">
        <div class="my-5">
            <div class="column center-xs middle-xs text-center">
                <h1 class="uppercase">{{$data['title']}}</h1>
                <!--<p class="text-muted fw-500">Declaration of Intellectual Property Rights</p>-->
            </div>


            <div class="mdc-card p-5 my-3">


                {!!$data['body']!!}
                </p>



            </div>
            <div class="col-xs-12 w-100 py-3 text-center">

                <button onClick="javascript:window.history.go(-1);return false;" class="mdc-button mdc-button--raised mdc-ripple-upgraded" type="button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">回上頁</span>
                </button>
            </div>
        </div>
    </div>
</div>


@stop