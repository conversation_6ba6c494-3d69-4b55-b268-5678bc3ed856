<?php

use Illuminate\Database\Seeder;

class boardbanner<PERSON>eed<PERSON> extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $tablename = 'board';
        $kind='banner';
        DB::table($tablename)->where('kind',$kind)->delete();
        //DB::table($tablename)->truncate();

        $this->faker = \Faker\Factory::create('zh_TW');

        for ($i = 0; $i < 10; ++$i) {
            $data[] = [
                'kind' => $kind,
                //'title' =>  $faker->sentence,
                'title' =>  $this->faker->realText10(),
                'memo' => $this->faker->email,
                'field1' => $faker->file('C:\Users\<USER>\Pictures\1Sample\banner.jpg', 'C:\AppServ\laravel\laravel\storage\app\public\images\news', false),
                //'field1' => $faker->image("C:\AppServ\laravel\laravel\storage\app\public\images\".$kind, 800, 800, 'cats',false),
                
                'body' => $this->faker->realText(),
                //'body' => $faker->randomHtml(2, 3),
                
                //'body' => $faker->text(2000),
                
                'begindate' => date("Y-m-d",strtotime(date("Y-m-d",strtotime("-1 month")))),
                
            ];
        }

        DB::table($tablename)->insert($data);

        // $this->call(UsersTableSeeder::class);
    }
}
