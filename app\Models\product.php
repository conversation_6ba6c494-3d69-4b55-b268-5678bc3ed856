<?php

namespace App\Models;

use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="product",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="productid", type="integer",description="自動編號", example="1"  )),
*         @OA\Schema( @OA\Property(property="productkind", type="string",description="物件種類", example="test"  )),
*         @OA\Schema( @OA\Property(property="producttitle", type="string",description="標題", example="test"  )),
*         @OA\Schema( @OA\Property(property="number", type="string",description="編號", example="test"  )),
*         @OA\Schema( @OA\Property(property="court", type="string",description="法院", example="test"  )),
*         @OA\Schema( @OA\Property(property="city1title", type="string",description="縣市", example="test"  )),
*         @OA\Schema( @OA\Property(property="city2title", type="string",description="鄉鎮", example="test"  )),
*         @OA\Schema( @OA\Property(property="auctions", type="string",description="拍賣情況", example="test"  )),
*         @OA\Schema( @OA\Property(property="debtor", type="string",description="債務人", example="test"  )),
*         @OA\Schema( @OA\Property(property="proofreadingday", type="string",description="校對日", example="test"  )),
*         @OA\Schema( @OA\Property(property="tenderdate", type="string",description="投標日", example="2021-08-01"  )),
*         @OA\Schema( @OA\Property(property="beattime", type="string",description="拍次", example="test"  )),
*         @OA\Schema( @OA\Property(property="nocross_point", type="string",description="點交否", example="test"  )),
*         @OA\Schema( @OA\Property(property="mainlawnestablishment", type="string",description="主建坪", example="test"  )),
*         @OA\Schema( @OA\Property(property="attachedtolawnestablishment", type="string",description="附屬坪", example="test"  )),
*         @OA\Schema( @OA\Property(property="additionalping", type="string",description="增建坪", example="test"  )),
*         @OA\Schema( @OA\Property(property="postulateping", type="string",description="公設坪", example="test"  )),
*         @OA\Schema( @OA\Property(property="carping", type="string",description="車位坪", example=""  )),
*         @OA\Schema( @OA\Property(property="noticelawnestablishment", type="string",description="公告建坪", example="test"  )),
*         @OA\Schema( @OA\Property(property="stakeholdersfloor", type="string",description="持分地坪", example="test"  )),
*         @OA\Schema( @OA\Property(property="pingtotalnumberof", type="string",description="總坪數", example="test"  )),
*         @OA\Schema( @OA\Property(property="other_ping", type="string",description="其他坪數", example=""  )),
*         @OA\Schema( @OA\Property(property="land_total_ping", type="string",description="土地總坪", example=""  )),
*         @OA\Schema( @OA\Property(property="floorprice", type="string",description="坪單價", example="test"  )),
*         @OA\Schema( @OA\Property(property="aftermakingvalue_added", type="string",description="拍後增值", example="test"  )),
*         @OA\Schema( @OA\Property(property="currentvalues", type="string",description="公告現值", example="test"  )),
*         @OA\Schema( @OA\Property(property="totalupset", type="integer",description="總底價", example="1"  )),
*         @OA\Schema( @OA\Property(property="margin", type="string",description="保證金", example="test"  )),
*         @OA\Schema( @OA\Property(property="address", type="string",description="地址", example="test"  )),
*         @OA\Schema( @OA\Property(property="buildname", type="string",description="大樓名稱", example="test"  )),
*         @OA\Schema( @OA\Property(property="pattern", type="string",description="法拍屋種類", example="test"  )),
*         @OA\Schema( @OA\Property(property="houseage", type="integer",description="屋齡", example="1"  )),
*         @OA\Schema( @OA\Property(property="postulatemorethan", type="string",description="公設比", example="test"  )),
*         @OA\Schema( @OA\Property(property="architecture", type="string",description="結構建材", example="test"  )),
*         @OA\Schema( @OA\Property(property="storey", type="string",description="樓高", example="test"  )),
*         @OA\Schema( @OA\Property(property="landaddress", type="string",description="土地地址", example="test"  )),
*         @OA\Schema( @OA\Property(property="landprice", type="string",description="土地公現", example="test"  )),
*         @OA\Schema( @OA\Property(property="buildings", type="string",description="建物", example="test"  )),
*         @OA\Schema( @OA\Property(property="bidsrecords", type="string",description="流標記錄", example="test"  )),
*         @OA\Schema( @OA\Property(property="hisrightis", type="string",description="他項權利", example="test"  )),
*         @OA\Schema( @OA\Property(property="transcriptinformation", type="string",description="謄本資料", example="test"  )),
*         @OA\Schema( @OA\Property(property="courttranscript", type="string",description="法院筆錄", example="test"  )),
*         @OA\Schema( @OA\Property(property="landvalue1", type="string",description="土地增值金額1", example="test"  )),
*         @OA\Schema( @OA\Property(property="landvalue2", type="string",description="土地增值金額2", example="test"  )),
*         @OA\Schema( @OA\Property(property="createdate", type="string",description="建立時間", example="2021-08-01"  )),
*         @OA\Schema( @OA\Property(property="management", type="string",description="管理方式", example="test"  )),
*         @OA\Schema( @OA\Property(property="fees", type="string",description="費用", example="test"  )),
*         @OA\Schema( @OA\Property(property="parkingmode", type="string",description="停車方式", example="test"  )),
*         @OA\Schema( @OA\Property(property="transportfunction", type="string",description="交通機能", example="test"  )),
*         @OA\Schema( @OA\Property(property="schooldistrict", type="string",description="學區", example="test"  )),
*         @OA\Schema( @OA\Property(property="hits", type="integer",description="點率閱", example="1"  )),
*         @OA\Schema( @OA\Property(property="location", type="string",description="放置位置", example="test"  )),
*         @OA\Schema( @OA\Property(property="online", type="integer",description="上下架", example="1"  )),
*         @OA\Schema( @OA\Property(property="sealedhuman", type="string",description="查封人", example="test"  )),
*         @OA\Schema( @OA\Property(property="thenumberofbidders", type="string",description="投標人數", example="test"  )),
*         @OA\Schema( @OA\Property(property="bidwere", type="string",description="得標人", example="test"  )),
*         @OA\Schema( @OA\Property(property="thebidprice", type="string",description="得標價格", example="test"  )),
*         @OA\Schema( @OA\Property(property="increasetheamountof", type="string",description="加價金額", example="test"  )),
*         @OA\Schema( @OA\Property(property="facingthelaneis", type="string",description="面臨路寬", example="test"  )),
*         @OA\Schema( @OA\Property(property="memo", type="string",description="備註", example="test"  )),
*         @OA\Schema( @OA\Property(property="mrtland", type="string",description="捷運路線", example="test"  )),
*         @OA\Schema( @OA\Property(property="mrtstation", type="string",description="捷運站名", example="test"  )),
*         @OA\Schema( @OA\Property(property="locationadmin", type="string",description="放置位置2", example="test"  )),
*         @OA\Schema( @OA\Property(property="point", type="string",description="點數", example="test"  )),
*         @OA\Schema( @OA\Property(property="auctionssortnum", type="string",description="排序號碼", example="test"  )),
*         @OA\Schema( @OA\Property(property="lat", type="string",description="經度", example="test"  )),
*         @OA\Schema( @OA\Property(property="lng", type="string",description="緯度", example="test"  )),
*         @OA\Schema( @OA\Property(property="url", type="string",description="網址", example="https://www.yahoo.com.tw"  )),
*         @OA\Schema( @OA\Property(property="img", type="string",description="圖案", example="test"  )),
*         @OA\Schema( @OA\Property(property="pdf", type="string",description="pdf", example=""  )),
*         @OA\Schema( @OA\Property(property="user_code", type="string",description="用戶代碼", example=""  )),
*         @OA\Schema( @OA\Property(property="branch_code", type="string",description="分店代碼", example=""  )),
*         @OA\Schema( @OA\Property(property="commission_date_start", type="string",description="委託日期(起)", example=""  )),
*         @OA\Schema( @OA\Property(property="commission_date_end", type="string",description="委託日期(迄)", example=""  )),
*         @OA\Schema( @OA\Property(property="floor_start", type="string",description="樓層(起)", example=""  )),
*         @OA\Schema( @OA\Property(property="floor_end", type="string",description="樓層(迄)", example=""  )),
*         @OA\Schema( @OA\Property(property="room_count", type="integer",description="格局(房)", example=""  )),
*         @OA\Schema( @OA\Property(property="living_room_count", type="integer",description="格局(廳)", example=""  )),
*         @OA\Schema( @OA\Property(property="hall_count", type="integer",description="格局(室)", example=""  )),
*         @OA\Schema( @OA\Property(property="bathroom_count", type="integer",description="格局(衛)", example=""  )),
*         @OA\Schema( @OA\Property(property="balcony_count", type="integer",description="格局(陽台)", example=""  )),
*         @OA\Schema( @OA\Property(property="virtual_tour_video", type="string",description="線上賞屋影片", example=""  )),
*         @OA\Schema( @OA\Property(property="created_at", type="string",description="建立時間", example="2021-08-12"  )),
*         @OA\Schema( @OA\Property(property="updated_at", type="string",description="編輯時間", example="2021-08-01"  )),

 *      }
 *)
 */
/*swagger api document end*/
class product extends baseModel {
    use HasFactory;
    public $timestamps = true;
    public $table = 'product';
    public $primaryKey = 'productid';
    //public $incrementing = false;//取消自動編號
    //欄位必填
    public $rules = [
        'id' => 'required',

    ];

    public $fieldFiles = [
        'img' => 'public/images/product',
        'pdf' => 'public/images/product',
    ];
    public $fieldInfo = [
'productid'=>['title'=>'自動編號','type'=>'int(10) unsigned'],//
'productkind'=>['title'=>'物件種類','type'=>'varchar(50)'],// //法院法拍屋,銀行銀拍屋,行政執行署,國有財產局,台北市政府,新北市政府,台灣菸酒公會,台北餐飲公會,屋主自售,
'producttitle'=>['title'=>'標題','type'=>'varchar(500)'],//
'number'=>['title'=>'編號','type'=>'varchar(50)'],//
'court'=>['title'=>'法院','type'=>'varchar(50)'],//
'city1title'=>['title'=>'縣市','type'=>'varchar(50)'],//
'city2title'=>['title'=>'鄉鎮','type'=>'varchar(50)'],//
'auctions'=>['title'=>'拍賣情況','type'=>'varchar(50)'],// //待標[1],流標[2],應買[1.1],停拍[4],撤回[5],得標[6],承受[7],
'debtor'=>['title'=>'債務人','type'=>'varchar(50)'],//
'proofreadingday'=>['title'=>'校對日','type'=>'varchar(50)'],//
'tenderdate'=>['title'=>'投標日','type'=>'varchar(50)'],//
'beattime'=>['title'=>'拍次','type'=>'varchar(50)'],// //1拍,2拍,3拍,應買,特拍,
'nocross_point'=>['title'=>'點交否','type'=>'varchar(50)'],// //不點交,點交,部份點交,
'mainlawnestablishment'=>['title'=>'主建坪','type'=>'varchar(50)'],//
'attachedtolawnestablishment'=>['title'=>'附屬坪','type'=>'varchar(50)'],//
'additionalping'=>['title'=>'增建坪','type'=>'varchar(50)'],//
'postulateping'=>['title'=>'公設坪','type'=>'varchar(50)'],//
'carping'=>['title'=>'車位坪','type'=>'varchar(50)'],//
'noticelawnestablishment'=>['title'=>'公告建坪','type'=>'varchar(50)'],//
'stakeholdersfloor'=>['title'=>'持分地坪','type'=>'varchar(50)'],//
'pingtotalnumberof'=>['title'=>'總坪數','type'=>'double(7,2)'],//
'other_ping'=>['title'=>'其他坪數','type'=>'double(7,2)'],//
'land_total_ping'=>['title'=>'土地總坪','type'=>'double(7,2)'],//
'floorprice'=>['title'=>'坪單價','type'=>'varchar(50)'],//
'aftermakingvalue_added'=>['title'=>'拍後增值','type'=>'varchar(50)'],//
'currentvalues'=>['title'=>'公告現值','type'=>'varchar(50)'],//
'totalupset'=>['title'=>'總底價','type'=>'int(11)'],//
'margin'=>['title'=>'保證金','type'=>'varchar(50)'],//
'address'=>['title'=>'地址','type'=>'varchar(300)'],//
'buildname'=>['title'=>'大樓名稱','type'=>'varchar(500)'],//
'pattern'=>['title'=>'法拍屋種類','type'=>'varchar(50)'],//
'houseage'=>['title'=>'屋齡','type'=>'int(11)'],// //10內新屋[0~10],10~20年[10~20],20~30年[21~30],30~40年[31~30],40~50年[41~50],50年以上[51~99],
'postulatemorethan'=>['title'=>'公設比','type'=>'varchar(50)'],//
'architecture'=>['title'=>'結構建材','type'=>'varchar(500)'],//
'storey'=>['title'=>'樓高','type'=>'varchar(500)'],//
'landaddress'=>['title'=>'土地地址','type'=>'mediumtext'],//
'landprice'=>['title'=>'土地公現','type'=>'varchar(400)'],//
'buildings'=>['title'=>'建物','type'=>'mediumtext'],//
'bidsrecords'=>['title'=>'流標記錄','type'=>'mediumtext'],//
'hisrightis'=>['title'=>'他項權利','type'=>'varchar(500)'],//
'transcriptinformation'=>['title'=>'謄本資料','type'=>'mediumtext'],//
'courttranscript'=>['title'=>'法院筆錄','type'=>'mediumtext'],//
'landvalue1'=>['title'=>'土地增值金額1','type'=>'varchar(50)'],//
'landvalue2'=>['title'=>'土地增值金額2','type'=>'varchar(50)'],//
'createdate'=>['title'=>'建立時間','type'=>'datetime'],//
'management'=>['title'=>'管理方式','type'=>'varchar(500)'],//
'fees'=>['title'=>'費用','type'=>'varchar(500)'],//
'parkingmode'=>['title'=>'停車方式','type'=>'varchar(500)'],//
'transportfunction'=>['title'=>'交通機能','type'=>'varchar(500)'],//
'schooldistrict'=>['title'=>'學區','type'=>'varchar(500)'],//
'hits'=>['title'=>'點率閱','type'=>'int(11)'],//
'location'=>['title'=>'放置位置','type'=>'varchar(100)'],// //賀成交[deal],HOT[hot],推[push1],強推[push2],新增物件[new],黃金店面[storefront],整棟透天[allowed],辦公廠房 [factories],台北捷運[TaipeiMRT],機場快線[AirportExpressLine],台中捷運[TaichungMRT],高雄捷運[KaohsiungMRT],新品上市[new],首頁下方大圖[home1],首頁下方小圖[home2],
'online'=>['title'=>'上下架','type'=>'tinyint(4)'],// //上架[1],下架[0],
'sealedhuman'=>['title'=>'查封人','type'=>'varchar(100)'],//
'thenumberofbidders'=>['title'=>'投標人數','type'=>'varchar(100)'],//
'bidwere'=>['title'=>'得標人','type'=>'varchar(100)'],//
'thebidprice'=>['title'=>'得標價格','type'=>'varchar(100)'],//
'increasetheamountof'=>['title'=>'加價金額','type'=>'varchar(100)'],//
'facingthelaneis'=>['title'=>'面臨路寬','type'=>'varchar(100)'],//
'memo'=>['title'=>'備註','type'=>'mediumtext'],//
'mrtland'=>['title'=>'捷運路線','type'=>'varchar(500)'],//
'mrtstation'=>['title'=>'捷運站名','type'=>'varchar(500)'],//
'locationadmin'=>['title'=>'放置位置2','type'=>'varchar(500)'],//
'point'=>['title'=>'點數','type'=>'varchar(50)'],//
'auctionssortnum'=>['title'=>'排序號碼','type'=>'double(5,3)'],//
'lat'=>['title'=>'經度','type'=>'varchar(50)'],//
'lng'=>['title'=>'緯度','type'=>'varchar(50)'],//
'url'=>['title'=>'網址','type'=>'varchar(255)'],//
'img'=>['title'=>'圖案','type'=>'mediumtext'],//
'pdf'=>['title'=>'pdf','type'=>'varchar(50)'],//
'user_code'=>['title'=>'用戶代碼','type'=>'varchar(50)'],//
'branch_code'=>['title'=>'分店代碼','type'=>'varchar(50)'],//
'commission_date_start'=>['title'=>'委託日期(起)','type'=>'date'],//
'commission_date_end'=>['title'=>'委託日期(迄)','type'=>'date'],//
'floor_start'=>['title'=>'樓層(起)','type'=>'varchar(10)'],//
'floor_end'=>['title'=>'樓層(迄)','type'=>'varchar(10)'],//
'room_count'=>['title'=>'格局(房)','type'=>'int(11)'],//
'living_room_count'=>['title'=>'格局(廳)','type'=>'int(11)'],//
'hall_count'=>['title'=>'格局(室)','type'=>'int(11)'],//
'bathroom_count'=>['title'=>'格局(衛)','type'=>'int(11)'],//
'balcony_count'=>['title'=>'格局(陽台)','type'=>'int(11)'],//
'virtual_tour_video'=>['title'=>'線上賞屋影片','type'=>'varchar(255)'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['productkind','producttitle','number','court','city1title','city2title','auctions','debtor','proofreadingday','tenderdate','beattime','nocross_point','mainlawnestablishment','attachedtolawnestablishment','additionalping','postulateping','carping','noticelawnestablishment','stakeholdersfloor','pingtotalnumberof','other_ping','land_total_ping','floorprice','aftermakingvalue_added','currentvalues','totalupset','margin','address','buildname','pattern','houseage','postulatemorethan','architecture','storey','landaddress','landprice','buildings','bidsrecords','hisrightis','transcriptinformation','courttranscript','landvalue1','landvalue2','createdate','management','fees','parkingmode','transportfunction','schooldistrict','hits','location','online','sealedhuman','thenumberofbidders','bidwere','thebidprice','increasetheamountof','facingthelaneis','memo','mrtland','mrtstation','locationadmin','point','auctionssortnum','lat','lng','url','img','pdf','user_code','branch_code','commission_date_start','commission_date_end','floor_start','floor_end','room_count','living_room_count','hall_count','bathroom_count','balcony_count','virtual_tour_video','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded = [];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = ['createdate','commission_date_start','commission_date_end','created_at','updated_at'];

    //   public function __construct() {
    //         $this->fillable = parent::getfillables();//接受$request->all();
    //         //$this->fillable =array_keys($this->fieldInfo);
    //         parent::__construct();
    //         parent::setFieldInfo($this->fieldInfo);
    //   }
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot() {
        parent::boot();
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
            $path = public_path('images/product/' . $model->productid);
            if (\File::exists($path)) {
                \File::delete($path);
            }
            parent::delFieldFile($model);
        });
        static::deleted(function ($model) {
        });
    }
}
