<?php

namespace App\Http\Middleware;

use Closure;
use PF;
use DB;

class ApiAuthAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $token = $request->bearerToken();
        $rows = DB::table('adminuser');
        $rows->selectRaw('*');
        $rows->myWhere('api_token|S', $token, 'token', 'Y');

        $rows = $rows->take(1);

        //PF::dbSqlPrint($rows);
        $rows = $rows->get();
        if ($rows->count() > 0) {
            $rs = $rows->first();

            $rs = get_object_vars($rs);
            $request->merge(['member' => $rs]);
        } else {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = 'token false';

            return response()->json($jsondata, 401);
        }

        return $next($request);
    }
}
