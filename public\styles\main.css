@import url(reset.css);
@import url(header.css);
@import url(footer.css);
@import url(index.css);
@import url(housing.css);
@import url(findings.css);
@import url(teamService.css);
@import url(swiper-bundle.css);
@import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css);
@import url(bootstrap.min.css);


.test{width: 82.8%;margin: 0 auto}
@media (max-width: 1199px){.test{width: 100%}}

/*----------------------------------*\
  # big title                     
\*----------------------------------*/
.title {   
  background: url('../images/bg_title.webp'),linear-gradient(to top right, #ffaa33 20%, #ff8000 60%, #ffaa33 100%);
  width: 100%;    
  height: 9rem;     
  text-align: center;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% auto;  
}
.title h1 span { 
  line-height: 9rem;
  letter-spacing: .18rem;
  color: var(--main); color: #FFF; 
  font-family: var(--mingle);
  font-weight: 800;
  font-size: var(--topic);
  display: inline-block;
  animation: slideLeft 1.5s forwards;
  opacity: 0;
  transition-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(200px);
  } 
  to {
    opacity: 1;
    transform: translateX(0%);
  }
}
@media (max-width: 1199px){
  .title { 
    width: 100%;  
    margin: 0 auto;
    height: 5em;     
  }
}
@media (max-width: 1199px){
  .title {       
    height: 4.5rem;
  }
  .title h1 span { 
    line-height: 4.5rem;    
  }
}

/*----------------------------------*\
  # main                   
\*----------------------------------*/
.page__wrap {  
  padding-top: calc(var(--header-h) + .1rem);
  padding-left: .1rem;
  padding-right: .1rem;
  /*padding-left: 6%;
  padding-right: 6%;*/
  min-height: calc(var(--vh, .45vh) * 100);
}
@media (max-width: 1199px){
  .page__wrap { 
    padding-top: var(--header-h);   
    padding-left: 0px;
    padding-right: 0px;    
  }
}
