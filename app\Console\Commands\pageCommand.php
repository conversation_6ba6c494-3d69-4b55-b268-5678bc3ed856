<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class pageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'page:create {--folder=} {--name=} {--repositoryname=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '建立頁面';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        try {
            //$isadmin = $this->ask('is admin(Y,N):');

            //$ismodel = $this->ask('is create model(Y,N):');
            $folder = 'C:/AppServ/laravel/1/sample/controller/';
            $tofolder = base_path('app/Http/Controllers/');
            $foldername = '';
            //$foldername = $this->ask('目錄名稱(管理者目錄可用admin)');
            $foldername = $this->option('folder');
            if ('/' == $foldername) {
                $foldername = '';
            }

            if (\Str::contains($foldername, ['api'])) {
                $folder .= 'api/';
            }
            if ('admin' == $foldername) {
                // $namecontroller = 'admin/'.$name;
                $foldername = 'admin';
                $folder = $folder.'admin';
                $tofolder = $tofolder.'admin/';
            } else {
                // if ('' != $foldername) {

                //     $foldernameview = $foldername.'.';
                // }
                //$foldername .= '/';
                $tofolder .= $foldername;
                // $namecontroller = $name;
            }
            $foldernamecontroller = '';
            if ('' != $foldername) {
                $foldernamecontroller = '\\'.$foldername;
            }
            $foldernamecontroller = str_replace('/', '', $foldernamecontroller);
            $foldernameview = $foldername.'.';

            $name = $this->option('name');
            $repositoryname = $this->option('repositoryname');
            if ('' == $name) {
                throw new \Exception('name is null');
            }
            //$repositoryname = $this->ask('資料表 名稱:');

            if ('Y' == $repositoryname || '' == $repositoryname) {
                $repositoryname = $name;
            }

            $msg = '';
            $msg .= 'tofolder:'.$foldername;
            $msg .= "\n";
            $tofile = $tofolder.'/'.$name.'Controller.php';

            $msg .= "create controller\n";
            if (false == \File::exists($tofile)) {
                if ('N' == $repositoryname) {
                    $ebody = \File::get($folder.'norepoController.php');
                } else {
                    $ebody = \File::get($folder.'Controller.php');
                }
                $ebody = str_replace('[+name+]', $name, $ebody);
                $ebody = str_replace('[+repositoryname+]', $repositoryname, $ebody);

                $ebody = str_replace('[+foldername+]', $foldername, $ebody);
                $ebody = str_replace('[+foldernameview+]', $foldernameview, $ebody);
                $ebody = str_replace('[+foldernamecontroller+]', $foldernamecontroller, $ebody);

                if ('Y' == $isadmin) {
                    $ebody = str_replace('[+admin+]', 'admin', $ebody);
                } else {
                    $ebody = str_replace('[+admin+]', '', $ebody);
                }
                \File::put($tofile, $ebody);
                //setupController.php$success = \File::copy($folder.'Controller.php', $tofile);
            }
            if (false == \Str::contains($foldername, ['api'])) {
                $msg .= "create index edit view\n";
                $array = explode(',', 'index,edit');
                $folder = resource_path('views/'.$foldername.'/'.$name);
                if (false == \File::isDirectory($folder)) {
                    \File::makeDirectory($folder, 0777, true, true);
                } else {
                    $msg .= $folder.' foler exist\n';
                }
                for ($i = 0; $i < count($array); ++$i) {
                    $folder = 'C:/AppServ/laravel/1/sample/views/'.$array[$i];
                    if ('Y' == $isadmin) {
                        $folder = 'C:/AppServ/laravel/1/sample/views/admin/'.$array[$i];
                    }
                    $tofile = resource_path('views/'.$foldername.'/'.$name.'/'.$array[$i].'.blade.php');
                    $msg .= $tofile."\n";
                    if (false == \File::exists($tofile)) {
                        $success = \File::copy($folder.'.blade.php', $tofile);
                        $msg .= $array[$i].('1' == $success ? 'OK' : $success);
                    } else {
                        $msg .= $tofile.' > '.$array[$i].' exist ';
                    }
                    $msg .= "\n";
                }
                $this->call('view:clear');
            }
            // if ('Y' == $ismodel) {
            //     $params = [
            //      'name' => $name,
            //     ];
            //     \Artisan::call('make:model', $params);

            //     $msg .= \Artisan::output();
            // }
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }
        echo $msg;
        // echo $this->argument('name');
    }
}
