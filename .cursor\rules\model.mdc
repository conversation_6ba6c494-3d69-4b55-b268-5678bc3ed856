---
description:
globs:
alwaysApply: false
---

# 任務

- 產生model關聯寫法與關聯範例程式
- 刪除關聯資料表資料


# 來源

- 請依照 `/dbspec.md` 資料表定義檔案

# 程式要求

- 修正只限引入的model檔案，不要新增其他檔案

- 在static::deleted 函式中找出關聯資料表，只限關聯資料表欄位同{table_id}

如:
```
\DB::delete('delete from {table}  where {table_id} = ?', array($model->{id}));
```

- 宣告其他model名請用小寫，並在下方補上範例程式，如果有多層table請一併補上
如:
```
public function {table}() {
        return $this->{xx}('App\Models\{table}', '{id}');
}

/*
$rows = \App\Models\{table}::selectRaw('*')
            ->with(['{table1}' => function ($query) {
                $query->selectRaw('{xx}')// 一定要有父層的KEY
                    ->with(['{table2}' => function ($query) {
                        $query->selectRaw('id,title'); // 一定要有父層的KEY
                    }]);
}]);
$rows = $rows->get();
*/














```