<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
//use Illuminate\Support\Facades\DB;

use App\Repositories\city2Repository;

class dependentdropdownController extends Controller {
    private $data;


    private $classtRepo;
    private $city2Repo;




    /**
     *建構子.
     */
    public function __construct(city2Repository $city2Repo) {
        //$this->limit="xx";
        parent::__construct();

        $this->city2Repo = $city2Repo;
    }

    public function city2(Request $request) {
        $datas['output'] = [];

        $rows = $this->city2Repo->selectRaw("left(concat(replace(city2title,'　',''),'　'),3) as id,left(concat(replace(city2title,'　',''),'　'),3) as name");

        $rows->myWhere('city1title|S', $request->input('depdrop_all_params')['city1title'], "city1title", 'N');
        $rows->myWhere('city1title|S', $request->input('depdrop_all_params')['acity1title'], "city1title", 'N');


        $rows = $rows->orderByRaw('city2title');
        $rows = $rows->get();

        $datas['output'] = $rows;
        $datas['selected'] = $request->input('selected');

        return response()->json($datas, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
