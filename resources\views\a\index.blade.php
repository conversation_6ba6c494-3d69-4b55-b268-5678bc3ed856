@extends('layouts.raw')
@section('css')
@endsection

@section('js')
    <script language=JavaScript>
        function oForm_onsubmit(form) {
            return true;
        }
    </script>
@endsection



@section('content')
    <div class="container-fluid p-2">
        <div class="form-group row">

            <div class="col-md-10">
                <button type="button" class="btn btn-warning" data-url="{{ request()->url() }}/clear" data-toggle="modal"
                    data-title="clear" data-width="800" data-height="600">
                    Clear</button>
                <button type="button" class="btn btn-warning" data-url="{{ request()->url() }}/migrate" data-toggle="modal"
                    data-title="migrate" data-width="800" data-height="600">
                    Migrate</button>


                <button type="button" class="btn btn-warning" onclick="window.open('{{ request()->url() }}/test')">
                    Test</button>
                <button type="button" class="btn btn-primary" data-url="{{ url('/') }}/a/seeder?seeder=devinit"
                    data-toggle="modal" data-title="專案初始" data-width="800" data-height="600">
                    專案初始</button>
                {{-- <button type="button" class="btn btn-warning" data-url="{{request()->url()}}/sql" data-toggle="modal" data-title="sql command log" data-width="" data-height="">
            Sql Log</button> --}}

                <div class="btn-group btn-sm">


                    <button type="button" class="btn btn-primary ">SQL匯入</button>
                    <button type="button" class="btn btn-primary  dropdown-toggle dropdown-toggle-split"
                        data-toggle="dropdown" aria-expanded="false">
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="{{ request()->url() }}/sqlfile" data-toggle="modal"
                            data-title="執行檔案.sql(部份)" data-width="" data-height="">部份download</a>
                        <a class="dropdown-item" href="{{ request()->url() }}/mysqlfile" data-toggle="modal"
                            data-title="執行檔案.sql(download)" data-width="" data-height="">全部download</a>
                        <a class="dropdown-item"
                            href="{{ request()->url() }}/mysqlfile?directory=storage\backup\" data-toggle="modal"
                            data-title="執行檔案.sql(storage\backup)" data-width="" data-height="">全部app\backup</a>

                    </div>
                </div>
                @{{ clear }}


            </div>
            <div class="col-md-2">

                <button type="button" onclick="window.location.reload();" class="btn btn-warning">重新整理</button>
            </div>
        </div>

        <hr>


        <script language=JavaScript>
            function oForm_onsubmit(form) {
                if (PF_FormMultiAll(form) == false) {
                    return false
                };
                //PF_FieldDisabled(form)
                return true;
            }
        </script>


        <!--novalidate-->
        <form name="oForm1" id="oForm1" method="post" language="javascript"
            action="{{ request()->url() }}/migrate_refresh_excute" target="modal-iframe"
            onsubmit="return oForm_onsubmit(this);">
            <div class="form-group row">
                <div class="col-md-2">
                    資料表:
                </div>
                <div class="col-md-6">
                    <select name="table_name" class="form-control" required @change="onChange($event)">
                        <option value="">無</option>
                        @foreach ($data['tabletitles'] as $key => $item)
                            <option value="{{ $key }}">{{ $key }}{{ $item == $key ? '' : '-' . $item }}
                            </option>
                        @endforeach
                    </select>


                </div>

                <div class="col-md-2">
                    <button class="btn btn-primary" data-url="iframe" data-toggle="modal" data-title="refresh"
                        data-width="800" data-height="900">refresh</button>
                </div>
                <div class="col-md-2">

                    <button type="button" class="btn btn-primary"
                        onclick="window.open('https://allennb.com.tw:442/sample/public/index?database={{ $data['database'] }}&table_name='+document.forms['oForm1'].elements['table_name'].value,'aa')">
                        產生器</button>
                </div>
            </div>
            <template v-if='tableinfo.fields.length>0'>




                <div class="form-group row">
                    <div class="col-md-12">
                        <span @click="onAi($event.target.textContent)">@{{ tableinfo.title }}</span> -
                        <span @click="onAi($event.target.textContent)">@{{ tableinfo.name }}</span>

                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-md-12">

                        <template v-for="(rs2, index) in tableinfo.fields">

                            <button type="button" class="btn btn-light m-1">
                                <span @click="onAi($event.target.textContent)">@{{ rs2.Field }}</span>
                                <span v-if="rs2.Comment!=''"
                                    @click="onAi($event.target.textContent)">@{{ rs2.Comment }}</span>

                            </button>

                        </template>


                    </div>


                </div>
                <div class="form-group row">

                    <div class="col-md-12">

                        <textarea v-model="ai" class="form-control" cols="37" rows="5" style='width:90%;height:200px'></textarea>


                    </div>


                </div>
                <div class="form-group row">
                    <div class="col-md-12">
                        <a href="javascript:;" @click="onAi1($event.target.textContent)">MCP</a>
                        <a href="javascript:;" @click="onAi1($event.target.textContent)">如果</a>
                        <a href="javascript:;" @click="onAi1($event.target.textContent)">大於</a>
                        <a href="javascript:;" @click="onAi1($event.target.textContent)">小於</a>
                        <a href="javascript:;" @click="onAi1($event.target.textContent)">now()</a>
                    </div>
                </div>
            </template>
        </form>





        <h3>新增資料表</h3>
        <form name="oForm4" id="oForm4" method="post" language="javascript"
            action="{{ request()->url() }}/createtablename" target="modal-iframe"
            onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->

            <div class="form-group row">

                <div class="col-md-8">
                    <input type="text" class="form-control" name="newtablename" id="newtablename" value=""
                        required requiredclass="required[1,TEXT]" placeholder="" />
                </div>

                <div class="col-md-4">

                    <button class="btn btn-primary" data-url="#" data-toggle="modal" data-title="新增資料表"
                        data-width="500" data-height="500">
                        確定</button>

                </div>


            </div>
        </form>
        <hr>

        <script language="JavaScript">
            $(function() {
                var ul = $("ul[role='tablist']");
                $("div[class^=tab-pane]").each(function(i) {
                    var title = $(this).attr('id');
                    var active = ($(this).attr('class') == 'tab-pane fade show active' || i == 0) ? 'active' :
                        '';
                    ul.append('<li class="nav-item"><a class="nav-link ' + active + '" id="name-' + title +
                        '"  data-toggle="tab" role="tab"  href="#' + title + '">' + title + '</a></li>');

                });
            });
        </script>

        <ul class="nav nav-tabs" id="nav-tab" role="tablist">

        </ul>


        <div class="tab-content">

            <div id="新增檔案" class="tab-pane fade p-2 show active">
                <h3>新增檔案</h3>
                <form name="oForm3" id="oForm3" method="post" language="javascript"
                    action="{{ request()->url() }}/createfile" target="modal-iframe"
                    onsubmit="return oForm_onsubmit(this);">
                    <div class="form-group row">
                        <div class="col-md-1">
                            資料表:
                        </div>
                        <div class="col-md-2">
                            <select name="table_name" class="form-control">
                                <option value="">無</option>
                                @foreach ($data['tabletitles'] as $key => $item)
                                    <option value="{{ $key }}">
                                        {{ $key }}{{ $item == $key ? '' : '-' . $item }}</option>
                                @endforeach
                            </select>


                        </div>
                        <div class="col-md-1">
                            目錄:
                        </div>
                        <div class="col-md-2">
                            <input type="text" class="form-control" name="foldername" value=""
                                requiredclass="required[0,TEXT]" placeholder="" />
                        </div>

                        <div class="col-md-1">
                            檔名:
                        </div>
                        <div class="col-md-2">
                            <input type="text" class="form-control" name="filename" value="" required required
                                requiredclass="required[1,TEXT]" placeholder="" />
                        </div>

                        <div class="col-md-3">
                            <button class="btn btn-warning" data-url="#" data-toggle="modal" data-title="新增檔案"
                                data-width="800" data-height="900">
                                確定</button>
                        </div>
                    </div>
                </form>

            </div>
            <div id="seeder" class="tab-pane fade p-2">
                <h3>seeder</h3>
                <form name="oForm2" id="oForm2" method="post" language="javascript"
                    action="{{ request()->url() }}/seeder" target="modal-iframe"
                    onsubmit="return oForm_onsubmit(this);">
                    <div class="form-group row">
                        <label class="col-md-2">
                            seeder檔案:
                        </label>
                        <div class="col-md-6">
                            <select name="seeder" class="form-control">
                                <option value="">無</option>
                                @foreach ($data['seeders'] as $key => $item)
                                    <option value="{{ $item }}">{{ $item }}</option>
                                @endforeach
                            </select>


                        </div>
                        <div class="col-md-4">

                            <button class="btn btn-info" data-url="iframe" data-toggle="modal" data-title="seeder"
                                data-width="800" data-height="900">
                                seeder</button>
                        </div>

                    </div>
                </form>
            </div>
            <div id="新增資料" class="tab-pane fade p-2">
                <h3>新增資料</h3>
                <form name="oForm4" id="oForm4" method="post" language="javascript"
                    action="{{ request()->url() }}/createdata" target="modal-iframe"
                    onsubmit="return oForm_onsubmit(this);">
                    <div class="form-group row">
                        <div class="col-md-1">
                            資料表:
                        </div>
                        <div class="col-md-2">
                            <select name="table_name" class="form-control" required>
                                <option value="">無</option>
                                @foreach ($data['tabletitles'] as $key => $item)
                                    <option value="{{ $key }}">
                                        {{ $key }}{{ $item == $key ? '' : '-' . $item }}</option>
                                @endforeach
                            </select>


                        </div>
                        <div class="col-md-1">
                            ID:
                        </div>
                        <div class="col-md-2">
                            <input type="text" class="form-control" name="id" value="10000" required
                                placeholder="" />
                        </div>
                        <div class="col-md-1">
                            筆數:
                        </div>
                        <div class="col-md-2">
                            <input type="text" class="form-control" name="count" value="10" required
                                placeholder="" />
                        </div>


                        <div class="col-md-3">
                            <button class="btn btn-warning" data-url="iframe" data-toggle="modal" data-title="新增資料"
                                data-width="800" data-height="900">
                                確定</button>
                        </div>
                    </div>
                </form>
            </div>

            <div id="樣版" class="tab-pane fade p-2">

                <div class="container-fluid p-1">
                    <div class="row">

                        <button class="btn btn-info m-1" @click="onFormatSetValue('[name],')">Sql cmd</button>
                        <button class="btn btn-info m-1"
                            @click="onFormatSetValue('$inputs[\'[name]\']=\$this->data[\'[name]\'];//[title][memo][br]')">inputs=data</button>
                        <button class="btn btn-info m-1"
                            @click="onFormatSetValue('$inputs[\'[name]\']=\$[name];//[title][memo][br]')">inputs=name</button>
                        <button class="btn btn-info m-1" @click="onFormatSetValue('\'[name]\'=>\'[title]\',[br]')">'name'
                            =>
                            'title'(excel)</button>
                    </div>
                    <div class="row">
                        <input type="text" class="form-control" name="format" v-model="inputs.format"
                            ref="myBtn" value='' @keyup="onFormat(event)" @click="onFormat(event)" />
                    </div>
                </div>
                <div class="container-fluid p-1">
                    <textarea name="fieldsmemos" v-model="inputs.fieldsmemos" class="form-control" cols="37" rows="5"
                        style='width:90%;height:200px'></textarea>

                </div>
                <hr>
                <div v-for="(item, index) in tableinfo.fields" class="container-fluid p-1">
                    <div class="row">
                        <div class="col-md-3">
                            <input type="text" class="form-control" :id="item.name" @click="onCopy($event)"
                                :value="item.name" :title="item.title" placeholder="" />
                        </div>
                        <div class="col-md-9">
                            <input type="text" class="form-control" :id="item.name + '_title'" @click="onCopy($event)"
                                :value="item.title + ((typeof(item.xmlmemo) != 'undefined') ? ' ; ' + item.xmlmemo : '')"
                                placeholder="" />

                        </div>
                    </div>


                </div>

            </div>
        </div>



    </div>

    <script type="text/javascript" src="{{ asset('Scripts/vue.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('Scripts/PJSFunVue.js') }}"></script>
    <script language=JavaScript>
        Vue.config.devtools = true;
        var app = new Vue({
            el: '#app',
            data: {
                datas: [],
                inputs: {
                    format: '',
                },
                tableinfo: {
                    fields: [],
                },
                clear: '',
                ai: '',
                // innersql: '',

            },

            methods: {
                loadData: function() {
                    console.log(["1", 1]);
                    let vm = this;
                    $.post("{{ url('a/clear') }}", {}).done(function(obj) {
                        vm.clear = obj;
                    });
                },
                onCopy(event) {
                    copy(event.target.id);
                },
                onFormatSetValue(value) {
                    console.log(value);
                    vm = this;
                    this.inputs.format = value;
                    const elem = this.$refs.myBtn;
                    elem.click();
                },
                onAi(value) {
                    this.ai += '`' + value + '` ';
                },
                onAi1(value) {
                    this.ai += value + ' ';
                },
                onFormat(event) {
                    vm = this;
                    //console.log(event);
                    var formateStr = vm.inputs.format;
                    var fieldsmemos = "";

                    vm.fields.forEach(rs => {

                        fieldsmemo = formateStr.replaceAll("[name]", rs.name);
                        fieldsmemo = fieldsmemo.replaceAll("[title]", rs.title);
                        fieldsmemo = fieldsmemo.replaceAll("[br]", '\r\n');
                        memo = "";
                        if (typeof(rs.xmlmemo) != "undefined") {
                            memo = rs.xmlmemo
                        }
                        fieldsmemo = fieldsmemo.replaceAll("[memo]", memo);
                        fieldsmemos += fieldsmemo;
                    });
                    //console.log(fieldsmemos);
                    vm.inputs.fieldsmemos = fieldsmemos;
                    //console.log(formateStr);
                },
                onChange(event) {
                    vm = this;

                    var dict = {
                        url: 'https://allennb.com.tw:442/sample/public/api/getfields',
                        data: {
                            'table_name': event.target.value,
                            'dbname': "{{ $data['database'] }}"

                        },
                        //data:jQuery("#oForm").serialize(),
                        //data:JSON.stringify(this.inputs),
                        dataType: 'json',
                        noloading: false,
                        //debug:true,
                    }
                    PF_ajax(dict).done(function(obj) {
                        if (obj.resultcode == 0) {

                            vm.tableinfo = obj.alltables[0];
                            //vm.innersql = obj.data.innersql;
                            // const elem = vm.$refs.myBtn;
                            // elem.click();
                            //vm.$refs.inputs['format'].$el.click()


                        } else {
                            _alert(obj.resultmessage);
                        }
                    }).fail(function(resp) {

                        _alert(resp.statusText);
                    });

                }

            },
            created: function() {
                this.loadData();
            },

        });
    </script>
@endsection
