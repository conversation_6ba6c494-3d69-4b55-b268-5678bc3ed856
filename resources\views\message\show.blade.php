@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')
<div class="px-3">
    <div class="theme-container">
        <div class="row">
            
            <nav aria-label="breadcrumb" class="col-xs-12">
                <ol class="breadcrumb end-xs">
                    <li class="breadcrumb-item"><a href="{{ url('/') }}/">首頁</a></li>
                    <li class="breadcrumb-item active" aria-current="page">留言板</li>
                </ol>
            </nav>
        </div>
    </div>
</div>

<div class="px-3">
    <div class="theme-container">
        <div class="my-5">
            <div class="column center-xs middle-xs text-center">
                <h1 class="uppercase">{{$data['title']}}</h1>
                <!--<p class="text-muted fw-500">Declaration of Intellectual Property Rights</p>-->
            </div>
            <div class="mdc-card p-5 my-3">
                <div class="row">
                    <div class="col-md-3">類別：
                        {{PF::xmlSearch($data['xmldoc'],"//參數設定檔/留言類別/KIND/傳回值","資料",$data['kind'])}}

                    </div>
                    <div class="col-md-3">時間：
                        {{PF::formatDate($data['created_at'])}}

                    </div>
                    <div class="col-md-3">姓名：
                        {{$data['name']}}
                    </div>
                    <div class="col-md-3">性別：
                        {{$data['sex']}}
                    </div>
                </div>
            </div>

            <div class="mdc-card p-5 my-3">

            <p>   
                {{PF::vbcrlf($data['body'])}}
            </p>

                <div class="mdc-card p-5 mb-3 bg-light">
                    <h2 class="uppercase text-center fw-500 mb-2">回覆留言</h2>
                    <div class="row pb-3 p-relative">
                        <div class="divider"></div>
                    </div>
                    <p>
                        {{PF::vbcrlf($data['rebody'])}}
                        
                    </p>
                </div>

            </div>
             
            <div class="col-xs-12 w-100 py-3 text-center">
                <button class="mdc-button mdc-button--raised mdc-ripple-upgraded" 
                onClick="javascript:window.history.go(-1);return false;" 
                type="button">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">回上頁</span>
                </button>
            </div>
        </div>
    </div>
</div>
@endsection