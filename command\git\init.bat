chcp 65001
cd..
cd..
set "current_dir=%~dp0"
echo Current directory: %current_dir%

REM 使用逗號作為分隔符進行切割
@echo off
for /f "tokens=1,2,3,4,5 delims=\" %%a in ("%current_dir%") do (
  set value1=%%a
  set value2=%%b
  set value3=%%c
  set value4=%%d
  set value5=%%e
)
set name=%value4%
if %name% == 1 (
    set name=%value5%
)
echo "專案:"%name%


rmdir /s /q ".git"
REM 在本地存儲庫中初始化 Git：打開終端（命令提示字元），導航到您的項目目錄，然後運行以下命令來初始化本地存儲庫：
git init

REM 將所有文件添加到 Git 中：運行以下命令將所有文件添加到 Git 中：
git add .

REM 在引號中，輸入一個描述您這次提交的簡短消息。
git commit -m "Initial commit"

REM 請將 <遠端存儲庫URL> 替換為您的遠端存儲庫 URL。
git remote add origin https://github.com/allencase/%name%.git
REM 如果您使用的是其他分支而不是 master，請將 master 替換為您的分支名稱。
git push -u origin master
cmd /k