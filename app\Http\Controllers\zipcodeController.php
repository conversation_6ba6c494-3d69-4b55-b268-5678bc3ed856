<?php

namespace App\Http\Controllers;

use ZipArchive;
use Illuminate\Http\Request;

class zipcodeController extends Controller
{
    private $data;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
    }

    public function index(Request $request)
    {
        if ($request->input('code') != md5(date('md'))) {
            abort(403);
        }
        $directories = [
        'app',
        'config',
        'database',
        'public/Scripts',
        'public/css',
        'resources',
        'routes',
        'storage/app',
        'storage/views',
        'storage/spec',
        ];

        $fileName = str_replace('/', '_', base_path('/'));
        $fileName = str_replace('\\', '_', $fileName);
        $fileName = str_replace(':', '_', $fileName);
        $fileName = str_replace('__', '_', $fileName);
        $zipFilePath = storage_path('app/'.$fileName.'.zip');

        if (\File::exists($zipFilePath)) {
            \File::delete($zipFilePath);
        }

        $this->zipDirectories($directories, $zipFilePath);
        //  if ('' != $request->input('isdownload')) {
        $headers = [
            'Content-Type' => 'application/zip',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
            'Content-Disposition' => 'attachment; filename='.basename($zipFilePath),
        ];
        $response = response()->download($zipFilePath, basename($zipFilePath), $headers);

        return $response;
        // } else {
        // return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        // // }
    }

    public function zipDirectories($directories, $zipFilePath)
    {
        // 創建壓縮文件
        $zip = new ZipArchive();
        if (true !== $zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE)) {
            // 壓縮文件打開失敗
            return false;
        }

        // 遍歷目錄列表
        foreach ($directories as $directory) {
            if (is_dir(base_path($directory))) {
                // 取得目錄名稱
                //$directoryName = basename($directory);
                //\PF::printr(['directoryName', $directory, $directoryName]);

                // 添加目錄及其內容到壓縮文件
                $this->addDirectoryToZip(base_path($directory), $directory, $zip);
            }
        }

        // 關閉壓縮文件
        $zip->close();

        return true;
    }

    public function addDirectoryToZip($directory, $directoryName, $zip)
    {
        $zip->addEmptyDir($directoryName);
        $files = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory), \RecursiveIteratorIterator::LEAVES_ONLY);

        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = substr($filePath, strlen($directory) + 1);

                $zip->addFile($filePath, $directoryName.'/'.$relativePath);
            }
        }
    }
}
