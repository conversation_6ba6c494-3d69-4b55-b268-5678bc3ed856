<?php

namespace App\Http\Controllers\admin;

use PF, PT, Auth;
use Exception, Config, DB;
use App\Models\failed_jobs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\admin\Controller;
use App\Http\Controllers\Controller\admin;

/***
"功能名稱":"排程失敗記錄",
"資料表":"failed_jobs",
"建立時間":"2024-03-11 23:09:22 ",
 ***/
class failed_jobsController extends Controller {

    private $data;
    private $xmlDoc;

    /**
     *TODO 建構子
     */
    public function __construct() {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');


        // FIXME 導覽列


        $this->data['nav'] = PT::nav($this->data['xmldoc'], "failed_jobs", $this->data['nav']);
        // FIXME 共用的hidden變數
        $this->data['hiddens'] = [];
        $this->data['displaynames'] = failed_jobs::getFieldTitleArray();
    }


    /**
     * TODO 資料列表
     *
     * @return  \Illuminate\Http\Response
     */
    public function index(Request $request) {


        //FIXME 定義那些欄位可以搜尋
        $fieldsearchname = [
            '' => '請選擇',
            'failed_jobs.uuid' => $this->data['displaynames']['uuid'], //UUID
            'failed_jobs.connection' => $this->data['displaynames']['connection'], //
            'failed_jobs.queue' => $this->data['displaynames']['queue'], //
            'failed_jobs.payload' => $this->data['displaynames']['payload'], //
            'failed_jobs.exception' => $this->data['displaynames']['exception'], //
        ];
        //FIXME 定義那些日期欄位可以搜尋
        $fieldsearchdatename = [
            'failed_jobs.failed_at' => $this->data['displaynames']['failed_at'], //
        ];
        $this->data['fieldsearchname'] = $fieldsearchname;
        $this->data['fieldsearchdatename'] = $fieldsearchdatename;
        $rows = $this->getRows($request);

        //$rows = $rows->take(10);

        //$rows = $rows->get();
        $rows = $rows->paginate(10);
        // 顯示sqlcmd
        $this->data['rows'] = $rows;
        $this->data['jobscount'] = \DB::table('jobs')->count();;


        return view('admin.failed_jobs.index', [
            'data' => $this->data,
        ]);
    }
    /**
     * 資料Rows
     *
     * @return  \Illuminate\Http\Response
     */
    public function getRows($request) {

        $rows = DB::table("failed_jobs")->selectRaw("failed_jobs.*");

        //依條件搜尋資料的SQL語法
        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere($request->input('searchdatename') . '|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere($request->input('searchdatename') . '|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');

        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), $request->input('sorttype') == "desc"  ? $request->input('sorttype') : "asc");
        } else {
            $rows->orderByRaw('failed_jobs.id desc');
        }
        //PF::dbSqlPrint($rows);
        return $rows;
    }


    /**
     * TODO 資料新增編輯儲存
     * @return  \Illuminate\Http\Response
     */
    public function work(Request $request) {



        $exitCode = \Artisan::call('queue:restart');
        $exitCode = \Artisan::call('queue:work', ['--stop-when-empty']);


        return back()->with('js', "_toast('手動重啟成功',1000,'success')");
    }
    /**
     * TODO 資料新增編輯儲存
     * @return  \Illuminate\Http\Response
     */
    public function store(Request $request) {


        foreach ($request->input('uuid') as $k => $uuid) {


            $exitCode = \Artisan::call('queue:retry', ['id' => $uuid]);
        };

        return back()->with('js', "_toast('重啟成功',1000,'success')");
    }
}
