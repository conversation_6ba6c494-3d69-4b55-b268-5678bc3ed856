@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
function oForm_onsubmit(form)
{
      if (PF_FormMultiAll(form) == false) {return false;}
           PF_FieldDisabled(form);//將全部button Disabled
      return true;
}
</SCRIPT>
<div class="card">
    <div class="card-body">
        <!--// TODO : 前端資料填寫-->
        <form name="oForm" class="container-fluid p-1" id="oForm" method="post" language="javascript"
            action="{{ request()->url() }}/../store" onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->


            <!-- <div class="form-group row">
                <label class="col-md-2">公司名稱：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <input type="text" class="form-control" name="company" title="公司名稱" requiredclass="required[0,TEXT]"
                        value="{{$data['company']}}" size="40" />
                </div>
            </div> -->


            <div class="form-group row">
                <label class="col-md-2">姓名：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">
                    <input type="text" class="form-control" name="name" title="姓名" required
                        requiredclass="required[1,TEXT]" value="{{$data['name']}}" size="40" />
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">電子信箱：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">
                    <input type="email" class="form-control" name="email" title="電子信箱" required
                        requiredclass="required[1,EMAIL]" value="{{$data['email']}}" size="40"
                        placeholder="ex <EMAIL>" />
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">電話：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <input type="tel" class="form-control" name="tel" title="電話" requiredclass="required[0,TEL]"
                        value="{{$data['tel']}}" maxlength="25" placeholder="ex xxx-xxxxxxxx#ext" />
                </div>
            </div>


            <!-- <div class="form-group row">
                <label class="col-md-2">分機：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <input type="text" class="form-control" name="ext" title="分機" requiredclass="required[0,TEXT]"
                        value="{{$data['ext']}}" size="40" />
                </div>
            </div> -->


            <!-- <div class="form-group row">
                <label class="col-md-2">性別：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    {{
	Form::myUIXml([		
		'xmldoc'=>$data['xmldoc'],		
		'type'=>"Radio",		
		'title'=>"性別",
		'node'=>"//參數設定檔/性別/KIND",
		'name'=>"sex",        
		'value'=>$data['sex'],
		'linecount'=>5,
		'requiredclass'=>"required[0,TEXT]",
		
		//'onClick'=>"if (this.value==1){\$('.species').show();}else{\$('.species').hide();}",
		
	])
}}


                </div>
            </div> -->


            <div class="form-group row">
                <label class="col-md-2">行動電話：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <input type="tel" class="form-control" name="mobile" title="行動電話" requiredclass="required[0,MOBILE]"
                        value="{{$data['mobile']}}" maxlength="25" placeholder="ex 0912345678" pattern="[0-9]{10}" />
                </div>
            </div>


            <!-- <div class="form-group row">
                <label class="col-md-2">標題：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">
                    <input type="text" class="form-control" name="title" title="標題" required
                        requiredclass="required[1,TEXT]" value="{{$data['title']}}" size="40" />
                </div>
            </div> -->


            <div class="form-group row">
                <label class="col-md-2">留言內容：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <textarea name="memo" cols="50" rows="5" class="form-control" title="留言內容"
                        requiredclass="required[0,TEXT]">{{$data['memo']}}</textarea>
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">回覆標題：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">
                    <input type="text" class="form-control" name="retitle" title="回覆標題" requiredclass="required[1,TEXT]"
                    required
                        value="{{$data['retitle']}}" size="40" />
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">回覆訊息：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">
                    <textarea name="rebody" cols="50" rows="5" class="form-control" title="回覆訊息"
                    required requiredclass="required[1,TEXT]">{{$data['rebody']}}</textarea>
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">回覆日期：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    {{PF::formatDate($data['redate'])}}

                </div>
            </div>



            <div align="center">
                <button type="submit" class="btn btn-success">回覆</button>
                <button type="reset" class="btn btn-secondary">取消</button>
                <!--<button type="submit" class="btn btn-primary" onclick="oForm.edit.value='';">複製一筆</button>-->
                <button type="reset" class="btn btn-secondary" onClick="javascript:window.history.go(-1)">回上一頁</button>

            </div>
            @include('admin.layouts.hidden', ['method'=>'EditoForm'])
            {!! Form::hidden("edit", $data['edit']) !!}
            {!! Form::hidden("gobackurl", $data['gobackurl'] ) !!}
        </form>
    </div>
</div>

@stop