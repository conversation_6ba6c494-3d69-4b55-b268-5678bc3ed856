<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
//use Illuminate\Foundation\Testing\DatabaseTransactions;

use Tests\TestCase;


class baseTest extends TestCase {
    //use DatabaseTransactions;
    use RefreshDatabase;

    public $myFaker;
    public $faker;
    public $placekind;
    public function setUp(): void {
        /*
     * @runInSeparateProcess
     */
        parent::setUp();

        $this->faker = \Faker\Factory::create('zh_TW');
        $this->myFaker = new \Database\Seeders\myFaker();
        $this->placekind = \App\Models\placekind::factory()->create([]);

        \Cache::forget('placekind');
        $response = $this->get('/');
    }
    public function tearDown(): void {
        parent::tearDown();
    }
}
