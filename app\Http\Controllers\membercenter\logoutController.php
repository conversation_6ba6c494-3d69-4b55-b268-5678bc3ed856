<?php

namespace App\Http\Controllers\membercenter;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class logoutController extends Controller {
    private $data;

    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {

        \Session::forget('myproducts');
        \Auth::guard('member')->logout();

        return redirect('/');
    }
}
