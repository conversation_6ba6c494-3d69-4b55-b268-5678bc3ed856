<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use DB;
use App\Repositories\messageRepository;

//command : php artisan db:seed --class=messageSeeder

class messageSeeder extends Seeder
{
    private $dbRepo;

    public function __construct(messageRepository $messageRepo)
    {
        
        $this->dbRepo = $messageRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        
        $this->dbRepo->select()
        //->delete();//全刪
        ->myWhere('kind|S', 'place', "del", 'Y')//條件式
        ->delete();
        
        
        $faker = \Faker\Factory::create('zh_TW');

        for ($i = 0; $i < 10; ++$i) {
            $data = [
    'kind'=>$faker->randomElement([1,2,3]),   //留言類別
    'name'=>$faker->firstNameMale. $faker->lastName,   //姓名
    'email'=>'test-'.rand(1,100).'@gmail.com',   //電子信箱
    'sex'=>$faker->randomElement(['先生','女士']),   //性別
    'title'=>$faker->realText(10),   //標題
    'body'=>$faker->realText(100),   //留言內容
    'updated_at'=>date("Y-m-d H:i:s"),   //異動時間
    'rebody'=>$faker->realText(100),   //回覆訊息

            ];
            $this->dbRepo->updateOrCreate($data);
        }

    }

}