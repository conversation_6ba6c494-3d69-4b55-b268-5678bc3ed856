/*findings (Search results)*/
/*----------------------------------*\
  # findings-subtitle                    
\*----------------------------------*/
.findings-subtitle { 
  position: relative;
  font-size: 1.8rem;
  font-weight: bold;  
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 2.7rem 0; margin: 2.7rem 0 0;
}
.findings-subtitle::before,
.findings-subtitle::after {
  content: "";
  flex: 1;
  height: 1px;
  background: #ccc;
  margin: 0 1rem;
}
/*----------------------------------*\
  # Best Value Property                     
\*----------------------------------*/
.findings_wrap { 
  width: 82.2%; width: 72%;
  margin: 1.35rem auto 2.7rem;  
}
.findings-grid {
  white-space: nowrap;
  width: 100%;
  padding: .63rem 0 0 0; 
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: .9rem;
}
.findings-grid .heart-icon {
  position: absolute;
  bottom: .45rem;
  right: .63rem;
  width: 1.2rem;
  height: 1.2rem;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  transition: transform 0.2s;
  z-index: 2;
  background-image: url('../images/heart_not.svg'); /* 預設為空心 */
}
.findings-grid .heart-icon.active {
  background-image: url('../images/heart_ok.svg'); /* 點擊後變實心 */
}
.findings-grid .imgBox {
  position: relative;
  width:100%;  
  padding-bottom:72%;
}
.findings-grid .imgBox img {  
  position:absolute;  
  top:0;  
  left:0;
  display:block;
  width:100%;
  height:100%; 
  object-fit:cover;
  object-position:50% 50%;  
}
a.property-card,
a.property-card:visited,
a.property-card:hover  {
  text-decoration:none; 
  color:inherit;  
}
.findings-grid .property-card {
  background: #fff;
  border-radius: .54rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);box-shadow: var(--box-sd);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.findings-grid .property-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
.findings-grid .imgBox {
  overflow: hidden;
}
.findings-grid .property-card img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease, filter 0.5s ease;
}
.findings-grid .property-card:hover img {
  transform: scale(1.05);
  filter: brightness(1.1) drop-shadow(0 0 10px rgba(255, 200, 0, 0.6));
}
.findings-grid .property-info {
  padding: .63rem;
}
.findings-grid .property-info h2 {
  font-size: .86rem;
  font-family: var(--base);
  line-height: 1.11;
  margin-bottom: .36rem;
  white-space: nowrap; /* 不換行 */
  overflow: hidden; /* 超出部分隱藏 */
  text-overflow: ellipsis; /* 顯示省略號 */  
  padding-bottom: .54rem;
  border-bottom: 1px dotted var(--mate);
}
.findings-grid .info-row { 
  display: flex;
  align-items: flex-end;
  justify-content: space-between; 
  color: var(--strong); 
  font-family: var(--base); 

  --fontSize:.72rem;
} 
.findings-grid .info-row p {
  margin-bottom: 0; 
  font-size: .72rem;
  font-size: var(--fontSize);
  font-weight: 400;  
}

.findings-grid .info-row p:nth-child(2){  
  width: calc(var(--fontSize) * 6.5);  
}
.findings-grid .info-row span {
  padding-left: .18rem;
}

.findings-grid .info-row span.num,
.findings-grid .info-row span.price {
  font-family: var(--mix);
  font-size: 120%;
  font-weight: 500;
  padding-right: .09rem;
}
.findings-grid .info-row span.price {
  color: var(--mate);
  font-weight: 700;
}
.findings-grid .property-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease, filter 0.5s ease;
}

@media (max-width: 1199px) {
  .findings_wrap {
    width: 90%;
    margin: 1.35rem auto 2.7rem;  
  }  
  .findings-grid {    
    grid-template-columns: repeat(3, 1fr);    
  }
}
@media (max-width: 767px) {
  .findings-grid {    
    grid-template-columns: repeat(2, 1fr);    
  }
}
@media (max-width: 415px) {
  .findings-grid {    
    grid-template-columns: repeat(1, 1fr);    
  }
}


/* --------- 切換按鈕*/
.view-toggle-wrap {
  width: 72%;
  margin: 0 auto .9rem;
  text-align: right;
}
.view-toggle-wrap i {
  font-size: 1.2rem;font-size: 1.53rem;
  color: #999;
  cursor: pointer;
  margin-left: 0.6rem;
  transition: color 0.3s ease, transform 0.2s ease;
}
.view-toggle-wrap i:hover {
  color: var(--main); 
  transform: scale(1.2);
}
.view-toggle-wrap i.active {
  color: var(--main); 
}

/*findings-table*/
.findings-table-view { 
  width: 100%;
  overflow-x: auto;
  font-size: 0.8rem;
}
.table-header,
.table-row {
  display: grid;  
  grid-template-columns: 6rem 4rem 6rem 1.5fr 6rem 6rem 5.4rem 4.5rem;  
  padding: .45rem;  
  border-bottom: 1px solid #ddd;
  align-items: center;
  min-width: 700px;
}
.table-header {
  font-weight: bold;
  background: var(--main);
  color: #fff;
  position: sticky;
  top: 0;
  z-index: 1;
}
/* 偶數行灰底，單數行白底 */
.table-row:nth-child(odd) {
  background-color: #ffffff;
}
.table-row:nth-child(even) {
  background-color: #f2f2f2;
}
.table-row div:nth-child(6) {
  color: var(--mate);   
}
/* 地址欄位：最多2行 */
.table-row div:nth-child(4) {
  padding-right:.72rem ;
  /*overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;    
  -webkit-box-orient: vertical;
  white-space: normal;
  line-height: 1.3;
  word-break: break-word;*/
}
.table-row div:nth-child(4) {
  white-space: nowrap;  
  overflow: hidden;        
  text-overflow: ellipsis;
}
/* 其他欄位都單行顯示，不換行 */
.table-row div:not(:nth-child(4)) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.table-row img {
  width: 90px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}
.findings-table-view a:nth-child(odd) .table-row {
  background-color: #ffffff;
}
.findings-table-view a:nth-child(even) .table-row {
  background-color: #f2f2f2;
}
.table-row:hover {
  background-color: #fff8ef;
  transition: background 0.3s;
}
.findings-table-view a:nth-child(odd) .table-row:hover {
  background-color: #ffe3c2;
  transition: background 0.3s;
  cursor: pointer;
}
.findings-table-view a:nth-child(even) .table-row:hover {
  background-color: #ffe3c2;
  transition: background 0.3s;
  cursor: pointer;
}

@media (max-width: 1199px) {
  .findings-table-view {
    overflow-x: scroll;
  }
  .table-header,
  .table-row {
    grid-template-columns: 6rem 2.7rem 4.5rem 1fr 4.5rem 4.5rem 4.2rem 2.7rem;
    font-size: 0.72rem;
    padding: .45rem .18rem;
  }
  .table-row div {
    /*white-space: nowrap;  
    overflow: hidden;        
    text-overflow: ellipsis;*/
  }
  /*.table-row div:nth-child(6) {
    color: var(--mate);  
  }*/
  .table-row div:nth-child(4) {
    /*white-space: nowrap;  
    overflow: hidden;        
    text-overflow: ellipsis;*/
  }
}
