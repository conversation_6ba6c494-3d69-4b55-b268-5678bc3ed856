<?php

namespace App\Repositories;

use App\Models\message;
use DB;
use PF;

class messageRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(message $model)
    {
        $this->model = $model;
    }
    /**
     *TODO 透過DB TABLE回傳ROWS
     */
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);
        return $rows;
    }
  

    public function create($inputs)
    {
        $this->model::create($inputs);
    }

    public function update(array $inputs, $id, $attribute = 'id')
    {
        $this->model::find($id)->update($inputs);
    }

    public function deleteIds($ids)
    {
        
        $this->model->whereIn($this->model->primaryKey, explode(",",$ids))->delete();
       
        // $rows = $this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // //PF::dbSqlPrint($rows);
        // $rows->get()->each(function ($row) {
        //     $row->delete();
        // });
        
    }
}
