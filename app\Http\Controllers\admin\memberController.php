<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

use App\Repositories\memberRepository;
use Exception, Config, DB;
use PF, PT, Auth;

/***
"功能名稱":"會員",
"資料表":"member",
"建立時間":"2023-07-11 10:17:12 ",
 ***/
//include('memberRepository.php');
//include('member.php')
class memberController extends Controller {

    private $data;
    private $xmlDoc;
    private $memberRepo;
    /**
     *TODO 建構子
     */
    public function __construct(memberRepository $memberRepo) {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->memberRepo = $memberRepo;

        // FIXME 導覽列



        $this->data['nav'] = PT::nav($this->data['xmldoc'], "member", $this->data['nav']);
        // FIXME 共用的hidden變數
        $this->data['hiddens'] = [];
        $this->data['displaynames'] = $this->memberRepo->getFieldTitleArray();
    }


    /**
     * TODO 資料列表
     *
     * @return  \Illuminate\Http\Response
     */
    public function index(Request $request) {


        //FIXME 定義那些欄位可以搜尋
        $fieldsearchname = [
            '' => '請選擇',
            'member.mobile' => $this->data['displaynames']['mobile'], //行動電話
            'member.name' => $this->data['displaynames']['name'], //姓名
            'member.sex' => $this->data['displaynames']['sex'], //性別 [:先生 ; :女士 ; ]
            'member.tel' => $this->data['displaynames']['tel'], //市話
            'member.email' => $this->data['displaynames']['email'], //電子信箱
            'member.patterns' => $this->data['displaynames']['patterns'], //法拍屋種類
            'member.totalupsets' => $this->data['displaynames']['totalupsets'], //總底價
            'member.city2s' => $this->data['displaynames']['city2s'], //區域
            'member.item_request' => $this->data['displaynames']['item_request'], //細項需求
            'member.myproducts' => $this->data['displaynames']['myproducts'], //我的最愛
            'member.isepaper' => $this->data['displaynames']['isepaper'], //是否訂閱電子報
            'member.memo' => $this->data['displaynames']['memo'], //備註
            'member.remember_token' => $this->data['displaynames']['remember_token'], //remember_token
            'member.lastlogin_ip' => $this->data['displaynames']['lastlogin_ip'], //登入IP
            'member.online' => $this->data['displaynames']['online'], //會員狀態 [1:正常 ; 2:停權 ; ]
            'member.adminuser_id' => $this->data['displaynames']['adminuser_id'], //編輯人員
            'member.adminuser_account' => $this->data['displaynames']['adminuser_account'], //編輯人員
        ];
        //FIXME 定義那些日期欄位可以搜尋
        $fieldsearchdatename = [
            'member.lastlogin_dt' => $this->data['displaynames']['lastlogin_dt'], //最後登入日期
            'member.created_at' => $this->data['displaynames']['created_at'], //建立時間
        ];
        $this->data['fieldsearchname'] = $fieldsearchname;
        $this->data['fieldsearchdatename'] = $fieldsearchdatename;
        $rows = $this->getRows($request);

        //$rows = $rows->take(10);

        //$rows = $rows->get();
        $rows = $rows->paginate(10);
        // 顯示sqlcmd
        $this->data['rows'] = $rows;

        return view('admin.member.index', [
            'data' => $this->data,
        ]);
    }
    /**
     * 資料Rows
     *
     * @return  \Illuminate\Http\Response
     */
    public function getRows($request) {

        $rows = $this->memberRepo->selectRaw("member.*");
        //依條件搜尋資料的SQL語法
        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');

        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), $request->input('sorttype') == "desc"  ? $request->input('sorttype') : "asc");
        } else {
            $rows->orderByRaw('member.id desc');
        }
        //PF::dbSqlPrint($rows);
        return $rows;
    }

    /**
     * TODO 資料建立
     *
     * @return  \Illuminate\Http\Response
     */
    public function create(Request $request) {
        $this->data['created_at'] = date('Y-m-d');

        return view('admin.member.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * TODO 資料編輯顯示
     * @return  \Illuminate\Http\Response
     */
    public function edit(Request $request) {

        $edit = $request->input('edit');

        $validators = null;
        $validators['edit'] = 'required';

        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }

        $rows = $this->memberRepo->select('member.*');
        $rows->where('member.id', '=', $edit);
        $rows = $rows->take(1);
        //  PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException("no data");
        }


        return view('admin.member.edit', [
            'data' => $this->data,
        ]);
    }


    /**
     * TODO 資料新增編輯儲存
     * @return  \Illuminate\Http\Response
     */
    public function store(Request $request) {

        $edit = $request->input('edit');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        $validators['mobile'] = ['required', 'regex:/(0)[0-9]/', 'not_regex:/[a-z]/', 'min:10']; //行動電話
        $validators['name'] = ['required']; //姓名
        $validators['password'] = ['required', 'confirmed', 'min:8']; //密碼
        $validators['email'] = ['required', 'email']; //電子信箱
        //$this->data['displaynames']['xx'] = 'xx';
        if ($validators != null) {
            $validator = \Validator::make($this->data, $validators);
            $validator->setAttributeNames($this->data['displaynames']);
            if ($validator->fails()) {
                return view('errors.validatorback')->withErrors($validator);
            }
        }
        //PF::printr($request->all());exit();
        $inputs = $request->all();
        //$inputs['mobile']=$this->data['mobile'];//行動電話-
        //$inputs['name']=$this->data['name'];//姓名-
        //$inputs['lineid']=$this->data['lineid'];//LINE ID-
        if ($this->data['password'] != "") {
            $inputs['password'] = \Hash::make($this->data['password']); //密碼-
        } else {
            unset($inputs['password']); //密碼-
        }
        //$inputs['sex']=$this->data['sex'];//性別-[:先生 ; :女士 ; ]
        //$inputs['tel']=$this->data['tel'];//市話-
        //$inputs['email']=$this->data['email'];//電子信箱-
        //$inputs['patterns']=$this->data['patterns'];//法拍屋種類-
        //$inputs['totalupsets']=$this->data['totalupsets'];//總底價-
        //$inputs['city2s']=$this->data['city2s'];//區域-
        //$inputs['item_request']=$this->data['item_request'];//細項需求-
        //$inputs['myproducts']=$this->data['myproducts'];//我的最愛-
        $inputs['isepaper'] = $this->data['isepaper']; //是否訂閱電子報-
        //$inputs['memo']=$this->data['memo'];//備註-
        //$inputs['api_token']=$this->data['api_token'];//api_token-
        //$inputs['remember_token']=$this->data['remember_token'];//remember_token-
        $inputs['lastlogin_ip'] = \Request::ip(); //登入IP-
        //$inputs['lastlogin_dt']=$this->data['lastlogin_dt'];//最後登入日期-
        //$inputs['logincount']=$this->data['logincount'];//登入次數-
        //$inputs['online']=$this->data['online'];//會員狀態-[1:正常 ; 2:停權 ; ]
        $inputs['adminuser_id'] = \Auth::guard('admin')->id(); //編輯人員-
        //$inputs['adminuser_account']=$this->data['adminuser_account'];//編輯人員-



        if ('' == $edit) {
            //PF::printr($inputs);exit();
            $edit = $this->memberRepo->create($inputs)->id;
            $this->data['alert'] = '新增成功';
        } else {
            //PF::printr($inputs); exit();
            $this->memberRepo->update($inputs, $edit);
            $this->data['alert'] = '更新成功';
        }

        //return back()->with('js', "_toast('更新成功',3000)");
        return view('admin.layouts.postsubmit',  [
            'data' => $this->data,
        ]);
    }
    /**
     * TODO 資料刪除
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request) {
        $rows = member::selectRaw('member.*');
        $rows->myWhere('id|ININT', $this->data['del'], 'del', 'Y');
        //$rows->delete();
        $rows->chunk(200, function ($rows) {
            foreach ($rows as $rs) {
                $rs->delete();
            }
        });

        return view('admin.layouts.postsubmit', [
            'data' => $this->data,
        ]);
    }

    /**
     * TODO 資料Excel匯入畫面
     *
     * @return \Illuminate\Http\Response
     */
    public function excelimport(Request $request) {
        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'member', "EXCEL匯入");
        return view('member.excelimport', [
            'data' => $this->data,
        ]);
    }
    /**
     * TODO 資料Excel匯入處理
     *
     * @return \Illuminate\Http\Response
     */
    public function excelimportstore(Request $request) {

        $import = new \App\Imports\memberImport($this->data);
        \Excel::import($import, $request->file('file1'));
        $msg = "";
        if ($import->results != null) {
            foreach ($import->results as $k => $v) {
                $msg .= ($k + 1) . " . " . $v . "<hr>";
            };
        }
        return response($msg);
    }
    /**
     * 資料Excel匯入範本.
     *
     * @return \Illuminate\Http\Response
     */
    public function excelsample(Request $request) {
        $rows = $this->getRows($request);
        $exports = new \App\Exports\memberExport($this->data, $this->data['displaynames'], $rows, 1);
        return \Excel::download($exports, '會員_範本.xlsx');
    }
    /**
     * TODO 資料Excel匯出.
     *
     * @return \Illuminate\Http\Response
     */
    public function excelexport(Request $request) {

        $rows = $this->getRows($request);
        $exports = new \App\Exports\memberExport($this->data, $this->data['displaynames'], $rows);
        /*
        foreach ($exports->rows as $rs) {
            PF::printr($rs->id);
        }
        */
        return \Excel::download($exports, '會員.xlsx');
    }
}
