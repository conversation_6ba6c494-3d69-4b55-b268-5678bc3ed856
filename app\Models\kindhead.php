<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="kindhead",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="kindheadid", type="integer",description="自動編號", example=""  )),
*         @OA\Schema( @OA\Property(property="kindheadtitle", type="string",description="種類標題", example=""  )),
*         @OA\Schema( @OA\Property(property="kindheadsort", type="string",description="排序號碼", example=""  )),
*         @OA\Schema( @OA\Property(property="kindheadbody", type="string",description="本文", example=""  )),
*         @OA\Schema( @OA\Property(property="kindmaincount", type="integer",description="第二層數量", example=""  )),
*         @OA\Schema( @OA\Property(property="productcount", type="integer",description="產品數量", example=""  )),
*         @OA\Schema( @OA\Property(property="alg", type="string",description="語系", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class kindhead extends baseModel
{
    use HasFactory;
    
    public $tabletitle = '';
    public $table = 'kindhead';
    public $primaryKey = 'kindheadid';
    //public $incrementing = false;//取消自動編號
      //欄位必填
      public $rules = [
		'id' => 'required',

    ];
    public $fieldInfo = [
'kindheadid'=>['title'=>'自動編號','type'=>'int(11)'],//
'kindheadtitle'=>['title'=>'種類標題','type'=>'varchar(500)'],//
'kindheadsort'=>['title'=>'排序號碼','type'=>'float(5,2)'],//
'kindheadbody'=>['title'=>'本文','type'=>'varchar(4000)'],//
'kindmaincount'=>['title'=>'第二層數量','type'=>'int(11)'],//
'productcount'=>['title'=>'產品數量','type'=>'int(11)'],//
'alg'=>['title'=>'語系','type'=>'varchar(50)'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['kindheadtitle','kindheadsort','kindheadbody','kindmaincount','productcount','alg']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    
    protected $dates = [];
  
//   public function __construct() {
//         $this->fillable = parent::getfillables();//接受$request->all();
//         //$this->fillable =array_keys($this->fieldInfo);
//         parent::__construct();        
//         parent::setFieldInfo($this->fieldInfo);
//   }         
    
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          
        });
         static::deleting(function ($model) {

          
        });
        static::deleted(function ($model) {
            
        });
    }	

}