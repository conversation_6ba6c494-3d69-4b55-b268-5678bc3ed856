@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
function oForm_onsubmit(form)
{
      if (PF_FormMultiAll(form) == false) {return false;}
           PF_FieldDisabled(form);//將全部button Disabled
      return true;
}
</SCRIPT>
<div class="card">
    <div class="card-body">
        <!--// TODO : 前端資料填寫-->
        <form name="oForm" class="container-fluid p-1" id="oForm" method="post" language="javascript"
            action="{{ request()->url() }}/../store" onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->


            <div class="form-group row">
                <label class="col-md-2">留言類別：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">
                    {{
	Form::myUIXml([		
		'xmldoc'=>$data['xmldoc'],		
		'type'=>"Select",		
		'title'=>"留言類別",
		'node'=>"//參數設定檔/留言類別/KIND",
		'name'=>"kind",        
		'value'=>$data['kind'],
		'linecount'=>5,
		'requiredclass'=>"required[1,TEXT]",
		
		//'onClick'=>"if (this.value==1){\$('.species').show();}else{\$('.species').hide();}",
		
	])
}}


                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">姓名：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <input type="text" class="form-control" name="name" title="姓名" requiredclass="required[0,TEXT]"
                        value="{{$data['name']}}" size="40" />
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">電子信箱：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <input type="email" class="form-control" name="email" title="電子信箱" requiredclass="required[0,EMAIL]"
                        value="{{$data['email']}}" size="40" placeholder="ex <EMAIL>" />
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">性別：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    {{
	Form::myUIXml([		
		'xmldoc'=>$data['xmldoc'],		
		'type'=>"Radio",		
		'title'=>"性別",
		'node'=>"//參數設定檔/性別/KIND",
		'name'=>"sex",        
		'value'=>$data['sex'],
		'linecount'=>5,
		'requiredclass'=>"required[0,TEXT]",
		
		//'onClick'=>"if (this.value==1){\$('.species').show();}else{\$('.species').hide();}",
		
	])
}}


                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">標題：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">
                    <input type="text" class="form-control" name="title" title="標題" required
                        requiredclass="required[1,TEXT]" value="{{$data['title']}}" size="40" />
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">留言內容：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <textarea name="body" cols="50" rows="5" class="form-control" title="留言內容"
                        requiredclass="required[0,TEXT]">{{$data['body']}}</textarea>
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">回覆訊息：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">
                    <textarea name="rebody" cols="50" rows="5" class="form-control" title="回覆訊息"
                        requiredclass="required[0,TEXT]">{{$data['rebody']}}</textarea>
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">Email通知 :</label>
                <div class="col-md-10">
                    <input type="checkbox" name="isemail" value="1">
                </div>
            </div>


            <div align="center">
                <button type="submit" class="btn btn-success">確定</button>
                <button type="reset" class="btn btn-secondary">取消</button>
                <!--<button type="submit" class="btn btn-primary" onclick="oForm.edit.value='';">複製一筆</button>-->
                <button type="reset" class="btn btn-secondary" onClick="javascript:window.history.go(-1)">回上一頁</button>

            </div>
            @include('admin.layouts.hidden', ['method'=>'EditoForm'])
            {!! Form::hidden("edit", $data['edit']) !!}
            {!! Form::hidden("gobackurl", $data['gobackurl'] ) !!}
        </form>
    </div>
</div>

@stop