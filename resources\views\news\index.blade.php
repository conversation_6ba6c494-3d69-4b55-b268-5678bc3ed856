@extends('layouts.master')
@section('css')


@endsection


@section('content')

<div class="px-3">
    <div class="theme-container">
        <div class="row">
            <nav aria-label="breadcrumb" class="col-xs-12">
                <ol class="breadcrumb end-xs">

                    <li class="breadcrumb-item"><a href="{{ url('/') }}/">首頁</a></li>
                    <li class="breadcrumb-item active" aria-current="page">房訊新聞</li>
                </ol>
            </nav>
        </div>
    </div>
</div>

<div class="px-3">
    <div class="theme-container">
        <div class="page-drawer-container mt-3">

            <div class="mdc-drawer-scrim page-sidenav-scrim"></div>
            <div class="page-sidenav-content">
                <div
                    class="row mdc-card between-xs middle-xs w-100 p-2 mdc-elevation--z1 text-muted d-md-none d-lg-none d-xl-none mb-3">
                    <button id="page-sidenav-toggle" class="mdc-icon-button material-icons">more_vert</button>
                    <h3 class="fw-500">My Account</h3>
                </div>
                <div class="mdc-card p-3">
                    <div class="mdc-data-table border-0 w-100 mt-3">
                        <table class="mdc-data-table__table" aria-label="Dessert calories">
                            <thead>
                                <tr class="mdc-data-table__header-row">

                                    <th class="mdc-data-table__header-cell">日期</th>
                                    <th class="mdc-data-table__header-cell">標題</th>
                                </tr>
                            </thead>
                            <tbody class="mdc-data-table__content">
                                @foreach ($data['rows'] as $rs)
                                


                                <tr class="mdc-data-table__row">

                                    <td class="mdc-data-table__cell">{{PF::formatDate($rs->created_at)}}</td>
                                    <td class="mdc-data-table__cell">
                                        
                                        <a href="{{ url('/') }}/news/show/{{$rs->id}}"
                                         title="{{$rs->title}}"
                                            class="mdc-button mdc-ripple-surface mdc-ripple-surface--primary normal"
                                            id="my-mdc-dialog">{{$rs->title}}</a></td>
                                </tr>
                                @endforeach
                                @if (count($data['rows'] )==0)
                                No Data
                                @endif

                            </tbody>
                        </table>
                    </div>
                </div>
                {{ $data['rows']!=null ? $data['rows']->links('layouts.paginate') :"" }}

            </div>
        </div>
    </div>
</div>



@endsection