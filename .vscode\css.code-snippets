{

    "font": {
        "prefix": "css font color size weight",
        "scope": "css",
        "body": [
            "divElement.css({",
            "'color' : '#999999'; //文字色彩",
            "'font-size' : '12px'; //文字大小",
            "'line-height' : '200%'; //設定行高",
            "'font-weight':'bold'; //文字粗體",
            " });",

        ]
    },
    "font js": {
        "prefix": "css font color size weight",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "'color' : '#999999', //文字色彩",
            "'font-size' : '12px', //文字大小",
            "'line-height' : '200%', //設定行高",
            "'font-weight':'bold', //文字粗體",
            " });",

        ]
    },

    "decoration": {
        "prefix": "css font decoration 底線",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "'text-decoration':'line-through', //加刪除線",
            "'text-decoration':'none', //刪除連結底線",
            " });",

        ]
    },
    "font align": {
        "prefix": "css font align 文字對齊",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "'text-align':'right', //文字靠右對齊",
            "'text-align':'left', //文字靠左對齊",
            "'text-align':'center' //文字置中對齊",
            " });",
        ]
    },
    "font vertical": {
        "prefix": "css font vertical 垂直對齊",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "'vertical-align':'top', /垂直向上對齊/",
            "'vertical-align':'bottom', /垂直向下對齊/",
            "'vertical-align':'middle', /垂直置中對齊/",
            "'vertical-align':'text-top', /文字垂直向上對齊/",
            "'vertical-align':'text-bottom', /文字垂直向下對齊/",
            "});",

        ]
    },
    "display": {
        "prefix": "css display 顯示隱藏",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "'display': 'none', //隱藏",
            "'display': 'block', //顯示",
            "});",

        ]
    },

    "padding": {
        "prefix": "css padding",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "'padding-top': '3px',",
            "'padding-bottom': '3px',",
            "'padding-left': '3px',",
            "'padding-right': '3px',",
            "'padding': '3px 2px 5px 1px',//上、右、下、左",
            "});",
        ]
    },

    "border": {
        "prefix": "css border 邊線",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "  'border-top':'thin solid; border-color:black;border-bottom:thin solid; border-color:black'",
            "});",
        ]
    },
    "background-color": {
        "prefix": "css background-color 背景顏色",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "  'background-color': 'cyan'",
            "});",
        ]
    },
    "background-url": {
        "prefix": "css background-url 背景圖",
        "scope": "javascript,vue,js",
        "body": [
            "divElement.css({",
            "  'background': '#D0E4F5 url(\"https://htmlcheatsheet.com/images/logo-css.png\") no-repeat scroll 0 0'",
            "});",
        ]
    },
    "style": {
        "prefix": "css style",
        "scope": "html,blade,vue",
        "body": [
            "<style>",
            "${1:}",
            "</style>"
        ]
    },










}