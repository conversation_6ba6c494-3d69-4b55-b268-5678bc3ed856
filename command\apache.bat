chcp 65001
@echo off
set "current_dir=%~dp0"
echo Current directory: %current_dir%

set "directoryvue=%current_dir%..\resources\js\views\"
set "directoryclient=%current_dir%..\resources\js\pages\"

if exist "%directoryvue%" (
    set "valid_directory=%directoryvue%"
) else if exist "%directoryclient%" (
    set "valid_directory=%directoryclient%"
)

@REM if defined valid_directory (
    echo Using directory: %valid_directory%
    python C:\pythoncode\filewatch\apache.py --path %current_dir%
@REM ) else (
@REM     echo "非vue3專案不執行apache start"
@REM )
