<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatefeedbackTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        if (!Schema::hasTable('feedback')) {
        Schema::create('feedback', function (Blueprint $table) {
            $table->increments('id');
            
            $table->string('company', 50)->nullable()->comment('公司名稱');
            $table->string('name', 100)->comment('姓名');
            $table->string('email', 500)->comment('電子信箱');
            $table->string('tel', 50)->nullable()->comment('電話');
            $table->string('ext', 50)->nullable()->comment('分機');
            $table->string('sex', 5)->nullable()->comment('性別');
            $table->string('mobile', 50)->nullable()->comment('行動電話');
            $table->string('title', 2000)->comment('標題');
            $table->string('memo')->nullable()->comment('留言內容');
            
            $table->string('retitle', 4000)->nullable()->comment('回覆標題');
            $table->string('rebody')->nullable()->comment('回覆訊息');
            $table->dateTime('redate')->nullable()->comment('回覆日期');
            $table->integer('memberid')->nullable()->comment('會員編號');
            $table->string('alg', 5)->nullable()->comment('語系');

            $table->integer('userid')->nullable()->comment('編輯人員');
            $table->string('useraccount', 50)->nullable()->comment('編輯人員');
            $table->timestamps();
        });
    }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('feedback');
    }
}
