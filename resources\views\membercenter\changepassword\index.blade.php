@extends('layouts.master')
@section('css')

@stop

@section('js')
@stop

@section('content')

<section class="login-area py-5">
    <div class="container">
        <div class="login-wrapper">
            <div class="login-box bg-white">
                <h4 class="text-center pb-3">變更密碼</h4>
                <script type="text/javascript">
                    function oForm_onsubmit(form) {
                        if (PF_FormMultiAll(form) == false) { return false };
                        if (typeof(form.elements['password']) != "undefined" && typeof(form.elements['password_confirmation']) !=
                            "undefined") {
                            if (form.elements['password'].value != form.elements['password_confirmation'].value) {
                                _alert('密碼與確認密碼不符');
                                form.elements['password1'].focus();
                                return false;
                            }
                        }
                        PF_FieldDisabled(form)
                        return true;
                    }
                </script>

                <form class="login-form" name="oForm" id="oForm" method="post" language="javascript" action="{{request()->url()}}/store" onsubmit="return oForm_onsubmit(this);">
                    <div class="mb-2">
                        {{$data['email']}}
                    </div>
                    <div class="mb-2">

                        <input type="password" class="form-control" id="exampleInputPassword1" placeholder="密碼" name="password" data-toggle="tooltip" data-placement="top" title="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *" requiredclass="[1,PASSWORD]">
                    </div>
                    <div class="mb-2">

                        <input type="password" name="password_confirmation" class="form-control" id="exampleInputPassword1" placeholder="確認密碼" name="password" data-toggle="tooltip" data-placement="top" title="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *" requiredclass="[1,PASSWORD]">
                    </div>

                    <button type="submit" class="btn btn-danger text-white">確定</button>
                </form>

            </div>

        </div>
    </div>
</section>




@stop