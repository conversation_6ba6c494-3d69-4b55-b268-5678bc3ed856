<?php

namespace App\Macros;

use DB;

/***
"功能名稱":"共用類別-Html select多層式",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
 ***/
/*
<div class="input-group">
  {{Form::myUISelectMulti([
     [
         'formname' =>'oForm',
         'title' =>'管理處',
         'sql' =>Gate::check('isAdminRole',['999']) ? "select id,mname from manage order by mname" : "select id,mname from manage where id in (".Auth::guard('admin')->user()->manage_ids.") order by mname",
         'name' => 'manage_id',
         'value' => $data['manage_id'],
         //'id'=>'manage_id',
         'required' => true,
     ],
     [
         'formname' =>'oForm',
         'title' =>'分校',
         'url' =>\request()->middlewareurlapi.'dependentdropdown/school',
         'name' => 'school_id',
         //'id' => 'school_id',
         'value' => $data['school_id'],
         'required' => true,
     ],
     [
         'formname' =>'oForm',
         'title' =>'班級',
         'url' =>\request()->middlewareurlapi.'dependentdropdown/classt',
         'name' => 'classt_id',
         //'id' => 'classt_id',
         'value' => $data['classt_id'],
         'required' => true,
     ],
 ])
}}
</div>
*/
class MyFormUISelectMulti {
    protected $arr;

    public function __construct($form, $arr) {
        $this->arr = $arr;
    }

    public function createHtml() {
        //try {
        $html = '';

        $html .= "<link href=\"" . url('/') . "/assets/dependent-dropdown/css/dependent-dropdown.min.css\" rel=\"stylesheet\">" . PHP_EOL;
        $html .= "<script src=\"" . url('/') . "/assets/dependent-dropdown/js/dependent-dropdown.min.js\" type=\"text/javascript\"></script>" . PHP_EOL;
        if (\Request::is('admin/*') || \Request::is('*/admin/*')) {
            $token = \Auth::guard('admin')->user()->api_token;
        } else if (\Request::is('membercenter/*') || \Request::is('*/membercenter/*')) {
            $token = \Auth::guard('member')->user()->api_token;
        }

        for ($i = 0; $i < count($this->arr); ++$i) {
            //$html .= '<div class="col">';
            $v = $this->arr[$i];
            $html .= PHP_EOL;
            if ('' == $v['firsttxt']) {
                $v['firsttxt'] = __('請選擇') . '..';
            }

            $html .= PHP_EOL;
            $html .= '<select name="' . $v['name'] . '"';
            $html .= ' id="' . $v['name'] . '"';

            $html .= ' class="form-control"';


            $html .= PHP_EOL;
            if (null != $v) {
                foreach ($v as $_key => $_value) {
                    if (false == in_array($_key, ['required', 'name', 'formname', 'url', 'firsttxt', 'sql', 'value', 'xmldoc', 'xpath'])) {
                        $html .= ' ' . $_key . '="' . $_value . '"';
                    }
                }
            }

            if ('' != $v['requiredclass']) {
                if ('required[1,' == substr($v['requiredclass'], 0, 11)) {
                    $html .= ' required ';
                }
            }
            if ('' != $v['required'] && $v['required'] == true) {

                $html .= ' required ';
            }

            $html .= PHP_EOL;

            $html .= ">\n";

            if ('' != $v['firsttxt']) {
                $html .= '<option value="">' . __($v['firsttxt']) . '</option>';
                $html .= PHP_EOL;
            }
            if ($i == 0) {
                if ('' != $v['xpath']) {
                    // if($v['xmldoc']==null){
                    //     throw new \CustomException("No load xmldoc");
                    // }
                    foreach ($v['xmldoc']->xpath($v['xpath']) as $v1) {
                        $selected = '';
                        $value = strval($v1->傳回值);

                        $title = $v1->資料;
                        if (false == \Request::is('admin/*')) {
                            $title = __($title);
                        }
                        if (trim($v['value']) == trim($value)) {
                            $selected = 'selected';
                        }
                        $html .= '<option value="' . $value . '" ' . $selected . '>' . $title . '</option>';

                        $html .= PHP_EOL;
                    }
                } elseif ('' != $v['sql']) {
                    $rows = DB::select($v['sql']);
                    foreach ($rows as $rs) {
                        $rs = get_object_vars($rs);
                        $arrkey = array_keys($rs);
                        $selected = '';

                        $value = $rs[$arrkey[0]];
                        $title = $rs[$arrkey[0]];

                        if (true == $this->arr['islg']) {
                            $title = __($title);
                        }

                        if (trim($v['value']) == trim($value)) {
                            $selected = 'selected';
                        }

                        if (count($arrkey) > 1) {
                            $title = $rs[$arrkey[1]];
                        }
                        $html .= '<option value="' . $value . '" ' . $selected . '>' . __($title) . '</option>';

                        $html .= PHP_EOL;
                    }
                }
            }
            $html .= "</select>" . PHP_EOL;
            if ($i > 0) {


                if ($v['value'] != "") {
                    $v['url'] .= "?selected=" . $v['value'];
                }

                if ($token != "") {
                    $v['url'] .= ((\Str::contains($v['url'], ['?'])) ? "&" : "?") . "token=" . $token;
                }

                $html .= "<script>" . PHP_EOL;
                $html .= "document.addEventListener(\"DOMContentLoaded\", () => {" . PHP_EOL;
                $html .= "$(\"#" . $v['formname'] . " select[name='" . $v['name'] . "']\").depdrop({" . PHP_EOL;
                $html .= "   url: \"" . $v['url'] . "\"," . PHP_EOL;
                $html .= "   depends: ['" . $this->arr[$i - 1]['name'] . "']," . PHP_EOL;
                if ($v['value'] != "") {
                    $html .= "   initDepends: ['" . $this->arr[$i - 1]['name'] . "']," . PHP_EOL;
                    $html .= "   initialize: true," . PHP_EOL;
                }
                $html .= "  });" . PHP_EOL;
                $html .= "});" . PHP_EOL;

                $html .= "</script> " . PHP_EOL;
            }
            $html .= " &nbsp;" . PHP_EOL;
            //$html .= '</div>' . PHP_EOL;
        }


        // } catch (\Exception $e) {
        //     $html = $e->getMessage();
        // }

        return $html;
    }
}
