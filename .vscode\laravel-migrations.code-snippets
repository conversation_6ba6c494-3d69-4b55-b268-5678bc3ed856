{

    "table string": {
        "prefix": "table string",
        "scope": "php",
        "body": ["\\$table->string('${1:name}',${3:50})->nullable()->comment('${2:備註}');"]
    },
    "table ipAddress": {
        "prefix": "table ip",
        "scope": "php",
        "body": ["\\$table->ipAddress('${1:clientip}')->nullable()->comment('${2:client ip}');"]
    },

    "table integer": {
        "prefix": "table integer",
        "scope": "php",
        "body": ["\\$table->integer('${1:name}')->nullable()->default(0)->comment('${3:備註}');"]
    },
    "table tinyInteger": {
        "prefix": "table integer tinyInteger",
        "scope": "php",
        "body": ["\\$table->tinyInteger('${1:name}')->default(0)->nullable()->comment('${3:上下架}');"]
    },
    "table float": {
        "prefix": "table float",
        "scope": "php",
        "body": ["\\$table->float('${1:name}',9,3)->nullable()->comment('${2:排序號碼}');"]
    },
    "table mediumText": {
        "prefix": "table string mediumText",
        "scope": "php",
        "body": ["\\$table->mediumText('${1:name}')->nullable()->comment('${2:備註}');"]
    },
    "table dateTime": {
        "prefix": "table dateTime",
        "scope": "php",
        "body": ["\\$table->dateTime('${1:name}_datetime')->nullable()->comment('${2:開始}時間');"]
    },
    "table time": {
        "prefix": "table time",
        "scope": "php",
        "body": ["\\$table->time('${1:name}_time')->nullable()->comment('${2:開始}時間');"]
    },

    "table unique": {
        "prefix": "table unique",
        "scope": "php",
        "body": ["\\$table->unique(['${1:name}'], '${2:member}_unique');"]
    },
    "table index": {
        "prefix": "table index",
        "scope": "php",
        "body": ["\\$table->index(array('${1:name}'));"]
    },
    "table auto 1000": {
        "prefix": "table auto",
        "scope": "php",
        "body": ["\\DB::statement('ALTER TABLE ${1:member} AUTO_INCREMENT = 10000;');"]
    },
    "table date ": {
        "prefix": "table date",
        "scope": "php",
        "body": ["\\$table->date('${1:birthday}_date')->nullable()->comment('${1:生日}');"]
    },

    "table 刪除欄位": {
        "prefix": "table del dropColumn",
        "scope": "php",
        "body": ["\\$$table->dropColumn(['${1:member}']);"]
    },
    "table engine": {
        "prefix": "table engine",
        "scope": "php",
        "body": ["\\$table->engine = \"MyISAM\";"]
    },
    "table softDeletes": {
        "prefix": "table del softDeletes",
        "scope": "php",
        "body": ["\\$table->softDeletes();"]
    },
    "table timestamp useCurrent": {
        "prefix": "table date timestamp ",
        "scope": "php",
        "body": ["\\$table->timestamp('created_at')->useCurrent()->comment('建立日期');",
            "\\$table->timestamp('updated_at')->comment('編輯日期');",
        ]
    },
    "table name": {
        "prefix": "table table name",
        "scope": "php",
        "body": ["\\DB::statement(\"ALTER TABLE ${1:member} COMMENT '${2:標題}'\");",

        ]
    },


}