<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PF;

//use Illuminate\Support\Facades\DB;

class userController extends Controller {
    private $data;

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id) {

        \Config::set('config.title',  '公司簡介 | ' . config('config.title'));
        $this->data['id'] = $id;

        return view(
            'user.index',
            [
                'data' => $this->data,
            ]
        );
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request) {
    }
}
