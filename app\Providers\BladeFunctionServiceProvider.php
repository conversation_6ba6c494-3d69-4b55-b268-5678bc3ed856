<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class BladeFunctionServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot()
    {
        /*    
          
            if (!preg_match("/^\s*([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)/", $expression, $matches)) {
                throw new \Exception("Invalid function name given in blade template: '$expression' is invalid");
            }

            $name = $matches[1];

           
            if (preg_match("/\((.*)\)/", $expression, $matches)) {
                $params = $matches[1];
            } else {
                $params = '';
            }

          
            Blade::directive($name, function ($expression) use ($name) {
              
                $expression = substr($expression, 1, -1);

              
                $expression = trim($expression);
                if ($expression) {
                    $expression .= ' , ';
                }

                return "<?php $name ($expression \$__env); ?>";
            });

          
            $params = trim($params);
            if ($params) {
                $params .= ' , ';
            }

           
            return "<?php function $name ( $params  \$__env ) { ?>";
        });

        Blade::directive('return', function ($expression) {
            return "<?php return ($expression); ?>";
        });

        Blade::directive('endfunction', function () {
            return '<?php } ?>';
        });
        */
    }

    /**
     * Register any application services.
     */
    public function register()
    {
    }
}
