@extends('admin.layouts.master')

@section('css')
@endsection

@section('js')
@endsection

@section('nav')
@endsection


@section('content')
    <div class="container-fluid noprint">
        <form name="SearchoForm" class="" method="post" language="javascript" action="{{ request()->url() }}"
            onsubmit="return SearchoForm_onsubmit(this);">

            @include('admin.layouts.search', ['method' => 'SearchoForm', 'except' => []])
        </form>
    </div>



    <!--排序-->
    <form name="SortoForm" method="post">
        @include('admin.layouts.hidden', ['method' => 'SortoForm', 'data' => $data])
    </form>



    <div class="float-left noprint">

        <button onclick="document.forms['oForm'].submit();" class="btn btn-primary btn-sm">重新加入jobs</button>
    </div>

    <div class="float-right noprint">
        <form name="oForm1" id="oForm1" method="post" language="javascript" action="{{ request()->url() }}/work"
            onsubmit="return oForm_onsubmit(this);" novalidate>

            <button class="btn btn-danger btn-sm">手動重啟</button>
            &nbsp;
            jobs : {{ $data['jobscount'] }}筆
        </form>


    </div>
    <div class="clearfix"></div>



    <form method="post" language="javascript" name="oForm" action="{{ request()->url() }}/store">
        @include('admin.layouts.hidden', ['method' => 'oForm', 'data' => $data])
        <div class="table-responsive-md">
            <table class="table table-striped table-hover  table-bordered table-fixed table-pc" mobile-count="5">
                <thead>
                    <tr valign="top" align="left">

                        <th align="center" width=80>
                            操作

                        </th>




                        <th width="" id="payload">payload</th>
                        <th width="" id="exception">exception</th>
                        <th width="" id="failed_at">failed_at</th>
                    </tr>
                </thead>

                <tbody>
                    @foreach ($data['rows'] as $rs)
                        <tr>
                            <td>




                                <input type="checkbox" name="uuid[]" value="{{ $rs->uuid }}">



                            </td>

                            <td>
                                <textarea class="form-control" cols="37" rows="5" style='width:90%;height:200px'>{{ $rs->payload }}</textarea>

                            </td>
                            <td>
                                <textarea class="form-control" cols="37" rows="5" style='width:90%;height:200px'>{{ $rs->exception }}</textarea>

                            </td>
                            <td>
                                {{ $rs->failed_at }}
                            </td>
                        </tr>
                    @endforeach

                </tbody>



            </table>
        </div>
    </form>
    @if (count($data['rows']) == 0)
        No Data
    @endif
    {{ method_exists($data['rows'], 'links') ? $data['rows']->links('layouts.paginate') : '' }}



    </div>
@endsection
