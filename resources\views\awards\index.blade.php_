@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')


<div class="px-3">
    <div class="theme-container">
        <div class="row">
            <nav aria-label="breadcrumb" class="col-xs-12">
                <ol class="breadcrumb end-xs">
                    <li class="breadcrumb-item"><a href="{{ url('/') }}">首頁</a></li>
                    <li class="breadcrumb-item"><a href="about">公司簡介</a></li>
                    <li class="breadcrumb-item active" aria-current="page">得獎最多</li>
                </ol>
            </nav>
        </div>
    </div>
</div>

<div class="px-3 mb-5">
    <div class="theme-container">
        <div class="row agents-wrapper">
            @foreach ($data['rows'] as $rs)

            <div class="row item col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 mb-5">
                <div class="mdc-card property-item list-item column-1">
                    <div class="thumbnail-section">
                        <div class="property-image">
                            <div class="swiper-container swiper-container-initialized swiper-container-horizontal" style="cursor: grab;">
                                <div class="swiper-wrapper" style="transition: all 0ms ease 0s; transform: translate3d(-398px, 0px, 0px);">

                                    @foreach (explode(",",$rs->field1) as $item)

                                    @if (File::exists(public_path('images/awards/'.$item)))
                                    <div class="swiper-slide" data-swiper-slide-index="{{$loop->index}}">
                                        <img src="{{ url('/') }}/images/awards/{{$item}}" alt="{{$rs->title}}" class="slide-item swiper-lazy swiper-lazy-loaded">

                                    </div>
                                    @php
                                    $i++
                                    @endphp
                                    @endif
                                    @endforeach



                                </div>
                                <div class="swiper-pagination white swiper-pagination-clickable swiper-pagination-bullets">
                                    <span class="swiper-pagination-bullet swiper-pagination-bullet-active" tabindex="0" role="button" aria-label="Go to slide 1"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 2"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 3"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 4"></span>
                                </div>
                                <button class="mdc-icon-button swiper-button-prev swipe-arrow" tabindex="0" role="button" aria-label="Previous slide"><i class="material-icons mat-icon-lg">keyboard_arrow_left</i></button>
                                <button class="mdc-icon-button swiper-button-next swipe-arrow" tabindex="0" role="button" aria-label="Next slide"><i class="material-icons mat-icon-lg">keyboard_arrow_right</i></button>
                                <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
                            </div>
                        </div>
                    </div>



                    <div class="property-content-wrapper">
                        <div class="property-content">
                            <div class="content">
                                <h1 class="title">{{$rs->title}}</h1>
                                {{$rs->memo}}
                                <hr class="my-3">

                                <p>{!!PF::vbcrlf($rs->body)!!}</p>
                            </div>
                            <div class="grow"></div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
            @if (count($data['rows'] )==0)
            No Data
            @endif



        </div>
        {{ $data['rows']!=null ? $data['rows']->links('layouts.paginate') :"" }}
    </div>
</div>


@endsection