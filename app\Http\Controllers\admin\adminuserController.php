<?php

namespace App\Http\Controllers\admin;

use DB;
use PF;
use PT;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\adminuserRepository;
use Illuminate\Support\Facades\Validator;

class adminuserController extends adminController {
    private $fieldnicknames;
    private $data;

    private $adminuserRepo;

    /**
     *建構子.
     */
    public function __construct(adminuserRepository $adminuserRepo) {
        //$this->limit="xx";
        parent::__construct();

        $this->data = PF::requestAll($this->data);

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->adminuserRepo = $adminuserRepo;
        //權限管理

        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'adminuser');

        $this->data['hiddens'] = [];
        $this->fieldnicknames = $this->adminuserRepo->getFieldTitleArray();
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $fieldsearchname = [
            '' => '請選擇',
            'account' => $this->fieldnicknames['account'],
            'name' => $this->fieldnicknames['name'],
            'email' => $this->fieldnicknames['email'],
            //'status|INT' => $this->fieldnicknames['status'],
            'online|INT' => $this->fieldnicknames['online'],
        ];

        $fieldsearchdatename = [
            'created_at' => $this->fieldnicknames['created_at'],
        ];
        $this->data['fieldsearchname'] = $fieldsearchname;
        $this->data['fieldsearchdatename'] = $fieldsearchdatename;

        $rows = $this->listRows($request);
        $rows = $rows->paginate(10);

        $this->data['rows'] = $rows;

        return view('admin.adminuser.index', [
            'data' => $this->data,
        ]);
    }

    private function listRows(Request $request) {
        try {
            //     $rows = $this->db::selectRaw("adminuser.*,case when adminuser.kindheadid<>'' then (select  GROUP_CONCAT(kindheadtitle SEPARATOR ',')  from kindhead where FIND_IN_SET(kindhead.kindheadid,adminuser.kindheadid)) else '' end as 'kindhead'");
            $rows = $this->adminuserRepo->select('adminuser.*');
            $rows->myWhere($this->data['searchname'], $this->data['search'], $this->fieldnicknames, 'N');
            //依條件時間搜尋資料的SQL語法

            $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|>=', $request->input('searchstartdate'), $this->fieldnicknames, 'N');
            $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|<=', $request->input('searchenddate'), $this->fieldnicknames, 'N');

            if ($this->data['sortname']) {
                $rows->orderBy($request->input('sortname'), 'desc' == $request->input('sorttype') ? $request->input('sorttype') : 'asc');
            } else {
                $rows->orderBy('id', 'desc');
            }

            return $rows;
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 資料建立顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request) {
        try {
            return view('admin.adminuser.edit', [
                'data' => $this->data,
            ]);
        } catch (\Exception $e) {
            //throw $e;
            die($e->getMessage());
        }
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request) {
    }

    /**
     * 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request) {
        $edit = $request->input('edit');

        $validators = null;
        $validators['edit'] = 'required|integer';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $rows = $this->adminuserRepo->select('*');
        $rows->where('id', '=', $edit);

        $rows = $rows->get();
        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('No data');
        }

        return view('admin.adminuser.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {
        $edit = $request->input('edit');

        $validators = null;
        if ('' == $edit) {
            $validators['password'] = ['required']; //姓名
            $validators['account'] = 'required|unique:adminuser,account';
        }

        $validators['name'] = 'required';
        //$validators['status'] = 'required';
        $validators['email'] = 'nullable|email';

        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = $request->all();
        unset($inputs['password']);
        if ('' != $this->data['password']) {
            $inputs['password'] = \Hash::make($this->data['password']);
        }/*密碼-*/
        if ('' == $this->data['role']) {
            $inputs['role'] = '999';
        }
        // $inputs['status'] = $this->data['status']; /*角色-*/
        // $inputs['name']  = $request->get('name'); /*姓名-*/
        // $inputs['email']  = $request->get('email'); /*E-Mail-*/
        // $inputs['online'] = $request->get('online'); /*是否核可-*/

        if ('' == $edit) {
            $inputs['api_token'] = hash('sha256', \Str::random(80));
            $inputs['created_at'] = date('Y-m-d H:i:s'); /*建立時間-*/
            $this->data['alert'] = '新增成功';
            $this->adminuserRepo->create($inputs);
        } else {
            $this->data['alert'] = '更新成功';
            $this->adminuserRepo->update($inputs, $edit);
        }

        /*if ($dbcount==0){
              return view("error")->withErrors("No update record");
         }
        */

        return view('admin.layouts/postsubmit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request) {
        $this->adminuserRepo->deleteIds($this->data['del']);
        // $rows = $this->db->myWhere('id|ININT', $this->data['del'], 'del', 'Y');
        // //PF::dbSqlPrint($rows);
        // $rows->get()->each(function ($row) {
        //     $row->delete();
        // });

        return view('admin/layouts/postsubmit', [
            'data' => $request->all(),
        ]);
    }
}
