@extends('layouts.master')

@section('css')
    {{-- 移除舊的 CSS，使用 Tailwind CSS --}}
@endsection

@section('js')
@endsection

@section('content')
    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">得獎最多</h2>

    <!-- 主標題區塊 -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-900 sm:text-4xl">
                    <span class="text-primary">得獎最多</span> 殊榮記錄
                </h1>
                <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
                    我們的專業服務與卓越表現，獲得各界肯定與獎項認可
                </p>
            </div>
        </div>
    </div>

    <!-- 獎項列表 -->
    <div class="bg-gray-50 min-h-screen py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if (count($data['rows']) > 0)
                <div class="grid gap-8 md:gap-10">
                    @foreach ($data['rows'] as $index => $rs)
                        <article
                            class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group">
                            <div class="lg:flex {{ $index % 2 == 0 ? '' : 'lg:flex-row-reverse' }}">
                                <!-- 圖片區域 -->
                                <div class="lg:w-1/2 relative overflow-hidden">
                                    <div class="aspect-w-16 aspect-h-9 lg:aspect-w-3 lg:aspect-h-2">
                                        {{ Html::myUIImage([
                                            'folder' => 'https://www.ebayhouse.com.tw/images/awards',
                                            'filename' => $rs->field1,
                                            'alt' => $rs->title,
                                            'width' => 600,
                                            'height' => 400,
                                            'noimg' => 'no-picture.gif',
                                            'class' => 'w-full h-full object-cover group-hover:scale-105 transition-transform duration-500',
                                        ]) }}
                                    </div>
                                    <!-- 獲獎標籤 -->
                                    <div class="absolute top-4 left-4">
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                                </path>
                                            </svg>
                                            獲獎記錄
                                        </span>
                                    </div>
                                </div>

                                <!-- 內容區域 -->
                                <div class="lg:w-1/2 p-6 lg:p-8 flex flex-col justify-center">
                                    <div class="flex-1">
                                        <!-- 標題 -->
                                        <h2 class="text-xl lg:text-2xl font-bold text-gray-900 mb-4 leading-tight">
                                            {{ $rs->title }}
                                        </h2>

                                        <!-- 日期 -->
                                        <div class="flex items-center text-sm text-gray-500 mb-4">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                            <time datetime="{{ $rs->created_at }}">
                                                {{ PF::formatDate($rs->created_at) }}
                                            </time>
                                        </div>

                                        <!-- 內容描述 -->
                                        <div class="text-gray-600 leading-relaxed space-y-3">
                                            {!! PF::vbcrlf($rs->body) !!}
                                        </div>
                                    </div>

                                    <!-- 裝飾性元素 -->
                                    <div class="mt-6 pt-4 border-t border-gray-100">
                                        <div class="flex items-center justify-between">
                                            <span class="text-xs text-gray-400 uppercase tracking-wide font-semibold">
                                                Achievement Record
                                            </span>
                                            <div class="flex space-x-1">
                                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                                <div class="w-2 h-2 bg-secondary rounded-full"></div>
                                                <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <!-- 分頁導航 -->
                <div class="mt-12">
                    {{ method_exists($data['rows'], 'links') ? $data['rows']->links('layouts.paginate') : '' }}
                </div>
            @else
                <!-- 無資料狀態 -->
                <div class="text-center py-16">
                    <div class="mx-auto h-24 w-24 text-gray-400 mb-6">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">暫無獲獎記錄</h3>
                    <p class="text-gray-500 max-w-md mx-auto">
                        目前尚未有獲獎資訊，我們會持續努力為客戶提供最優質的服務。
                    </p>
                    <div class="mt-6">
                        <a href="{{ url('/') }}"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-red-700 transition-colors duration-200">
                            返回首頁
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
