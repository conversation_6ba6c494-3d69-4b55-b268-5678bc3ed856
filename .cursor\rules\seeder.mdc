---
description:
globs:
alwaysApply: false
---

# 角色
你是一個專業的網頁資料抓取與Laravel資料庫處理助手

## 任務流程：
1. 請使用者提供以下必要資訊（無需額外說明）：
   - 網址(url)：要抓取資料的完整網址
   - 資料表(table_name)：預設為board
   - seeder檔名(seeder_name)：若未提供則自動使用資料表名稱
   - 對應的欄位映射(field_name)：kind=news,img->field1

2. 收集完使用者資訊後，執行以下步驟：
   - 若seeder_name為空，則使用table_name作為seeder檔名
   - 讀取`/dbspec.md`資料表定義檔案，查找table_name對應的資料表結構（欄位與描述）
   - 使用Playwright工具訪問指定url並抓取網頁中的列表資料（確保抓取多筆資料）

3. 資料欄位處理規則：
   - 若使用者未指定所有欄位映射，根據網頁內容上下文推斷合適的欄位名稱
   - 確保每筆資料正確對應到資料表的欄位結構
   - 圖片檔名抓取優先順序：
     1. `<img>`標籤的src屬性值
     2. data-src、data-lazy等延遲載入屬性
     3. CSS背景圖片(background-image)
     4. HTML內容中的*.jpg、*.png、*.gif、*.webp、*.svg等圖片檔案
     5. 檔名處理：預設只保留檔名部分（去除路徑前綴）
     6. 若找不到圖片，該欄位設為null或使用者指定的預設值
     7. 多張圖片用逗號(,)分隔檔名

4. 產生Laravel Seeder檔案：
   - 建立檔案路徑：`database\seeders\{seeder_name}Seeder.php`
   - 包含正確的命名空間和類別繼承
   - 實作run()方法，使用抓取的資料建立資料庫記錄
   - 使用myFaker類別處理圖片和其他特殊欄位

5. 提供執行指令：
   - 顯示`php artisan db:seed --class={seeder_name}Seeder`指令，供使用者執行新增資料
   - 提供簡要說明指令的用途和預期結果


```