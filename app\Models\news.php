<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="news",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="news_no", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="news_type", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="news_date", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="media", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="title", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="news", type="",description="", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class news extends baseModel
{
    use HasFactory;
    
    public $tabletitle = '';
    public $table = 'news';
    public $primaryKey = '';
    //public $incrementing = false;//取消自動編號
      //欄位必填
      public $rules = [
		'id' => 'required',

    ];
    public $fieldInfo = [
'news_no'=>['title'=>'','type'=>'varchar(255)'],//
'news_type'=>['title'=>'','type'=>'varchar(255)'],//
'news_date'=>['title'=>'','type'=>'varchar(255)'],//
'media'=>['title'=>'','type'=>'varchar(255)'],//
'title'=>['title'=>'','type'=>'varchar(255)'],//
'news'=>['title'=>'','type'=>'varchar(255)'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['news_no','news_type','news_date','media','title','news']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    
    protected $dates = [];
  
//   public function __construct() {
//         $this->fillable = parent::getfillables();//接受$request->all();
//         //$this->fillable =array_keys($this->fieldInfo);
//         parent::__construct();        
//         parent::setFieldInfo($this->fieldInfo);
//   }         
    
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          
        });
         static::deleting(function ($model) {

          
        });
        static::deleted(function ($model) {
            
        });
    }	

}