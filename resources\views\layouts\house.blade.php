<script type="text/javascript">
    function changeshowtype(value) {
        // 檢查 oForm1 是否存在隱藏欄位
        var form = document.getElementById('oForm1'); // 獲取表單引用
        if (document.forms['oForm1'].elements['showtype']) {
            document.forms['oForm1'].elements['showtype'].value = value;
        } else {
            var newInput = document.createElement('input');
            newInput.type = 'hidden';
            newInput.name = 'showtype';
            newInput.value = value;
            form.appendChild(newInput);
        }
        oForm1.submit();
    }

    function sortTable(sortname) {
        var form = document.getElementById('oForm1'); // 獲取表單引用
        if (!form.elements['sortname']) {
            var newInput = document.createElement('input');
            newInput.type = 'hidden';
            newInput.name = 'sortname';
            newInput.value = sortname;
            form.appendChild(newInput);

        }

        if (!form.elements['sorttype']) {

            var newInput = document.createElement('input');
            newInput.type = 'hidden';
            newInput.name = 'sorttype';
            newInput.value = 'desc';
            form.appendChild(newInput);

        }
        if (form.elements['sorttype'].value == 'desc') {
            form.elements['sorttype'].value = 'asc';
        } else {
            form.elements['sorttype'].value = 'desc';
        }
        form.elements['sortname'].value = sortname;



        oForm1.submit();
    }
</script>
<a name="house"></a>
@if (request()->path() == '/')
    <h2 class="index-subtitle">熱門物件</h2>
@else
    <h2 class="findings-subtitle">搜尋結果</h2>
@endif

<form name="oForm1" id="oForm1" metdod="post" language="javascript" action="{{ request()->url() }}/#house">
    <div class="view-toggle-wrap">
        <i id="btn-grid" class="fa-solid fa-th-large active" onclick="changeshowtype(1)" title="卡片模式"></i>
        <i id="btn-table" class="fa-solid fa-rectangle-list" onclick="changeshowtype(2)" title="表格模式"></i>
    </div>
    @include('admin.layouts.hiddenall')
</form>
@if (count($data['rows']) == 0)
    No Data
@endif


<section class="findings_wrap">

    @if ($data['showtype'] == '2')
        <div class="list-view">
            <!-- 桌面版表格 -->
            <table class="property-table  w-full border-collapse">

                <thead>
                    <tr class="bg-white border-b" style="background-color: #FF8000;">
                        <th class=" text-left">
                            物件

                        </th>
                        <th class=" text-center" onclick="sortTable('auctions')">狀態 <i class="fa fa-sort"
                                aria-hidden="true"></i></th>
                        <th class=" text-center" onclick="sortTable('tenderdate')">投標時間 <i class="fa fa-sort"
                                aria-hidden="true"></i></th>
                        <th class=" text-left" onclick="sortTable('address')">法拍物件地址 <i class="fa fa-sort"
                                aria-hidden="true"></i></th>
                        <th class=" text-center" onclick="sortTable('pattern')">類型 <i class="fa fa-sort"
                                aria-hidden="true"></i></th>
                        <th class=" text-center" onclick="sortTable('noticelawnestablishment')">公告建坪 <i
                                class="fa fa-sort" aria-hidden="true"></i></th>
                        <th class=" text-center" onclick="sortTable('floorprice')">拍賣底價 <i class="fa fa-sort"
                                aria-hidden="true"></i></th>
                        <th class=" text-center" onclick="sortTable('margin')">單價 <i class="fa fa-sort"
                                aria-hidden="true"></i></th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($data['rows'] as $rs)
                        <!-- 物件2 -->
                        <tr class="bg-white border-b">
                            <td class="p-3">
                                <a href="{{ url('/') }}/house/show/{{ $rs->productid }}"
                                    title="{{ $rs->city1title }}{{ $rs->city2title }}{{ $rs->producttitle }}">
                                    {{ Html::myUIImage([
                                        'folder' => 'https://www.ebayhouse.com.tw/images/product',
                                        'filename' => collect(explode(',', $rs->img))->last(),
                                        'alt' => $rs->producttitle,
                                        'noimg' => 'no-picture.gif',
                                        'class' => 'w-26 h-24 object-cover', // Reduced size for mobile
                                    ]) }}
                                </a>
                            </td>
                            <td class="p-3 text-center text-red-500 font-bold">{{ $rs->auctions }}</td>
                            <td class="p-3 text-center">{{ $rs->tenderdate }}</td>
                            <td class="p-3">{{ $rs->city1title }}{{ $rs->city2title }} {{ $rs->address }}</td>
                            <td class="p-3 text-center">{{ $rs->pattern }}</td>
                            <td class="p-3 text-center">
                                {{ $rs->noticelawnestablishment }}坪
                            </td>
                            <td class="p-3 text-center text-red-500 font-bold">
                                {{ PF::formatNumber($rs->totalupset, -1) }}
                                萬</td>
                            <td class="p-3 text-center">{{ $rs->floorprice }}萬</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <div class="findings-grid hotGoods">
            @foreach ($data['rows'] as $rs)
                @include('layouts.houseitem')
            @endforeach

        </div>
        <!-- //findings-grid End -->
    @endif
</section>
<!--物件列表 end-->
