// bg-set
let bgSet = document.querySelectorAll("[data-bgImg]");
for (let i = 0; i < bgSet.length; i++) {
  let url = bgSet[i].getAttribute('data-bgImg');
  bgSet[i].style.backgroundImage = "url('" + url + "')";
}



// heart-icon (Add to Favorites)
document.querySelectorAll('.heart-icon').forEach(icon => {
  icon.addEventListener('click', () => {
    icon.classList.toggle('active');
  });
});



//⛓️‍💥nav-active
const currentLocation = location.href;
const menuItem = document.querySelectorAll('a');
const menuLenth = menuItem.length;
for (let i = 0; i < menuLenth; i++) {
  if (menuItem[i].href === currentLocation) {
    menuItem[i].className = 'active'
  }
}

//⛓️‍💥header-nav (burger:新增overlay) - 加入安全檢查
const burger = document.querySelector(".nav-button");
const panel = document.querySelector(".nav");
const overlay = document.querySelector("#overlay");

if (burger && panel && overlay) {
  burger.addEventListener('click', function () {
    panel.classList.toggle("open");
    burger.classList.toggle("toggle");
    overlay.classList.toggle("active");

    if (overlay.classList.contains("active")) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }, false);

  overlay.addEventListener('click', function () {
    panel.classList.remove("open");
    burger.classList.remove("toggle");
    overlay.classList.remove("active");
    document.body.style.overflow = "";
  }, false);
} else {
  console.log('main.js: 導航列元素未找到，跳過 burger 功能');
}



// safari-vh
let vh = window.innerHeight * 0.01;
document.documentElement.style.setProperty('--vh', `${vh}px`);
window.addEventListener('resize', () => {
  let vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
});













