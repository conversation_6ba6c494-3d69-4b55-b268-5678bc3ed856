/*字型*/
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+TC:wght@200..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poiret+One&display=swap');

* {
	margin: 0; 
	padding: 0;
	border: 0;
	list-style:none;		
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
}

html {
	font-size: 20px;	font-size: 18px;	
}
@media (min-width: 3000px){
  html {
		font-size: 30px;
	}
}
@media (min-width: 5000px){
  html {
		font-size: 42px;
	}
}
@media (max-width: 1199px){
	html {
		font-size: 18.9px;	
	}
}
@media (max-width: 599px){
	html {
		font-size: 17.1px;	
	}
}
/*----------------------------------*\
	# var                     
\*----------------------------------*/
:root { 
	--main: #FF8000;
	--mate: #FF4000; 	
	--match:#FDCA99;
	--gold: #bc8c43;	
		
	--bg:radial-gradient(ellipse, #f0f0f0, #fff8f0);
	--bestBg:radial-gradient(ellipse, #f0f0f0, #efd4b9);
	--scrollbar: #faa945;
	--scrollbar: #68686b;
	--scrollbarBg: #f0f0f0;		
	
	--header: rgba(255,255,255,1);		
	--footer: linear-gradient(to bottom left,#fdb85d 0%,#ff8000 40%,#ff8000 60%,#f39b28 100%);
	--burger: #272727;
	--panel: rgba(253,202,153,.9);
	--border: #ccc;	
	--input: #FFF;
	
	--font: #181818;
	--white: #FFF;
	--strong: #333;
	--weak: #f3f1f0;	

	--btn-h: 2.6rem;
	--header-h: 4.5rem;
	/*--footer-h: 7.2rem;
	--footer-h: 9rem;	*/		
	--full-h: calc(var(--vh, 1vh) * 100); /*100vh*/
	--min-h: calc(var(--full-h) - var(--footer-h) - var(--header-h));		
				
	--tx-sd: -1px 0 1px rgba(0,0,0,.41);
	--tx-sd: 0 1.8px 1.8px rgba(0, 0, 0, 0.36);
	--box-sd: 0 1px 2px 0 rgba(0, 0, 0, 0.24);	

	/*Font Family*/
	--en: 'Hind Siliguri', sans-serif; 
	--order: 'Poiret One', cursive;
	--cursive:'Great Vibes', cursive;  
	--num:'Cinzel Decorative', sans-serif;  
  /*--mix: "Raleway",'Noto Sans TC',sans-serif; */
  --mix: 'Noto Sans TC',"Microsoft JhengHei", sans-serif; 
  /*--mingle:"Raleway",'Noto Serif TC', serif; */
  --mingle:'Noto Serif TC',"Microsoft JhengHei", serif;	
  --base: "Noto Sans TC",'Roboto', sans-serif;
  --luyi: 'Noto Serif TC', 'Hind Siliguri',serif;  

  /*Font Size*/  
  --great: calc(5.4rem + .54vw);  
  --huge: calc(2.7rem + .27vw);  
  --huge: calc(3.6rem + .36vw); 
  --topic: calc(1.8rem + .1875vw); 
  --title: calc(1.35rem + .1875vw); 
	--keyword: calc(1.08rem + .25vw); 
	--big: calc(.9rem + .25vw); 
	--small: calc(.81rem + .1875vw); 
	--tiny: calc(.72rem + .125vw); 
  --bitty: calc(.63rem + .09vw); 
  --p: calc(.81rem + .1875vw);
	
}
@media (max-width: 1199px){
	:root {
		--header-h: 50px;
		--footer-h: 99px;			
		--footer-h: 69px;	
		--footer-h: 60px;
		--tx-sd: -0.6px 0 0.6px rgba(0,0,0,.34);
		--box-sd: 0 0.6px 1.2px 0 rgba(0, 0, 0, 0.24);
	}
}
/*----------------------------------*\
	# element ( common )                  
\*----------------------------------*/
body {
  overflow-x:hidden; 
	/*scroll-behavior: smooth;*/
	position: relative; 
	background: var(--bg);	
	line-height: 1.71;	
	color: var(--font);
  font-family: var(--base);
}
h1,h2,h5,h6 {
	line-height: 1.11;
}
h1 { font-size: calc(1.8rem + .5vw)  } 
h2 { font-size: calc(1.26rem + .25vw) } 
h3 { font-size: calc(1.17rem + .25vw) } 
h4 { font-size: calc(1.08rem + .218vw) } 
h5 { font-size: calc(.9rem + .1875vw)} 
 p { font-size: calc(.81rem + .1875vw) } 
h6 { font-size: calc(.72rem + .125vw) }  

h1,h2,h3 {
  font-family: var(--mingle);
}
h1 {
	font-weight: 900;  
}
h2 {
	font-weight: 800;  
}
h3 {
	font-weight: 700;	
}
h4,h5,h6,p {
	font-weight: 400;	
}
a,a:visited,a:hover  {
	text-decoration:none; 
	color:inherit;	
}

a:focus {
	outline: none; 
}

img { 
	display: block;
  max-width: 100%; 
}

/*----------------------------------*\
	# element ( form )                    
\*----------------------------------*/
input[type="text"],
input[type="search"],
input[type="password"],
input[type="date"],
input[type="email"],
input[type="tel"],
select,
textarea {   
  border: solid 0.05rem var(--border);
  background: var(--input);
  border-radius: .18rem;    
  outline: none; 
  width:100%;
  padding: 0 .54rem;   
  color: var(--font);
  font-size: var(--small);
}
textarea { 
  padding:.3rem .54rem; 
  height: auto; 
  line-height: 1.68;  
}
 button,.btn {
  outline: none; 
  display:block; 
  margin:auto;
  text-align:center;
  max-width: 100%; 
  background: var(--main);  
  color: var(--white);
	transition: background-color 0.54s;
  cursor: pointer; 
  border-radius: .36rem;
}
button:hover, 
.btn:hover {
  background: var(--main);   
} 

/*----------------------------------*\
	# element ( form )                    
\*----------------------------------*/
html {
  scrollbar-width: thin;           
  scrollbar-color: #FF8000 #f0f0f0; 
  scrollbar-color: var(--scrollbar) var(--scrollbarBg);
}
::-webkit-scrollbar {
  width: 9px; 
}
::-webkit-scrollbar-track {
  background: #f0f0f0; 
}
::-webkit-scrollbar-thumb {
  background-color: #FF8000; 
  border-radius: 7.2px;
  border: 1px solid transparent;
  background-clip: content-box;
}

