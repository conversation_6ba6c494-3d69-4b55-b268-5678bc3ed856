<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

//類別
class kind extends baseModel
{
    public $tabletitle = '類別';
    public $table = 'kind';
    public $primaryKey = 'kindid';

    //欄位必填
    public $rules = [
        'kindtitle' => 'required',
'kind' => 'required',
    ];
    public $fieldInfo = [
'kindid'=>['title'=>'自動編號','type'=>'int(11)'],//
'kindtitle'=>['title'=>'種類標題','type'=>'varchar(255)'],//
'kind'=>['title'=>'種類','type'=>'varchar(255)'],// //法拍屋[1],中古屋[2],土地專區[3],
'kindfield1'=>['title'=>'','type'=>'varchar(4000)'],//
'kindfield2'=>['title'=>'','type'=>'varchar(4000)'],//
'kindfield3'=>['title'=>'','type'=>'varchar(4000)'],//
'created_at'=>['title'=>'建立時間','type'=>'datetime'],//
'updated_at'=>['title'=>'異動時間','type'=>'datetime'],//
'kindsortnum'=>['title'=>'排序號碼','type'=>'float(5,2)'],//
'alg'=>['title'=>'語系','type'=>'varchar(10)'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['kindtitle','kind','kindfield1','kindfield2','kindfield3','created_at','updated_at','kindsortnum','alg']; //可充許傳入的欄位
    protected $guarded = [];
    protected $dates = ['created_at','updated_at'];

    // public function __construct($attr = array(), $exists = false)
    // {
    //     parent::__construct($attr, $exists);

    //     parent::setFieldInfo($this->fieldInfo);
    // }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
        });
        static::deleted(function ($model) {
        });
    }
}
