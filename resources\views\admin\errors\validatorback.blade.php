@extends('layouts.empty')
@section('content')
<script language=JavaScript>
$(function() {

    try {
        var msg='';
        
        @if ($errors!=null && $errors->any())
            @foreach ($errors->all() as $error)
                msg+='{{$error}}\r\n';
            @endforeach
        @endif
        Swal.fire({
            //title: "Are you sure?",
            text: msg,
            icon: "warning",
            //buttons: true,
            dangerMode: true,
            })
            .then((willDelete) => {

                history.back();
            
            });
        //_alert(msg);
        
    } catch (error) {
        alert(error)        
    }
});
</script>

@endsection