<?php

namespace App\Repositories;

use App\Models\viewcount;
use DB;
use PF;

class viewcountRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(viewcount $model)
    {
        $this->model = $model;
    }
    /**
     *TODO 透過DB TABLE回傳ROWS
     */
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);
        return $rows;
    }
    /**
     *TODO 透過KEY找一筆資料
     */
    public function find($id, $column="*")
    {
        
        $rows=$this->select($column);
        $rows->where($this->model->primaryKey,"=", $id);        
        $rows = $rows->take(1);

        return $rows;
    }
   

    public function create($inputs)
    {
        $this->model::create($inputs);
    }

    public function update(array $inputs, $id, $attribute = 'id')
    {
        $this->model::find($id)->update($inputs);
    }

    public function deleteIds($ids)
    {
        
        $this->model->whereIn($this->model->primaryKey, explode(",",$ids))->delete();
       
        // $rows = $this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // //PF::dbSqlPrint($rows);
        // $rows->get()->each(function ($row) {
        //     $row->delete();
        // });
        
    }
}
