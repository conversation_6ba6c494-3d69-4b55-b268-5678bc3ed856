<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圖片輪播展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Swiper/8.4.7/swiper-bundle.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Swiper/8.4.7/swiper-bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: "#dc2626", // red-600
                            foreground: "#ffffff",
                        },
                        secondary: {
                            DEFAULT: "#2563eb", // blue-600
                            foreground: "#ffffff",
                        },
                    }
                }
            }
        }
    </script>

    <style>
        .swiper-button-next,
        .swiper-button-prev {
            color: #3b82f6;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin-top: -20px;
        }

        .swiper-button-next:after,
        .swiper-button-prev:after {
            font-size: 16px;
            font-weight: bold;
        }

        .swiper-pagination-bullet {
            background: #3b82f6;
            opacity: 0.5;
        }

        .swiper-pagination-bullet-active {
            opacity: 1;
        }

        .thumbnail-swiper .swiper-slide {
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.3s;
        }

        .thumbnail-swiper .swiper-slide-thumb-active {
            opacity: 1;
            border: 2px solid #3b82f6;
        }
    </style>
</head>

<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
        <!-- 主要內容區域 -->
        <div class="main p-6">
            <!-- 圖片輪播區域 -->
            <section class="slider__wrap mb-8">
                <div class="slider__flex flex flex-col lg:flex-row gap-4">
                    <!-- 主圖輪播 -->
                    <div class="sliders flex-1">
                        <div class="swiper main-swiper rounded-lg overflow-hidden shadow-md">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <div class="imgBox aspect-video bg-gray-200 flex items-center justify-center">
                                        <img src="https://picsum.photos/800/600?random=1" alt="產品圖片 1"
                                            class="w-full h-full object-cover" />
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="imgBox aspect-video bg-gray-200 flex items-center justify-center">
                                        <img src="https://picsum.photos/800/600?random=2" alt="產品圖片 2"
                                            class="w-full h-full object-cover" />
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="imgBox aspect-video bg-gray-200 flex items-center justify-center">
                                        <img src="https://picsum.photos/800/600?random=3" alt="產品圖片 3"
                                            class="w-full h-full object-cover" />
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="imgBox aspect-video bg-gray-200 flex items-center justify-center">
                                        <img src="https://picsum.photos/800/600?random=4" alt="產品圖片 4"
                                            class="w-full h-full object-cover" />
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="imgBox aspect-video bg-gray-200 flex items-center justify-center">
                                        <img src="https://picsum.photos/800/600?random=5" alt="產品圖片 5"
                                            class="w-full h-full object-cover" />
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>

                    <!-- 縮圖輪播 -->
                    <div class="slider__col w-full lg:w-32 flex lg:flex-col">
                        <div class="slider__thumbs flex-1">
                            <div class="swiper thumbnail-swiper h-full">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide mb-2 lg:mb-4">
                                        <div class="imgBox aspect-square bg-gray-200 rounded-md overflow-hidden">
                                            <img src="https://picsum.photos/150/150?random=1" alt="縮圖 1"
                                                class="w-full h-full object-cover" />
                                        </div>
                                    </div>
                                    <div class="swiper-slide mb-2 lg:mb-4">
                                        <div class="imgBox aspect-square bg-gray-200 rounded-md overflow-hidden">
                                            <img src="https://picsum.photos/150/150?random=2" alt="縮圖 2"
                                                class="w-full h-full object-cover" />
                                        </div>
                                    </div>
                                    <div class="swiper-slide mb-2 lg:mb-4">
                                        <div class="imgBox aspect-square bg-gray-200 rounded-md overflow-hidden">
                                            <img src="https://picsum.photos/150/150?random=3" alt="縮圖 3"
                                                class="w-full h-full object-cover" />
                                        </div>
                                    </div>
                                    <div class="swiper-slide mb-2 lg:mb-4">
                                        <div class="imgBox aspect-square bg-gray-200 rounded-md overflow-hidden">
                                            <img src="https://picsum.photos/150/150?random=4" alt="縮圖 4"
                                                class="w-full h-full object-cover" />
                                        </div>
                                    </div>
                                    <div class="swiper-slide mb-2 lg:mb-4">
                                        <div class="imgBox aspect-square bg-gray-200 rounded-md overflow-hidden">
                                            <img src="https://picsum.photos/150/150?random=5" alt="縮圖 5"
                                                class="w-full h-full object-cover" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 按鈕區域 -->
            <section class="btnBox mb-8">
                <div class="flex gap-4 flex-wrap">
                    <button
                        class="pdf-button bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md font-medium transition-colors duration-200">
                        拍賣單位公告
                    </button>
                    <button
                        class="print-button bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors duration-200 flex items-center gap-2">
                        <i class="fa-solid fa-print"></i>
                        列印
                    </button>
                </div>
            </section>

            <!-- 地圖區域 -->
            <section class="map_wrap mb-8">
                <div class="topic mb-4">
                    <p class="text-xl font-bold text-gray-800 border-l-4 border-blue-600 pl-4">【 電子地圖 】</p>
                </div>
                <div class="map-container bg-gray-200 rounded-lg overflow-hidden shadow-md">
                    <iframe
                        src="https://maps.google.com.tw/maps?f=q&source=s_q&hl=zh-TW&geocode=&q=台北市信義區市府路45號&ie=UTF8&z=16&output=embed"
                        class="w-full h-96 border-0" allowfullscreen="" loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
            </section>

            <!-- 諮詢表單 -->
            <section class="mb-8">
                <div class="topic mb-6">
                    <p class="text-xl font-bold text-gray-800 border-l-4 border-blue-600 pl-4">【 免費諮詢 】</p>
                </div>
                <form class="queryForm bg-gray-50 p-6 rounded-lg shadow-sm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-row">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="name">
                                姓名 <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="name" name="name" required placeholder="請輸入您的姓名"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>

                        <div class="form-row">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="tel">室內電話</label>
                            <input type="tel" id="tel" name="tel" placeholder="ex xxx-xxxxxxxx#ext"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>

                        <div class="form-row">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="mobile">
                                行動電話 <span class="text-red-500">*</span>
                            </label>
                            <input type="tel" id="mobile" name="mobile" required
                                placeholder="ex 09123456789" pattern="09[1-8][0-9]([\-|\s]?)[0-9]{3}\1[0-9]{3}"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>

                        <div class="form-row">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="address">案件地址</label>
                            <input type="text" id="address" name="memo" value="台北市信義區 市府路45號"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>

                        <div class="form-row md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2" for="email">
                                電子信箱 <span class="text-red-500">*</span>
                            </label>
                            <input type="email" id="email" name="email" required
                                placeholder="ex <EMAIL>"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                        </div>
                    </div>

                    <div class="mt-6">
                        <button type="submit"
                            class="w-full md:w-auto bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-md font-medium transition-colors duration-200">
                            送出
                        </button>
                    </div>
                </form>
            </section>

            <!-- 超值物件 -->
            <section class="bestvalue_wrap">
                <div class="topic mb-6">
                    <p class="text-xl font-bold text-gray-800 border-l-4 border-blue-600 pl-4">【 超值物件 】</p>
                </div>
                <div class="bestvalue-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <!-- 模擬物件卡片 -->
                    <div
                        class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                        <div class="aspect-video bg-gray-200">
                            <img src="https://picsum.photos/300/200?random=10" alt="物件圖片"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">台北市信義區豪宅</h3>
                            <p class="text-sm text-gray-600 mb-2">3房2廳2衛，35坪</p>
                            <p class="text-lg font-bold text-blue-600">NT$ 2,800萬</p>
                        </div>
                    </div>

                    <div
                        class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                        <div class="aspect-video bg-gray-200">
                            <img src="https://picsum.photos/300/200?random=11" alt="物件圖片"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">新北市板橋區公寓</h3>
                            <p class="text-sm text-gray-600 mb-2">2房1廳1衛，25坪</p>
                            <p class="text-lg font-bold text-blue-600">NT$ 1,200萬</p>
                        </div>
                    </div>

                    <div
                        class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                        <div class="aspect-video bg-gray-200">
                            <img src="https://picsum.photos/300/200?random=12" alt="物件圖片"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">桃園市中壢區透天</h3>
                            <p class="text-sm text-gray-600 mb-2">4房3廳3衛，45坪</p>
                            <p class="text-lg font-bold text-blue-600">NT$ 1,680萬</p>
                        </div>
                    </div>

                    <div
                        class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                        <div class="aspect-video bg-gray-200">
                            <img src="https://picsum.photos/300/200?random=13" alt="物件圖片"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-800 mb-2">台中市西屯區新成屋</h3>
                            <p class="text-sm text-gray-600 mb-2">3房2廳2衛，30坪</p>
                            <p class="text-lg font-bold text-blue-600">NT$ 1,550萬</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // 縮圖輪播初始化
        const thumbnailSwiper = new Swiper('.thumbnail-swiper', {
            direction: window.innerWidth >= 1024 ? 'vertical' : 'horizontal',
            slidesPerView: window.innerWidth >= 1024 ? 4 : 5,
            spaceBetween: 10,
            freeMode: true,
            watchSlidesProgress: true,
            breakpoints: {
                1024: {
                    direction: 'vertical',
                    slidesPerView: 4,
                },
                0: {
                    direction: 'horizontal',
                    slidesPerView: 5,
                }
            }
        });

        // 主圖輪播初始化
        const mainSwiper = new Swiper('.main-swiper', {
            spaceBetween: 10,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            thumbs: {
                swiper: thumbnailSwiper,
            },
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            loop: true,
        });

        // 按鈕功能
        document.querySelector('.pdf-button').addEventListener('click', function() {
            alert('開啟拍賣單位公告');
        });

        document.querySelector('.print-button').addEventListener('click', function() {
            window.print();
        });

        // 表單提交處理
        document.querySelector('.queryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('表單已提交！（這是示例功能）');
        });

        // 響應式處理
        window.addEventListener('resize', function() {
            if (thumbnailSwiper) {
                if (window.innerWidth >= 1024) {
                    thumbnailSwiper.changeDirection('vertical');
                } else {
                    thumbnailSwiper.changeDirection('horizontal');
                }
            }
        });
    </script>
</body>

</html>
