基本用法
Formatters 格式化器
Faker\Provider\Base 基本
$randomDigit = $this->faker->randomDigit,//生成0-9之間的隨機數
$randomDigitNotNull = $this->faker->randomDigitNotNull,//生成1-9之間的隨機數
$randomNumber = $this->faker->randomNumber(5, true),//生成5位整數，true表示嚴格模式，即只能5位
$randomFloat = $this->faker->randomFloat(2, 0, 10),//生成浮點數，兩位小數點，範圍是0-10之間
$numberBetween = $this->faker->numberBetween(0, 100),//生成隨機整數，範圍是0-100之間
$randomLetter = $this->faker->randomLetter,//返回a-z之間任意的一個小寫字符
$randomElements = $this->faker->randomElements(['a', 'b', 'c', 'd'], 2),//返回數組中的隨機兩個元素
$randomElement = $this->faker->randomElement(['aa', 'bb', 'cc', 'dd']),//隨機返回數組中的一個元素
$suffle = $this->faker->shuffle('hello, world'); //將字串中的字符打亂返回
$suffle = $this->faker->shuffle(['aa', 'bb', 'cc', 'dd']); //將數組中的元素打亂返回
$numerify = $this->faker->numerify('Hello #####'),//#####替換為隨機數字，輸出類似：Hello 03501
$lexify = $this->faker->lexify('Hello ???'),//???替換為3個隨機小寫字符，輸出類似：Hello krg
$bothify = $this->faker->bothify('hello ##??'),//#替換為隨機數字,?替換為隨機小寫字符.輸出類似：hello 15cr
$asciify = $this->faker->asciify('hello *****'),//*替換為隨機字符，輸出類似：hello 5Ynt[
$regexify = $this->faker->regexify('[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}'),//根據正則表達式返回字串
Faker\Provider\Lorem 文本
$word = $this->faker->word,//返回一個單詞
$words = $this->faker->words(3, false),//返回3個單詞，false表示返回一個數組；true表示返回一個字符串，單詞之間用空格分開
$sentence = $this->faker->sentence(5, true),//返回一個句子，false表示只能含有5個單詞，true表示可以在5個單詞左右
$sentences = $this->faker->sentences(3, false),//返回3條句子，false表示返回一個數組，true表示將三條句子拼成一條返回
$paragraph = $this->faker->paragraph(3, true),//返回一個段落，由3條句子組成。false表示只能有3條句子，true表示可以在3條句子左右
$paragraphs = $this->faker->paragraphs(4, false),//返回4個段落。false表示返回一個數組，true表示將段落拼接在一起，並且用換行符分割
$text = $this->faker->text(200),//返回一段文本，最多只能含有200個字符
Faker\Provider\en_US\Person 人物
$title = $this->faker->title('female'),//參數：title($gender = null|'male'|'female') .返回稱呼。例如：Mrs.|Prof.|Dr.
$titleMale = $this->faker->titleMale,//返回男性稱呼
$titleFemale = $this->faker->titleFemale,//返回女性稱呼
$name = $this->faker->name('female'),//參數：name($gender = null|'male'|'female') .返回姓名
$firstName = $this->faker->firstName('female'),//參數：firstName($gender = null|'male'|'female') .返回名
$firstNameMale = $this->faker->firstNameMale,//男性名字
$firstNameFemale = $this->faker->firstNameFemale,//女性名字
$lastName = $this->faker->lastName,//姓
Faker\Provider\en_US\Address 地址
$cityPrefix = $this->faker->cityPrefix,//城市前綴.如：Lake
$secondaryAddress = $this->faker->secondaryAddress,//二級地址.如：Suite 061
$state = $this->faker->state,//州、省（如：Colorado、四川省）
$stateAbbr = $this->faker->stateAbbr,//省份簡稱.如：晉、蒙、浙、冀
$citySuffix = $this->faker->citySuffix,//城市後綴.如：side、land、port、Ville
$streetSuffix = $this->faker->streetSuffix,//街道後綴.如：Ramp、Plains
$buildingNumber = $this->faker->buildingNumber,//建築物編號
$city = $this->faker->city,//城市
$streetName = $this->faker->streetName,//街道名稱
$streetAddress = $this->faker->streetAddress,//街道地址
$postcode = $this->faker->postcode,//郵政編碼
$address = $this->faker->address,//地址（城市+區）
$country = $this->faker->country,//國家
$latitude = $this->faker->latitude,//緯度 latitude($min = -90, $max = 90)
$longitude = $this->faker->longitude,//經度 longitude($min = -180, $max = 180)
Faker\Provider\en_US\PhoneNumber 電話號碼
$phoneNumber = $this->faker->phoneNumber,//手機號碼
$tollFreePhoneNumber = $this->faker->tollFreePhoneNumber;
$e164PhoneNumber = $this->faker->e164PhoneNumber;
Faker\Provider\en_US\Company 公司
$catchPhrase = $this->faker->catchPhrase,//口號
$bs = $this->faker->bs;
$company = $this->faker->company,//公司名稱
$companySuffix = $this->faker->companySuffix,//公司名稱後綴
$jobTitle = $this->faker->jobTitle,//職稱
Faker\Provider\en_US\Text 文本
$realText = $this->faker->realText(),//一段敘事文本
Faker\Provider\DateTime 日期時間
$unixTime = $this->faker->unixTime,//返回隨機時間戳
$unixTime = $this->faker->unixTime('now'),//返回隨機時間戳 可選最後截止時間
$dateTime = $this->faker->dateTime,//返回一個隨機的DateTime對象
$dateTime = $this->faker->dateTime('now', 'PRC'),//返回一個隨機的DateTime對象，可選擇最後截止時間和時區
$dateTimeAD = $this->faker->dateTimeAD,//返回一個隨機的DateTime對象
$dateTimeAD = $this->faker->dateTimeAD('now', 'PRC'),//返回一個隨機的DateTime對象，可選擇最後截止時間和時區
$iso8601 = $this->faker->iso8601,//返回一個隨機的字符串形式的時間
$iso8601 = $this->faker->iso8601('now'),//返回一個隨機的字符串形式的時間,可選擇最後截止時間
$date = $this->faker->date("Y-m-d H:i:s", 'now'),//指定格式返回時間,可選擇最後截止時間
$time = $this->faker->time('Y-m-d H:i:s', 'now'),//(同上)指定格式返回時間,可選擇最後截止時間
$dateTimeBetween = $this->faker->dateTimeBetween('2019-01-01', 'now', 'PRC'),//返回指定時間區間的DateTime對象，可選時區
$dateTimeInInterval = $this->faker->dateTimeInInterval('-5 years', '+5 days', 'PRC'),//返回指定時間區間的DateTime對象，可選時區(第一個參數：開始時間，第二個參數：時間範圍)
$dateTimeThisCentury = $this->faker->dateTimeThisCentury,//返回一個本世紀內的DateTime對象
$dateTimeThisCentury = $this->faker->dateTimeThisCentury('1950-01-01', 'PRC'),//返回一個本世紀內的DateTime對象.指定截止時間和時區
$dateTimeThisDecade = $this->faker->dateTimeThisDecade,//返回一個前十年內的DateTime對象
$dateTimeThisDecade = $this->faker->dateTimeThisDecade('2015-01-01', 'PRC'),//返回一個前十年內的DateTime對象.指定允許的最後時間和時區
$dateTimeThisYear = $this->faker->dateTimeThisYear,//返回一個前一年內的DateTime對象
$dateTimeThisYear = $this->faker->dateTimeThisYear('now', 'PRC'),//返回一個前一年內的DateTime對象.指定允許的最後時間和時區
$dateTimeThisMonth = $this->faker->dateTimeThisMonth,//返回一個前一個月內的DateTime對象
$dateTimeThisMonth = $this->faker->dateTimeThisMonth('-15 days', 'PRC'),//返回一個前一個月內的DateTime對象.指定允許的最後時間和時區
$amPm = $this->faker->amPm,//上午/下午
$dayOfMonth = $this->faker->dayOfMonth,//返回幾號
$dayOfWeek = $this->faker->dayOfWeek,//返回星期幾
$month = $this->faker->month,//返回月份
$monthName = $this->faker->monthName,//返回月份名稱
$year = $this->faker->year,//返回年份
$year = $this->faker->year('2000-01-01'),//返回年份.可指定最後截止日期
$timezone = $this->faker->timezone,//返回時區
Faker\Provider\Internet 互聯網
$email = $this->faker->email,//返回一個隨機郵箱
$safeEmail = $this->faker->safeEmail,//返回一個以@example.com結尾的安全郵箱
$freeEmail = $this->faker->freeEmail,//返回一個隨機郵箱
$companyEmail = $this->faker->companyEmail,//返回企業郵箱（中文語言包下不可用）
$freeEmailDomain = $this->faker->freeEmailDomain,//返回一個郵件域名
$safeEmailDomain = $this->faker->safeEmailDomain,//返回安全的郵件域名
$userName = $this->faker->userName,//用戶名
$password = $this->faker->password,//密碼
$domainName = $this->faker->domainName,//域名（中文語言包下不可用）
$domainWord = $this->faker->domainWord,//不帶後綴的域名（中文語言包下不可用）
$tld = $this->faker->tld,//域名後綴：如com、org（中文語言包下不可用）
$url = $this->faker->url,//返回一個隨機url（中文語言包下不可用）
$slug = $this->faker->slug;
$ipv4 = $this->faker->ipv4,//返回一個ipv4地址
$ipv6 = $this->faker->ipv6,//返回一個ipv6地址
$localIpv4 = $this->faker->localIpv4;
$macAddress = $this->faker->macAddress,//mac地址
Faker\Provider\UserAgent 用戶代理
$userAgent = $this->faker->userAgent,//返回一個隨機的用戶代理信息
$chrome = $this->faker->chrome,//返回一個chrome瀏覽器的用戶代理信息
$firefox = $this->faker->firefox,//返回一個firefox瀏覽器的用戶代理信息
$safari = $this->faker->safari,//返回一個safari瀏覽器的用戶代理信息
$opera = $this->faker->opera,//返回一個opera瀏覽器的用戶代理信息
$internetExplorer = $this->faker->internetExplorer,//返回一個internetExplorer瀏覽器的用戶代理信息
Faker\Provider\Payment 支付
$creditCardType = $this->faker->creditCardType,//信用卡類型
$creditCardNumber = $this->faker->creditCardNumber,//信用卡號
$creditCardExpirationDate = $this->faker->creditCardExpirationDate,//信用卡到期日 (DateTime對象)
$creditCardExpirationDateString = $this->faker->creditCardExpirationDateString,//信用卡到期日期字符串
$creditCardDetails = $this->faker->creditCardDetails,//信用卡詳情（數組）
$swiftBicNumber = $this->faker->swiftBicNumber;
$iban = $this->faker->iban,//國際銀行賬戶
Faker\Provider\Color 顏色
$hexcolor = $this->faker->hexcolor,//十六進制的隨機色：'#fa3cc2'
$rgbcolor = $this->faker->rgbcolor,//RGB格式的隨機色（字串形式）：'0,255,122'
$rgbColorAsArray = $this->faker->rgbColorAsArray,//RGB格式的隨機色（數組形式）：array(0,255,122)
$rgbCssColor = $this->faker->rgbCssColor,//RGB格式的隨機色的css表示：'rgb(0,255,122)'
$safeColorName = $this->faker->safeColorName,//一個安全的隨機色名稱
$colorName = $this->faker->colorName,//隨機色名稱
Faker\Provider\File 文件
$fileExtension = $this->faker->fileExtension,//文件後綴
$mimeType = $this->faker->mimeType,//mime類型
// 將一個隨機文件從源文件復制到目標目錄，並返回fullpath
$file = $this->faker->file($sourceDir = './up1', $targetDir = './up2');
// 將一個隨機文件從源文件復制到目標目錄，並返回basename
$file = $this->faker->file($sourceDir = './up1', $targetDir = './up2', false);
Faker\Provider\Image 圖片
$imageUrl = $this->faker->imageUrl(),//https://lorempixel.com/640/480/?93028
$imageUrl = $this->faker->imageUrl(320, 320, 'cats'),//https://lorempixel.com/320/320/cats/?68416
$imageUrl = $this->faker->imageUrl(320, 320, 'cats', true, 'Faker'),//https://lorempixel.com/320/320/cats/Faker/?68118
$imageUrl = $this->faker->imageUrl(320, 320, 'cats', true, 'Faker', true),//https://lorempixel.com/gray/320/320/cats/Faker/?28732

//生成圖片並保存到本地 '/tmp/13b73edae8443990be1aa8f1a483bc27.jpg'
$image = $this->faker->image('./tmp', 320, 320);
//設置類別並生成圖片保存到本地  'tmp/13b73edae8443990be1aa8f1a483bc27.jpg'
$image = $this->faker->image('./tmp', 320, 320, 'cats');
//設置類別並生成圖片保存到本地，只返回文件名 '13b73edae8443990be1aa8f1a483bc27.jpg'
$image = $this->faker->image('./tmp', 320, 320, 'cats', false);
// 生成沒有隨機化的圖像
$image = $this->faker->image('./tmp', 320, 320, 'cats', true, false);
//生成圖片並加上水印字體  'tmp/13b73edae8443990be1aa8f1a483bc27.jpg'
$image = $this->faker->image('./tmp', 320, 320, 'cats', true, true, 'Faker');
Faker\Provider\Uuid UUID
$uuid = $this->faker->uuid,//生成一個uuid
Faker\Provider\Barcode 條形碼
$ean13 = $this->faker->ean13,//8105964964473
$ean8 = $this->faker->ean8,//05994441
$isbn13 = $this->faker->isbn13,//9780971408739
$isbn10 = $this->faker->isbn10,//**********
Faker\Provider\Miscellaneous 各種各樣的
$boolean = $this->faker->boolean,//生成一個布爾值
$md5 = $this->faker->md5,//生成一個md5碼
$sha1 = $this->faker->sha1,//生成一個sha1
$sha256 = $this->faker->sha256,//生成一個sha256
$locale = $this->faker->locale,//生成一個地區編碼：如zh_CN
$countryCode = $this->faker->countryCode,//生成一個國家編碼。如：UK
$languageCode = $this->faker->languageCode,//生成一個語言編碼.如：en
$currencyCode = $this->faker->currencyCode,//生成一個貨幣代碼.如：EUR
$emoji = $this->faker->emoji,//生成一個emoji表情
Faker\Provider\Biased 偏向的
//得到10到20之間的隨機數，並且更多機會接近20
$biasedNumberBetween = $this->faker->biasedNumberBetween($min = 10, $max = 20, $function = 'sqrt');
Faker\Provider\HtmlLorem html文本
//生成不超過2級深度的HTML文檔，並且在任何級別上都不超過3個元素。
$randomHtml = $this->faker->randomHtml(2, 3);
Language specific formatters 特定於語言的格式器
Faker\Provider\zh_CN\Payment
// 生成一個隨機的銀行名稱(基於真實的中國銀行的列表)
echo $this->faker->bank; // '中國建設銀行'
Faker\Provider\zh_TW\Person
// Generates a random personal identify number
echo $this->faker->personalIdentityNumber; // A2********
Faker\Provider\zh_TW\Company
// Generates a random VAT / Company Tax number
echo $this->faker->VAT; //********
