<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

/***
"功能名稱":"seeder - 建立假資料預設要",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 17:05:17",
***/
class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        // $files_arr = scandir(dirname(__FILE__)); //store filenames into $files_array
        // foreach ($files_arr as $key => $file) {
        //     try {
        //         if ('migrationsSeeder.php' !== $file && 'DatabaseSeeder.php' !== $file && 'baseSeeder.php' !== $file && '.' !== $file[0]) {
        //             $files = explode('.', $file);
        //             if ('php' == $files[1]) {
        //                 $this->call('\\Database\\Seeders\\'.$files[0]);
        //             }
        //         }
        //     } catch (\Exception $e) {
        //         echo $e->getMessage();
        //     }
        // }

        $this->call(adminuserSeeder::class);
        $this->call(city1Seeder::class);
        $this->call(city2Seeder::class);
        //  $this->call(kindheadSeeder::class);
        //  $this->call(kindmainSeeder::class);
        //$this->call(productSeeder::class);
        //$this->call(feedbackSeeder::class);
        //$this->call('feedbackSeeder.php');

        //$this->command->info('User table seeded!');
    }
}
