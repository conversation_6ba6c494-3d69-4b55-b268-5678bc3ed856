<?php

namespace App\Listeners;


use Illuminate\Console\Events\ScheduledTaskStarting as Event;

class LogScheduledTaskStarting {



    public function handle($event) {



        $projectName = \config('config.name') . "-" . \config('app.env');


        //echo $projectName;
        $event->task->description = $projectName . " - " . $event->task->description;
        //$event->task->runInBackground()->withoutOverlapping()->emailOutputTo('<EMAIL>');
        $event->task->withoutOverlapping();
    }
}
