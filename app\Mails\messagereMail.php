<?php

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use PF;
use App\Repositories\messageRepository;

//留言版
class messagereMail extends Mailable
{
    public $tries = 3;
    use Queueable;
    use SerializesModels;
    public $id;
    public $data;
    private $messageRepo;

    // 讓外部可以將參數指定進來
    public function __construct($id)
    {
        $this->id = $id;
        $this->data = $data;
        $this->messageRepo = app(messageRepository::class);
    }

    public function build()
    {
        $rows = $this->messageRepo->selectRaw('message.*');
        $rows->myWhere('id|N', $this->id, 'key', 'N');
        $rows->orderBy('id', 'desc');
        $rows = $rows->take(1);
        //PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }

            $this->from(PF::getFromEmail(), config('config.name'));
            $this->subject('留言版回覆');
            $this->view('email.messagere');

            //$this->view('email.raw');
            //$this->attach(storage_path('app/message.xlsx'));

            $this->with($this->data);
            //$this->to($rs->email);
            $this->to(explode(';', $rs->email));
            //$this->cc(config('config.email'));
            $this->bcc(explode(';', config('config.email')));
        } else {
            throw new \CustomException('No data');
        }

        return $this;
    }
}
