<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Exception,Session,Config,DB;
use PF,PT,Auth;



class formquerylogController extends adminController
{

    private $fieldnicknames;
	private $data;	
    private $xmlDoc;
	 /**
     *TODO 建構子
     */
    public function __construct()
    {
      
            //$this->limit="xx";         
            parent::__construct();            
            //將request全部導入到$this->data變數中
            $this->data=PF::requestAll($this->data);
            $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
            
            // FIXME 導覽列            
            
            $this->data['nav'] = PT::nav($this->data['xmldoc'],"formquerylog",$this->data['nav']);
            
            // FIXME 共用的hidden變數
            $this->data['hiddens']=[];

        $this->db = new \App\Models\formquerylog();
        $this->fieldnicknames = $this->db->fieldnicknames;
           
      
        
    }
    
    /**
     * TODO 資料列表
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
      
      
            //FIXME 定義那些欄位可以搜尋
            $fieldsearchname=[
                '' => '請選擇',
				'pagename'=>$this->fieldnicknames["pagename"],
'pathinfo'=>$this->fieldnicknames["pathinfo"],
'formbody'=>$this->fieldnicknames["formbody"],
'querybody'=>$this->fieldnicknames["querybody"],
		
                
            ];
            //FIXME 定義那些日期欄位可以搜尋
            $fieldsearchdatename=[
               
'created_at'=>$this->fieldnicknames["created_at"],
'updated_at'=>$this->fieldnicknames["updated_at"],

            ];
            $this->data['fieldsearchname']=$fieldsearchname;
            $this->data['fieldsearchdatename']=$fieldsearchdatename;           
             
            
            $rows=$this->getRows($request);
            
            //$rows->limit(1);
            //PF::dbSqlPrint($rows);
            $rows = $rows->paginate(10);            			
            // 顯示sqlcmd
        	
            
            $this->data['rows'] = $rows;            
            return view('admin.formquerylog.index', [
                'data' => $this->data,
            ]);
   
   
    }
     /**
     * 資料Rows
     *
     * @return \Illuminate\Http\Response
     */
    public function getRows($request)
    {
        $rows = DB::table('formquerylog')->selectRaw('formquerylog.*');
            
             $rows->myWhere($request->input('searchname'),$request->input('search'), $this->fieldnicknames, 'N');
            
	
	        //依條件時間搜尋資料的SQL語法    
            $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|>=', $request->input('searchstartdate'), $this->fieldnicknames, 'N');
            $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|<=', $request->input('searchenddate'), $this->fieldnicknames, 'N');
	
            if ($request->input('sortname')){
                $rows->orderBy($request->input('sortname'), $request->input('sorttype')=="desc"  ? $request->input('sorttype') : "asc"  );
            }else{
                
                $rows->orderBy('id', 'desc');
            }            
            return $rows;

    }
    /**
     * TODO 資料建立
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
    
            
            return view('admin.formquerylog.edit', [
                'data' => $this->data,
            ]);
    
    }
  

    /**
     * 資料單一詳細頁     
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
    }

    /**
     * TODO 資料編輯顯示
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
     
       $edit = $request->input('edit');

        $validators = null;
        $validators['edit'] = 'required';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        } 
        
        $rows = DB::table('formquerylog');
        $rows->select('formquerylog.*');
              
        $rows->Where('id', '=', $edit);
        $rows->limit(1);
        
        //dd($row);        
        //PF::dbSqlPrint($rows);
        
        if ($rows->count() > 0) {
            //將資料庫欄位與值全部導入到$this->data
            foreach ($rows->first() as $key => $value) {                        
                $this->data[$key] = $value;
            }
            
        } else {
            throw new \CustomException("no data");
        }
  
               

        return view('admin.formquerylog.edit', [
                'data' => $this->data,
            ]);
  
    }

    /**
     * TODO 資料新增編輯儲存
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
  
    
  		$edit=$request->input('edit');
        //FIXME 那些欄位為必填判斷
        $validators = null;
       
		

        $validator = \Validator::make($this->data, $validators);
        
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = null;

        $inputs['pagename']=$this->data['pagename'];/*功能名稱-*/	
$inputs['pathinfo']=$this->data['pathinfo'];/*程式位置-*/	
$inputs['formbody']=$this->data['formbody'];/*FORM欄位值-*/	
$inputs['querybody']=$this->data['querybody'];/*get內容-*/	

       
        if ('' ==$edit) {
             $inputs['created_at']=date('Y-m-d H:i:s');/*建立時間-*/	

         //PF::printr($inputs);exit(); 
            $row=$this->db->create($inputs);
            $edit=$row->id;
            $this->data['alert']='新增成功';
        } else {    
             //PF::printr($inputs); exit(); 
            $this->db->myWhere('id|N', $edit, 'edit', 'Y')->update($inputs);       
            $this->db::find($edit)->update($inputs);
            $this->data['alert']='更新成功';
        }       
        
        //Session::flash('msg', $msg);
        //return back()->with('success','update ok');
        //return back()->with('js','alert(\'更新成功\')');
        return view('admin.layouts/postsubmit',  [
                'data' => $this->data,
            ]);
  
     
    }

    /**
     * TODO 資料刪除
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {

                


        $rows=$this->db->myWhere('id|ININT', $this->data['del'],'del','Y');
        //PF::dbSqlPrint($rows);
        $rows->get()->each(function($row){ 
            $row->delete();
        });

        

        //$this->db::destroy(explode(",", $this->data['del']));
  
        return view('admin/layouts/postsubmit', [
                'data' => $this->data,
            ]);
      
    }
    
}
