<?php

namespace App\Jobs;

use PF;
use Mail;
use Config;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class Send<PERSON>mailJob implements ShouldQueue {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    protected $object;

    public function __construct($object) {
        $this->object = $object;
    }

    public function handle() {

        $ebody = $this->object['body'];

        // 要處理比較久的事情就丟這裡
        //Mail::to($this->to)->queue(new Send($this->config, $this->data));
        Mail::send([], [], function ($message) use ($ebody) {
            $message->from(config('mail.from.address'), config('mail.from.name'));
            $message->to($this->object['to'], '管理者');
            $message->subject($this->object['subject']);
            $message->setBody($ebody, 'text/html');
        });
    }
}
