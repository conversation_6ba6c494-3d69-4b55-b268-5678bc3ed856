@extends('layouts.master')
@section('css')

@stop

@section('js')

@stop

@section('content')
    <section id="section" style="height:350px;vertical-align: bottom;">
        <div class="page-wrap d-flex flex-row align-items-center p-5">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-12 text-center">
                        <BR><BR><BR><BR><BR>
                        <div class="mb-4 lead">{!!  __($msg) !!}</div>
                        <button type="button" style="display:none" onClick="javascript:window.history.go(-1);return false;"
                            class="btn btn-warning">上一步</button>

                    </div>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            document.addEventListener("DOMContentLoaded", () => {

                var referrer = document.referrer;
                //console.log(["referrer", referrer]);
                if (referrer != "") {
                    $("button").show();
                }

            });
        </script>
    </section>

@stop
