<div class="row item col-xs-12 col-sm-6 col-md-4 col-lg-3 col-xl-3">
    <div class="mdc-card property-item grid-item column-4 full-width-page m-3">
        <div class="property-item">
            <div class="property-image etest">
                <a href="{{ url('/') }}/house/show/{{$rs->productid}}" itle="{{$rs->city1title}}{{$rs->city2title}}{{$rs->producttitle}}">
                    {{
                     Html::myUIImage([
                 'folder' => "images/product",
                 'filename' => $rs->img,
                 'alt' => $rs->title,
                //  'width' => 300,
                //  'height' => 300,
                'style' => 'width:390px',
                 'class' => 'img-fluid',
                     ])
             }}
                </a>
                <div class="hots">
                    <span>

                        @if (Str::contains($rs->location, ['new']))
                        <img src="{{ url('/') }}/images/new.png" alt="" width="40">
                        @endif
                        @if (Str::contains($rs->location, ['hot']))
                        <img src="{{ url('/') }}/images/hot.gif" alt="" width="40">
                        @endif
                    </span>
                </div>
                <div class="prices justify-content-between row w-100">
                    <span>
                        @if (Str::contains($rs->location, ['push1']))
                        <img src="{{ url('/') }}/images/p1.png" alt="" width="40" class="ml-2 mx-1">
                        @endif
                        @if (Str::contains($rs->location, ['push2']))
                        <img src="{{ url('/') }}/images/p2.png" alt="" width="40">
                        @endif
                    </span>
                    <span>{{$rs->totalupset}}萬</span>
                </div>
            </div>
        </div>
        <div class="property-content-wrapper">
            <div class="property-content">
                <div class="content">
                    <div class="features">
                        <h2 class="title">
                            <a href="{{ url('/') }}/house/show/{{$rs->productid}}" title="{{$rs->city1title}}{{$rs->city2title}}{{$rs->producttitle}}">
                                {{$rs->producttitle}}
                                @if (Str::contains($rs->location, ['push1']))
                                <img src="/images/p1.png" alt="" height="40">
                                @endif
                                @if (Str::contains($rs->location, ['push2']))
                                <img src="/images/p2.png" alt="" height="40">
                                @endif
                            </a>
                        </h2>
                        <span productid="{{$rs->productid}}">
                            {{$rs->city1title}}{{$rs->city2title}}{{$rs->address}}</span>
                        <br />
                        總坪: <b>{{$rs->pingtotalnumberof}}</b>坪<br />
                        類型: {{$rs->pattern}}<br />
                        屋齡: {{$rs->houseage}}年
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>