<?php

namespace [namespace];

use Livewire\Component;

class [class] extends Component
{
    public $city1;
    /*
    protected $validationAttributes = [
        'email' => 'email address'
    ];
    */
    public function __construct() {
        $this->[class]Repo = app(\App\Repositories\[class]Repository::class);

    }
    public function mount($city1) {
        $this->city1 = $city1;
    }

    public function render()
    {
        if ($this->city1 != "") {
            $rows = $this->[class]Repo->selectRaw('*');
            $rows->myWhere('city1title|S', $this->city1, "kind", 'N');
            $rows = $rows->get();
            $this->city2rows = $rows;
        }
        return view('[view]');
    }
    //wire:click="updatedXX"
    public function updatedXX() {

    }
}
