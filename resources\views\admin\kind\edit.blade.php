@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
    function oForm_onsubmit(form)
    {
        if (PF_FormMultiAll(form)==false){return false};
   PF_FieldDisabled(form)//將全部button Disabled
          return true;
        }        
    </SCRIPT>
<div class="card">
<div class="card-body">
<!--// TODO : 前端資料填寫-->

        <form name="oForm" id="oForm"  method="post" language="javascript" action="{{ URL::to('admin/kind/store') }}"  ENCTYPE="multipart/form-data" onsubmit="return oForm_onsubmit(this);"><!--novalidate-->


    
    
{{$data['pmmodify']}}        




    <div align="center">

        <button type="submit" class="btn btn-success">確定</button>        
        <button type="reset" class="btn btn-secondary">取消</button>
        <button type="reset" class="btn btn-secondary" onClick="javascript:window.history.go(-1)">回上一頁</button>
        
        {!!Session::get('success')!!}
    </div>
    @include('admin.layouts.hiddencommon')            
    {!! Form::hidden("edit",  $data['edit']) !!}
    {!! Form::hidden("gobackurl", $data['gobackurl'] ) !!}
    </form>
</div>
</div>


@endsection

