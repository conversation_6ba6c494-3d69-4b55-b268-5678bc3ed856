<?php

namespace App\Console\Commands;

use DB;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

/**
 * 產生 TypeScript 類型定義檔案的命令
 * 使用方式: php artisan generate:typescript-types
 */
class generateTypescriptTypesCommand extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:typescript-types {--table= : 指定特定資料表名稱}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '根據資料表結構產生 TypeScript 類型定義檔案';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle() {
        $this->info('開始產生 TypeScript 類型定義檔案...');

        // 檢查 types 目錄是否存在，不存在則建立
        $typesDir = resource_path('js/types');
        if (!is_dir($typesDir)) {
            mkdir($typesDir, 0755, true);
            $this->info("建立目錄: {$typesDir}");
        }

        // 取得指定的資料表或所有資料表
        $specificTable = $this->option('table');
        $tables = Schema::getAllTables();

        if ($specificTable) {
            $tables = collect($tables)->filter(function ($table) use ($specificTable) {
                return head($table) === $specificTable;
            })->all();

            if (empty($tables)) {
                $this->error("找不到資料表: {$specificTable}");
                return 1;
            }
        }

        $generatedCount = 0;

        foreach ($tables as $table) {
            $tableName = head($table);

            // 跳過系統資料表
            if (in_array($tableName, ['migrations', 'password_resets', 'failed_jobs', 'personal_access_tokens'])) {
                continue;
            }

            $this->info("處理資料表: {$tableName}");

            try {
                $this->generateTypeDefinition($tableName);
                $generatedCount++;
                $this->line("✓ 已產生 {$tableName}.d.ts");
            } catch (\Exception $e) {
                $this->error("產生 {$tableName} 類型定義時發生錯誤: " . $e->getMessage());
            }
        }

        $this->info("完成！共產生了 {$generatedCount} 個 TypeScript 類型定義檔案。");
        return 0;
    }

    /**
     * 為指定資料表產生 TypeScript 類型定義
     *
     * @param string $tableName
     * @return void
     */
    private function generateTypeDefinition(string $tableName): void {
        // 取得資料表欄位資訊
        $columns = DB::select("SHOW FULL COLUMNS FROM {$tableName}");

        // 取得資料表註解
        $tableComment = $this->getTableComment($tableName);

        // 產生介面內容
        $interfaceContent = $this->buildInterfaceContent($tableName, $columns, $tableComment);

        // 寫入檔案
        $fileName = "{$tableName}.d.ts";
        $filePath = resource_path("js/types/{$fileName}");

        file_put_contents($filePath, $interfaceContent);
    }

    /**
     * 取得資料表註解
     *
     * @param string $tableName
     * @return string
     */
    private function getTableComment(string $tableName): string {
        $comments = DB::select("SHOW TABLE STATUS LIKE '{$tableName}'");
        return isset($comments[0]->Comment) ? $comments[0]->Comment : '';
    }

    /**
     * 建立介面內容
     *
     * @param string $tableName
     * @param array $columns
     * @param string $tableComment
     * @return string
     */
    private function buildInterfaceContent(string $tableName, array $columns, string $tableComment): string {
        $interfaceName = Str::camel($tableName);
        $properties = [];

        foreach ($columns as $column) {
            $fieldName = $column->Field;
            $fieldType = $this->mapDatabaseTypeToTypeScript($column->Type);
            $isNullable = $column->Null === 'YES';
            $comment = $column->Comment;

            // 處理可選欄位（nullable 或有預設值）
            $optional = $isNullable || $column->Default !== null ? '?' : '';

            // 建立屬性註解
            $propertyComment = $comment ? "  {$comment}" : '';
            $propertyComment = trim($comment);

            $properties[] = "        /** {$propertyComment} */\n        {$fieldName}{$optional}: {$fieldType}";
        }

        $propertiesString = implode("\n", $properties);
        $tableCommentLine = $tableComment ? " * {$tableComment}\n" : '';

        return <<<TS
/**
 * {$tableName} 資料表類型定義
 *{$tableCommentLine} */
declare global {
    interface {$interfaceName} {
{$propertiesString}
    }
}

export {}

TS;
    }

    /**
     * 將資料庫欄位類型對應到 TypeScript 類型
     *
     * @param string $databaseType
     * @return string
     */
    private function mapDatabaseTypeToTypeScript(string $databaseType): string {
        // 移除括號內的長度限制
        $baseType = preg_replace('/\([^)]*\)/', '', $databaseType);

        switch (true) {
            case Str::contains($baseType, ['int', 'bigint', 'tinyint', 'smallint', 'mediumint']):
                return 'number';

            case Str::contains($baseType, ['decimal', 'float', 'double']):
                return 'number';

            case Str::contains($baseType, ['varchar', 'char', 'text', 'mediumtext', 'longtext']):
                return 'string';

            case Str::contains($baseType, ['date', 'datetime', 'timestamp', 'time']):
                return 'string';

            case Str::contains($baseType, ['json']):
                return 'any';

            case Str::contains($baseType, ['enum']):
                // 嘗試解析 enum 值
                if (preg_match('/enum\((.*)\)/', $databaseType, $matches)) {
                    $enumValues = str_replace("'", '"', $matches[1]);
                    return $enumValues;
                }
                return 'string';

            case Str::contains($baseType, ['boolean', 'bool']):
                return 'boolean';

            default:
                return 'any';
        }
    }
}
