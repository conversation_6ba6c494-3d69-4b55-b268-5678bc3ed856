<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use PF;
use DB;

class apiController extends adminController
{
    private $fieldnicknames;
    private $data;
    private $xmlDoc;
    private $sSQLCmd;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";

        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function searchchange(Request $request)
    {
        try {
        switch ($this->data['xmltitle']) {
            case '考卷種類':
            $this->sSQLCmd = "select kindid,kindtitle from kind where kind='qakind'";
            break;
            case '收貨縣市':
            case '店家縣市':
            case '縣市':
             $this->sSQLCmd = 'select city1title,city1title from city1';
             break;
            case '商品第一層種類':
             $this->sSQLCmd = 'select kindheadid,kindheadtitle from kindhead';
             break;
             case '商品種類':
             $this->sSQLCmd = "select kindid,kindtitle from kind where kind='productkind' order by alg,kindsortnum";
             break;
             case '產品':
             $this->sSQLCmd = 'select productid,title from product order by title';
            break;
             case '負責業務':
             $this->sSQLCmd = 'select userid,name from adminuser order by name';
             break;
             case '店家鄉鎮':
             $this->sSQLCmd = 'select city1title,city1title from city1 order by sortnum desc';
             break;
             case '客戶地區':
             $this->sSQLCmd = 'select partid,parttitle from citypart order by parttitle';
             break;
             case '最後操作者':
             $this->sSQLCmd = 'select account,account from adminuser order by account';
              
             break;
            case '會員':
               $this->sSQLCmd = 'select memberid,name from member order by name';
               break;
            break;
        }
            if ('' != $this->data['xmltitle']) {
                if ('' != $this->sSQLCmd) {
                    //\DB::enableQueryLog();// 顯示sqlcmd
                    
                    $pdo = DB::getPdo();
                    $rows = $pdo->prepare($this->sSQLCmd);
                    $rows->setFetchMode(\PDO::FETCH_NUM);
                    $rows->execute();
                    $rows = $rows->fetchAll();
                    $htmlbody = '<select name="search" class="form-control">';
                    foreach ($rows as $rs) {                        
                        $htmlbody .= '<option value="'.$rs[0].'"';
                        if (trim($this->data['search']) == trim($rs[0])) {
                            $htmlbody .= ' selected';
                        }
                        $htmlbody .= '>'.$rs[1].'</option>';
                    }
                    $htmlbody .= '</select>';
                } else {
                    $this->data['xmltitle'] = str_replace(' ', '', $this->data['xmltitle']);
                    
                        if (count($this->data['xmldoc']->xpath('//參數設定檔/'.$this->data['xmltitle'].'/KIND')) > 0) {
                        $htmlbody = '<select name="search" class="form-control">';
                            foreach ($this->data['xmldoc']->xpath('//參數設定檔/'.$this->data['xmltitle'].'/KIND') as $v) {
                                if ('' != $v->傳回值) {
                                    $value = $v->傳回值;
                                } else {
                                    $value = $v->資料;
                                }
                                $htmlbody.='<option value="'.$value.'"';
                                if (trim($this->data['search']) == trim($value)) {
                                    $htmlbody.=' selected';
                                }
                                $htmlbody.= '>'.$v->資料.'</option>';
                            }
                            $htmlbody.='</select>';
                        }
                }
            } 
        } catch (\Exception $e) {
        }
        if ('' == $htmlbody) {
            $htmlbody='<input type="text" name="search"  class="form-control" value="'.htmlspecialchars($this->data['search']).'" size="10">';
        }

        return response($htmlbody);
    }

    public function datetime(Request $request)
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';

        try {
            $jsondata['datetime'] = date('Y-m-d H:i:s');
        } catch (\Exception $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        }

        return response()->json($jsondata);
    }

    
}
