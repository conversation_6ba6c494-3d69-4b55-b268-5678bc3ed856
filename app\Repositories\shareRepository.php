<?php

namespace App\Repositories;

use App\Models\share;
use DB;
use PF;

class shareRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(share $model)
    {
        $this->model = $model;
    }
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    public function view($rows)
    {
        $rows->join('diversionurl', 'diversionurl.id', '=', 'diversionlog.diversionurl_id');
        return $rows;
    }


    public function create($inputs)
    {

        return parent::create($inputs);
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        return  parent::update($inputs,$id,'id') ;      
    }

    public function deleteIds($ids)
    {
    //     $this->sharexxRepo = app(\App\Repositories\sharexxRepository::class);
    //     $rows = $this->sharexxRepo->select(null);
    //     $rows->myWhere('share_id|ININT', $ids, 'share_id', 'Y');
    //     $rows->delete();

        
    //    \DB::delete('delete sharesign from  sharesign INNER JOIN sharexx ON (sharexx.id=sharesign.sharexx_id) where sharexx.share_id in (?)',[$ids]);
        
        parent::deleteIds($ids);
      

        // $rows=$this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // $rows->get()->each(function ($rs) {
        //     $path=storage_path($this->model->table.'/'.$rs->img.".json");            
        //     //$path=public_path('images/'.$this->model->table.'/'.$rs->img);
        //     if (\File::exists($path)) {
        //        \File::delete($path);
        //     }            
        //     $rs->delete();
        // });
        
    }
}
