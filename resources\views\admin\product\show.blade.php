@extends('admin.layouts.master')

@section('css')
@stop


@section('js')
    <!-- <script language=JavaScript>
        $('#circle').removeClass();
        $('.loading').removeClass('show');
    </script> -->

@stop

@section('nav')
    {!! $data['nav'] !!}
@endsection

@section('content')

    <SCRIPT language=JavaScript>
        function oForm_onsubmit(form) {
            if (PF_FormMultiAll(form) == false) {
                return false
            };


            PF_FieldDisabled(form) //將全部button Disabled
            return true;
        }
    </SCRIPT>
    <div class="card">
        <div class="card-body">
            <!--// TODO : 前端資料填寫-->

            <script type="text/javascript">
                function oForm_onsubmit(form) {
                    if (!form.reportValidity()) {
                        checkValidity(window.event, form);
                        return false;
                    }
                    PF_FieldDisabled(form)
                    return true;
                }
            </script>

            <form name="oForm" id="oForm" method="post" language="javascript"
                action="{{ url('/') }}/admin/product/parse" onsubmit="return oForm_onsubmit(this);" novalidate>
                {{-- 將 body 的值設定給名為 'body' 的隱藏欄位 --}}
                <div class="form-group row">
                    <div class="col-md-6">
                        @if ($data['url'] != '')
                            <button type="button" onclick="window.open('{{ $data['url'] }}','_blank')"
                                class="btn btn-success btn-block">透明房訊</button>
                        @endif
                        {{ Form::hidden('url', $data['url']) }}
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-primary btn-block">重新解析</button>

                    </div>
                </div>
                <div class="form-group row">




                    <table width="100%" style='border:3px #cccccc solid;' cellpadding='10' border='1'>

                        <tr>
                            <td width="50%">

                                <textarea name="body" class="form-control" cols="37" rows="5" style='width:100%;height:700px'>{!! $data['body'] !!}</textarea>
                            </td>
                            <td>
                                <textarea name="body1" class="form-control" cols="37" rows="5" style='width:100%;height:700px'>{!! $data['body1'] !!}</textarea>
                            </td>
                        </tr>
                    </table>



            </form>






        </div>
    </div>

@stop
