<?php

namespace Tests\Feature;

use Tests\Feature\baseTest;
use App\Models\product;
use App\Models\feedback;
use Illuminate\Foundation\Testing\RefreshDatabase;

class houseTest extends baseTest {
    use RefreshDatabase;

    public function setUp(): void {
        parent::setUp();

        // 建立測試用的法拍物件資料
        $this->product = product::factory()->create([
            'online' => 1,
            'city1title' => '台北市',
            'city2title' => '大安區',
            'pattern' => '公寓',
            'beattime' => '3拍',
            'totalupset' => 1000,
            'houseage' => 10,
            'pingtotalnumberof' => 30,
            'img' => 'test.jpg',
            'courttranscript' => '法院筆錄內容'
        ]);
    }

    /**
     * 測試顯示法拍物件列表
     */
    public function test_顯示法拍物件列表_index() {
        // 模擬請求
        $response = $this->get('/house');

        // 檢查回應
        $response->assertStatus(200);
        $this->checkHtml($response);
        $response->assertViewIs('house.index');
    }

    /**
     * 測試顯示單一法拍物件詳細資料
     */
    public function test_顯示單一法拍物件詳細資料_show() {
        // 模擬請求
        $response = $this->get('/house/' . $this->product->productid);

        // 檢查回應
        $response->assertStatus(200);
        $this->checkHtml($response);
        $response->assertViewIs('house.show');
    }

    /**
     * 測試列印法拍物件資料
     */
    public function test_列印法拍物件資料_print() {
        // 模擬請求
        $response = $this->get('/house/print?productid=' . $this->product->productid);

        // 檢查回應
        $response->assertStatus(200);
        $this->checkHtml($response);
        $response->assertViewIs('house.print');
    }

    /**
     * 測試儲存聯絡表單
     */
    public function test_儲存聯絡表單_store() {
        // 建立測試資料
        $feedbackData = [
            'name' => '測試使用者',
            'email' => '<EMAIL>',
            'memo' => '測試留言內容',
            'google_recaptcha_token' => 'valid_token'
        ];

        // 模擬請求
        $response = $this->post('/house', $feedbackData);

        // 檢查回應
        $response->assertStatus(302);
        $this->assertDatabaseHas('feedback', [
            'email' => '<EMAIL>'
        ]);
    }

    /**
     * 測試顯示法院筆錄
     */
    public function test_顯示法院筆錄_courttranscript() {
        // 模擬請求
        $response = $this->get('/house/courttranscript?productid=' . $this->product->productid);

        // 檢查回應
        $response->assertStatus(200);
        $this->checkHtml($response);
        $response->assertViewIs('house.courttranscript');
    }
}
