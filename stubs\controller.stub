<?php

namespace {{ namespace }};

use {{ rootNamespace }}Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;
use Exception,DB;
use PF;
use App\Repositories\[+repositoryname+]Repository;
class {{ class }} extends Controller
{
    private $data;
    public function __construct([+repositoryname+]Repository $[+repositoryname+]Repo)
    {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->[+repositoryname+]Repo=$[+repositoryname+]Repo;
        $this->data['displaynames'] = $this->[+repositoryname+]Repo->getFieldTitleArray();
        
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        $rows = $this->[+name+]Repo->selectRaw('*');
        $rows->myWhere('[+name+]id|N', $this->data['[+name+]'], '[+name+]id', 'Y');
        $rows->orderByRaw('[+name+]id desc');
        $rows = $rows->paginate(10);
        $this->data['rows'] = $rows;
        return view('[+foldernameview+][+name+].index', [
            'data' => $this->data
            ]
       );
    }
    public function show(Request $request,$[+name+]id)
    {
        $rows = $this->[+repositoryname+]Repo->selectRaw('[+repositoryname+].*');
        $rows->myWhere('id|N', $this->data['[+name+]'], '[+name+]id', 'Y');
        $rows->orderByRaw('id desc');

        $rows = $rows->take(1)->get();
        //$rows = $rows->get();
        //dd($row);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
            
        } else {
            throw new \CustomException('No data');
        }
        
        // Config::set('config.keyword', '');
        // Config::set('config.description', '');

        return view('[+foldernameview+][+name+].show', [
            'data' => $this->data,
            ]
       );
    }

}
