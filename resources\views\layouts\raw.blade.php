<!doctype html>
<html lang="{{ app()->getLocale() }}">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta http-equiv="content-Type" content="text/html; charset=utf-8" />
        <meta http-equiv="content-Language" content="zh-tw" />

        <title>@yield('title',PF::getConfig('title'))</title>
        <meta name="robots" content="noarchive,noindex,nofollow" />
        <meta name="distribution" content="Taiwan" />
        <meta name="revisit-after" content="1 days" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Cache-Control" content="private" />
        <meta http-equiv="Expires" content="0" />

        <link href="{{ asset('css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />
        <link href="{{ asset('css/font-awesome.min.css') }}" rel="stylesheet" type="text/css" />
        <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet" type="text/css" />
        <link href="{{ asset('css/css.css') }}" rel="stylesheet" type="text/css" />
        <script type="text/javascript" src="{{ asset('Scripts/jquery.js') }}"></script>
        <script type="text/javascript" src="{{ asset('Scripts/jquery-migrate.min.js') }}"></script>
        <script type="text/javascript" src="{{ asset('Scripts/bootstrap.bundle.min.js') }}"></script>
        <script type="text/javascript" src="{{ asset('Scripts/sweetalert.min.js') }}"></script>
        <script type="text/javascript" src="{{ asset('Scripts/PJSFunc.js') }}?d={{(config('app.env') != 'production') ? date('His') : '1'}}"></script>
        <script type="text/javascript" src="{{ asset('Scripts/JSFunc.js') }}"></script>
        @yield('css')

        @yield('js')
        @include('layouts.masterjs')

    </head>

    <body class="antialiased">
        <div id="app">
            <div class="loading show" id="loading">
                <svg class="spinner" width="65px" height="65px" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                    <circle id="circle" class="path" fill="none" stroke-width="6" stroke-linecap="round" cx="33" cy="33" r="30"></circle>
                </svg>
            </div>


            @yield('content')

        </div>

    </body>

</html>