<?php

namespace App\Http\Controllers\admin;

use DB;
use PF;
use PT;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\epostRepository;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Validator;

class epostController extends adminController {
    private $fieldnicknames;
    private $data;
    private $xmldoc;
    private $epostRepo;

    /**
     *建構子.
     */
    public function __construct(epostRepository $epostRepo) {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->epostRepo = $epostRepo;
        $this->fieldnicknames = [
            'eposttitle' => '標題',
            'epostbody' => '內容',
        ];

        $this->data['nav'] = PT::nav($this->data['xmldoc'], $this->data['edit']);
        if ('' != config('config.zh.name')) {
            if ('' == $this->data['alg']) {
                $this->data['alg'] = 'zh';
            }
        }
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        return view('admin.epost.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料建立顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request) {
        try {
            return view('admin.epost.edit', [
                'data' => $this->data,
            ]);
        } catch (\Exception $e) {
            //throw $e;
            die($e->getMessage());
        }
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request) {
    }

    /**
     * 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request) {
        $edit = $request->input('edit');
        //PF::printr($edit);
        $validators = null;
        $validators['edit'] = 'required';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }

        $rows = $this->epostRepo->selectRaw('epost.*');

        if ('' != config('config.zh.name')) {
            $rows->myWhere('alg|S', $this->data['alg'], 'alg', 'Y');
        }
        $rows->myWhere('epostid|S', $this->data['edit'], 'id', 'Y');

        $rows = $rows->limit(1);
        //$rs=get_object_vars(rs);//to rs['XX'];
        //PF::dbSqlPrint($rows);
        $rows = $rows->get();
        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        }

        $type = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/傳回值', '形態', $request->input('edit'));
        if ($type == $request->input('edit')) {
            $type = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/權限/選單/KIND/KIND/傳回值', '形態', $request->input('edit'));
        }

        if ('' == $type) {
            $type = 'html';
        }
        $this->data['type'] = $type;

        return view('admin.epost.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {
        $edit = $request->input('edit');

        $validators = null;
        if ('' == $edit) {
        }
        $validators['epostbody'] = 'required';

        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = null;

        //$inputs['eposttitle'] = $request->input('eposttitle'); /*標題-*/

        $inputs['alg'] = $this->data['alg'];
        $inputs['epostid'] = $edit; /*內容-*/
        $inputs['epostbody'] = str_replace('&#39;', "'", $this->data['epostbody']);
        if ('' != config('config.zh.name')) {
            $this->epostRepo->updateOrCreate(['epostid' => $edit, 'alg' => $this->data['alg']], $inputs);
        } else {
            $this->epostRepo->updateOrCreate(['epostid' => $edit], $inputs);
        }

        \Cache::put('epost_' . $edit, $inputs['epostbody']);

        return back()->with('js', "_toast('更新成功',500)");
    }

    /**
     * 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request) {
        $dbcount = DB::table('epost')->whereIn('epostid', $request->input('del'))->delete();

        return view('admin/layouts/postsubmit', [
            'data' => $request->all(),
        ]);
    }
}
