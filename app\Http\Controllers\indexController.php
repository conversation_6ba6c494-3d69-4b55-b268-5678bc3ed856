<?php

namespace App\Http\Controllers;

use DB;
use PF;
use Illuminate\Http\Request;
//use Illuminate\Support\Facades\DB;
use App\Repositories\boardRepository;
use App\Repositories\productRepository;

class indexController extends Controller {
    private $data;
    private $productRepo;
    private $boardRepo;

    /**
     *建構子.
     */
    public function __construct(productRepository $productRepo, boardRepository $boardRepo) {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->productRepo = $productRepo;
        $this->boardRepo = $boardRepo;
        // $this->city1Repo = $city1Repo;
        // $this->city2Repo = $city2Repo;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        // $rows = DB::table('product')->select('*');
        // $rows->myWhere('online|N', 1, 'online', 'N');

        // $rows->myWhere('location', 'deal', 'locationadmin', 'N');
        // //PF::dbSqlPrint($rows);
        // $rows->orderByRaw('RAND()');
        // $rows = $rows->take(8);
        // $rows = $rows->get();
        // $this->data['rowsdeal'] = $rows;

        $rows = $this->productRepo->select('*');
        $rows->myWhere('online|N', 1, 'online', 'N');
        $rows->myWhere('auctions|S', '待標', 'key', 'N');
        $rows->myWhere('locationadmin', 'master', 'locationadmin', 'N');
        if ($request->input('showtype') == "2") {
            $rows->orderByRaw('tenderdate desc');
        } else {        //PF::dbSqlPrint($rows);
            $rows->orderByRaw('RAND()');
        }
        $rows = $rows->take(21);

        $rows = $rows->get();

        $this->data['rows'] = $rows;

        $rowsbanner = DB::table('board')->selectRaw('*');
        $rowsbanner->myWhere('kind|S', 'banner', 'kind', 'N');
        $rowsbanner->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
        $rowsbanner->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        $rowsbanner->orderByRaw('boardsort,id desc');
        $rowsbanner = $rowsbanner->get();
        $this->data['rowsbanner'] = $rowsbanner;

        $viewcount = \Cache::remember('viewcount', 5 * 60, function () use ($kindid) {
            // $body = '';
            // $rows = DB::table('viewcount')->selectRaw('sum(hits) as total');
            // $rows = $rows->get();
            // $totay = 1;
            // if ($rows->count() > 0) {
            //     $rs = $rows->first();
            //     $total = $rs->total;
            // }

            $rows = DB::table('viewcount')->selectRaw('hits');
            $rows = $rows->take(1);
            $rows = $rows->get();
            $totay = 1;
            if ($rows->count() > 0) {
                $rs = $rows->first();
                $totay = $rs->hits;
            }

            $d = date('Y-m-d');
            $onlinecount = rand(80, 100);
            //在線人數：$onlinecount 今日訪客：$totay 累計瀏覽：$total 更新日期：$d
            $body = <<<EOF
                    訪客：$totay 人  更新：$d

                EOF;

            return  $body;
        });

        $this->data['viewcount'] = $viewcount;

        $rowslink = DB::table('board')->selectRaw('*');
        $rowslink->myWhere('kind|S', 'link', 'kind', 'N');
        $rowslink->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
        $rowslink->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        $rowslink->orderByRaw('boardsort desc,id desc');
        $rowslink = $rowslink->get();
        $this->data['rowslink'] = $rowslink;
        if (\Cache::get('city2') == null) {
            $rows = DB::table('city2')->selectRaw('*');
            $rows->orderByRaw('city2title');
            $rows = $rows->get();
            \Cache::forever('city2', $rows);
        }


        // $rowscity1 = $this->city1Repo->selectRaw('*');
        // $rowscity1->orderByRaw('sortnum desc');
        // $rowscity1 = $rowscity1->get();

        // $this->data['rowscity1'] = $rowscity1;
        // //$city1title=PF::request("city1title");

        // if ($this->data['city1title']){
        //     $rowscity2 = $this->city2Repo->selectRaw('*');
        //     $rowscity2->myWhere('city1title|S',$this->data['city1title'], 'city1title', 'Y');
        //     $rowscity2->orderByRaw('city2title');
        //     $rowscity2 = $rowscity2->get();

        //     $this->data['rowscity2'] = $rowscity2;
        // }

        // $rows = DB::table('board')->select('*');
        // $rows->myWhere('kind|INS', 'banner', 'kind', 'Y');
        // $rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
        // $rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        // $rows->orderByRaw('boardsort desc ,id desc');
        // $rows = $rows->get();
        // $this->data['rowsbanner'] = $rows;
        // $rows = DB::table('board')->select('*');
        // $rows->myWhere('kind|INS', 'news', 'kind', 'Y');
        // $rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
        // $rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        // $rows->orderByRaw('boardsort desc ,id desc');
        // $rows = $rows->get();
        // $this->data['rowsnews'] = $rows;
        // $rowsbanner1 = $rows->filter(function($rs, $key) {
        //     if ( $rs->kind == 'banner') {
        //         return true;
        //     }
        // });
        // // $rowsbanner2 = $rows->filter(function($rs, $key) {
        // //     if ($rs->kind == 'banner2') {
        // //         return true;
        // //     }
        // // });
        // $rowsexperience = $rows->filter(function($rs, $key) {
        //     if ($rs->kind == 'experience') {
        //         return true;
        //     }
        // });

        // $rows2 = $rows->filter(function($rs, $key) {
        //     if ($rs->kindid == 2 && $rs->kind == 'news') {
        //         return true;
        //     }
        // });

        //$this->data['rowsbanner2'] = $rowsbanner2;
        //$this->data['rowsexperience'] = $rowsexperience;
        if ($this->data['showtype'] == "") {
            $this->data['showtype'] = "1";
        }


        return view(
            'index.index',
            [
                'data' => $this->data,
            ]
        );
    }
}
