<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="message1",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="msg_no", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_type", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_mean", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_people", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_sex", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_mail", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_msg", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_ans", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="msg_date", type="",description="", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class message1 extends baseModel
{
    use HasFactory;
    
    public $tabletitle = '';
    public $table = 'message1';
    public $primaryKey = '';
    //public $incrementing = false;//取消自動編號
      //欄位必填
      public $rules = [
		'id' => 'required',

    ];
    public $fieldInfo = [
'msg_no'=>['title'=>'','type'=>'varchar(255)'],//
'msg_type'=>['title'=>'','type'=>'varchar(255)'],//
'msg_mean'=>['title'=>'','type'=>'varchar(255)'],//
'msg_people'=>['title'=>'','type'=>'varchar(255)'],//
'msg_sex'=>['title'=>'','type'=>'varchar(255)'],//
'msg_mail'=>['title'=>'','type'=>'varchar(255)'],//
'msg_msg'=>['title'=>'','type'=>'varchar(255)'],//
'msg_ans'=>['title'=>'','type'=>'varchar(255)'],//
'msg_date'=>['title'=>'','type'=>'varchar(255)'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['msg_no','msg_type','msg_mean','msg_people','msg_sex','msg_mail','msg_msg','msg_ans','msg_date']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    
    protected $dates = [];
  
//   public function __construct() {
//         $this->fillable = parent::getfillables();//接受$request->all();
//         //$this->fillable =array_keys($this->fieldInfo);
//         parent::__construct();        
//         parent::setFieldInfo($this->fieldInfo);
//   }         
    
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          
        });
         static::deleting(function ($model) {

          
        });
        static::deleted(function ($model) {
            
        });
    }	

}