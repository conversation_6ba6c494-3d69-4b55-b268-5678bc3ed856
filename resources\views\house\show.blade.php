@extends('layouts.master')
@section('css')
@endsection

@section('js')
    <script type="application/ld+json">
      {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": "法拍-{{$data['city1title']}}{{$data['city2title']}}{{$data['address']}} - {{PF::getConfig('name')}}",
        @if ($data['img'] != null)
        "image": [
          "{{ url('/') }}/images/product/{{ end(explode(",",$data['img']))}}"
        ],
        @endif
        "description": "{{ PF::noHtml($data['courttranscript']) }}",
        "sku": "",
        "mpn": "",
        "brand": {
          "@type": "Brand",
          "name": "{{PF::getConfig('name')}}"
        },
        "review": {
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": 4,
            "bestRating": 5
          },
          "author": {
            "@type": "Person",
            "name": "{{PF::getConfig('name')}}"
          }
        },
        @if ($data['totalupset']!="")
        "offers": {
            "@type": "Offer",
            "priceValidUntil": "{{ date('Y-m-d', strtotime(PF::rocDateToWestDate($data['tenderdate']))) }}",
            "price": {{ $data['totalupset'] }},
            "priceCurrency": "TWD"
        },
        @endif
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": 4.4,
          "reviewCount": {{$data['hits']!=''? $data['hits'] :0 }}
        }

      }
</script>
@endsection

@section('content')

    <section class="fc-address">
        <h2><i class="fa-solid fa-location-dot"></i>
            {{ $data['number'] }}
            &nbsp;&nbsp;&nbsp;
            {{ $data['producttitle'] }}

        </h2>
        <h3><span>{{ PF::formatNumber($data['totalupset'], 0) }}</span>萬</h3>
    </section>
    <div class="main-layout">
        <div class="main">
            <section class="slider__wrap">
                <div class="slider__flex">
                    <!-- sliders (bigPic) start-->
                    <div class="sliders">
                        <div class="swiper-container">
                            <div class="swiper-wrapper">
                                @if ($data['img'] != '')
                                    @foreach (array_reverse(explode(',', $data['img'])) as $key => $item)
                                        <div class="swiper-slide">
                                            <div class="imgBox">
                                                <img src="https://www.ebayhouse.com.tw/images/product/{{ $item }}"
                                                    alt="{{ $data['producttitle'] }}" />
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                    <!-- sliders (bigPic) end-->
                    <!-- sliders (thumbnail) start-->
                    <div class="slider__col">
                        <div class="slider__prev"></div>
                        <div class="slider__thumbs">
                            <div class="swiper-container">
                                <div class="swiper-wrapper">
                                    @if ($data['img'] != '')
                                        @foreach (array_reverse(explode(',', $data['img'])) as $key => $item)
                                            <div class="swiper-slide">
                                                <div class="imgBox">
                                                    <img src="https://www.ebayhouse.com.tw/images/product/{{ $item }}"
                                                        alt="{{ $data['producttitle'] }}" />
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="slider__next"></div>
                    </div>
                    <!-- sliders (thumbnail) end-->
                </div>
            </section>

            <section class="btnBox">
                @if ($data['pdf'] != '')
                    <button class="pdf-button"
                        onclick="window.open('{{ url('/') }}/images/product/{{ $data['pdf'] }}', '_blank')">拍賣單位公告</button>
                @endif
                <button class="print-button"
                    onclick="window.open('{{ url('/') }}/house/print?productid={{ $data['productid'] }}', '_blank')"><i
                        class="fa-solid fa-print"></i>列印</button>
            </section>

            <section class="map_wrap">
                <div class="topic">
                    <p>【 電子地圖 】</p>
                </div>
                <div class="map-container">
                    <iframe
                        src="https://maps.google.com.tw/maps?f=q&source=s_q&hl=zh-TW&geocode=&q={{ urlencode($data['city1title'] . $data['city2title'] . $data['address']) }}&ie=UTF8&z=16&output=embed"
                        width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
            </section>

            <section>
                <div class="topic">
                    <p>【 免費諮詢 】</p>
                </div>
                <form class="queryForm" action="{{ url('/') }}/house/store" method="post">
                    @csrf
                    <div class="form-row">
                        <label class="required" for="name">姓名：</label>
                        <input type="text" id="name" name="name" required placeholder="請輸入您的姓名"
                            value="{{ $data['name'] ?? '' }}">
                    </div>

                    <div class="form-row">
                        <label for="tel">室內電話：</label>
                        <input type="tel" id="tel" name="tel" placeholder="ex xxx-xxxxxxxx#ext"
                            value="{{ $data['tel'] ?? '' }}">
                    </div>

                    <div class="form-row">
                        <label class="required" for="mobile">行動電話：</label>
                        <input type="tel" id="mobile" name="mobile" required placeholder="ex 09123456789"
                            value="{{ $data['mobile'] ?? '' }}" pattern="09[1-8][0-9]([\-|\s]?)[0-9]{3}\1[0-9]{3}">
                    </div>

                    <div class="form-row">
                        <label for="address">案件地址：</label>
                        <input type="text" id="address" name="memo"
                            value="{{ $data['city1title'] }}{{ $data['city2title'] }} {{ $data['address'] }}">
                    </div>

                    <div class="form-row">
                        <label class="required" for="email">電子信箱：</label>
                        <input type="email" id="email" name="email" required placeholder="ex <EMAIL>"
                            value="{{ $data['email'] ?? '' }}">
                    </div>

                    <input type="hidden" value="" name="google_recaptcha_token" id="recaptchaResponse">
                    <button type="submit">送出</button>
                </form>
            </section>

            <section class="bestvalue_wrap">
                <div class="topic">
                    <p>【 超值物件 】</p>
                </div>
                <!-- bestvalue-grid Start -->
                <div class="bestvalue-grid">

                    @foreach ($data['rows'] as $rs)
                        @include('layouts.houseitem')
                    @endforeach

                </div>
                <!-- //bestvalue-grid End -->
            </section>
        </div>
        <!-- //.main End-->
        <div class="side">
            <div class="topic">
                <p>【 物件資料 】</p>
            </div>
            <div class="info-box">

                @if (in_array($data['productkind'], ['1', '3']))


                    <div class="info-item"><span>公告底價</span><span
                            class="eye-catch">{{ PF::formatNumber($data['totalupset'], 0) }} 萬</span></div>
                    <div class="info-item"><span>公告建坪</span><span>{{ PF::formatNumber($data['pingtotalnumberof'], 2) }}
                            坪</span></div>
                    <div class="info-item"><span>每坪單價</span><span class="eye-catch">{{ $data['floorprice'] }} 萬</span>
                    </div>
                    <div class="info-item"><span>保 證 金</span><span>{{ PF::formatNumber($data['totalupset'] * 0.2, 0) }}
                            萬</span></div>
                    <div class="info-item"><span>投標日期</span><span class="eye-catch">{{ $data['tenderdate'] }}</span>
                    </div>
                    <div class="info-item"><span>狀　　態</span><span class="eye-catch">{{ $data['auctions'] }}</span></div>
                    <div class="info-item"><span>拍　　次</span><span>{{ $data['beattime'] }}</span></div>
                    <div class="info-item"><span>主建坪數</span><span>{{ $data['mainlawnestablishment'] }} 坪</span></div>
                    <div class="info-item">
                        <span>附屬建物</span><span>{{ str_replace('坪', '', $data['attachedtolawnestablishment']) }} 坪</span>
                    </div>
                    <div class="info-item"><span>公共設施</span><span>{{ $data['postulateping'] }} 坪</span></div>
                    <div class="info-item"><span>增建面積</span><span>{{ $data['additionalping'] }} 坪</span></div>
                    <div class="info-item"><span>地坪</span><span>{{ $data['stakeholdersfloor'] }} 坪</span></div>
                @else
                    {{-- 中古屋物件資料顯示 --}}
                    <div class="info-item"><span>型　　態</span><span>{{ $data['pattern'] ?? '未提供' }}</span></div>
                    <div class="info-item"><span>樓層/樓高</span><span>
                            @if ($data['floor_end'])
                                {{ $data['floor_end'] }}
                            @endif
                            @if ($data['storey'])
                                / {{ $data['storey'] }}
                            @endif
                        </span></div>
                    <div class="info-item"><span>屋　　齡</span><span>{{ $data['houseage'] . '年' ?? '' }}</span></div>
                    <div class="info-item"><span>格　　局</span><span>
                            @if ($data['room_count'] || $data['living_room_count'] || $data['hall_count'] || $data['bathroom_count'])
                                {{ $data['room_count'] ?? '0' }}房
                                {{ $data['living_room_count'] ?? '0' }}廳

                                {{ $data['bathroom_count'] ?? '0' }}衛
                                @if ($data['balcony_count'])
                                    {{ $data['balcony_count'] }}陽台
                                @endif
                            @else
                                未提供
                            @endif
                        </span></div>
                    <div class="info-item"><span>社區名稱</span><span>{{ $data['buildname'] ?? '未提供' }}</span></div>
                    <div class="info-item"><span>總坪數</span><span class="eye-catch">
                            {{ PF::formatNumber(
                                ($data['mainlawnestablishment'] ?? 0) +
                                    (str_replace('坪', '', $data['attachedtolawnestablishment']) ?? 0) +
                                    ($data['postulateping'] ?? 0) +
                                    ($data['carping'] ?? 0),
                                2,
                            ) }}
                            坪</span></div>
                    <div class="info-item"><span>主建坪數</span><span>{{ $data['mainlawnestablishment'] }} 坪</span></div>

                    <div class="info-item">

                        <span>附屬坪數</span><span>{{ str_replace('坪', '', $data['attachedtolawnestablishment']) }} 坪</span>
                    </div>
                    <div class="info-item"><span>公設坪數</span><span>{{ $data['postulateping'] }} 坪</span></div>
                    <div class="info-item"><span>車位坪數</span><span>{{ $data['carping'] }} 坪</span></div>
                    <div class="info-item"><span>土地持分</span><span>{{ $data['stakeholdersfloor'] }} 坪 </span></div>
                    <div class="info-item"><span>車位型式</span><span>{{ $data['parkingmode'] }}</span></div>
                    <div class="info-item"><span>建物結構</span><span>{{ $data['architecture'] }}</span></div>

                @endif
            </div>
        </div>
        <!-- //.side End-->
    </div>

    <!-- Google reCAPTCHA Scripts -->
    <script src="https://www.google.com/recaptcha/api.js?render={{ config('recaptcha.id') }}"></script>
    <script>
        $("button").each(function(index) {
            $(this).prop('disabled', true);
        });
        grecaptcha.ready(function() {
            grecaptcha.execute("{{ config('recaptcha.id') }}", {
                action: 'homepage'
            }).then(function(token) {
                var recaptchaResponse = document.getElementById('recaptchaResponse');
                recaptchaResponse.value = token;
                $("button").each(function(index) {
                    $(this).prop('disabled', false);
                });
            });
        });
    </script>
    <style>
        .grecaptcha-badge {
            visibility: hidden;
        }
    </style>
@endsection
