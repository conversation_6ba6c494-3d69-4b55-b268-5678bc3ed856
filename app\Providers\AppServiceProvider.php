<?php

namespace App\Providers;

use DB;
use Log;
//use Illuminate\Database\Eloquent\Builder;
use Form;
use Html;
use App\Macros\MyFormUIDb;
use App\Macros\MyBuilderDB;
use App\Macros\MyFormUIDb2;

use App\Macros\MyFormUIXml;
use Illuminate\Support\Str;
use App\Macros\MyFormUIXml2;
use App\Macros\MyHtmlUIImage;
use App\Macros\MyFormUIUpload;
use App\Macros\MyFormUISelectDate;
use Illuminate\Support\HtmlString;
use App\Macros\MyFormUIMultiUpload;
use App\Macros\MyFormUISelectMulti;
use App\Macros\MyFormUISelectThird;
use Illuminate\Support\Facades\Queue;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Translation\Translator;
use Illuminate\Support\ServiceProvider;
use App\Services\Translator\JsonTranslator;

class AppServiceProvider extends ServiceProvider {
    /**
     * Register any application services.
     */
    public function register() {
        //覆寫多國語系
        // $this->app->extend('translator', function (Translator $translator) {
        //     $trans = new JsonTranslator($translator->getLoader(), $translator->getLocale());
        //     $trans->setFallback($translator->getFallback());
        //     return $trans;
        // });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot() {
        \URL::forceScheme('https');
        \Schema::defaultStringLength(255);
        error_reporting(E_ALL ^ E_NOTICE);
        //儲存全部SQLCOMMAND LOG
        if ('local' == \config('app.env')) {
            DB::listen(function ($query) {
                $sql = $query->sql;
                $bindings = $query->bindings;
                $time = $query->time;

                Log::info(vsprintf(str_replace(['?'], ['\'%s\''], $sql), $bindings) . "  [time:$time]");
                // $query->bindings
                // $query->time
            });
        }

        // Str::macro('isLength', function ($str, $length) {
        //     return static::length($str) == $length;
        // });
        \Illuminate\Database\Query\Builder::macro('myWhere', function ($names, $value, $nicknamearray, $isrequired) {
            $myDB = new MyBuilderDB($this);

            return $myDB->sql($names, $value, $nicknamearray, $isrequired);
        });
        \Illuminate\Database\Eloquent\Builder::macro('myWhere', function ($names, $value, $nicknamearray, $isrequired) {
            $myDB = new MyBuilderDB($this);

            return $myDB->sql($names, $value, $nicknamearray, $isrequired);
        });
        Form::macro('myUIXml', function ($arr) {
            return  new HtmlString(with(new MyFormUIXml($this, $arr))->createHtml());
        });
        Form::macro('myUIXml2', function ($arr) {
            return  new HtmlString(with(new MyFormUIXml2($this, $arr))->createHtml());
        });
        Form::macro('myUIDb', function ($arr) {
            return  new HtmlString(with(new MyFormUIDb($this, $arr))->createHtml());
        });
        Form::macro('myUIDb2', function ($arr) {
            return  new HtmlString(with(new MyFormUIDb2($this, $arr))->createHtml());
        });
        //Form::macro('myUISelectThird', function($arr){
        //    return  new HtmlString(with(new MyFormUISelectThird($this, $arr))->createHtml());
        //});
        Form::macro('myUISelectMulti', function ($arr) {
            return  new HtmlString(with(new MyFormUISelectMulti($this, $arr))->createHtml());
        });

        Form::macro('myUIUpload', function ($arr) {
            return  new HtmlString(with(new MyFormUIUpload($this, $arr))->createHtml());
        });
        Form::macro('myUIMultiUpload', function ($arr) {
            return  new HtmlString(with(new MyFormUIMultiUpload($this, $arr))->createHtml());
        });
        Form::macro('myUISelectDate', function ($arr) {
            return  new HtmlString(with(new MyFormUISelectDate($this, $arr))->createHtml());
        });

        HTML::macro('myUIImage', function ($arr) {
            return  new HtmlString(with(new MyHtmlUIImage($this, $arr))->createHtml());
        });
        // Queue::before(function (JobProcessing $event) {
        //     \Log::error("Queue::before");
        //     // $event->connectionName
        //     // $event->job
        //     // $event->job->payload()
        // });

        // Queue::after(function (JobProcessed $event) {
        //     \Log::error("Queue::after");
        //     // $event->connectionName
        //     // $event->job
        //     // $event->job->payload()
        // });
        Queue::failing(function (JobFailed $event) {
            $msg = 'Queue::failing -> connectionName:' . $event->connectionName . ' | exception:' . $event->exception;
            \Log::error($msg);
        });
    }
}
