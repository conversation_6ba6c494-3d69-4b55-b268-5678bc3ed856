{"date": {"prefix": "date 顯示年月日時分秒", "scope": "php", "body": ["date('Y-m-d H:i:s')"]}, "div_center ": {"prefix": "div center", "scope": "html,blade,vue-html", "body": ["<div align=\"center\" style=''>", " ${1:body} ", " </div>"]}, "div_left ": {"prefix": "div left", "scope": "html,blade,vue-html", "body": ["<div align=\"left\" style=''>", " ${1:body} ", " </div>"]}, "div_right ": {"prefix": "div right", "scope": "html,blade,vue-html", "body": ["<div align=\"right\" style=''>", " ${1:body} ", " </div>"]}, "span ": {"prefix": "span", "scope": "html,blade,vue-html", "body": ["<span style=''>", " ${1:body} ", " </span>"]}, "placeholder ": {"prefix": "pl placeholder", "scope": "html,blade,vue-html", "body": ["placeholder=\"${1:title}\""]}, "placeholder tel": {"prefix": "pl placeholder tel", "scope": "html,blade,vue-html", "body": ["placeholder=\"ex:02-29631234\""]}, "placeholder email": {"prefix": "pl placeholder email", "scope": "html,blade,vue-html", "body": ["placeholder=\"ex:<EMAIL>\""]}, "placeholder mobile": {"prefix": "pl placeholder mobile", "scope": "html,blade,vue-html", "body": ["placeholder=\"ex:0912345678\""]}, "parseInt": {"prefix": "int 轉數字", "scope": "js,typescript", "body": ["parseInt(${1:n},10)"]}, "轉數字": {"prefix": "int intval 轉數字", "scope": "php,typescript", "body": ["intval(${1:n})"]}, "轉文字": {"prefix": "string strval 轉文字", "scope": "php", "body": ["strval($data['${1:txt}'])"]}, "字串長度": {"prefix": "string len 字串長度", "scope": "php", "body": ["mb_strlen(\\$${1:txt},\"UTF-8\");"]}, "substr": {"prefix": "string substr 去除第1碼", "scope": "php", "body": ["substr(\\$${1:txt},1);"]}, "數字隨機": {"prefix": "rnd int 隨機 rand", "scope": "php", "body": ["rand(1, 100)"]}, "tr": {"prefix": "trtd", "scope": "html,blade,vue-html", "body": ["<tr>", "<td>", "${1:title}", "</td>", "<td>", "${2:value}", "</td>", "</tr>"]}, "td": {"prefix": "td", "scope": "html,blade,vue-html", "body": ["<td>", "${1:title}", "</td>"]}, "th_html": {"prefix": "th", "scope": "php,html", "body": ["<th>", "${1:title}", "</th>"]}, "thr": {"prefix": "thr", "scope": "html,blade,vue-html", "body": ["", "<th width=\"\" id=\"`${1:id}`\">${2:title}</th>", "<td>{{ \\$rs->${1:id}}}</td>"]}, "replace": {"prefix": "replace", "scope": "php", "body": ["\\$${1:ebody}=str_replace([' ','、'], '', \\$${1:ebody});"]}, "strtotime_string to strtotime": {"prefix": "date string to timestamp", "scope": "php", "body": ["strtotime(date(\"Y/m/d H:i:s\"))"]}, "strtotime_string to datetime ": {"prefix": "date string to datetime", "scope": "php", "body": ["\\$${1:date_str} = date('Y-m-d H:i:s', strtotime(\\$${1:date_str})"]}, "轉中文": {"prefix": "str", "scope": "php", "body": ["strval(${1:table})"]}, "print_空白": {"prefix": "print 空白 space", "scope": "html,php,blade,vue-html", "body": ["&nbsp;"]}, "space": {"prefix": "space", "scope": "php", "description": "PHP的空白space", "body": ["PHP_EOL"]}, "php 多行": {"prefix": "php str line", "scope": "php", "body": ["\\$body=<<<EOF", "      \".\\{$this->data['name']}", "      \".\\$rawId", "EOF;"]}, "upload": {"prefix": "upload ENCTYPE multipart/form-data", "scope": "html,blade,vue-html", "body": ["ENCTYPE=\"multipart/form-data\""]}, "php_eol": {"prefix": "php 斷行", "scope": "php", "description": "PHP_EOL", "body": ["PHP_EOL"]}, "php_eol_html": {"prefix": "php 斷行", "scope": "html,blade,vue-html", "body": ["<?echo PHP_EOL;?>"]}, "json_encode": {"prefix": "json encode 壓 object -> string", "scope": "php", "description": "json_encode", "body": ["\\$json = json_encode(\\$body,JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);"]}, "json_remove index": {"prefix": "json remove index", "scope": "php", "body": ["\\$jsonbody=array_values(\\$jsonbody)"]}, "xml": {"prefix": "xml setup", "scope": "xml", "body": ["   <${1:baord} ReturnFlag=\"1\">", "        <KIND>", "            <資料>交流</資料>", "            <傳回值>1</傳回值>", "        </KIND>", "        <KIND>", "            <資料>抽獎</資料>", "            <傳回值>2</傳回值>", "        </KIND>     ", "    </${1:baord}>"]}, "xml kind": {"prefix": "xml kind", "scope": "xml", "body": ["        <KIND>", "            <資料>${1:功能}</資料>", "            <傳回值>${2:1}</傳回值>", "        </KIND>"]}, "rtrim去除最後一碼": {"prefix": "string rtrim 去除最後一碼", "scope": "php", "body": ["\\$${1:temp} = ltrim(rtrim(\\$${1:temp}, \",\"),\",\");"]}, "array判斷是否存在": {"prefix": "array in_array exist 判斷是否存在", "scope": "php", "body": ["if((in_array('${1:baord}',['${2:baord}','b']))){", "", "}"]}, "array判斷是否存在1": {"prefix": "array to string url", "scope": "php", "body": ["\\$str = http_build_query(\\$inputs);"]}, "array_reverse": {"prefix": "array array_reverse 反向", "scope": "php", "body": ["\\$${1:datas} = array_reverse(\\$${1:datas});"]}, "array_slice": {"prefix": "array array_slice 取前10筆", "scope": "php", "body": ["\\$${1:datas} = array_slice(\\$${1:datas},-10);"]}, "is_array": {"prefix": "array is_array 判斷是否為陣列", "scope": "php", "body": ["if(is_array(${1:baord})){", "", "}"]}, "is_string": {"prefix": "str is_string 判斷是否為字串", "scope": "php", "body": ["if(is_string(${1:baord})){", "", "}"]}, "array搜尋位置": {"prefix": "array 一層 search", "scope": "php", "body": ["\\$array = array(0 => 'blue', 1 => 'red', 2 => 'green', 3 => 'red');", "", "\\$key = array_search('green', \\$array); // \\$key = 2;"]}, "array只取值": {"prefix": "array 二層取 values array_values", "scope": "php", "body": ["\\$array = array(\"size\" => \"XL\", \"color\" => \"gold\");", "array_values(\\$array);"]}, "array只取key": {"prefix": "array 二層取 key array_keys", "scope": "php", "body": ["\\$array = array(\"size\" => \"XL\", \"color\" => \"gold\");", "array_keys($array)"]}, "array 二層key value": {"prefix": "array 二層 範例 + foreach", "scope": "php", "body": [" \\$items = [[", "            'title' => 'x',", "            'value' => '',", "            ],", "            [", "                'title' => 'y',", "                'value' => '',", "            ],", "            ];", "foreach (\\$items as \\$k => \\$v){", "   \\$v;", "};   "]}, "array 一層有key value": {"prefix": "array  key foreach", "scope": "php", "body": ["\\$items = ['productcontactperson', 'owner', 'parking'];", "foreach (\\$items as \\$k => \\$v) {", "\\$v", "}"]}, "array 沒有key value": {"prefix": "array 一層 範例 + foreach", "scope": "php", "body": [" \\$items = ['${1:baord}','${2:baord}','${3:baord}'];", "foreach (\\$items as \\$k => \\$v){", "   \\$v;", "};   "]}, "array implode": {"prefix": "array string 陣列轉字串", "scope": "php", "body": [" implode('&',\\$${1:baord});"]}, "array unset map": {"prefix": "array del 刪除陣列 map", "scope": "php", "body": [" unset(\\$inputs[\"${1:baord}\"]);"]}, "array unset rs": {"prefix": "array del 刪除陣列 rs", "scope": "php", "body": [" unset(\\$rs->${1:baord});"]}, "string mb_strlen": {"prefix": "str mb_strlen長度 ", "scope": "php", "body": [" <?if (11 == mb_strlen(\\$${1:baord})) {?>", "}?>   "]}, "in_array": {"prefix": "array in_array多筆值是否存在", "scope": "php", "body": ["if (in_array(\\$${1:baord}, ['${2:baord}', '${3:baord}'])) {", "", "}"]}, "memo": {"prefix": "memo 加檔案功能說明", "scope": "php", "body": ["/***", "\"功能名稱\":\"${1:會員管理}\",", "\"資料表\":\"${2:member}\",", "\"備註\":\"${3: }\",", "\"建立時間\":\"$CURRENT_YEAR-$CURRENT_MONTH-$CURRENT_DATE $CURRENT_HOUR:$CURRENT_MINUTE:$CURRENT_SECOND\",", "***/"]}, "input_text_readonly": {"prefix": "text input readonly ", "scope": "html,blade,vue-html", "body": [" readonly "]}, "input_text_disabled": {"prefix": "text input disabled ", "scope": "html,blade,vue-html", "body": [" disabled "]}, "for1": {"prefix": "for 數字小到大", "scope": "php", "body": ["for (\\$i=0; \\$i <\\$${2:member} ; \\$i++) { ", "            if (\\$qty1count >= \\$rs['buyqty']) {", "                //break;", "            }", " }"]}, "for2": {"prefix": "for 數字大到小", "scope": "php", "body": ["for (\\$i = count(\\$json_old['buys']) - 1; \\$i >= 0; --\\$i) {", "            if (\\$qty1count >= \\$rs['buyqty']) {", "                //break;", "            }", "        }"]}, "php_Ceil": {"prefix": "math Ceil 無條件去進", "scope": "php", "body": ["for (\\$i = count(\\$json_old['buys']) - 1; \\$i >= 0; --\\$i) {", "            if (\\$qty1count >= \\$rs['buyqty']) {", "                //break;", "            }", "        }"]}, "php_Ceil_": {"prefix": "math Ceil 無條件去進", "scope": "php", "body": ["Ceil(${1:123})"]}, "php_floor": {"prefix": "math floor 無條件去小數", "scope": "php", "body": ["floor(${1:123})"]}, "php_round": {"prefix": "math round 四捨五入", "scope": "php", "body": ["round(${1:123})"]}, "copy": {"prefix": "array copy ", "scope": "php", "body": ["\\$newRecord = clone \\$rs;"]}}