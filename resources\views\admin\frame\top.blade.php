@extends('admin.layouts.master')
@section('css')
    <style>
        body {
            overflow: hidden;
            /*刪除水平與垂直捲軸*/
            overflow-x: hidden;
            /*刪除水平捲軸*/
            overflow-y: show;

        }
    </style>
@endsection

@section('js')
@endsection



@section('content')
    <div class="wrapper" class="hold-transition sidebar-mini layout-fixed">
        <nav class="navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">

                    <a class="nav-link" data-widget="pushmenu" href="#" role="button" onclick="toggleLeftFrame()"><i
                            class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-md-block">
                    <a href="#" class="nav-link" style="font-size:12px">
                        {{ Auth::guard('admin')->user()->name }}

                        ({{ PF::xmlSearch($data['xmldoc'], '//參數設定檔/角色/KIND/傳回值', '資料', \Auth::guard('admin')->user()->role) }})
                    </a>
                </li>


            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">

                    <a href="{{ \request()->middlewareurl }}usermodify/edit" class="nav-link" target="maincontent">
                        <i class="fas fa-user-edit"></i>

                    </a>
                </li>
                <li class="nav-item">

                    <a class="nav-link" href="{{ \request()->middlewareurl }}frame/logout" target="_top">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </li>

            </ul>
        </nav>


        <!-- 在右側框架中加入按鈕 -->

    </div>
    <script>
        var isopen = false;

        function toggleLeftFrame() {
            var cols = $('#frameset', window.top.document).attr('cols');
            if (cols == '200,*') {
                $('#frameset', window.top.document).attr('cols', '0,*');
            } else {
                $('#frameset', window.top.document).attr('cols', '200,*');

            }


        }
    </script>
@endsection
