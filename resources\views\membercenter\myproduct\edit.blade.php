@extends('admin.layouts.master')

@section('css')

@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
    function oForm_onsubmit(form)
    {
        if (PF_FormMultiAll(form)==false){return false};
   PF_FieldDisabled(form)//將全部button Disabled
   return true;
}
</SCRIPT>

<!--// TODO : 前端資料填寫-->
<form name="oForm" id="oForm" method="post" language="javascript" action="{{ request()->url() }}/../store"
    onsubmit="return oForm_onsubmit(this);">
    <!--novalidate-->


    <div align="center">
        <button type="submit" class="btn btn-success">確定</button>
        <button type="reset" class="btn btn-secondary">取消</button>
        <!--<button type="submit" class="btn btn-primary" onclick="oForm.edit.value='';">複製一筆</button>-->
        <button type="reset" class="btn btn-secondary" onClick="javascript:window.history.go(-1)">回上一頁</button>

    </div>
    @include('admin.layouts.hidden', ['method'=>'EditoForm'])
    {!! Form::hidden("edit", $data['edit']) !!}
    {!! Form::hidden("gobackurl", $data['gobackurl'] ) !!}
</form>

@stop