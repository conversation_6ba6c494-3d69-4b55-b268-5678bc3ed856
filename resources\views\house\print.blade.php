<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房產資訊 - {{ $data['city1title'] }}
        {{ $data['city2title'] }}
        {{ $data['address'] }}</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="{{ url('/') }}/css/css.css" />
    <link rel="stylesheet" href="{{ url('/') }}/css/print.css" />
    <style>
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <img src="{{ url('/') }}/images/slogo.png" border="0" align="absmiddle" width="30">
        {{ config('config.name') }}
    </div>

    <div class="location">
        <i class="fas fa-map-marker-alt"></i> {{ $data['city1title'] }}
        {{ $data['city2title'] }}
        {{ $data['address'] }}
        {{ $data['storey'] != '' ? '(' . $data['storey'] . ')' : '' }}
        @if ($data['buildname'] != '')
            [{{ $data['buildname'] }}]
        @endif
    </div>

    <div class="property-summary">
        <div class="summary-item">
            <div class="summary-label">公告底價</div>
            <div class="summary-value">{{ PF::formatNumber($data['totalupset'], 0) }}<span
                    class="summary-unit">萬</span>
            </div>
        </div>
        <div class="summary-item">
            <div class="summary-label">公告建坪</div>
            <div class="summary-value">{{ PF::formatNumber($data['noticelawnestablishment'], 0) }}<span
                    class="summary-unit">坪</span></div>
        </div>
        <div class="summary-item">
            <div class="summary-label">單價</div>
            <div class="summary-value">{{ PF::formatNumber($data['floorprice'], 0) }}<span
                    class="summary-unit">萬/坪</span></div>
        </div>
    </div>

    <div class="content-container">
        <!-- 左側圖片 -->
        <div class="image-container">
            @foreach ($data['images'] as $key => $item)
                <div class="property-image">
                    <div class="">

                        {{ Html::myUIImage([
                            'folder' => 'https://www.ebayhouse.com.tw/images/product',
                            'filename' => $item,
                            //  'width' => 300,
                            //  'height' => 300,
                            //'noimg' => 'no-picture.gif',
                            //'class' => 'card-img',
                        ]) }}


                    </div>
                </div>
            @endforeach

        </div>

        <!-- 右側資訊 -->
        <div class="info-container">
            <div class="section-title">【法拍資訊】</div>
            <table class="info-table">
                <tr>
                    <td>拍賣時間：</td>
                    <td>{{ $data['tenderdate'] }}</td>
                </tr>
                <tr>
                    <td>狀態：</td>
                    <td>{{ $data['beattime'] }}</td>
                </tr>
                <tr>
                    <td>點交狀態：</td>
                    <td>{{ $data['nocross_point'] }}</td>
                </tr>
                <tr>
                    <td>保留價：</td>
                    <td>{{ $data['margin'] }}萬</td>
                </tr>
            </table>

            <div class="section-title">【標的資訊】</div>
            <table class="info-table">
                <tr>
                    <td>權狀坪數：</td>
                    <td>{{ PF::formatNumber($data['pingtotalnumberof'], 2) }}坪</td>
                </tr>
                <tr>
                    <td>主建坪：</td>
                    <td>{{ $data['mainlawnestablishment'] }}坪</td>
                </tr>
                <tr>
                    <td>附屬建物：</td>
                    <td>{{ str_replace('坪', '', $data['attachedtolawnestablishment']) }}坪</td>
                </tr>
                <tr>
                    <td>公共設施：</td>
                    <td>{{ $data['postulateping'] }}坪</td>
                </tr>
                <tr>
                    <td>車位坪數：</td>
                    <td>{{ $data['carping'] }}坪</td>
                </tr>
                <tr>
                    <td>增建坪數：</td>
                    <td>{{ $data['additionalping'] }}坪</td>
                </tr>
                <tr>
                    <td>地坪：</td>
                    <td>{{ $data['stakeholdersfloor'] }}坪</td>
                </tr>
                <tr>
                    <td>屋型：</td>
                    <td>{{ $data['pattern'] }}</td>
                </tr>
                <tr>
                    <td>屋齡：</td>
                    <td>{{ $data['houseage'] }}</td>
                </tr>
                <tr>
                    <td>樓層：</td>
                    <td>{{ $data['storey'] }}層</td>
                </tr>
            </table>

            <div class="print-button noprint">
                <i class="fas fa-print"></i> 列印
            </div>
        </div>
    </div>

    <div class="footer">

        <div class="hotline">
            <div class="hotline-number">
                <img src="{{ url('/') }}/images/slogo.png" border="0" align="absmiddle" width="30">服務專線：
                {{ config('config.tel2') }}
            </div>
            <button class="contact-button">
                <i class="fas fa-phone"></i> 立即聯繫
            </button>
        </div>
    </div>

    <script>
        // 列印功能
        document.querySelector('.print-button').addEventListener('click', function() {
            window.print();
        });
    </script>
</body>

</html>
