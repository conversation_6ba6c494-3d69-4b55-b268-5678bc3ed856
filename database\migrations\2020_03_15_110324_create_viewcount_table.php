<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateViewcountTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('viewcount')) {
        Schema::create('viewcount', function (Blueprint $table) {
            $table->string('kind',50)->nullable();
            $table->dateTime('viewcountdate')->nullable()->comment('點閱時間');           
            $table->integer('hits')->default(1);                        
            $table->timestamps();
        });
    }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('viewcount');
    }
}
