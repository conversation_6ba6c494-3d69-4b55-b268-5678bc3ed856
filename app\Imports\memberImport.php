<?php

namespace App\Imports;

use DB;
//use Maatwebsite\Excel\Concerns\ToModel;
use PF;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Illuminate\Database\Eloquent\ModelNotFoundException;

HeadingRowFormatter::default('none');
/***
"功能名稱":"Excel匯入-會員",
"資料表":"member",
"建立時間":"2025-05-25 10:38:54 ",
 ***/
class memberImport implements ToCollection, WithHeadingRow, WithBatchInserts, WithChunkReading, WithCalculatedFormulas {
    protected $data;

    protected $fields;

    public $results;

    public function __construct($data) {
        set_time_limit(0);
        ini_set('memory_limit', '512M');
        $this->data = $data;



        $this->fields = array_keys($this->data['displaynames']);
    }

    public function collection(Collection $rows) {

        $inserts = [];
        $updateCount = 0;
        foreach ($rows as $rs) {
            try {
                if ('dev' == \config('app.env')) {
                    PF::printr($rs);
                }
                $inputolds = [];

                foreach ($rs as $k => $v) {
                    //if (in_array($k, $this->fields)) {
                    $inputolds[array_search($k, $this->data['displaynames'])] = $rs[$k];
                    //}
                }
                unset($inputolds["0"]);
                $result = $inputolds[0];
                $inputs = $inputolds;

                //$inputs['mobile'] =$rs['行動電話'];//行動電話-
                //$inputs['name'] =$rs['姓名'];//姓名-
                //$inputs['lineid'] =$rs['LINE ID'];//LINE ID-
                //$inputs['password'] =$rs['密碼'];//密碼-
                //$inputs['sex'] = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/性別/KIND/資料', '傳回值', $inputolds['sex']); //性別 [:先生 ; :女士 ; ]
                //$inputs['tel'] =$rs['市話'];//市話-
                //$inputs['email'] =$rs['電子信箱'];//電子信箱-
                //$inputs['patterns'] = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/法拍屋種類/KIND/資料', '傳回值', $inputolds['patterns']); //法拍屋種類
                //$inputs['totalupsets'] = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/總底價/KIND/資料', '傳回值', $inputolds['totalupsets']); //總底價
                //$inputs['postals'] =$rs['區域'];//區域-
                //$inputs['item_request'] =$rs['細項需求'];//細項需求-
                //$inputs['myproducts'] =$rs['我的收藏'];//我的收藏-
                //$inputs['isepaper'] =$rs['是否訂閱電子報'];//是否訂閱電子報-
                //$inputs['memo'] =$rs['備註'];//備註-
                //$inputs['api_token'] =$rs['api_token'];//api_token-
                //$inputs['remember_token'] =$rs['remember_token'];//remember_token-
                //$inputs['lastlogin_ip'] =$rs['登入IP'];//登入IP-
                //$inputs['lastlogin_dt'] = $this->formateDate($rs['最後登入日期']);
                //$inputs['logincount'] =$rs['登入次數'];//登入次數-
                //$inputs['online'] = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/會員狀態/KIND/資料', '傳回值', $inputolds['online']); //會員狀態 [1:正常 ; 2:停權 ; ]
                //$inputs['adminuser_id'] =$rs['編輯人員'];//編輯人員-
                //$inputs['adminuser_account'] =$rs['編輯人員'];//編輯人員-
                //$inputs['created_at'] = $this->formateDate($rs['建立時間']); //now();
                unset($inputs["0"]);

                //檢查行動電話格式,若第一碼是9且不足10碼則前面補0
                if (isset($inputs['mobile']) && strlen($inputs['mobile']) < 10 && substr($inputs['mobile'], 0, 1) == '9') {
                    $inputs['mobile'] = str_pad($inputs['mobile'], 10, '0', STR_PAD_LEFT);
                }

                //FIXME 那些欄位為必填判斷
                $validators = null;
                $validators['mobile'] = ['required']; //行動電話
                $validators['name'] = ['required']; //姓名
                //$validators['password'] = ['required', 'confirmed', 'min:8']; //密碼
                $validators['email'] = ['required', 'email']; //電子信箱
                $validator = Validator::make($inputs, $validators);
                $validator->setAttributeNames($this->data['displaynames']);
                if ($validator->fails()) {
                    throw new \CustomException(implode(',', $validator->messages()->all()));
                }
                //PF::printr($inputs);
                $rows = \App\Models\member::selectRaw('id'); //一定要有key才可以update
                $rows->myWhere('mobile|S', $inputs['mobile'], "mobile", 'Y');
                //PF::dbSqlPrint($rows);
                $rs = $rows->first();
                if ($rs != null) {
                    $rs->update($inputs);
                    $result .= '更新成功';
                    $updateCount++;
                } else {
                    $inputs['password'] = \Hash::make($inputs['mobile']);   //密碼
                    $result .= '新增成功';
                    $inserts[] = $inputs;
                }
            } catch (ModelNotFoundException $e) {
                $result .= '<font color=red>不存在</font>';
            } catch (\CustomException $e) {
                $result .= '<font color=red>' . $e->getMessage() . '</font>';
            } catch (\Exception $e) {
                \Log::error($e->getMessage());
                $result .= '<font color=red>' . $e->getMessage() . '</font>';
            } finally {
                $this->results[] = $result;
            }

            //echo '<br>';
        }


        $this->results[] = '編輯 ' . $updateCount . ' 筆數成功';
        $chunks = array_chunk($inserts, 500);
        if (count($inserts) > 0) {
            $chunks = array_chunk($inserts, 500);
            foreach ($chunks as $chunk) {
                //\PF::printr(['chunk', $chunk]);
                DB::table('member')->insert($chunk);
                $this->results[] = '新增 ' . count($chunk) . ' 筆數成功';
                ob_implicit_flush(true);
                ob_flush();
            }
        }
    }


    public function batchSize(): int {
        return 10000;
    }

    public function chunkSize(): int {
        return 10000;
    }
    public function formateDate($v) {
        if (PF::isEmpty($v)) {
            return null;
        }
        if (PF::isDate($v)) {
            if (mb_strlen($v) > 8) {
                $v = date('Y-m-d H:i:s', strtotime($v));
            } else {
                $v = date('Y-m-d', strtotime($v));
            }
        } else {
            try {
                //$v = Date::excelToDateTimeObject($v)->format('Y-m-d H:i:s');
                $v = \Carbon::createFromFormat('Ymd', $v)->format('Y-m-d');
            } catch (\Exception $e) {
                $v = '';
            }
        }

        return  $v;
    }
}
