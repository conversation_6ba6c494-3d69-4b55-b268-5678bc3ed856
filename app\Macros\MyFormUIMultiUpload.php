<?php

namespace App\Macros;

use Form;
use Html;

/***
"功能名稱":"共用類別-Html檔案上傳",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
 ***/
class MyFormUIMultiUpload {
    protected $arr;

    public function __construct($form, $arr) {
        $this->arr = $arr;
    }

    public function createHtml() {
        try {
            $html = '';
            $storage = base_path('public/');
            if (PHP_OS == 'Linux') {
                $filename = $storage . $this->arr['folder'] . '/' . $this->arr['filename'];
            } else {
                $filename = $storage . $this->arr['folder'] . '/' . iconv('utf-8', 'big5', $this->arr['filename']);
            }

            // if (false == file_exists($filename) && '' != $this->arr['filename']) {
            //     $html .= '*';
            // }

            $num = substr(md5(uniqid(rand(), true)), 0, 5);

            $html .= '<input type="hidden"  name="' . $this->arr['name'] . '_width"  value="' . $this->arr['width'] . '"  />' . PHP_EOL;
            $html .= '<input type="hidden"  name="' . $this->arr['name'] . '_height"  value="' . $this->arr['height'] . '"  />' . PHP_EOL;

            $html .= '<table width="100%" border="0" cellpadding="3"  class="a' . $num . '" align="left">' . PHP_EOL;

            $imgsplit = explode(',', $this->arr['filename']);
            if ('' != $this->arr['filename']) {
                $imgcount = count($imgsplit);
            } else {
                $imgcount = 0;
            }

            for ($x = -1; $x < $imgcount; ++$x) {
                $html .= '<tr>';
                $html .= '<td>';
                $html .= Form::myUIUpload([
                    'name' => $this->arr['name'] . '[]',
                    'folder' => $this->arr['folder'],
                    'filename' => $imgsplit[$x],
                    'title' => $this->arr['title'],
                    'width' => $this->arr['width'],
                    'height' => $this->arr['height'],
                    'accept' => $this->arr['accept'],
                    'isshowname' => $this->arr['isshowname'],
                    'isdisplayname' => $this->arr['isdisplayname'],
                    'isdel' => 'N',
                ]);

                $html .= '</td>' . PHP_EOL;
                $html .= '<td valign="top">' . PHP_EOL;
                if ('N' != $this->arr['isdel']) {
                    $html .= '<input type="button" class="btn btn-danger btn-sm" onclick="DeleteRow(this)" value="' . __('刪除') . '"  />' . PHP_EOL;
                }
                $html .= '</td>' . PHP_EOL;
                if (null != $this->arr['ismove']) {
                    $html .= '<td width="170">' . PHP_EOL;
                    $html .= "<a href=\"#\"  onclick=\"MoveRow(this,'up');return false;\">▲ </a> | " . PHP_EOL;
                    $html .= "<a href=\"#\"  onclick=\"MoveRow(this,'down');return false;\">▼</a> | " . PHP_EOL;
                    $html .= "<a href=\"#\" onclick=\"MoveRow(this,'top');return false;\">Top</a> | " . PHP_EOL;
                    $html .= "<a href=\"#\" onclick=\"MoveRow(this,'bottom');return false;\">Bottom</a>" . PHP_EOL;
                    $html .= '</td>' . PHP_EOL;
                }

                $html .= '</tr>' . PHP_EOL;
            }
            $html .= '<tfoot>';
            $html .= '<tr>' . PHP_EOL;
            $html .= '<td>' . PHP_EOL;
            $html .= '<script language=JavaScript>' . PHP_EOL;
            $html .= 'var test_canvas = document.createElement("canvas");' . PHP_EOL;
            $html .= 'if (test_canvas.getContext){' . PHP_EOL;
            $html .= "document.write('" . __('支援多檔案上傳') . "');" . PHP_EOL;
            $html .= '}' . PHP_EOL;
            $html .= '</script>' . PHP_EOL;
            $html .= '</td>' . PHP_EOL;
            $html .= '<td>' . PHP_EOL;
            $html .= "<input type=\"button\" class=\"btn btn-warning btn-sm\" onclick=\"CreateRow('a" . $num . "')\" value=\"" . __('新增') . '" />' . PHP_EOL;
            $html .= '</td>' . PHP_EOL;
            $html .= '<td>' . PHP_EOL;

            $html .= '</td>' . PHP_EOL;
            $html .= '</tr>' . PHP_EOL;
            $html .= '</tfoot>' . PHP_EOL;

            $html .= '</table>' . PHP_EOL;
            if ('N' != $this->arr['ismove']) {
                $html .= '<SCRIPT language=JavaScript>' . PHP_EOL;
                $html .= "$('.a" . $num . " tbody tr:eq(0)').hide();" . PHP_EOL;
                $html .= "CreateRow('a" . $num . "');" . PHP_EOL;
                $html .= '</SCRIPT>' . PHP_EOL;
            }
        } catch (\Exception $e) {
            $html .= $e->getMessage();
        }

        return $html;
    }
}
