<?php

namespace App\Services;

/**
 * Class JsonTranslator.
 */
class formquerylogService {
    public $pagename;



    public function __construct() {
    }

    public function run() {
        $inputs = null;

        $inputs['pagename'] = $this->pagename;
        $inputs['pathinfo'] = $_SERVER['REQUEST_URI'];
        $inputs['raw'] = file_get_contents('php://input');

        $cc = '';

        foreach (\Request::all() as $key => $item) {
            $inputs['formbody'] .= $cc . $key . '=' . $item;
            $cc = '&';
        }

        $inputs['querybody'] = $_SERVER['QUERY_STRING'];
        $inputs['created_at'] = date('Y-m-d H:i:s'); /*建立時間-*/

        \App\Models\formquerylog::create($inputs);


        return $inputs['formbody'];
    }

    public function insert($msg) {
        $inputs = null;
        $inputs['pagename'] = $this->pagename;
        $inputs['pathinfo'] = $_SERVER['REQUEST_URI'];
        $inputs['raw'] = $msg;
        $inputs['created_at'] = date('Y-m-d H:i:s'); /*建立時間-*/

        \App\Models\formquerylog::create($inputs);
    }
}
