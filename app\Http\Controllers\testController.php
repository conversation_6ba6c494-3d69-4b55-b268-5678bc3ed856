<?php

namespace App\Http\Controllers;

use PF;
use Illuminate\Http\Request;
use App\Services\ProductService;
//use Illuminate\Support\Facades\DB;

class testController extends Controller {
    private $data;

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        return view('test.index', [
            'data' => $this->data,
        ]);
    }


    public function net() {
        try {
            $url = 'https://www.yahoo.com.tw';
            $client = new \GuzzleHttp\Client(['base_uri' => $url, 'verify' => false]);
            $response = $client->request('GET');
            $statusCode = $response->getStatusCode();
            PF::printr($statusCode);
            $content = $response->getBody();
            ///PF::printr($content);
        } catch (\CustomException $e) {
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    public function day(Request $request) {
        try {
            $day = new \App\Services\dayService();
            echo $day->day();
        } catch (\CustomException $e) {
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    public function allproduct(Request $request) {
        try {
            $woo104 = new \App\Services\woo104Service();
            $woo104->allproduct();
        } catch (\CustomException $e) {
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
    public function handleExpiredProducts(Request $request) {
        try {
            app(ProductService::class)->handleExpiredProducts();
        } catch (\CustomException $e) {
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
}
