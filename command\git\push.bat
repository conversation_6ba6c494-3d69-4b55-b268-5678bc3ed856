chcp 65001
::cd..
::cd..
@echo off
for /f "skip=1" %%x in ('wmic os get localdatetime') do if not defined MyDate set MyDate=%%x
for /f %%x in ('wmic path win32_localtime get /format:list ^| findstr "="') do set %%x
set fmonth=00%Month%
set fday=00%Day%
set today=%Year%%fmonth:~-2%%fday:~-2%

git status --short
set input=
set /p input=請輸入本次上傳的標題:
if %input% == "" (
   echo "取消"
   cmd /k
)
git add -u
echo %today%-%input%
git commit -m "%today%-%input%"
git push --force origin master
::for /F "delims=" %%i in ('git log -1 --pretty^=%%H') do set "commit_id=%%i"
::git reset --hard %commit_id%
echo "上傳成功"
cmd /k



