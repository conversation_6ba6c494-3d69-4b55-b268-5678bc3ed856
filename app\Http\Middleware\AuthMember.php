<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;

class AuthMember //extends Middleware
{
    protected $except = [
        //'membercenter/logout',
        //'membercenter/edit/store',
    ];

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null) {
        foreach ($this->except as $excluded_route) {
            if ($request->path() === $excluded_route) {
                //Skipping $excluded_route from auth check...
                return  $next($request);
            }
        }

        if (false == \Auth::guard('member')->check()) {
            $loginurl = $request->url();
            if (0 == substr_count($loginurl, '/api/')) {
                $cc = '?';
                if ('' != $request->getQueryString()) {
                    $loginurl .= $cc . $request->getQueryString();
                }
                foreach ($request->all()  as $key => $item) {
                    $loginurl .= $cc . $key . '=' . $item;
                    $cc = '&';
                }

                \Session::put('loginurl', $loginurl);
                \Session::save();
            }

            if (substr_count($request->path(), '/api') > 0) {
                $jsondata['resultcode'] = 999;
                $jsondata['resultmessage'] = '請先登入';

                return response()->json($jsondata);
            }

            return redirect('/member/login');
            //return redirect('/auth/facebooklogin');
        }

        return $next($request);
    }
}
