@extends('layouts.raw')
@section('css')
@endsection

@section('js')
@endsection


@section('content')
    <script type="text/javascript">
        function oForm_onsubmit(form) {
            if (PF_FormMultiAll(form) == false) {
                return false
            };
            PF_FieldDisabled(form)
            return true;
        }
    </script>

    <form name="oForm" id="oForm" method="post" language="javascript" action="{{ Request::getRequestUri() }}"
        onsubmit="return oForm_onsubmit(this);">
        <!--novalidate-->
        {{ Form::hidden('code', $data['code']) }}
        <div class="container-fluid p-10">

            <div class="form-group row">
                <h2>環境：</h2>
            </div>

            <div class="form-group row">
                <label class="col-md-2">主機IP<font class=DMIn></font></label>
                <div class="col-md-10">
                    {{ $data['serverip'] }}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">laravel版本<font class=DMIn></font></label>
                <div class="col-md-10">
                    {{ app()->version() }}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">php版本<font class=DMIn></font></label>
                <div class="col-md-10">
                    {!! phpversion() !!}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">debug model</label>
                <div class="col-md-10">
                    {!! $data['debugmodel'] !!}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">APP_ENV</label>
                <div class="col-md-10">
                    {!! $data['APP_ENV'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">upload_max_filesize<font class=DMIn></font></label>
                <div class="col-md-10">
                    {!! $data['upload_max_filesize'] !!}(允許上傳檔案的最大容量.)


                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">max_execution_time<font class=DMIn></font></label>
                <div class="col-md-10">
                    {!! $data['max_execution_time'] !!}秒
                    (每個 script 執行時間上限, 單位是秒;建議360秒(5分鐘).)
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">memory_limit<font class=DMIn></font></label>
                <div class="col-md-10">
                    {!! $data['memory_limit'] !!}

                    (每個執行中的 script 最多可使用的系統記憶體資源;建議512M.)
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">主機時間<font class=DMIn></font></label>
                <div class="col-md-10">
                    {{ $data['nowstatus'] }}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">timezone</label>
                <div class="col-md-10">
                    {!! $data['timezone'] !!}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">register_argc_argv</label>
                <div class="col-md-10">
                    {!! $data['register_argc_argv'] !!} (cron jobs artisan schedule:run可否傳參數)
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">short_open_tag</label>
                <div class="col-md-10">
                    {!! $data['short_open_tag'] !!} (php語法可否支援< ? ?> ) </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">php目錄</label>
                <div class="col-md-10">
                    {!! $_SERVER['DOCUMENT_ROOT'] !!}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">加排程資訊</label>

                <div class="col-md-10">

                    php -d register_argc_argv=On {!! str_replace('public/', '', base_path()) !!}/artisan schedule:run
                    >>{!! str_replace('public/', '', base_path()) !!}/storage/logs/laravel-$(date +\%Y-\%m-\%d).log

                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">public php</label>

                <div class="col-md-10">

                    {!! $data['public_php'] !!}

                </div>
            </div>



            <div class="form-group row">
                <h2>資料庫</2>
            </div>

            <div class="form-group row">
                <label class="col-md-2">mysql是否連結成功</label>
                <div class="col-md-10">
                    {!! $data['dbstatus'] !!}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">mysql版本</label>
                <div class="col-md-10">
                    {!! $data['dbversion'] !!}(建議5.7以上)
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">mysql主機時間</label>
                <div class="col-md-10">
                    {!! $data['dbtime'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">dbname</label>
                <div class="col-md-10">
                    {!! $data['dbname'] !!}
                </div>
            </div>

            <div class="form-group row">
                <h4>cache</h4>
            </div>

            <div class="form-group row">
                <label class="col-md-2">file</label>
                <div class="col-md-10">
                    {!! $data['cachefile'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">memcached</label>
                <div class="col-md-10">
                    {!! $data['cachememcached'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">redis</label>
                <div class="col-md-10">
                    {!! $data['cacheredis'] !!}
                </div>
            </div>

            <div class="form-group row">
                <h4>電子信箱</h4>
            </div>

            <div class="form-group row">
                <label class="col-md-2">SMTP</label>
                <div class="col-md-10">
                    {!! $data['mailstatus'] !!}

                    <button onclick="$('#oForm input[name=\'isemail\']').val(1)" class="btn btn-primary">
                        寄發
                    </button>
                    {{ Form::hidden('isemail', 0) }}
                </div>
            </div>


            <div class="form-group row">
                <h4>QUEUE</h4>
            </div>

            <div class="form-group row">
                <label class="col-md-2">SMTP-queue:work</label>
                <div class="col-md-10">
                    {!! $data['mailqueuestatus'] !!}
                    <button onclick="$('#oForm input[name=\'isqueueemail\']').val(1)" class="btn btn-primary">
                        寄發
                    </button>
                    {{ Form::hidden('isqueueemail', 0) }}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">未處理筆數</label>
                <div class="col-md-10">
                    {!! $data['queuecount'] !!}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">QUEUE_CONNECTION</label>
                <div class="col-md-10">
                    {{ config('queue.default') }}
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">grep queue:work</label>
                <div class="col-md-10">
                    <pre>
{{ $data['grep_queue_work'] }}
                    </pre>

                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">加排程資訊</label>

                <div class="col-md-10">
                    清除queue:work<BR>
                    php -d register_argc_argv=On {!! str_replace('public/', '', base_path()) !!}/artisan queue:restart > /dev/null
                    2>&1 &<BR>
                    不會常駐每分鐘執完就關閉。<BR>
                    php -d register_argc_argv=On {!! str_replace('public/', '', base_path()) !!}/artisan queue:work --stop-when-empty > /dev/null
                    2>&1 &
                    <BR>


                </div>
            </div>

            <div class="form-group row">

                <h4>元件</h4>
            </div>

            <div class="form-group row">
                <label class="col-md-2">session</label>
                <div class="col-md-10">
                    {!! $data['session'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">是否支援exif(php_exif.dll)</label>
                <div class="col-md-10">
                    {!! $data['exifstatus'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">是否支援縮圖GD(php_gd.dll)</label>
                <div class="col-md-10">
                    {!! $data['gdstatus'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">是否支援curl(php_curl.dll)</label>
                <div class="col-md-10">
                    {!! $data['curlstatus'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">simplexml_load_file(php_xmlrpc.dll)</label>
                <div class="col-md-10">
                    {!! $data['xmlstatus'] !!}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">fileifno</label>
                <div class="col-md-10">
                    {!! $data['fileinfostatus'] !!}
                </div>
            </div>

            <div class="form-group row">
                <h4>檔案寫入權限</h4>
            </div>
            @foreach ($data['folderarray'] as $item)
                <div class="form-group row">
                    <label class="col-md-2">目錄寫入{!! $item !!}</label>
                    <div class="col-md-10">
                        {!! $data[$item . 'status'] !!}
                    </div>
                </div>
            @endforeach
            @foreach ($data['filearray'] as $item)
                <div class="form-group row">
                    <label class="col-md-2">檔案寫入{!! $item !!}</label>
                    <div class="col-md-10">
                        {!! $data[$item . 'status'] !!}
                    </div>
                </div>
            @endforeach
            <div class="form-group row">
                <label class="col-md-2">驗證碼</label>
                <div class="col-md-10">
                    {!! $data['captcha_img'] !!}
                </div>
            </div>

            <div class="form-group row">
                <h4>log</h4>
            </div>

            <!-- <div class="form-group row">
                                                                                                                                    <label class="col-md-2">schedule.log</label>
                                                                                                                                    <div class="col-md-10">
                                                                                                                                        <textarea name="SYMBOL" cols="37" rows="5" style='width:90%;height:200px'>{!! $data['schedule.log'] !!}</textarea>
                                                                                                                                    </div>
                                                                                                                                </div> -->





            <div class="form-group row">
                <label class="col-md-2">laravel.log{!! date('Y-m-d') !!}</label>
                <div class="col-md-10">
                    <textarea name="SYMBOL" cols="37" rows="5" style='width:90%;height:200px'>{!! $data['laravel.log'] !!}</textarea>

                </div>
            </div>




        </div>
    </form>
@endsection
