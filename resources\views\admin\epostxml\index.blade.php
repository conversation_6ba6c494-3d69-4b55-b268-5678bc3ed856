@extends('admin.layouts.master')
@section('css')
@endsection

@section('js')
@endsection
@section('nav')
{!!$data['nav']!!}
@endsection


@section('content')



<div class="container">
    <form name="SearchoForm" class="form-inline" method="post" language="javascript"
        action="{{request()->getRequestUri()}}" onsubmit="return SearchoForm_onsubmit(this);">
        @include('admin.layouts.search', [])
    </form>
</div>


<div align="right" style=''>
    <form name="AddoForm" method="post" language="javascript" action="{{url('admin/adminuser')}}/create">
        <button type="submit" class="btn btn-info">新增</button>
        @include('admin.layouts.hidden', ['method'=>'AddoForm'])
    </form>
</div>
<table border="0" width="100%" cellpadding="3" align="center" class="oFormTable">
    <!--排序的參數-->
    <form name="SortoForm" method="post">
        @include('admin.layouts.hidden', ['method'=>'SortoForm'])
        <thead>
            <tr valign="top" align="left">

                <th align="center" width="100">
                    <button type="button" class="btn btn-danger"
                        onclick="if (confirm('確定要刪除？')==false){return false;};document.forms['oForm'].action='{{url('admin/epostxml/destroy')}}';document.forms['oForm'].submit();">刪除</button>
                    <span id="delA"
                        onClick="if (document.getElementById('delA').innerHTML=='全選'){try{checkAll(jQuery($('.all')))}catch(e){alert('目前無可刪除資料');return false;};document.getElementById('delA').innerHTML='全不選';}else{try{uncheckAll($('.all'))}catch(e){};document.getElementById('delA').innerHTML='全選';}">全選</span>
                </th>


            </tr>
        </thead>

    </form>


    <!--傳給下一頁的參數-->
    <form method="post" language="javascript" name="oForm" action="{{url('admin/adminuser')}}">
        {!! Form::hidden("page", $request->input("page") ) !!}
        @include('admin.layouts.hidden', ['method'=>'oForm'])
        <tbody>
            @foreach ($data['rows'] as $rs)

            <tr>

                <td valign="top" title="編輯">
                    <button type="submit" class="btn btn-info"
                        onclick="javascript:document.forms['oForm'].action='{{url('admin/adminuser')}}/edit/?edit={{ $rs->userid }}';">編輯</button>



                    @if (Auth::guard('admin')->user()->status=="999")
                    <input type="checkbox" name="del[]" class="all" value="{{$rs->userid}}">
    @endif

</td>


            </tr>
            @endforeach

        </tbody>
    </form>


</table>

{{ $data['rows']->links('layouts.paginate') }}
@endsection