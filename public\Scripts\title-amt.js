document.addEventListener("DOMContentLoaded", function () {

  // Title Animation - 加入安全檢查
  var spanText = function spanText(text) {
    if (!text) return; // 安全檢查
    var string = text.innerText;
    var spaned = '';
    for (var i = 0; i < string.length; i++) {
      if (string.substring(i, i + 1) === ' ') spaned += string.substring(i, i + 1);
      else spaned += '<span>' + string.substring(i, i + 1) + '</span>';
    }
    text.innerHTML = spaned;
  }

  var headline = document.querySelector("h1");
  if (headline) { // 加入安全檢查
    spanText(headline);
  }

  let animations = document.querySelectorAll('.animation');
  animations.forEach(animation => {
    let letters = animation.querySelectorAll('span');
    letters.forEach((letter, i) => {
      letter.style.animationDelay = (i * 0.1) + 's';
    })
  })

});