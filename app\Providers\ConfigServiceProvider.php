<?php

namespace App\Providers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\ServiceProvider;
use DB;

class ConfigServiceProvider extends ServiceProvider
{
    //不同網址不同資料庫的設定
    public function register() {
        //Select DB based on domain url...
        //echo 0;
        $currDomain =request()->getHost();
       //echo $currDomain;
       // echo 2;
        $currDB = 'mysql';
        //if($currDomain == 'allennb') $currDB = 'mysql1';
        if($currDomain == 'mega1.881.tw') $currDB = 'mysql1';
    
        config([
            'database.default' => $currDB,
        ]);
        DB::reconnect(); // this will help in reconnecting the database if the connection is already established. and import the DB facade. 
    }
   
}
