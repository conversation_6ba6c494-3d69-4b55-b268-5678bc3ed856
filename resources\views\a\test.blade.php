@extends('layouts.raw')
@section('css')

@endsection

@section('js')
@endsection

@section('content')

<script type="text/javascript">
    function sleep(n) {
        var start = new Date().getTime();
        while (true)
            if (new Date().getTime() - start > n) break;
    }
    var sendcount = 0;
    var sendcount0 = 0;
    var sendcount1 = 0;
    var tcount = 0;
    document.addEventListener("DOMContentLoaded", () => {
        tcount = jQuery("[rel='resend']").length;
        $("#tcount").html(tcount);
    });

    function action(id, class1, method) {
        console.log(class1 + ":" + method);
        // i=0;

        // jQuery("[rel='resend']").each(function (i,item) {
        // setTimeout(function () {
        //i++;
        //var url=$(item).attr("href");
        //var id=$(item).attr("id");

        var dict = {
            url: "{{ url('/') }}/api/a/action",

            data: {
                'class': class1,
                'method': method
            },
            //data:JSON.stringify(this.inputs),
            //dataType: 'json',
            //debug:true,
        }
        $("#message_" + id).html('loading...');
        $("#message_" + id).show();
        PF_ajax(dict).done(function(obj) {
            //console.log(id);

            //    mytimer= setTimeout(function () {
            //     $("#message_" + id).hide(1000);
            //    }, 10000);
            console.log(obj.resultmessage);
            $("#message_" + id).html(obj.resultmessage);
            if (obj.resultmessage.indexOf('failure') > -1) {
                $("#message_" + id).addClass('text-danger');
            } else {
                if (obj.resultcode == 0) {
                    sendcount1++;
                    //$("#message_"+id).html("OK");
                    $("#message_" + id).addClass('text-dark');
                } else {
                    sendcount0++;
                    $("#message_" + id).addClass('text-danger');
                }
            }
        }).fail(function(resp) {
            console.log(resp);
            sendcount0++;
            $("#message_" + id).html(resp.responseText);
            $("#message_" + id).addClass('text-danger');
        }).then(function() {
            // sendcount=sendcount1+sendcount0;
            // if(tcount==sendcount){
            // _alert("已完成 : "+sendcount+" 筆",'success');
            // }
        });
        // }, i*1000);
        // });
    }
</script>


<SCRIPT language=JavaScript>
    function oForm_onsubmit(form) {
        if (PF_FormMultiAll(form) == false) { return false; }

        PF_FieldDisabled(form); //將全部button Disabled
        return true;
    }
</SCRIPT>

<h2>預計執行筆數:
    <span id="sendcount">0</span>
    /<span id="tcount">0</span>
    (
    成功
    <span id="sendcount1" class="badge bg-success">0</span>
    /
    失敗:<span id="sendcount0" class="badge bg-danger">0</span>
    )
</h2>





@foreach ($data['rows'] as $key => $rs )

<div class="card card-success p-2">
    <div class="card-header">
        <h3 class="card-title">{{$rs['name']}}</h3>
    </div>
    <div class="card-body">

        @foreach ($rs['rows'] as $key1 => $rs1 )
        <div class="callout callout-info">
            <div class="form-group row">
                <label class="col-md-2">
                    {{$rs1['method']}}
                </label>
                <div class="col-md-10">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <button type="button" class="btn btn-primary" rel="resend" onclick="action('{{$key}}_{{$key1}}','tests\\{{str_replace('\\','\\\\',$rs['name'])}}','{{$rs1['method']}}')">測試</button>
                        </div>
                        <input class="form-control" id="url_{{$key}}_{{$key1}}" type="text" value="{{$rs1['file']}}" readonly>
                        <div class="input-group-append">
                            <button type="button" onclick="copy('url_{{$key}}_{{$key1}}')" class="btn btn-info">複製</button>

                        </div>
                    </div>
                </div>
            </div>
            <pre id="message_{{$key}}_{{$key1}}" style="display:none">
            </pre>
        </div>







        @endforeach
    </div>
</div>
@endforeach






@endsection