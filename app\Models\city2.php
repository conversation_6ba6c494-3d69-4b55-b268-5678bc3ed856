<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use PF;
//地區
class city2 extends baseModel
{
    

	public $tabletitle = '地區';
    public $table = 'city2';
    public $primaryKey = 'city2id';
    
    //欄位必填
    public $rules = [
		'city1title' => 'required',
'city2title' => 'required',
'Postal' => 'required',
'city2id' => 'required',

    ];
    public $fieldInfo = [
'city1title'=>['title'=>'縣市','type'=>'varchar(50)'],//
'city2title'=>['title'=>'鄉鎮','type'=>'varchar(50)'],//
'Postal'=>['title'=>'郵遞區號','type'=>'varchar(50)'],//
'city1id'=>['title'=>'縣市編號','type'=>'int(11)'],//
'city2id'=>['title'=>'自動編號','type'=>'int(11)'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    
    protected $fillable = ['city1title','city2title','Postal']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = [];    
  /*
  public function __construct($attr = array(), $exists = false) {
        $this->fillable = parent::getfillables();//接受$request->all();
        parent::__construct($attr, $exists);
        
        parent::setFieldInfo($this->fieldInfo);
  }         
*/
  public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          

        });
         static::deleting(function ($model) {
            //  DB::table('city2')->select()
            // ->myWhere('city2id|N', $model->city2id, "city2id", 'Y')
            // ->delete();



          
        });
        static::deleted(function ($model) {
            
        });

    }	


}
