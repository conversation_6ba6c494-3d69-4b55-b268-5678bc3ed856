@extends('layouts.master')

@section('css')
    {{-- 移除原有的 CSS 文件，改用 Tailwind CSS --}}
@endsection

@section('js')
@endsection

@section('content')
    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">媒體專訪</h2>
    <!-- 麵包屑導航 -->


    <!-- 主要內容區域 -->
    <div class="bg-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- 頁面標題 -->
            <div class="text-center mb-12">

                <p class="text-lg text-gray-600 max-w-2xl mx-auto">探索我們在各大媒體的專業報導與深度訪談</p>
                <div class="mt-6 w-24 h-1 bg-primary mx-auto rounded-full"></div>
            </div>

            <!-- 媒體專訪列表 -->
            @if (count($data['rows']) > 0)
                <div class="grid gap-8 md:gap-12">
                    @foreach ($data['rows'] as $index => $rs)
                        <article class="group">
                            <div
                                class="grid lg:grid-cols-2 gap-8 items-center {{ $index % 2 == 1 ? 'lg:grid-flow-col-dense' : '' }}">
                                <!-- 圖片區域 -->
                                <div
                                    class="relative overflow-hidden rounded-2xl shadow-lg {{ $index % 2 == 1 ? 'lg:col-start-2' : '' }}">
                                    <a href="{{ $rs->memo }}" target="_blank"
                                        class="block aspect-[4/3] relative group-hover:scale-105 transition-transform duration-300">
                                        {{ Html::myUIImage([
                                            'folder' => 'https://www.ebayhouse.com.tw/images/media',
                                            'filename' => $rs->field1,
                                            'alt' => $rs->title,
                                            'width' => 750,
                                            'height' => 350,
                                            'noimg' => 'no-picture.gif',
                                            'class' => 'w-full h-full object-cover',
                                        ]) }}
                                        <!-- 播放按鈕覆蓋層 -->
                                        <div
                                            class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                                            <div
                                                class="bg-white bg-opacity-90 rounded-full p-4 transform scale-0 group-hover:scale-100 transition-transform duration-300">
                                                <svg class="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M8 5v10l8-5-8-5z" />
                                                </svg>
                                            </div>
                                        </div>
                                    </a>
                                </div>

                                <!-- 內容區域 -->
                                <div class="space-y-6 {{ $index % 2 == 1 ? 'lg:col-start-1' : '' }}">
                                    <!-- 日期標籤 -->
                                    <div class="flex items-center space-x-2 text-sm">
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                            {{ PF::formatDate($rs->created_at) }}
                                        </span>
                                    </div>

                                    <!-- 標題 -->
                                    <h2
                                        class="text-2xl lg:text-3xl font-bold text-gray-900 leading-tight group-hover:text-primary transition-colors duration-200">
                                        {{ $rs->title }}
                                    </h2>

                                    <!-- 內容摘要 -->
                                    <div class="text-gray-600 leading-relaxed text-lg">
                                        {!! PF::vbcrlf($rs->body) !!}
                                    </div>

                                    <!-- 閱讀更多按鈕 -->
                                    <div class="pt-4">
                                        <a href="{{ $rs->memo }}" target="_blank"
                                            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 shadow-lg hover:shadow-xl">
                                            觀看完整報導
                                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- 分隔線 (除了最後一個項目) -->
                            @if (!$loop->last)
                                <div class="border-b border-gray-200 mt-12"></div>
                            @endif
                        </article>
                    @endforeach
                </div>
            @else
                <!-- 空狀態 -->
                <div class="text-center py-16">
                    <svg class="mx-auto h-24 w-24 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <h3 class="mt-4 text-lg font-medium text-gray-900">目前沒有媒體專訪資料</h3>
                    <p class="mt-2 text-gray-500">請稍後再來查看最新的媒體報導內容。</p>
                </div>
            @endif

            <!-- 分頁導航 -->
            @if (method_exists($data['rows'], 'links'))
                {{ $data['rows']->links('layouts.paginate') }}
            @endif
        </div>
    </div>

@endsection
