<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/***
"功能名稱":"建立 資料表",
"資料表":"product",
"建立時間":"2022-05-21 11:45:22 ",
 ***/
class CreateproductTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (!Schema::hasTable('product')) {

            Schema::create('product', function (Blueprint $table) {
                $table->engine = 'MyISAM';
                $table->increments('productid')->comment('自動編號');
                $table->string('productkind', 50)->nullable()->comment('物件種類');
                $table->string('producttitle', 500)->nullable()->comment('標題');
                $table->string('number', 50)->comment('編號');
                $table->string('court', 50)->nullable()->comment('法院');
                $table->string('city1title', 50)->nullable()->comment('縣市');
                $table->string('city2title', 50)->nullable()->comment('鄉鎮');
                $table->string('auctions', 50)->nullable()->comment('拍賣情況');
                $table->string('debtor', 50)->nullable()->comment('債務人');
                $table->string('proofreadingday', 50)->nullable()->comment('校對日');
                $table->string('tenderdate', 50)->nullable()->comment('投標日');
                $table->string('beattime', 50)->nullable()->comment('拍次');
                $table->string('nocross_point', 50)->nullable()->comment('點交否');
                $table->string('mainlawnestablishment', 50)->nullable()->comment('主建坪');
                $table->string('attachedtolawnestablishment', 50)->nullable()->comment('附屬坪');
                $table->string('additionalping', 50)->nullable()->comment('增建坪');
                $table->string('postulateping', 50)->nullable()->comment('公設坪');
                $table->string('carping', 50)->nullable()->comment('車位坪');

                $table->string('noticelawnestablishment', 50)->nullable()->comment('公告建坪');
                $table->string('stakeholdersfloor', 50)->nullable()->comment('持分地坪');
                $table->float('pingtotalnumberof', 7, 2)->nullable()->default(0)->comment('總坪數');

                $table->float('other_ping', 7, 2)->nullable()->default(0)->comment('其他坪數');
                $table->float('land_total_ping', 7, 2)->nullable()->default(0)->comment('土地總坪');


                $table->string('floorprice', 50)->nullable()->comment('坪單價');
                $table->string('aftermakingvalue_added', 50)->nullable()->comment('拍後增值');
                $table->string('currentvalues', 50)->nullable()->comment('公告現值');
                $table->integer('totalupset')->nullable()->default(0)->comment('總底價');
                $table->string('margin', 50)->nullable()->comment('保證金');
                $table->string('address', 300)->comment('地址');
                $table->string('buildname', 500)->nullable()->comment('大樓名稱');
                $table->string('pattern', 50)->nullable()->comment('法拍屋種類');
                $table->integer('houseage')->nullable()->default(0)->comment('屋齡');
                $table->string('postulatemorethan', 50)->nullable()->comment('公設比');
                $table->string('architecture', 500)->nullable()->comment('結構建材');
                $table->string('storey', 500)->nullable()->comment('樓高');
                $table->mediumtext('landaddress')->nullable()->comment('土地地址');
                $table->string('landprice', 400)->nullable()->comment('土地公現');
                $table->mediumtext('buildings')->nullable()->comment('建物');
                $table->mediumtext('bidsrecords')->nullable()->comment('流標記錄');
                $table->string('hisrightis', 500)->nullable()->comment('他項權利');
                $table->mediumtext('transcriptinformation')->nullable()->comment('謄本資料');
                $table->mediumtext('courttranscript')->nullable()->comment('法院筆錄');
                $table->string('landvalue1', 50)->nullable()->comment('土地增值金額1');
                $table->string('landvalue2', 50)->nullable()->comment('土地增值金額2');
                $table->dateTime('createdate')->nullable()->comment('建立時間');
                $table->string('management', 500)->nullable()->comment('管理方式');
                $table->string('fees', 500)->nullable()->comment('費用');
                $table->string('parkingmode', 500)->nullable()->comment('停車方式');
                $table->string('transportfunction', 500)->nullable()->comment('交通機能');
                $table->string('schooldistrict', 500)->nullable()->comment('學區');
                $table->integer('hits')->nullable()->default(0)->comment('點率閱');
                $table->string('location', 100)->nullable()->comment('放置位置');
                $table->tinyInteger('online')->nullable()->default(1)->comment('上下架');
                $table->string('sealedhuman', 100)->nullable()->comment('查封人');
                $table->string('thenumberofbidders', 100)->nullable()->comment('投標人數');
                $table->string('bidwere', 100)->nullable()->comment('得標人');
                $table->string('thebidprice', 100)->nullable()->comment('得標價格');
                $table->string('increasetheamountof', 100)->nullable()->comment('加價金額');
                $table->string('facingthelaneis', 100)->nullable()->comment('面臨路寬');
                $table->mediumtext('memo')->nullable()->comment('備註');
                $table->string('mrtland', 500)->nullable()->comment('捷運路線');
                $table->string('mrtstation', 500)->nullable()->comment('捷運站名');
                $table->string('locationadmin', 500)->nullable()->comment('放置位置2');
                $table->string('point', 50)->nullable()->comment('點數');
                $table->float('auctionssortnum', 5, 3)->nullable()->default(0)->comment('排序號碼');
                $table->string('lat', 50)->nullable()->comment('經度');
                $table->string('lng', 50)->nullable()->comment('緯度');
                $table->string('url', 255)->nullable()->comment('網址');
                $table->mediumtext('img')->nullable()->comment('圖案');
                $table->string('pdf', 50)->nullable()->comment('pdf');



                // 用戶與分店資訊
                $table->string('user_code', 50)->nullable()->comment('用戶代碼');
                $table->string('branch_code', 50)->nullable()->comment('分店代碼');

                // 委託日期
                $table->date('commission_date_start')->nullable()->comment('委託日期(起)');
                $table->date('commission_date_end')->nullable()->comment('委託日期(迄)');

                // 樓層資訊
                $table->string('floor_start', 10)->nullable()->comment('樓層(起)');
                $table->string('floor_end', 10)->nullable()->comment('樓層(迄)');

                $table->integer('room_count')->nullable()->default(0)->comment('格局(房)');
                $table->integer('living_room_count')->nullable()->default(0)->comment('格局(廳)');
                $table->integer('hall_count')->nullable()->default(0)->comment('格局(室)');
                $table->integer('bathroom_count')->nullable()->default(0)->comment('格局(衛)');
                $table->integer('balcony_count')->nullable()->default(0)->comment('格局(陽台)');
                $table->string('virtual_tour_video')->nullable()->comment('線上賞屋影片');



                $table->timestamps();
                $table->unique(['number', 'city1title'], 'product_unique');
            });
            \DB::statement("ALTER TABLE product COMMENT '物件'");
        }
        /*
                $table->integer('activitysession_id')->unsigned()->comment('場次');
                $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

                $table->string('kind',50)->index()->comment('種類');
                $table->mediumText('body')->nullable()->comment('說明');
                $table->dateTime('begindate')->nullable()->comment('開始時間');
                $table->integer('hits')->default(0)->comment('點率次數');
                $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
                $table->integer('userid')->nullable()->comment('編輯人員');
                $table->string('useraccount', 50)->nullable()->comment('編輯人員');
                $table->string('account',50)->unique();;
                $table->timestamps('reviewed_at')->default('now');
                $table->unique(array('kind', 'kindid'));
                $table->index(array('kind', 'kindid'));
                $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('product');
    }
}
