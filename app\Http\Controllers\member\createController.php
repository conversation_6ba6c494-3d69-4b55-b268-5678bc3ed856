<?php

namespace App\Http\Controllers\member;

use DB;
use PF;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\city1Repository;
use App\Repositories\city2Repository;
use App\Repositories\memberRepository;

class createController extends Controller {
    private $data;
    private $fieldnicknames;

    private $memberRepo;
    private $city1Repo;
    private $city2Repo;





    public function __construct(memberRepository $memberRepo, city1Repository $city1Repo, city2Repository $city2Repo) {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->memberRepo = $memberRepo;

        $this->city1Repo = $city1Repo;
        $this->city2Repo = $city2Repo;
        $this->data['displaynames'] = $this->memberRepo->getFieldTitleArray();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $validators = null;
        $validators['name'] = 'required';

        $validators['email'] = ['required', 'email']; //電子信箱
        $validators['mobile'] = 'required|regex:/^09\d{8}$/';
        $validators['name'] = ['required']; //input name:password_confirmation

        $validator = \Validator::make($this->data, $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
            //throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        $this->data['title'] = '會員註冊';
        $rows = $this->city1Repo->selectRaw('*');
        $rows->myWhere('online|N', 1, "kind", 'N');
        $rows->orderByRaw('sortnum desc');
        $rows = $rows->get();
        $this->data['city1rows'] = $rows;

        return view(
            'member.create.index',
            [
                'data' => $this->data,
            ]
        );
    }

    public function store(Request $request) {
        $memberService = new \App\Services\memberService();
        $id = $memberService->create($request, $this->data);
        //$edit = DB::getPdo()->lastInsertId();//取得ID
        $this->data['alert'] = __('加入成功');
        //$this->data['alert'] = __('我們將儘快審查您的資料');
        $member = \App\Models\member::where('id', $id)->first();
        //PF::printr($member);
        \Auth::guard('member')->login($member, true);

        return redirect('/')->with('js', '_alert("加入成功","success")');

        //return redirect('membercenter/step')->with('js', '_alert("加入成功")');

        //return redirect('/')->with('js', '_toast("加入成功",500)');

        return view(
            'member.login',
            [
                'data' => $this->data,
            ]
        );
    }
}
