<?php

namespace App\Exceptions;

use DB;
use Mail;
use File;
use Exception;
use Throwable;
use CustomException;
use App\Libraries\PF;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\View;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Request;
use Illuminate\View\ViewNotFoundException;
use Illuminate\Support\ItemNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class Handler extends ExceptionHandler {
    public $statusCode;

    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    // 不要寫進 log 裡的 Exception
    protected $dontReport = [
        CustomException::class,
        \BadMethodCallException::class,
        \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        \Symfony\Component\HttpKernel\Exception\HttpException::class,
        \Illuminate\Validation\ValidationException::class,
        NotFoundException::class,
        ViewNotFoundException::class,
        \Illuminate\Contracts\Container\BindingResolutionException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    // 紀錄哪些資料不要被記錄進 flash 中
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];
    public function report(Throwable $exception) {

        $logfile = storage_path('logs/laravel-' . date('Y-m-d') . '.log');
        if (false == \File::exists($logfile)) {
            \File::put($logfile, '');
            chmod($logfile, 0777);
        }
        if (File::isWritable($logfile) == false) {
            chmod($logfile, 0777);
            return;
        }
        //dd($exception);
        // 處理郵件發送異常
        // if (
        //     $exception instanceof \Symfony\Component\Mailer\Exception\TransportException
        //     || $exception instanceof \Swift_TransportException
        //     || $exception instanceof \Illuminate\Mail\MailSendException
        // ) {
        //     \Log::error('郵件發送失敗：' . $exception->getMessage());
        //     return;
        // }

        try {
            // 嘗試記錄異常到日誌
            parent::report($exception);
        } catch (Throwable $e) {
            // 如果無法記錄異常到日誌，不進行任何操作或者輸出到控制台
            //$this->handleLoggingFailure($e);
        }
    }

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register() {
        // $this->reportable(function (Throwable $e) {
        // });

        $this->renderable(function (Throwable $e, $request) {
            return $this->handleException($e, $request);
        });
    }

    public function setStatusCode($e, $request) {
        if (null == $e) {
            return;
        }
        if (method_exists($e, 'getStatusCode')) {
            $statusCode = $e->getStatusCode();
        } else {
            $statusCode = 500;
            if ('Unauthenticated.' == $e->getMessage()) {
                $statusCode = 400; //400：Bad request，请求验证失败
            }
        }


        if ($e instanceof \CustomException ||  Str::contains($e->getMessage(), ['The payload is invalid', 'Too few arguments to function', 'method is not supported', 'No query results for model'])) {
            $statusCode = 512;
        } else {
            $errors = explode('\\', $e->getMessage());
            //echo get_class($e);
            if (($e instanceof ItemNotFoundException)) {
                $statusCode = 404;
                throw new \CustomException('查無資料', 404);
            } else if (($e instanceof \BadMethodCallException)) {
                $statusCode = 404;
                throw new \CustomException('找不到函式:' . $errors[count($errors) - 1], 404);
            } else {
                if (
                    '404' == $statusCode || Str::contains($e->getMessage(), ['does not exist', 'No such file or directory', 'must be an instance of Illuminate\Http\Reques', 'The Process class relies on proc_open', 'Argument 1 passed to']) ||
                    $e instanceof \BadMethodCallException ||
                    $e instanceof \ReflectionException ||
                    $e instanceof \NotFoundHttpException ||
                    $e instanceof \ViewNotFoundException ||
                    $e instanceof \NotFoundException || Str::containsAll($e->getMessage(), ['View', 'not found.'])
                ) {
                    $statusCode = 404;
                }
            }
        }
        // echo "<hr>";
        // echo $statusCode.$e->getMessage();
        // echo "<hr>";
        $this->statusCode = $statusCode;
        //\PF::printr(['\$this->statusCode', $this->statusCode]);
    }



    public function handleException(Throwable $e, $request) {
        $msg = $e->getMessage();

        $this->setStatusCode($e, $request);

        if ($e instanceof QueryException || \Str::contains($e->getMessage(), ['SQLSTATE['])) {
            if (Str::contains($e->getMessage(), ['Duplicate entry'])) {
                try {
                    //echo $e->getMessage();

                    $dbUnique = Str::between($e->getMessage(), "for key '", '_unique');
                    $tableName = Str::between($e->getMessage(), "insert into `", '` (`');
                    $dbUnique = str_replace($tableName . '_', '', $dbUnique);

                    $tableTitle = DB::select("SHOW TABLE STATUS LIKE '" . $tableName . "'");

                    if (isset($tableTitle[0]->Comment)) {
                        $tableTitle = $tableTitle[0]->Comment;
                    }

                    $tableNameModel = 'App\\Models\\' . $tableName;
                    $reflectionClass = new \ReflectionClass($tableNameModel);
                    $fieldTitle = $reflectionClass->getDefaultProperties()['fieldInfo'][$dbUnique]['title'];


                    $msg = $tableTitle . ' - ' . $fieldTitle . ' ' . Str::between($e->getMessage(), 'Duplicate entry', 'for key') . ' 不得重覆 ';
                } catch (\Exception $e) {
                    $msg = Str::between($e->getMessage(), 'Duplicate entry', 'for key') . ' 不得重覆 ';
                }
                $this->statusCode = '512';
            } elseif (Str::contains($e->getMessage(), ['cannot be null'])) {
                try {
                    $fieldName = Str::between($e->getMessage(), 'Column \'', '\' cannot');

                    //echo $e->getMessage();
                    $tableName = Str::between($e->getMessage(), 'insert into `', '` ');
                    $modelName = 'App\\Models\\' . $tableName;

                    $reflectionClass = new \ReflectionClass($modelName);

                    $tableTitle = DB::select("SHOW TABLE STATUS LIKE '" . $tableName . "'");

                    if (isset($tableTitle[0]->Comment)) {
                        $tableTitle = $tableTitle[0]->Comment;
                    }


                    $fieldTitle = $reflectionClass->getDefaultProperties()['fieldInfo'][$fieldName]['title'];
                    $msg = $tableTitle . ' - ' . $fieldTitle . '(' . $fieldName . ')' . ' 不得為空 ';
                } catch (\Exception $e) {
                    $msg = $fieldName . ' 不得空白 ';
                }
                $this->statusCode = '512';
            } else {
                if (\Str::contains(\config('app.env'), ['dev', 'testing'])) {
                    $msg = '資料庫語法錯誤=>' . $e->getMessage();
                } else {
                    $msg = __('使用此功能出現錯誤,我們已經將你的錯誤訊息EMAIL給管理者,將儘速為您處理!');
                }
            }
        } elseif (Str::contains($e->getMessage(), ['No query results for model'])) {
            $tableName = Str::between($e->getMessage(), "No query results for model [App\\Models\\", ']');
            $tableTitle = DB::select("SHOW TABLE STATUS LIKE '" . $tableName . "'");
            if (isset($tableTitle[0]->Comment)) {
                $tableTitle = $tableTitle[0]->Comment;
            }
            $msg =  'No Data (' . $tableTitle . ')';
        } elseif ($e instanceof \ReflectionException) {
            $msg = '服務端出現異常=>' . $e->getMessage();
        } elseif (
            $e instanceof \Symfony\Component\Mailer\Exception\TransportException
            || $e instanceof \Swift_TransportException
            || $e instanceof \Illuminate\Mail\MailSendException
        ) {
            $msg = '發信系統異常';
        } elseif ($e instanceof \RuntimeException) {
            if (401 == $this->statusCode) {
                if (PF::isEmpty($e->getMessage())) {
                    $msg = '認證失敗';
                }
            } elseif (403 == $this->statusCode) {
                $msg = '權限不足';
            } else {
                $msg = '服務逾時請重試=>' . $e->getMessage();
            }
        } elseif (404 == $this->statusCode || 400 == $this->statusCode || 512 == $this->statusCode) {
            $msg = $e->getMessage();
        } else {
            $message = strval($e->getMessage());
            $message = str_replace(base_path(''), "", $message);
            $msg = 'Error File : ' . str_replace(base_path(), '', $e->getFile()) . '<br>Message : ' . $message . '<br>Line : ' . $e->getLine();
        }


        if ('' == $msg) {
            $msg = ' Status Code:' . $e->getStatusCode();
        }

        $statusPage = '500';
        if ('404' == $this->statusCode || '512' == $this->statusCode) {
            $statusPage = $this->statusCode;
        }

        if ('404' == $statusPage) {
            $lasturl = Request::path();
            if (\Str::contains($lasturl, ['.'])) {
                return response('no found');
            }
        }
        if (Str::contains($e->getMessage(), ['The payload is invalid', 'Too few arguments to function', 'method is not supported'])) {
            $this->statusCode = '512';
            $statusPage = 512;

            $msg = '資料格式有誤(' . $e->getMessage() . ')';
        }
        // if (Str::contains($e->getMessage(), ['No query results for model'])) {
        //     $msg = 'No data ';
        //     try {
        //         $model = Str::between($e->getMessage(), 'App\\Models\\', ']');
        //         $modelName = 'App\\Models\\' . $model;
        //         $reflectionClass = new \ReflectionClass($modelName);
        //         $tabletitle = $reflectionClass->getDefaultProperties()['tabletitle'];
        //         if ('' != $tabletitle) {
        //             $msg .= ' (' . $tabletitle . ')';
        //         } else {
        //             $msg .= ' (' . $model . ')';
        //         }
        //     } catch (\Exception $e) {
        //         //echo $e->getMessage(); //throw e;
        //     }
        // }
        //寄信通知
        $this->sendMailError($e, $request);

        try {
            if ($request->is('api/*') || $request->is('*/api*')) {
                $jsondata['resultcode'] = $this->statusCode;
                $jsondata['resultmessage'] = $msg;

                return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
            } elseif ($request->is('admincontrol/*') || $request->is('admin/*') || $request->is('*/admincontrol/*') || $request->is('*/admin/*')) {
                // 優先使用 admin/errors 目錄下的 view，若不存在則使用 errors 目錄
                if (\View::exists('admin.errors.' . $statusPage)) {
                    $error_file = 'admin/errors/' . $statusPage;
                } else {
                    $error_file = 'errors/' . $statusPage;
                }
                // 回傳對應的錯誤頁面 view
                return response()->view($error_file, ['msg' => $msg, 'statusCode' => $this->statusCode], $this->statusCode);
            } else {
                if (View::exists('errors/' . $statusPage)) {
                    return response()->view('errors/' . $statusPage, ['msg' => $msg, 'statusCode' => $this->statusCode], $this->statusCode);
                } elseif (View::exists('admin/errors/' . $statusPage)) {
                    return response()->view('admin/errors/' . $statusPage, ['msg' => $msg, 'statusCode' => $this->statusCode], $this->statusCode);
                } else {
                    return response($msg);
                }
            }
        } catch (\Exception $e) {
            return response($e->getMessage());
        }
    }
    public function sendMailError($e, $request) {
        try {
            if (\config('app.env') == 'dev' || \config('app.env') == 'local') {
                return;
            }

            if (512 == $this->statusCode ||  $this->statusCode < 500) {
                return;
            }

            if ($e instanceof CustomException) {
                return;
            }

            if (Str::contains($e->getMessage(), ['找不到函式', 'does not exist', 'Could not move the file', 'Argument 2 passed', 'Request, string given', "attempting to log", "SMTP server with"])) {
                return;
            }

            $msg = $e->getMessage();
            if (PF::isEmpty($msg)) {
                return;
            }

            foreach ($request->all() as $_key => $_value) {
                if ('' != $_value) {
                    if (is_array($_value)) {
                        $RequestForm .= $_key . '=' . implode(',', $_value) . '&';
                    } else {
                        $RequestForm .= $_key . '=' . $_value . '&';
                    }
                }
            }

            if ('' != env('LOG_SLACK_WEBHOOK_URL', '')) {
                \Log::channel('slack')->error(\PF::getConfig('name'), [
                    '錯訊網址 ' => \Request::getUri(),
                    '錯訊訊息' => $msg . PHP_EOL . PHP_EOL . strval($e),
                    '用戶端傳送內容' => $RequestForm,
                    '用戶端傳送方式' => $_SERVER['REQUEST_METHOD'],
                    '用戶端傳送方式' => $_SERVER['HTTP_USER_AGENT'],
                    '用戶端IP' => \Request::ip(),
                    '前一頁網址' => $_SERVER['HTTP_REFERER'],
                    '異常時間' => date('Y.m.d D H:i'),
                ]);

                return;
            }

            $ebody = view('email.errmsg', [
                'RequestForm' => $RequestForm,
                'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'],
                'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'],
                'msg' => $msg,
                'PATH_INFO' => \Request::getUri(),
                'clientip' => \Request::ip(),
                'date' => date('Y.m.d D H:i'),
                'HTTP_REFERER' => $_SERVER['HTTP_REFERER'],
            ])->render();
            \Mail::to('<EMAIL>') //收件人
                // ->cc($moreUsers) //副本
                // ->bcc($evenMoreUsers) //密件副本
                ->queue(new \App\Mails\sendMail(
                    [
                        'subject' => $_SERVER['SERVER_NAME'] . '錯誤回報',
                        'raw' => $ebody,
                    ]
                ));
        } catch (Exception $e) {
        }
    }

    // protected function unauthenticated($request, AuthenticationException $exception)
    // {
    //     return $request->expectsJson()
    //                 ? response()->json(['message' => $exception->getMessage()], 401)
    //                 : redirect()->guest(route('login'));
    // }
}
