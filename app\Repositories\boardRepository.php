<?php

namespace App\Repositories;

use DB;
use PF;
use App\Models\board;

class boardRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(board $model)
    {
        $this->model = $model;
    }

    /**
     *TODO 透過DB TABLE回傳ROWS.
     */
    public function select($field = '*')
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    /**
     *TODO 透過KEY找一筆資料.
     */
    public function find($id, $column = '*')
    {
        $rows = $this->select($column);
        $rows->where($this->model->primaryKey, '=', $id);
        $rows = $rows->take(1);

        return $rows;
    }

    public function create($inputs)
    {
        $rows = parent::create($inputs);
        \Cache::flush('board');

        return $rows;
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        $rows = parent::update($inputs, $id, 'id');
        \Cache::flush('board');

        return $rows;
    }

    public function deleteIds($ids)
    {
        $this->model->whereIn($this->model->primaryKey, explode(',', $ids))->delete();
        \Cache::flush('board');
        // $rows = $this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // //PF::dbSqlPrint($rows);
        // $rows->get()->each(function ($row) {
        //     $row->delete();
        // });
    }
}
