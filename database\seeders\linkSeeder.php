<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use DB;
use App\Repositories\boardRepository;

class linkSeeder extends Seeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        
        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->boardRepo->select()
        ->myWhere('kind|S', 'link', "del", 'Y')
        ->delete();
        
        $faker = \Faker\Factory::create('zh_TW');

        for ($i = 0; $i < 10; ++$i) {
            $data = [
                'kind' => 'link',
                //'title' =>  $faker->sentence,
                'title' => '內政部時價登錄-'.$i,
                'memo' =>  'http://lvr.land.moi.gov.tw/homePage.action',


                'begindate' => date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month')))),
            ];
            $this->boardRepo->create($data);
        }
        
        

        

        // $this->call(UsersTableSeeder::class);
    }

}
