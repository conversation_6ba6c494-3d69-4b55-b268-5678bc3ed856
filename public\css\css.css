@media print {
    .noprint {
        display: none;
        visibility: hidden;
    }

    th {
        text-align: left;
    }
}
input[type="datetime-local"],
input[type="date"] {
    height: 40px;
}

.table-fixed th {
    word-break: keep-all; /* 不換行 */
    white-space: nowrap; /* 不換行 */
    height: 25px !important;
    overflow: hidden !important;
}

.loading {
    z-index: -1;
    opacity: 0;
    transition: 0.5s;
    background: rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
}

.loading.show {
    z-index: 200;
    opacity: 1;
}

.loading .spinner {
    animation: rotator 1.4s linear infinite;
}

.loading .path {
    stroke-dasharray: 187;
    stroke-dashoffset: 0;
    transform-origin:  center;
    animation: dash 1.4s ease-in-out infinite, colors 5.6s ease-in-out infinite;
}

@keyframes rotator {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(270deg);
    }
}

@keyframes colors {
    0% {
        stroke: #4285f4;
    }

    25% {
        stroke: #de3e35;
    }

    50% {
        stroke: #f7c223;
    }

    75% {
        stroke: #1b9a59;
    }

    100% {
        stroke: #4285f4;
    }
}

@keyframes dash {
    0% {
        stroke-dashoffset: 187;
    }
    50% {
        stroke-dashoffset: 46.75;
        transform: rotate(135deg);
    }
    100% {
        stroke-dashoffset: 187;
        transform: rotate(450deg);
    }
}
/*mobile  */
@media screen and (max-width: 724px) and (min-width: 0) {
    img .mobile {
        display: block;
    }
    img .pc {
        display: none;
    }
}
/*pc*/
@media screen and (min-width: 725px) {
    img .mobile {
        display: none;
    }
    img .pc {
        display: block;
    }
}
.form-check-inline > label {
    font-weight: normal !important;
}

.table-header {
    background-color: #e63946;
    color: white;
}

.property-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

@media (max-width: 768px) {
    .property-image {
        height: 100px;
    }
    .houseimghouse{
        min-height: 500px !important;
    }
}

.grid-view .property-card {
    display: block;
}

.list-view .property-card {
    display: none;
}

.grid-view .property-table {
    display: none;
}

.list-view .property-table {
    display: table;
}

/* 手機版優化 */
@media (max-width: 768px) {
    .property-card .grid {
        grid-template-columns: 1fr;
    }
        .houseimghouse {
            min-height: 300px !important;
        }

    /* 列表視圖在手機版的優化 */
    .list-view .property-table {
        display: block;
        width: 100%;
        overflow-x: auto;
    }

    .list-view .property-table th,
    .list-view .property-table td {
        font-size: 0.875rem;
        padding: 0.5rem;
    }

    /* 手機版卡片式列表視圖 */
    .list-view-mobile {
        display: none;
    }

    .list-view .list-view-mobile {
        display: block;
    }

    .list-view .property-table.desktop-only {
        display: none;
    }
}


.tab-container {
    border: 1px solid #ddd;
    border-bottom: none;
    display: inline-flex;
    border-radius: 4px 4px 0 0;
    overflow: hidden;
    /* 新增格線樣式 */
    /* display: grid; */
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1px;
    background-color: #ddd;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1px;
}

.property-tab {
    padding: 8px 16px;
    font-size: 16px;
    margin: 0;
    border-radius: 0;
    transition: all 0.2s;
    cursor: pointer;
    position: relative;
    /* background-color: #f5f5f5; */
    color: #333;
    text-align: center;
    /* 新增格線樣式 */
    border: none;
}

.property-tab.active {
    background-color: #e74c3c;
    color: white;
    font-weight: bold;
}

.property-tab:not(.active) {
    background-color: #f5f5f5;
    color: #333;
}

.property-tab:not(.active):hover {
    background-color: #e0e0e0;
}

/* 頁籤下方指示線 */
.property-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e74c3c;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .tab-container {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}


