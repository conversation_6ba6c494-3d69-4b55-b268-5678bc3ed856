<?php

namespace App\Services;

use DB;

/**
 * Class JsonTranslator.
 */
class 104wooService
{
    private $db;

    public function __construct()
    {
    }

    public function run()
    {
        $sms = new \App\Services\smsService();

        $rows = DB::table('sign')->selectRaw('*');

        //$rows->whereRaw('smsdated is null and convert(smsdate,DATETIME)>convert(now(),DATETIME)');

        $rows = $rows->get();
        //dd($row);

        if (0 == $rows->count()) {
            throw new \CustomException('No data');
        }
        foreach ($rows as $rs) {
            try {
                echo $rs->name;
                if ('' != $rs->mobile) {
                    $smsbody = view('email.sms', get_object_vars($rs))->render();

                    $sms->send($rs->mobile, $smsbody);
                }else{
                    throw new \CustomException("no mobile");
                }
            } catch (\Exception $e) {
                echo $e->getMessage();
                \Log::error($e->getMessage());
            } finally {
                $inputs = null;
                $inputs['smsdated'] = date('Y-m-d H:i:s');
                \App\Models\sign::myWhere('id', $rs->id, 'id', 'Y')->update($inputs);
            }
        }
    }
}
