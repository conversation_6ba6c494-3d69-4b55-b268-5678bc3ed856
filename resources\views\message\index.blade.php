@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')
<div class="px-3">
    <div class="theme-container">
        <div class="row">
            <nav aria-label="breadcrumb" class="col-xs-12">
                <ol class="breadcrumb end-xs">

                    <li class="breadcrumb-item"><a href="{{ url('/') }}/">首頁</a></li>
                    <li class="breadcrumb-item active" aria-current="page">留言板</li>
                </ol>
            </nav>
        </div>
    </div>
</div>

<div class="px-3">
    <div class="theme-container">
        <div class="page-drawer-container mt-3">

            <div class="mdc-drawer-scrim page-sidenav-scrim"></div>
            <div class="page-sidenav-content pl-0">
                <p>＊若想詢問屋況相關事項請直接來電洽詢，會較迅速<br>
                    ＊法拍屋相關問題可先至「常見問題」中查詢<br>
                    ＊請勿張貼個人廣告，如有需要合作結盟者，請來電
                    {{config('config.tel1')}}
                    洽詢
                    <span class="mdc-theme--text-primary-on-light">{{config('config.name')}}很高興為您服務</span>
                </p>

                <div class="row mdc-card p-3 mb-3">
                    <script language=JavaScript>
                        function oForm2_onsubmit(form) {
                            if (PF_FormMultiAll(form) == false) { return false };
                            PF_FieldDisabled(form)
                            return true;
                        }
                    </script>
                    <form name="oForm2" id="oForm2" method="post" language="javascript" action="{{ url('/message') }}" onsubmit="return oForm_onsubmit(this);">
                        {{ Form::hidden("kind", $data["kind"] ) }}
                        <!--novalidate-->

                        <div class="mdc-menu-surface--anchor col-6">
                            <button class="mdc-button mdc-ripple-surface text-muted mutable mdc-ripple-upgraded" style="--mdc-ripple-fg-size:99px; --mdc-ripple-fg-scale:1.8149; --mdc-ripple-fg-translate-start:97.5px, -31.2813px; --mdc-ripple-fg-translate-end:33.4063px, -31.5px;">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label"><span class="mutable">請選擇類別</span></span>
                                <i class="material-icons mdc-button__icon m-0">arrow_drop_down</i>
                            </button>

                            <div class="mdc-menu mdc-menu-surface" style="transform-origin: center top; left: 0px; max-height: 468.219px; top: 36px;">
                                <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                                    @foreach ($data['xmldoc']->xpath("//參數設定檔/留言類別/KIND") as $v)

                                    <li class="mdc-list-item" role="menuitem" tabindex="-1">
                                        <span class="mdc-list-item @if ($data['totalupset']==$v->資料) mdc-list-item--selected @endif" data-value="{{$v->資料}}" onclick="document.forms['oForm2'].elements['kind'].value=$(this).attr('data-value');;">{{$v->資料}}</span>

                                    </li>
                                    @endforeach

                                </ul>
                            </div>

                    </form>

                </div>

                <div class="col-6 text-right">
                    <script language=JavaScript>
                        function oForm1_onsubmit(form) {
                            if (PF_FormMultiAll(form) == false) { return false };
                            PF_FieldDisabled(form)
                            return true;
                        }
                    </script>
                    <form name="oForm1" id="oForm1" method="post" language="javascript" action="{{ url('/message/create') }}" onsubmit="return oForm1_onsubmit(this);">
                        <!--novalidate-->

                        <button type="submit" class="mdc-button mdc-button--raised bg-accent mdc-ripple-upgraded">
                            <span class="mdc-button__ripple"></span>
                            <span class="mdc-button__label">我要留言</span>
                        </button>
                    </form>
                </div>


            </div>
            <div class="mdc-card p-3">
                <div class="mdc-data-table border-0 w-100 mt-3">
                    <table class="mdc-data-table__table" aria-label="Dessert calories">
                        <thead>
                            <tr class="mdc-data-table__header-row">
                                <th class="mdc-data-table__header-cell">類別</th>
                                <th class="mdc-data-table__header-cell">標題</th>
                                <th class="mdc-data-table__header-cell">性別</th>
                                <th class="mdc-data-table__header-cell">留言者</th>
                                <th class="mdc-data-table__header-cell">留言時間</th>
                            </tr>
                        </thead>
                        <tbody class="mdc-data-table__content">
                            @foreach ($data['rows'] as $rs)


                            <tr class="mdc-data-table__row">
                                <td class="mdc-data-table__cell">
                                    {{PF::xmlSearch($data['xmldoc'],"//參數設定檔/留言類別/KIND/傳回值","資料",$rs->kind)}}</td>
                                <td class="mdc-data-table__cell">

                                    <a href="{{ url('/') }}/message/show/{{$rs->id}}" class="mdc-button mdc-ripple-surface mdc-ripple-surface--primary normal" id="my-mdc-dialog">{{$rs->title}}</a>
                                </td>
                                <td class="mdc-data-table__cell">{{$rs->sex}}</td>
                                <td class="mdc-data-table__cell">{{$rs->name}}</td>
                                <td class="mdc-data-table__cell">{{PF::formatDate($rs->created_at)}}</td>
                            </tr>
                            @endforeach


                        </tbody>
                    </table>
                    @if (count($data['rows'] )==0)
                    No Data
                    @endif
                </div>
            </div>
            {{ $data['rows']!=null ? $data['rows']->links('layouts.paginate') :"" }}
        </div>
    </div>


</div>

@endsection