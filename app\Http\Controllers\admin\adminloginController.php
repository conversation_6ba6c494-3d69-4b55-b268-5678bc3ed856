<?php

namespace App\Http\Controllers\admin;

use PF;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\adminuserRepository;
use Illuminate\Support\Facades\Validator;
use App\Repositories\adminuserloginlogRepository;

class adminloginController extends Controller
{
    private $adminuserRepo;
    private $adminuserloginlogRepo;
    private $data;

    /**
     *建構子.
     */
    public function __construct(adminuserRepository $adminuserRepo, adminuserloginlogRepository $adminuserloginlogRepo)
    {
        $this->middleware('AuthAdmin');
        //$this->middleware('guest:admin', ['except' => 'logout']);
        //$this->limit="xx";
        parent::__construct();
        //$this->middleware('guest:admin');
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->adminuserRepo = $adminuserRepo;
        $this->adminuserloginlogRepo = $adminuserloginlogRepo;
        $this->fieldnicknames = $this->adminuserRepo->getFieldTitleArray();

        \Config::set('config.title', \config('config.name').'後端管理');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ('' != $this->data['productid']) {
            if (\Str::contains(\Auth::guard('admin')->user()->role, ['900'])) {
                return redirect()->to('admin/product/edit?edit='.$this->data['productid']);
            }
        }

        return view('admin.adminlogin.index', [
        'data' => $this->data,
    ]);
    }

    public function login(Request $request)
    {
        try {
            $validators = null;
            if ('' == $this->data['productid']) {
                $validators['account'] = 'required';
            }
            $validators['password'] = 'required';
            $validators['google_recaptcha_token'] = ['required', 'string', new \App\Rules\MyValidatorsGoogleRecapchaV3()];
            // if ('@DEBUG' != $this->data['captcha']) {
            //     $validators['captcha'] = 'required|captcha';
            // }

            // $messages = [
            //     'required' => 'The :attribute field is requxxxired.',
            //     'captcha' => 'The :attribute field is requxxxired.',
            // ];
            $validator = Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->fieldnicknames);

            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
            $account = $request->input('account');
            if ('' != $this->data['productid']) {
                $account = 'master';
            }

            $loginstatus = '';

            //$this->middleware('guest:vendor', ['except' => 'logout']);

            if (\Auth::guard('admin')->attempt(['account' => $account, 'password' => $request->input('password'), 'online' => 1], $this->data['iskeep'])) {
                //\DB::update('UPDATE place  SET  signcount=(select count(*) from sign where place.placeid=sign.placeid ) ');

                $inputs['lastlogin_dt'] = date('Y-m-d H:i:s');
                $inputs['lastlogin_ip'] = \Request::ip();
                $inputs['api_token'] = hash('sha256', \Str::random(80));
                $this->adminuserRepo->update($inputs, \Auth::guard('admin')->user()->id);

                $loginstatus = __('成功');
                if (\Str::contains(\Auth::guard('admin')->user()->role, ['900'])) {
                    return response("<script>location.href='../product/edit?edit=".$request->input('productid').")';</script>");
                }

                return response("<script>top.location.href='../frame';</script>");
            //return redirect()->intended('/admin/main');
            } else {
                $loginstatus = __('失敗');
                throw new \CustomException(__('登入失敗').' (IP:'.request()->ip().')');
            }
        } catch (\CustomException $e) {
            return back()->with('msg', $e->getMessage());
        } finally {
            if ('' != $account) {
                $inputs = null;
                $inputs['account'] = $account;
                $inputs['clientip'] = request()->ip();
                $inputs['created_at'] = date('Y-m-d H:i:s');
                $inputs['loginstatus'] = $loginstatus;
                $this->adminuserloginlogRepo->create($inputs);
            }
        }
    }
}
