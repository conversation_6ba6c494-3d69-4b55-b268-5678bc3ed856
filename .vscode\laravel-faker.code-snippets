{
    "faker 生成0-9之間的隨機數": {
        "prefix": "faker number 生成0-9之間的隨機數",
        "scope": "php",
        "body": [
            " \\$this->faker->randomDigit//生成0-9之間的隨機數 "
        ]
    },
    "faker 生成英文的亂碼": {
        "prefix": "faker english name生成英文的亂碼",
        "scope": "php",
        "body": [
            " Str::random(10)//生成英文的亂碼"
        ]
    },
    "faker 生成1-9之間的隨機數": {
        "prefix": "faker //生成1-9之間的隨機數",
        "scope": "php",
        "body": [
            " \\$this->faker->randomDigitNotNull//生成1-9之間的隨機數"
        ]
    },
    "faker 生成浮點數，兩位小數點，範圍是0-10之間": {
        "prefix": "faker float 生成浮點數，兩位小數點，範圍是0-10之間",
        "scope": "php",
        "body": [
            " \\ $this->faker->randomFloat(2, 0, 10)//生成浮點數，兩位小數點，範圍是0-10之間 "
        ]
    },
    "faker XX-YY整數之間": {
        "prefix": "faker number XX-YY整數之間",
        "scope": "php",
        "body": [
            " \\$this->faker->numberBetween(100000, 999999),   //XX-YY整數之間 "
        ]
    },
    "faker 返回數組中的隨機兩個元素": {
        "prefix": "faker 返回數組中的隨機兩個元素",
        "scope": "php",
        "body": [
            " \\$this->faker->randomElements(['a', 'b', 'c', 'd'], 2)//返回數組中的隨機兩個元素 "
        ]
    },
    "faker random": {
        "prefix": "faker str random 隨機英數字",
        "scope": "php",
        "body": [
            " \\Str::random(${1:10}), "
        ]
    },

    "faker 隨機返回數組中的一個元素": {
        "prefix": "faker str 隨機返回數組中的一個元素",
        "scope": "php",
        "body": [
            "\\$this->faker->randomElement(['aa', 'bb', 'cc', 'dd'])//隨機返回數組中的一個元素 "
        ]
    },
    "faker #####替換為隨機數字，輸出類似：Hello 03501": {
        "prefix": "faker str test_ 03501",
        "scope": "php",
        "body": [
            " \\$this->faker->numerify('${1:test}_##########')//#####替換為隨機數字，輸出類似：Hello 03501 "
        ]
    },
    "faker upload": {
        "prefix": "faker upload",
        "scope": "php",
        "body": [
            " \\Illuminate\\Http\\UploadedFile::fake()->image('test.png') "
        ]
    },
    "faker myFaker->upload": {
        "prefix": "faker myFaker->upload",
        "scope": "php",
        "body": [
            " \\$this->myFaker->getImage('images/news', \\$w = 100, \\$h = 100) "
        ]
    },

    "faker ???替換為3個隨機小寫字符，輸出類似：Hello krg": {
        "prefix": "faker str 輸出類似：Hello krg",
        "scope": "php",
        "body": [
            " \\$this->faker->lexify('Hello ???')//???替換為3個隨機小寫字符，輸出類似：Hello krg "
        ]
    },
    "faker 返回一段文本，最多只能含有200個字符": {
        "prefix": "faker str 返回一段文本，最多只能含有200個字符",
        "scope": "php",
        "body": [
            " \\$this->faker->text(200)//返回一段文本，最多只能含有200個字符 "
        ]
    },
    "faker 返回一個句子，false表示只能含有5個單詞，true表示可以在5個單詞左右": {
        "prefix": "faker str string 返回一個句子，false表示只能含有5個單詞，true表示可以在5個單詞左右",
        "scope": "php",
        "body": [
            " \\$this->faker->sentence(5, true)//返回一個句子，false表示只能含有5個單詞，true表示可以在5個單詞左右 "
        ]
    },
    "faker 返回一個隨機郵箱": {
        "prefix": "faker email 返回一個隨機郵箱",
        "scope": "php",
        "body": [
            " \\$this->myFaker->getEmail()//返回一個隨機郵箱 "
        ]
    },
    "faker 公司名稱": {
        "prefix": "faker company 公司名稱",
        "scope": "php",
        "body": [
            " \\$this->faker->company//公司名稱 "
        ]
    },
    "faker 圖片": {
        "prefix": "faker image 圖片",
        "scope": "php",
        "body": [
            "  \\$this->myFaker->getImage('images/banner', 390, 290),   //圖片 "
        ]
    },
    "faker http 圖片": {
        "prefix": "faker http image 圖片",
        "scope": "php",
        "body": [
            "  \\$this->myFaker->getHttpImage('images/banner', 390, 290),   //圖片 "
        ]
    },
    "faker 室內電話": {
        "prefix": "faker tel 室內電話",
        "scope": "php",
        "body": [
            " \\$this->myFaker->getTel(),    //室內電話 "
        ]
    },
    "faker 行動電話": {
        "prefix": "faker mobile 行動電話",
        "scope": "php",
        "body": [
            " \\$this->myFaker->getMobile(),   //行動電話 "
        ]
    },
    "faker 密碼": {
        "prefix": "faker password 密碼",
        "scope": "php",
        "body": [
            " \\Hash::make('a123456789'),   //密碼 "
        ]
    },
    "faker 返回一個隨機url": {
        "prefix": "faker 返回一個隨機url",
        "scope": "php",
        "body": [
            " \\$this->myFaker->getUrl()//域名"
        ]
    },
    "faker 圖網址": {
        "prefix": "faker image url 圖網址",
        "scope": "php",
        "body": [
            " \\$imageUrl = \\$this->myFaker->imageUrl()//https://lorempixel.com/640/480/?93028 "
        ]
    },
    "faker 縣市": {
        "prefix": "faker city1 縣市",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getCity1(),   //城市 "
        ]
    },
    "faker 鄉鎮": {
        "prefix": "faker city2 鄉鎮",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getCity2(),   //鄉鎮 "
        ]
    },
    "faker 地址": {
        "prefix": "faker address 地址",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getCity1().\\$this->myFaker->getCity2().\\$this->myFaker->getAddress(),   //城市+鄉鎮+地址 "
        ]
    },
    "faker 日期": {
        "prefix": "faker date 日期",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getDate(),   //日期 "
        ]
    },
    "faker 姓名": {
        "prefix": "faker name 姓名",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getName(),   //姓名 "
        ]
    },
    "faker account": {
        "prefix": "faker acccount 帳號",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getAccount(),   //帳號 "
        ]
    },
    "faker_身份證": {
        "prefix": "faker uid 身份證",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getUid(),   //身份證 "
        ]
    },
    "faker getXml": {
        "prefix": "faker xml search",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getXml('${1:name}'),   //取XML資料 "
        ]
    },
    "faker getimg": {
        "prefix": "faker img",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getImage('images/${1:banner}', 1020, 800), "
        ]
    },
    "faker getimgname": {
        "prefix": "faker img name",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getImageName(), "
        ]
    },
    "faker getDocumentName": {
        "prefix": "faker doc name",
        "scope": "php",
        "body": [
            "\\$this->myFaker->getDocumentName(), "
        ]
    },


    "faker kind foeach": {
        "prefix": "faker kind foreach",
        "scope": "php",
        "body": ["\\$rows = \\$this->kindRepo->selectRaw('kindid'),",
            "\\$rows->myWhere('kind|S', '${1:name}', 'kind', 'Y'),",
            "\\$rows = \\$rows->get(),",
            "foreach (\\$rows as \\$rs) {",
            "    \\$data = [",
            "         '${1:name}' => 10000,",
            "          'kind_id' => \\$rs->kindid,",
            "    ],",
            "    \\$this->${1:name}Repo->create(\\$data),",
            "}",
            "",
        ]
    },
    "myFaker": {
        "prefix": "myFaker",
        "scope": "php",
        "body": ["\\$this->myFaker = new \\Database\\Seeders\\myfaker(),"]
    },
        "factory": {
            "prefix": "factory",
            "scope": "php",
            "body": ["\\$this->${1:member} = \\App\\Models\\\\${1:member}::factory()->create([",
                "      '${2:member_id}' => \\$this->${2:member}->id,",

                "    ]);",
            ]
        },
        "factory 多筆": {
            "prefix": "factory 多筆",
            "scope": "php",
            "body": [
                "\\$this->${1:member} = \\App\\Models\\\\${1:member}::factory()->count(2)->create([",
                "      '${2:member_id}' => \\$this->${2:member}->id,",
                "    ]);",
            ]
        },


}