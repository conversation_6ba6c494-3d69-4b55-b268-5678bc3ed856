<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PF;

class productExport implements FromCollection, WithColumnFormatting, WithHeadings, ShouldAutoSize, WithEvents {
    protected $data;
    protected $fields;
    protected $fieldnicknames;

    /**
     *TODO 定義要顯示的欄位.
     */
    public function __construct($data, $fieldnicknames, $rows) {
        ini_set('memory_limit', '256M');
        $this->fieldnicknames = $fieldnicknames;
        $this->data = $data;

        $this->fields = [
            'number' => '',
            'city1title' => '',
            'city2titleaddress' => '行政區+地址',

            'pattern' => '',
            'houseage' => '',

            'storey' => '',
            'tenderdate' => '',
            'totalupset' => '',
            'pingtotalnumberof' => '',
            'stakeholdersfloor' => '',
            'beattime' => '',
            'nocross_point' => '',
        ];

        $rows = $rows->get();
        //dd($rows);
        $this->data['rows'] = $rows;
    }

    /**
     *TODO 組資料(陣列轉集合).
     */
    public function collection() {
        $arrays = [];

        foreach ($this->data['rows'] as $rs) {
            $rs = get_object_vars($rs);
            $dict = null;
            foreach ($this->fields as $key => $value) {
                $value = $rs[$key];
                switch ($key) {
                    // case 'created_at':
                    //     $value = PF::formatDate($value);
                    //     break;
                    case 'location':
                        $value = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/放置位置/KIND/傳回值', '資料', $value);
                        break;

                    case 'locationadmin':
                        $value = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/管理者放置位置/KIND/傳回值', '資料', $value);
                        break;
                    case 'city2titleaddress':
                        $value = $rs['city2title'] . $rs['address'];
                        break;
                    case 'beattime':
                        $value = str_replace("拍", '', $rs['beattime']);
                        break;
                }
                $dict[$key] = $value;
            }
            $arrays[] = $dict;
        }

        return new Collection($arrays);
    }

    /**
     *TODO 調整欄位寬度.
     */
    public function registerEvents(): array {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $i = 0;
                $chrnumber = 65;
                $chr1 = '';
                foreach ($this->fields as $key => $value) {
                    $width = mb_strlen($this->fieldnicknames[$key], 'UTF-8') * 5;
                    if ($width < 10) {
                        $width = 10;
                    }
                    $event->sheet->getDelegate()->getColumnDimension(strval(chr(65 + $i)))->setAutoSize(false)->setWidth($width);
                    //$event->sheet->getDelegate()->getColumnDimension(strval(chr(65 + $i)))->setAutoSize(true);
                    ++$i;
                    if (25 == $i) {
                        $i = 0;
                        $chr1 = 'A';
                    }
                }
            },
        ];
    }

    /**
     *TODO 資料格式化.
     */

    /**
     * @return array
     */
    public function columnFormats(): array {
        // $sheet->setColumnFormat(array(
        //     'B' => '0',
        //     'D' => '0.00',
        //     'F' => '@',
        //     'F' => 'yyyy-mm-dd',
        // ));
        $i = 0;
        $fomats = [];
        foreach ($this->fields as $key => $value) {
            $fx = chr(65 + $i);
            switch ($key) {
                case 'signcount':
                    $fomats[$fx] = '0';
                    break;
                    // case 'ordergroupnumber':
                    //     $fomats[$fx] = '@';
                    //break;
            }
            ++$i;
        }

        return $fomats;
    }

    /**
     *TODO 比對標題.
     */
    public function headings(): array {
        $heads = null;
        foreach ($this->fields as $key => $value) {
            if ('' != $value) {
                $heads[] = $value;
            } elseif (array_key_exists($key, $this->fieldnicknames)) {
                $value = $this->fieldnicknames[$key];
                $heads[] = $value;
            } else {
                $heads[] = $key;
                //$this->fields[$key] = $key;
            }
        }

        return $heads;
    }
}

// $this->fields = [
//     "productkind"=>"",
// "producttitle"=>"",
// "number"=>"",
// "online"=>"",
// "court"=>"",
// "location"=>"",
// "proofreadingday"=>"",
// "auctions"=>"",
// "tenderdate"=>"",
// "debtor"=>"",
// "beattime"=>"",
// "totalupset"=>"",
// "nocross_point"=>"",
// "margin"=>"",
// "mainlawnestablishment"=>"",
// "city1title"=>"",
// "city2title"=>"",
// "attachedtolawnestablishment"=>"",
// "address"=>"",
// "additionalping"=>"",
// "pattern"=>"",
// "postulateping"=>"",
// "buildname"=>"",
// "noticelawnestablishment"=>"",
// "houseage"=>"",
// "stakeholdersfloor"=>"",
// "postulatemorethan"=>"",
// "storey"=>"",
// "pingtotalnumberof"=>"",
// "floorprice"=>"",
// "architecture"=>"",
// "aftermakingvalue_added"=>"",
// "management"=>"",
// "parkingmode"=>"",
// "fees"=>"",
// "transportfunction"=>"",
// "facingthelaneis"=>"",
// "schooldistrict"=>"",
// "sealedhuman"=>"",
// "thenumberofbidders"=>"",
// "thebidprice"=>"",
// "memo"=>"",
// "increasetheamountof"=>"",
// "landprice"=>"",
// "landaddress"=>"",
// "currentvalues"=>"",
// "bidsrecords"=>"",
// "courttranscript"=>"",
// "buildings"=>"",
// "landvalue1"=>"",
// "hisrightis"=>"",
// "transcriptinformation"=>"",
// "landvalue2"=>"",
// "createdate"=>"",
// "mrtland"=>"",
// "mrtstation"=>"",
// "hits"=>"",
// "bidwere"=>"",
// "locationadmin"=>"",
// "point"=>"",
// "auctionssortnum"=>"",
// // "lat"=>"",
// // "lng"=>"",
// "created_at"=>"",
// "updated_at"=>"",
// "url"=>"",
// //"img"=>"",
