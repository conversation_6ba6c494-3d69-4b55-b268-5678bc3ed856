@media print {
    .noprint {
        display: none;
    }
    th {
        text-align: left;
    }
}



input[type="number"]{
    width:100px;
}



input[type="datetime-local"], input[type="date"] {
    height: 40px;
}

.table-fixed th {
    word-break: keep-all; /* 不換行 */
    white-space: nowrap; /* 不換行 */
    height: 25px !important;
    overflow: hidden !important;
}

.loading {
    z-index: -1;
    opacity: 0;
    transition: .5s;
    background: rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
}

    .loading.show {
        z-index: 200;
        opacity: 1;
    }

    .loading .spinner {
        animation: rotator 1.4s linear infinite;
    }

    .loading .path {
        stroke-dasharray: 187;
        stroke-dashoffset: 0;
        transform-origin: center;
        animation: dash 1.4s ease-in-out infinite, colors 5.6s ease-in-out infinite;
    }

@keyframes rotator {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(270deg);
    }
}

@keyframes colors {
    0% {
        stroke: #4285F4;
    }

    25% {
        stroke: #DE3E35;
    }

    50% {
        stroke: #F7C223;
    }

    75% {
        stroke: #1B9A59;
    }

    100% {
        stroke: #4285F4;
    }
}


@keyframes dash {
    0% {
      stroke-dashoffset: 187;
    }
    50% {
      stroke-dashoffset: 46.75;
      transform: rotate(135deg);
    }
    100% {
      stroke-dashoffset: 187;
      transform: rotate(450deg);
    }
  }

