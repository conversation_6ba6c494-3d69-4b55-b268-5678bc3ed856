<a href="{{ url('/') }}/house/show/{{ $rs->productid }}" class="property-card"
    title="{{ $rs->city1title }}{{ $rs->city2title }}{{ $rs->producttitle }}">
    <div class="imgBox">
        {{ Html::myUIImage([
            'folder' => 'https://www.ebayhouse.com.tw/images/product',
            'filename' => collect(explode(',', $rs->img))->last(),
            'alt' => $rs->title,
            'noimg' => 'no-picture.gif',
            'class' => 'img-fluid',
        ]) }}
        <div class="heart-icon" title="加入收藏">
            @if (Auth::guard('member')->check())
                @if (Str::contains(Auth::guard('member')->user()->myproducts, [$rs->productid]))
                    <span class="okHeart" rel='ok'
                        onclick="myproduct(this,'{{ $rs->productid }}');return false;"></span>
                @else
                    <span class="notHeart" rel='ok'
                        onclick="myproduct(this,'{{ $rs->productid }}');return false;"></span>
                @endif
            @else
                <span class="notHeart" rel='ok' onclick="_alert('請先登入')"></span>
            @endif
        </div>
    </div>
    <div class="property-info">
        <h2>{{ $rs->city1title }}{{ $rs->city2title }}{{ $rs->address }}</h2>
        <div class="info-row">
            <p>底價:<span class="price">{{ PF::formatNumber($rs->totalupset, 0) }}</span>萬</p>
            <p>類型:<span>{{ $rs->pattern }}</span></p>
        </div>
        <div class="info-row">
            <p>總坪:<span class="num">{{ $rs->pingtotalnumberof }}</span>坪</p>
            <p>單坪:<span class="num">{{ PF::formatNumber($rs->floorprice, 0) }}</span>萬</p>
        </div>
    </div>
</a>
