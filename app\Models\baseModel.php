<?php

namespace App\Models;

use PF;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Eloquent\Model as Eloquent;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class baseModel extends Eloquent {
    use HasFactory;
    public $fieldInfo;
    public $fileFields;

    public static function run($model) {
        //$table = $model->table;
        $fieldInfo = $model->fieldInfo;

        foreach ($model->attributes as $key => $value) {
            if (is_array($value)) {
                $value = array_diff($value, array(''));

                $value = implode(',', $value);
                $value = ltrim(rtrim($value, ','), ',');

                $model->attributes[$key] = $value;
                continue;
            }
            if (is_string($value)) {
                $value = trim($value);
            }
            $type = $fieldInfo[$key]['type'];
            if (null == $type) {
                continue;
            }
            if ('datetime' == $type || 'date' == $type || 'timestamp' == $type) {
                if (false == PF::isEmpty($value)) {
                    $value = str_replace('T', ' ', $value);
                }
                //  else {
                //     $yyymmdd = \Request::get($key.'_yy').'-'.\Request::get($key.'_mm').'-'.\Request::get($key.'_dd');
                //     if ('--' != $yyymmdd) {
                //         $value = $yyymmdd;
                //     }
                // }
                if (false == PF::isDate($value)) {
                    $value = null;
                }
                $model->attributes[$key] = $value;
            } elseif (\Str::contains($type, ['int', 'float'])) {

                if ($type == "tinyint(4)") {
                    if (gettype($value) == "boolean") {
                        if ($value == true) {
                            $value = 1;
                        } else if ($value == false) {
                            $value = 0;
                        }
                    }
                } else {
                    if (($value != "0") && ('' == $value || false == is_numeric($value))) {
                        $value = null;
                    }
                }
                $model->attributes[$key] = $value;
            }
        }
        //\PF::printr(["model->attributes", $model->attributes]);
    }

    public function getfillables($guardeds = []) {
        // PF::printr($guardeds);
        $table = $this->table;
        $fillables = $GLOBALS[$table . '_fillable'];
        if (null == $fillables) {
            $fillables = Schema::getColumnListing($this->getTable());
            $GLOBALS[$table . '_fillable'] = $fillables;
        }
        // if (null != $guardeds) {
        //     PF::printr(111);
        //     foreach ($guardeds as $k => $v) {
        //         PF::printr($v);
        //         unset($fillables[array_search($v, $fillables)]);
        //     }
        // }

        return $fillables;
    }

    public static function boot() {
        parent::boot();

        static::creating(function ($model) {
            baseModel::run($model);
        });
        static::created(function ($model) {
            if ('Y' != \config('app.isdblog')) {
                return;
            }
            if ('dblog' == $model->table) {
                return;
            }

            if (request()->is('api/admin/*') || request()->is('admin/*')) {
                $jsonnew = [];
                foreach ($model->attributes as $key => $value) {
                    if (is_array($value)) {
                        $value = implode(',', $value);
                    }
                    $value = trim($value);
                    $title = $model->fieldInfo[$key]['title'];
                    if (PF::isEmpty($title)) {
                        $title = $key;
                    }
                    $jsonnew[$title] = $value;
                }

                $inputs = null;
                $inputs['table'] = $model->table;
                $inputs['title'] = $GLOBALS['nav'] . ' > 新增'; //$model->tabletitle;
                //$inputs['old'] = json_encode($jsonold, JSON_UNESCAPED_UNICODE);
                $inputs['new'] = json_encode($jsonnew, JSON_UNESCAPED_UNICODE);
                $inputs['ip'] = request()->ip();
                $inputs['adminuser_id'] = request()->adminuser['id'];
                $inputs['useraccount'] = request()->adminuser['account'];

                //PF::printr($inputs);
                \App\Models\dblog::create($inputs);
            }
        });
        static::updating(function ($model) {
            baseModel::run($model);
        });
        static::deleted(function ($model) {
            if ('Y' != \config('app.isdblog')) {
                return;
            }

            if (request()->is('api/admin/*') || request()->is('admin/*')) {
                $jsonnew = [];

                foreach ($model->attributes as $key => $value) {
                    $value = trim($value);

                    $title = $model->fieldInfo[$key]['title'];
                    if (PF::isEmpty($title)) {
                        $title = $key;
                    }

                    $jsonnew[$title] = $value;
                    //}
                }

                //if (count($jsonnew) > 0) {
                $inputs = null;
                $inputs['table'] = $model->table;
                $inputs['title'] = $GLOBALS['nav'] . ' >刪除'; //$model->tabletitle;
                //$inputs['old'] = json_encode($jsonold, JSON_UNESCAPED_UNICODE);
                $inputs['new'] = json_encode($jsonnew, JSON_UNESCAPED_UNICODE);
                $inputs['adminuser_id'] = request()->adminuser['id'];
                $inputs['ip'] = request()->ip();
                $inputs['useraccount'] = request()->adminuser['account'];

                \App\Models\dblog::create($inputs);
                //}
            }
        });

        static::updated(function ($model) {
            if ('Y' != \config('app.isdblog')) {
                return;
            }

            if (request()->is('api/admin/*') || request()->is('admin/*')) {
                $jsonold = [];
                $jsonnew = [];

                foreach ($model->attributes as $key => $value) {
                    $value = trim($value);
                    $oldvalue = $model->getOriginal($key);
                    // if ($oldvalue != $value) {
                    $title = $model->fieldInfo[$key]['title'];
                    if (PF::isEmpty($title)) {
                        $title = $key;
                    }

                    $jsonold[$title] = $oldvalue;
                    $jsonnew[$title] = $value;
                    //}
                }

                //if (count($jsonnew) > 0) {
                $inputs = null;
                $inputs['table'] = $model->table;
                $inputs['title'] = $GLOBALS['nav'] . ' > 編輯'; //$model->tabletitle;
                $inputs['old'] = json_encode($jsonold, JSON_UNESCAPED_UNICODE);
                $inputs['new'] = json_encode($jsonnew, JSON_UNESCAPED_UNICODE);
                $inputs['adminuser_id'] = request()->adminuser['id'];
                $inputs['ip'] = request()->ip();
                $inputs['useraccount'] = request()->adminuser['account'];

                //PF::printr($inputs);
                \App\Models\dblog::create($inputs);
                //}
            }
        });
    }

    public function setFieldInfo($fieldInfo) {
        $this->fieldInfo = $fieldInfo;
    }

    protected function getFieldTitleArray() {
        if (null == $this->fieldInfo) {
            return;
        }

        $v1 = [];
        foreach ($this->fieldInfo as $k => $v) {
            // PF::printr($v['title']);
            $v1[$k] = $v['title'];
        }

        return $v1;
    }
    //刪除欄位屬於檔案類型
    public static function delFieldFile($model) {

        if ($model->fieldFiles != null) {
            foreach ($model->fieldFiles as $field => $folder) {
                if ($model->{$field} != "") {
                    $files = explode(",", $model->{$field});
                    foreach ($files as $k2 => $file) {
                        $path = base_path($folder . "/" . $file);
                        if (\File::exists($path)) {
                            \File::delete($path);
                        }
                    }
                }
            }
        }
    }
}
