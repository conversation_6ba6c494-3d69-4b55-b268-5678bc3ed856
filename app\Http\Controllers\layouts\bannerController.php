<?php

namespace App\Http\Controllers\layouts;

use DB;
use PF;
use App\Http\Controllers\Controller;

class bannerController extends Controller {
    public $data;

    public function index() {
        $this->boardRepo = app(\App\Repositories\boardRepository::class);
        $rowsbanner = $this->boardRepo->selectRaw('*');
        $rowsbanner->myWhere('kind|S', 'banner', 'kind', 'N');
        $rowsbanner->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
        $rowsbanner->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        $rowsbanner->orderByRaw('boardsort,id desc');
        $rowsbanner = $rowsbanner->get();
        $this->data['rowsbanner'] = $rowsbanner;
        return view('layouts.banner', [
            'data' => $this->data,
        ]);
    }
}
