<?php

namespace App\Listeners;

/***
"功能名稱":"資料庫操作記錄",
"建立時間":"2022-01-18 13:21:10",
 ***/

use PF;

class dblogListener {
    /**
     * Create the event listener.
     */
    public function __construct() {
    }

    /**
     * Handle the event.
     *
     * @param object $event
     */
    public function handle($event) {
        //if ('dev' == \config('app.env')) {
        $time = $event->time;
        if ($time > 10000 || 'dev' == \config('app.env')) {
            try {
                $sql = $event->sql;
                $bindings = $event->bindings;
                $sql = PF::dbToSqlStr($sql, $bindings);

                //\File::append(storage_path('logs/log.sql'), PHP_EOL . $sql . PHP_EOL . "/*[time:$time]*/" . PHP_EOL . PHP_EOL);
                \Log::info(PHP_EOL . $sql . PHP_EOL . "  [執行超過10秒:$time]" . PHP_EOL);
                //throw new \Exception('no data');
            } catch (\Exception $e) {
                //    echo $e->getMessage(); //throw e;
            } finally {
            }
        }
        //}
    }
}
