@extends('admin.layouts.master')

@section('css')

@stop

@section('js')

@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
    function oForm_onsubmit(form) {
        if (PF_FormMultiAll(form) == false) {
            return false
        };

        if (PF_dbCheckQual(form, 'adminuser', form.elements['account'], '帳號') == false) {
            return false;
        }

        if (typeof(form.elements['password']) != "undefined" && typeof(form.elements['password1']) != "undefined") {
            if (form.elements['password'].value != form.elements['password1'].value) {
                alert('密碼與確認密碼不符');
                form.elements['password1'].focus();
                return false;
            }
        }
        if (PF_dbCheckQual(form, 'adminuser', form.elements['name'], '姓名') == false) {
            return false;
        }

        PF_FieldDisabled(form) //將全部button Disabled
        return true;
    }
</SCRIPT>
<div class="card">
    <div class="card-body">
        <!--// TODO : 前端資料填寫-->
        <form name="oForm" id="oForm" method="post" language="javascript" action="{{ URL::to('admin/adminuser/store') }}" onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->


            <div class="form-group row">
                <label class="col-md-2">帳號：<font class="text-danger">*</font></label>
                <div class="col-md-10">


                    @if ($data['edit'])
                    {{$data['account']}}
                    @else
                    <input type="text" name="account" class="form-control" required requiredclass="required[1,ACCOUNT]" value="{{$data['account']}}" maxlength="25" size="40" pattern="[A-Za-z]{1}[A-Za-z0-9]{3,20}" data-toggle="tooltip" data-placement="top" title="請輸入3至20個字元的文數字,第一個字需為英文字母" name="account" title="帳號" unique="{{$data['account']}}" table="adminuser" />
                    @endif
                </div>

            </div>


            <div class="form-group row">
                <label class="col-md-2">密碼：<font class="text-danger">*</font></label>
                <div class="col-md-10">

                    <input type="password" name="password" class="form-control" data-toggle="tooltip" autocomplete="new-password" {{  $data['edit'] == '' ? 'required' : '' }} data-placement="top" requiredclass="required[0,PASSWORD]" value="" size="40" maxlength="20" pattern="[A-Za-z0-9]{8,20}" title="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *" />
                </div>

            </div>
            <div class="form-group row">
                <label class="col-md-2">姓名：<font class="text-danger">*</font></label>
                <div class="col-md-10">
                    {!! Form::hidden("name1", $data['name'] ) !!}
                    <input type="text" name="name" title="姓名" class="form-control" required requiredclass="required[1,TEXT]" value="{{$data['name']}}" size="40" />
                </div>

            </div>

            <div class="form-group row">
                <label class="col-md-2">E-Mail：<font class="text-danger">*</font></label>
                <div class="col-md-10">


                    <input type="email" name="email" title="E-Mail" class="form-control" requiredclass="required[0,EMAIL]" value="{{$data['email']}}" size="40" placeholder="ex <EMAIL>" />

                </div>

            </div>
            <div class="form-group row">
                <label class="col-md-2">角色：<font class="text-danger">*</font></label>
                <div class="col-md-10">


                    {{Form::myUIXml([
                                            'xmldoc' => $data['xmldoc'],
                                            'type' =>'radio',
                                            'title' =>'角色',
                                            'node' => '//參數設定檔/角色/KIND',
                                            'name' => 'role',
                                            'value' => $data['role'],
                                            //'linecount' => 4,
                                            'requiredclass' => 'required[1,TEXT]',
                                        ])
                }}
                </div>

            </div>
            <div class="form-group row">
                <label class="col-md-2">是否核可：<font class="text-danger">*</font></label>
                <div class="col-md-10">

                    {{ Form::hidden("online", "" ) }}
                    {{Form::checkbox('online', 1,$data['online'])}}
                </div>

            </div>
            <div align="center">

                <button type="submit" class="btn btn-success">確定</button>
                <button type="reset" class="btn btn-secondary">取消</button>


                <button type="reset" class="btn btn-secondary" onClick="javascript:window.history.go(-1)">回上一頁</button>


            </div>

            @include('admin.layouts.hidden', ['method'=>'EditoForm'])

            {!! Form::hidden("edit", $data['edit']) !!}
            {!! Form::hidden("gobackurl", $data['gobackurl'] ) !!}

        </form>
    </div>
</div>

@stop