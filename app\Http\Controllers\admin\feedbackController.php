<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;
use App\Repositories\feedbackRepository;
use PF;
use PT;

class feedbackController extends Controller
{
    private $fieldnicknames;
    private $data;
    private $xmlDoc;
    private $feedbackRepo;

    /**
     *TODO 建構子.
     */
    public function __construct(feedbackRepository $feedbackRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->feedbackRepo = $feedbackRepo;

        // FIXME 導覽列

        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'feedback', $this->data['nav']);

        // FIXME 共用的hidden變數
        $this->data['hiddens'] = [];

        $this->data['displaynames'] = $this->feedbackRepo->getFieldTitleArray();
    }

    /**
     * TODO 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //FIXME 定義那些欄位可以搜尋
        $fieldsearchname = [
                '' => '請選擇',
                //'company' => $this->data['displaynames']['company'],
'name' => $this->data['displaynames']['name'],
'email' => $this->data['displaynames']['email'],
'tel' => $this->data['displaynames']['tel'],
//'ext' => $this->data['displaynames']['ext'],
//'sex' => $this->data['displaynames']['sex'],
'mobile' => $this->data['displaynames']['mobile'],
//'title' => $this->data['displaynames']['title'],
'memo' => $this->data['displaynames']['memo'],
'retitle' => $this->data['displaynames']['retitle'],
'rebody' => $this->data['displaynames']['rebody'],
//'memberid|INT' => $this->data['displaynames']['memberid'],
//'alg' => $this->data['displaynames']['alg'],
            ];
        //FIXME 定義那些日期欄位可以搜尋
        $fieldsearchdatename = [
'feedback.created_at' => $this->data['displaynames']['created_at'],
'feedback.updated_at' => $this->data['displaynames']['updated_at'],
'feedback.redate' => $this->data['displaynames']['redate'],
            ];
        $this->data['fieldsearchname'] = $fieldsearchname;
        $this->data['fieldsearchdatename'] = $fieldsearchdatename;

        $rows = $this->getRows($request);

        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $rows = $rows->paginate(10);
        // 顯示sqlcmd

        $this->data['rows'] = $rows;

        return view('admin/feedback.index', [
                'data' => $this->data,
            ]);
    }

    /**
     * 資料Rows.
     *
     * @return \Illuminate\Http\Response
     */
    public function getRows($request)
    {
        $rows = $this->feedbackRepo->selectRaw('feedback.*');

        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');

        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');

        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), 'desc' == $request->input('sorttype') ? $request->input('sorttype') : 'asc');
        } else {
            $rows->orderByRaw('id desc');
        }

        return $rows;
    }

    /**
     * TODO 資料建立.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        return view('admin/feedback.edit', [
                'data' => $this->data,
            ]);
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
    }

    /**
     * TODO 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $edit = $request->input('edit');

        $validators = null;
        $validators['edit'] = 'required';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $rows = $this->feedbackRepo->select('*');
        $rows->where('id', '=', $edit);
        $rows = $rows->take(1);
        //  PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            //將資料庫欄位與值全部導入到$this->data
            foreach ($rows->first() as $key => $value) {
                $this->data[$key] = $value;
            }
            if ('' == $this->data['retitle']) {
                $this->data['retitle'] = 'Re:'.$this->data['title'];
            }
        } else {
            throw new \CustomException('no data');
        }

        return view('admin/feedback.edit', [
                'data' => $this->data,
            ]);
    }

    /**
     * TODO 資料新增編輯儲存.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $edit = $request->input('edit');
        //FIXME 那些欄位為必填判斷
        $validators = null;

        $validators['name'] = ['required'];
        $validators['email'] = ['required', 'email'];
        //boardController.php$validators['title'] = ['required'];

        $validator = \Validator::make($this->data, $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = $request->all();

        //$inputs['company']=$this->data['company'];/*公司名稱-*/
        //$inputs['name']=$this->data['name'];/*姓名-*/
        //$inputs['email']=$this->data['email'];/*電子信箱-*/
        //$inputs['tel']=$this->data['tel'];/*電話-*/
        //$inputs['ext']=$this->data['ext'];/*分機-*/
        //$inputs['sex']=$this->data['sex'];/*性別-*/
        //$inputs['mobile']=$this->data['mobile'];/*行動電話-*/
        //$inputs['title']=$this->data['title'];/*標題-*/
        //$inputs['memo']=$this->data['memo'];/*留言內容-*/
        //$inputs['retitle']=$this->data['retitle'];/*回覆標題-*/
        //$inputs['rebody']=$this->data['rebody'];/*回覆訊息-*/
        //$inputs['redate']=$this->data['redate'];/*回覆日期-*/
        //$inputs['memberid']=$this->data['memberid'];/*會員編號-*/
        //$inputs['alg']=$this->data['alg'];/*語系-*/
        $inputs['redate'] = date('Y-m-d H:i:s'); /*回覆日期-*/
        //$inputs['memberid'] = $this->data['memberid']; /*會員編號-*/
        //$inputs['alg'] = $this->data['alg']; /*語系-  //[繁體中文:zh], [英文:en], [日文:ja], [韓文:ko], */
        $inputs['userid'] = \Auth::guard('admin')->user()->userid; /*編輯人員-*/
        $inputs['useraccount'] = \Auth::guard('admin')->user()->account; /*編輯人員-*/

        //PF::printr($inputs); exit();
        $this->feedbackRepo->update($inputs, $edit);
        $this->data['alert'] = '回覆成功';
        \Mail::queue(new \App\Mails\feedbackreMail($edit));
        //return back()->with('success','update ok');
        //return back()->with('js','_alert(\'更新成功\')');
        return view('admin.layouts/postsubmit', [
                'data' => $this->data,
            ]);
    }

    /**
     * TODO 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $this->feedbackRepo->deleteIds($this->data['del']);

        return view('admin/layouts/postsubmit', [
                'data' => $this->data,
            ]);
    }
}
