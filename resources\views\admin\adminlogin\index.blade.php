@extends('admin.layouts.master')
@section('css')

@endsection

@section('js')
@endsection

@section('content')

<script language=JavaScript>
    function oForm_onsubmit(form) {

        //    if (PF_FormMulti('1','TEXT',form.elements['xx'],'訊息種類')==false){return false;}
        if (PF_FormMultiAll(form) == false) { return false };
        PF_FieldDisabled(form)
        return true;
    }
</script>



<div class="hold-transition login-page">
    <div class="login-box">
        @if ($data['productid']=="")
        <div class="login-logo">
            <b>{{PF::getConfig('name')}}後端管理</b>
        </div>
        @endif
        <div class="card">
            <div class="card-body login-card-body">
                @if (session('msg'))
                <div class="alert alert-danger" role="alert">
                    {{Session::get('msg')}}
                </div>
                @endif
                @if ($data['productid']=="")
                <p class="login-box-msg">Sign in to start your session</p>
                @endif
                <form class="form loginForm" method="post" language="javascript" action="{{request()->url()}}/adminlogin/login" onsubmit="return oForm_onsubmit(this);">

                    @if ($data['productid']=="")
                    <div class="input-group mb-3">
                        <input type="text" name="account" class="form-control" placeholder="帳號">
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-user"></span>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="input-group mb-3">
                        <input type="password" name="password" class="form-control" placeholder="密碼" data-toggle="tooltip" data-placement="top" title="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # $ % & *">
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>
                    {{-- <div class="input-group mb-3">
                        <input type="text" name="captcha" class="form-control" placeholder="認證碼">
                        <div class="input-group-append">

                            <img src="{{captcha_src('flat')}}" align="absmiddle" style="cursor: pointer" onclick="this.src='{{captcha_src()}}'+Math.random()">
            </div>
        </div> --}}

        <div class="row">
            <div class="col-8">
                <div class="icheck-primary">

                    {{Form::checkbox('iskeep',
                                1,$data['iskeep'],['v-model'=>'inputs.iskeep'])}}
                    <label for="remember">
                        記住我
                    </label>
                </div>
            </div>

            <div class="col-4">
                <button type="submit" class="btn btn-primary btn-block">登入</button>
            </div>

        </div>
        @if ($data['productid']=="")
        <div class="social-auth-links text-center mb-3">
            <p>- OR -</p>
            <a href="{{ url('/') }}" class="btn btn-block btn-danger">
                回前台
            </a>


        </div>
        <!-- /.social-auth-links -->
        @endif
        <p class="mb-1">
            您的IP位置:{{Request::ip()}}
        </p>

        <p class="mb-0">
            主機位置:
            <?=$_SERVER['HTTP_HOST']; ?>
        </p>
    </div>
    <script src="https://www.google.com/recaptcha/api.js?render={{config('recaptcha.id')}}"></script>
    <script>
        $("button").each(function(index) {
            $(this).prop('disabled', true);
        });


        grecaptcha.ready(function() {
            grecaptcha.execute("{{config('recaptcha.id')}}", { action: 'homepage' }).then(function(token) {
                var recaptchaResponse = document.getElementById('recaptchaResponse');
                recaptchaResponse.value = token;
                $("button").each(function(index) {
                    $(this).prop('disabled', false);
                });
            });
        });
    </script> <input type="hidden" value="" name="google_recaptcha_token" id="recaptchaResponse">
    {{ Form::hidden("productid", $data["productid"] ) }}
    </form>
    <style>
        .grecaptcha-badge {
            visibility: hidden;
        }
    </style>



</div>

</div>
</div>

@stop