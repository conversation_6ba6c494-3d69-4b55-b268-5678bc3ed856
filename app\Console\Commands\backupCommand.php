<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class backupCommand extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:database {day=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'backup database';
    public $day = 1;

    public function __construct() {

        parent::__construct();
    }

    public function handle() {
        try {
            $day = $this->argument('day');
            if ('' != $day) {
                $this->day = $day;
            }

            $database = \config('database.connections.mysql.database');
            $username = \config('database.connections.mysql.username');
            $password = \config('database.connections.mysql.password');

            $backupPath = storage_path('backup/');

            $this->delHistoryFile($backupPath);
            $fileName = $database . '-backup-' . \Carbon::now()->format('Y-m-d') . '.sql';

            $filePath = $backupPath . $fileName;

            if (!is_dir($backupPath)) {
                mkdir($backupPath);
            }


            //\Log::info('mysqldump -u ' . $username . ' -p' . $password . ' ' . $database . ' > ' . $filePath);
            exec('mysqldump -u ' . $username . ' -p' . $password . ' ' . $database . ' > ' . $filePath);


            \Log::info('Database backup created at: ' . $filePath);

            $source = $filePath;
            $backup_database_folder = \config('app.backup_database_folder');

            if ('' != $backup_database_folder) {
                $destination = $backup_database_folder . $fileName;
                \File::move($source, $destination);
                $this->delHistoryFile(\config('app.backup_database_folder'));
                \Log::info('Database backup moved to: ' . $destination);
            }
        } catch (\Exception $e) {
            \Log::error('Error backup:database: ' . $e->getMessage());
        }
    }

    public function delHistoryFile($backupPath) {
        $files = \File::files($backupPath, $hidden = false);
        foreach ($files as $k => $v) {
            $filedate = \Str::afterLast($v->getRealpath(), 'backup-');
            $filedate = str_replace('.sql', '', $filedate);

            //if (date('Y-m-d') >= date('Y-m-d', strtotime('-15 day', strtotime($filedate)))) {
            if (date('Y-m-d', strtotime($filedate)) <= date('Y-m-d', strtotime('-' . $this->day . ' day', strtotime(now())))) {
                \Log::info('del file:' . $v->getRealpath());
                \File::delete($v->getRealpath());
            }
        }
    }
}
