<?php

namespace App\Http\Controllers\api;

use PHPHtmlParser\Dom;
use Illuminate\Http\Request;
//use Illuminate\Support\Facades\DB;
use PF;
use DB;
use App\Libraries\UploadFile;

class productController extends Controller
{
    private $data;
    private $db;
    private $fieldnicknames;

    /**
     *建構孝.
     */
    public function __construct()
    {
        parent::__construct();
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->db = new \App\Models\product();

        $this->data = PF::requestAll($this->data);
        $this->fieldnicknames = $this->db->fieldnicknames;
    }

    public function noHtm2($str)
    {
        $str = trim($str);
        $str = preg_replace('@<(.*?)>@is', '', $str);

        return $str;
    }

    public function noHtml1($str)
    {
        $str = trim($str);
        $str = preg_replace('@<script(.*?)</script>@is', '', $str);
        $str = preg_replace('@<iframe(.*?)</iframe>@is', '', $str);
        $str = preg_replace('@<style(.*?)</style>@is', '', $str);
        $str = preg_replace('@<input(.*?)>@is', '', $str);
        $str = preg_replace('@<div(.*?)>@is', '', $str);
        //$str = preg_replace( "@<(.*?)>@is", "", $str );
        $str = preg_replace('/\s(?=\s)/', '', $str);
        $str = preg_replace('/[\n\r\t]/', ' ', $str);
        $str = str_replace("'", '', $str);

        return $str;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';
        try {
            $hs = new \App\Services\htmlparseService($this->data);
            //$hs->debug = 1;
            $hs->run($request);

            $jsondata['resultmessage'] =$hs->resultmessage;
            $jsondata['productid'] =$hs->productid;
            //echo 'product/'.$hs->productid.'.html';
            \File::put(storage_path('product/'.$hs->productid.'.html'), $this->data['body']);
        } catch (\Exception $e) {
            $jsondata['resultcode'] = 999;
            //echo $e->getMessage();
            $jsondata['resultmessage'] = $e->getMessage();
        }
        //return response('ok');
        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
