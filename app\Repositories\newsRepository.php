<?php

namespace App\Repositories;

use App\Models\news;
use DB;
use PF;

class newsRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(news $model)
    {
        $this->model = $model;
    }
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    public function view($rows)
    {
        $rows->join('diversionurl', 'diversionurl.id', '=', 'diversionlog.diversionurl_id');
        return $rows;
    }


    public function create($inputs)
    {

        return parent::create($inputs);
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        return  parent::update($inputs,$id,'id') ;      
    }

    public function deleteIds($ids)
    {
    //     $this->newsxxRepo = app(\App\Repositories\newsxxRepository::class);
    //     $rows = $this->newsxxRepo->select(null);
    //     $rows->myWhere('news_id|ININT', $ids, 'news_id', 'Y');
    //     $rows->delete();

        
    //    \DB::delete('delete newssign from  newssign INNER JOIN newsxx ON (newsxx.id=newssign.newsxx_id) where newsxx.news_id in (?)',[$ids]);
        
        parent::deleteIds($ids);
      

        // $rows=$this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // $rows->get()->each(function ($rs) {
        //     $path=storage_path($this->model->table.'/'.$rs->img.".json");            
        //     //$path=public_path('images/'.$this->model->table.'/'.$rs->img);
        //     if (\File::exists($path)) {
        //        \File::delete($path);
        //     }            
        //     $rs->delete();
        // });
        
    }
}
