<?php

namespace App\Http\Controllers\admin;

use PF;
use PT;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\View;
use App\Repositories\messageRepository;
use Illuminate\Support\Facades\Validator;

class messageController extends Controller
{
    private $fieldnicknames;
    private $data;
    private $xmlDoc;
    private $messageRepo;

    /**
     *TODO 建構子.
     */
    public function __construct(messageRepository $messageRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->messageRepo = $messageRepo;

        // FIXME 導覽列

        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'message', $this->data['nav']);

        // FIXME 共用的hidden變數
        $this->data['hiddens'] = [];

        $this->data['displaynames'] = $this->messageRepo->getFieldTitleArray();
    }

    /**
     * TODO 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //FIXME 定義那些欄位可以搜尋
        $fieldsearchname = [
                '' => '請選擇',
                'kind' => $this->data['displaynames']['kind'],
'name' => $this->data['displaynames']['name'],
'email' => $this->data['displaynames']['email'],
'sex' => $this->data['displaynames']['sex'],
'title' => $this->data['displaynames']['title'],
'body' => $this->data['displaynames']['body'],
'rebody' => $this->data['displaynames']['rebody'],
            ];
        //FIXME 定義那些日期欄位可以搜尋
        $fieldsearchdatename = [
'message.created_at' => $this->data['displaynames']['created_at'],
'message.updated_at' => $this->data['displaynames']['updated_at'],
            ];
        $this->data['fieldsearchname'] = $fieldsearchname;
        $this->data['fieldsearchdatename'] = $fieldsearchdatename;

        $rows = $this->getRows($request);

        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $rows = $rows->paginate(10);
        // 顯示sqlcmd

        $this->data['rows'] = $rows;

        return view('admin/message.index', [
                'data' => $this->data,
            ]);
    }

    /**
     * 資料Rows.
     *
     * @return \Illuminate\Http\Response
     */
    public function getRows($request)
    {
        $rows = $this->messageRepo->selectRaw('message.*');

        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');

        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');

        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), 'desc' == $request->input('sorttype') ? $request->input('sorttype') : 'asc');
        } else {
            $rows->orderByRaw('id desc');
        }

        return $rows;
    }

    /**
     * TODO 資料建立.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        return view('admin/message.edit', [
                'data' => $this->data,
            ]);
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $rows = $this->messageRepo->select('*');
        $rows->where('id', '=', $id);
        $rows = $rows->take(1);
        //  PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('no data');
        }

        return view('admin/message.show', [
                'data' => $this->data,
            ]);
    }

    /**
     * TODO 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $edit = $request->input('edit');

        $validators = null;
        $validators['edit'] = 'required';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $rows = $this->messageRepo->select('*');
        $rows->where('id', '=', $edit);
        $rows = $rows->take(1);
        //  PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            //將資料庫欄位與值全部導入到$this->data
            foreach ($rows->first() as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('no data');
        }

        return view('admin/message.edit', [
                'data' => $this->data,
            ]);
    }

    /**
     * TODO 資料新增編輯儲存.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $edit = $request->input('edit');
        //FIXME 那些欄位為必填判斷
        $validators = null;

        $validators['kind'] = ['required'];
        $validators['title'] = ['required'];

        $validator = \Validator::make($this->data, $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = $request->all();

        //$inputs['kind']=$this->data['kind'];/*留言類別-*/
        //$inputs['name']=$this->data['name'];/*姓名-*/
        //$inputs['email']=$this->data['email'];/*電子信箱-*/
        //$inputs['sex']=$this->data['sex'];/*性別-*/
        //$inputs['title']=$this->data['title'];/*標題-*/
        //$inputs['body']=$this->data['body'];/*留言內容-*/

        //PF::printr($inputs); exit();
        $this->messageRepo->update($inputs, $edit);
        $this->data['alert'] = '更新成功';

        if ('1' == $this->data['isemail']) {
            \Mail::queue(new \App\Mails\messagereMail($edit));
        }

        //return back()->with('success','update ok');
        //return back()->with('js','_alert(\'更新成功\')');
        return view('admin.layouts/postsubmit', [
                'data' => $this->data,
            ]);
    }

    /**
     * TODO 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $this->messageRepo->deleteIds($this->data['del']);

        return view('admin/layouts/postsubmit', [
                'data' => $this->data,
            ]);
    }
}
