<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
//php artisan migrate:refresh --path=/database/migrations/_create_{{ table }}_table.php
return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('{{ table }}')) {
            Schema::create('{{ table }}', function (Blueprint $table) {
                //$table->engine = 'MyISAM';
                $table->increments('id')->from(10000)->comment('自動編號');
                $table->string('title')->comment('標題');
                $table->mediumText('body')->nullable()->comment('內文');

                //$table->unique(['classt_id', 'member_id'], '{{ table }}_unique');
                //$table->mediumText('jsonbody')->nullable()->comment('json');
                //$table->date('begin_date')->nullable()->comment('開始時間');
                //$table->date('close_date')->nullable()->comment('結束時間');

                $table->timestamp('created_at')->useCurrent()->comment('建立時間');
                $table->timestamp('updated_at')->useCurrentOnUpdate()->nullable()->comment('更新時間');
            });
            \DB::statement("ALTER TABLE {{ table }} COMMENT 'XX'");
        }
        /*
        $table->unsignedBigInteger('activitysession_id')->comment('場次');
        $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

        $table->string('kind',50)->index()->comment('種類');
        $table->mediumText('body')->nullable()->comment('說明');
        $table->dateTime('begindate')->nullable()->comment('開始時間');
        $table->integer('hits')->default(0)->comment('點率次數');
        $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
        $table->integer('adminsuer_id')->nullable()->comment('編輯人員');
        $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
        $table->string('edit_account',50)->comment('修改人');
        $table->string('account',50)->unique();;
        $table->timestamps('reviewed_at')->default('now');
        $table->unique(array('kind', 'kindid'));
        $table->index(array('kind', 'kindid'));
        $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('{{ table }}');
    }
};