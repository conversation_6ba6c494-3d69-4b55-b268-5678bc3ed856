<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateKindTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('kind')) {
        Schema::create('kind', function (Blueprint $table) {
            $table->bigIncrements('kindid');
            $table->string('kind')->index()->comment('種類');
            $table->string('kindtitle', 255)->comment('標題');
            for ($i = 1; $i <= 3; ++$i) {
                $table->string('kindfield'.$i,2000)->nullable()->comment('其他欄位'.$i);
            }
            $table->string('alg', 5)->comment('語系');
            $table->float('kindsortnum', 5, 3)->nullable()->comment('排序號碼');
            $table->timestamps();
        });
    }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('kind');
    }
}
