<?php

namespace App\Console\Commands;

use DB;
use PF;
use Illuminate\Console\Command;

//command "php artisan makephpunit:model"
class makephpunitCommand extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'makephpunit:file {--workspaceFolder=} {--fileName=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '產生測試程式';
    public $data;

    public function __construct() {
        parent::__construct();
    }



    public function handle() {
        $workspaceFolder = $this->option('workspaceFolder');
        //echo "workspaceFolder" . $workspaceFolder . PHP_EOL;
        $fileName =  $this->option('fileName');
        $fileName =  str_replace($workspaceFolder, "", $fileName);
        //echo "fileName" . $fileName . PHP_EOL;
        $folder = str_replace($workspaceFolder, "", $fileName);
        $folder = str_replace("\\app\\", "", $folder);
        $folder = str_replace($fileName, '', $folder);
        $folder = str_replace('.php', '', $folder);
        if (\Str::contains($folder, ['Controller', 'Services']) == false) {
            echo "只限Controller,Services目錄";
            exit();
        }

        $folder = str_replace('Http\\Controllers\\', '', $folder);
        //$items=explode("chr(13).chr(10)",$s);
        $folders = explode("\\", $folder);
        $folder = str_replace(end($folders), '', $folder);

        $fileName = str_replace('.php', '', $fileName);
        $fileName = str_replace("\\app\\", 'App\\', $fileName);
        $name = end(explode("\\", $fileName));
        $name = str_replace("Controller", '', $name);
        $name = str_replace("Service", '', $name);
        $tableName = str_replace("Controller", '', $name);

        // echo "name:" . $name . PHP_EOL;
        // echo "tableName:" . $tableName . PHP_EOL;

        //exit();
        //$items=explode("chr(13).chr(10)",$s);
        $items = explode("\\", $fileName);
        $namespace = $fileName;
        $namespace = str_replace("App\Http\Controllers", '', $namespace);
        $namespace = str_replace("service", '', $namespace);

        //\PF::printr(["namespace ", $namespace]);
        $namespace = str_replace($name, '', $namespace);
        $namespace = rtrim($namespace, "\\");
        if ($namespace == "\\") {
            $namespace = "";
        }
        //\PF::printr(["namespace ", $namespace]);
        $url = $namespace . "\\" . str_replace("Controller", '', $name);;
        $url = str_replace("\\", '/', $url);
        $url = str_replace("App", '', $url);
        $url = str_replace("Controller/", '', $url);
        $saveFileName = base_path('/tests/Feature/' . $folder .  $name . "Test.php");
        //\PF::printr(["url ", $url]);
        if (\Str::contains($url, ['Services'])) {
            $saveFileName = base_path('/tests/Service/' .  $name  . "Test.php");
            $namespace = "Service";
        } else {
            $namespace = "Feature" . $namespace;
            $namespace = str_replace("Controller", '', $namespace);
        }
        $namespace =  rtrim($namespace, "\\");
        if (\File::exists($saveFileName)) {
            echo $saveFileName . " 已存在";
            exit();
        }
        // echo $saveFileName;
        // exit();
        //exit();
        $this->data['tableName'] = $tableName;
        $this->data['name'] = $name;
        $this->data['url'] = $url;
        $this->data['namespace'] = $namespace;
        $reflector = new \ReflectionClass($fileName);
        foreach ($reflector->getMethods() as $m) {
            $item['name'] = strval($m->name);
            if (\Str::contains($item['name'], ['middleware'])) {
                break;
            }
            if (\Str::contains($item['name'], ['_construct', 'checkRoleRows', 'listRows', 'getRows', 'create'])) {
                continue;
            }
            $title = "";
            switch ($item['name']) {
                case 'index':
                    $title = "列表";
                    break;
                case 'edit':
                    $title = "顯示編輯";
                    break;
                case 'store':
                    $title = "編輯儲存";
                    break;
                case 'destroy':
                    $title = "刪除";
                    break;
                case 'show':
                    $title = "單筆顯示";
                    break;
                case 'importstore':
                    $title = "Excel匯入";
                    break;
                case 'excelexport':
                    $title = "Excel匯出";
                    break;
                case 'excelsample':
                    $title = "Excel匯出範本";
                    break;
                default:
                    # code...
                    break;
            }
            $item['title'] = $title;
            $this->data['functions'][] = $item;
        }

        $body = \File::get('C:\AppServ\laravel\1\sample\tests\index.blade.php');
        $body = \Blade::render($body, $this->data);
        //echo $body;


        \File::put($saveFileName, $body);
    }
}
