<?php

namespace Database\Factories;

use App\Models\adminuser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class AdminuserFactory extends Factory {
    /**
     * Factory對應的模型
     *
     * @var string
     */
    protected $model = adminuser::class;

    /**
     * 定義模型的預設狀態
     *
     * @return array
     */
    public function definition() {
        return [
            'account' => $this->faker->unique()->userName,
            'name' => $this->faker->name,
            'password' => bcrypt('password'), // 預設密碼
            'api_token' => Str::random(80),
            'remember_token' => Str::random(10),
            'email' => $this->faker->unique()->safeEmail,
            'role' => $this->faker->randomElement([900, 999]), // 董事或管理者
            'lastlogin_dt' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'lastlogin_ip' => $this->faker->ipv4,
            'linenotify_token' => Str::random(40),
            'online' => 1, // 預設為開啟狀態
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * 設定為管理者角色
     */
    public function admin() {
        return $this->state(function (array $attributes) {
            return [
                'role' => 999,
            ];
        });
    }

    /**
     * 設定為董事角色
     */
    public function director() {
        return $this->state(function (array $attributes) {
            return [
                'role' => 900,
            ];
        });
    }
}
