<?php

namespace App\Http\Controllers;

use DB;
use PF;
use Config;
use Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\feedbackRepository;
use Illuminate\Support\Facades\Validator;

//use Illuminate\Support\Facades\DB;

class contactController extends Controller
{
    private $data;
    private $feedbackRepo;

    /**
     *建構子.
     */
    public function __construct(feedbackRepository $feedbackRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        //$this->db = new \App\Models\feedback();
        $this->feedbackRepo = $feedbackRepo;
        $this->data['displaynames'] = $this->feedbackRepo->getFieldTitleArray();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        \Config::set('config.'.app()->getLocale().'.title', __('與我聯絡').' | '.config('config.title'));

        return view('contact.index', [
            'data' => $this->data,
            ]
       );
    }

    /**
     * 資料新增編輯儲存.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        /*欄位規格與必填判斷*/
        $validators = null;

        $validators['name'] = 'required';
        $validators['email'] = 'required|email';
        // //        $validators['title'] = 'required';
//         if ('@DEBUG' != $this->data['captcha']) {
//             $validators['captcha'] = 'required|captcha';
//         }
        $validators['google_recaptcha_token'] = ['required', 'string', new \App\Rules\MyValidatorsGoogleRecapchaV3()];
        $validator = Validator::make($request->all(), $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = $request->all();

        $inputs['memberid'] = \Auth::guard('member')->user()->memberid;
        $inputs['alg'] = app()->getLocale(); /*語系-  //[繁體中文:zh], [英文:en], [日文:ja], [韓文:ko], */

        //PF::printr($inputs);exit();
        $this->feedbackRepo->create($inputs);

        //Session::flash('msg', $msg);
        //return back()->with('success','update ok');
        //return redirect('contact')->with('js', '_alert("已傳送訊息，將由專人與您聯繫，謝謝")');
        return back()->with('js', '_alert(\'已傳送訊息，將由專人與您聯繫，謝謝\')');
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
    }
}
