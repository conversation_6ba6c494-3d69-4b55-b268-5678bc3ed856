@echo off
chcp 65001
cd..
set "current_dir=%~dp0"

@REM set "directoryvue=%current_dir%..\resources\js\views\"
@REM set "directorynuxt=%current_dir%..\resources\js\pages\"
@REM echo %directoryvue%
@REM echo %directorynuxt%

@REM if exist "%directoryvue%" (
@REM     echo vue exists
@REM     goto :end
@REM )

@REM if exist "%directorynuxt%" (
@REM     echo nuxt exists
@REM     goto :end
@REM )

echo %current_dir%
C:\inetpub\wwwroot\codelive\codelive\bin\Debug\net6.0\codelive.exe --path %current_dir%

:end
cmd /k