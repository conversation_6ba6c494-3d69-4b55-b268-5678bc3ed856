<?php

namespace App\Http\Controllers\admin;

use DB;
use PF;
use PT;
use App\Libraries\PM;
use Illuminate\Http\Request;
use App\Libraries\UploadFile;
use Illuminate\Support\Facades\View;
use App\Repositories\epostRepository;
use Illuminate\Support\Facades\Input;

class epostxmlController extends adminController
{
    private $fieldnicknames;
    private $data;

    private $hiddenarray;
    private $pm;
    private $epostRepo;

    /**
     * 建構值
     */
    public function __construct(epostRepository $epostRepo)
    {
        //$this->limit="xx";
        parent::__construct();

        $this->data = PF::requestAll($this->data);
        //PF::printr($this->data);
        $this->db = new \App\Models\board();
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        $this->data['xmlspec'] = PF::xmlDoc('spec/epost'.$this->data['edit'].'.xml');

        $this->pm = new PM(\Request::all(), $this->data['xmldoc'], $this->data['xmlspec']);

        //導覽列
        $this->data['nav'] = PT::nav($this->data['xmldoc'], $this->data['edit']);
        $this->epostRepo = $epostRepo;
        $this->fieldnicknames = null;
        $xPath = "//table/Field[@edit='Y']";
        $v = $this->data['xmlspec']->xpath($xPath);
        for ($i = 0; $i < count($v); ++$i) {
            $this->fieldnicknames[strval($v[$i]['name'])] = $v[$i]['title'];
        }

        if ('' != config('config.zh.name')) {
            if ('' == $this->data['alg']) {
                $this->data['alg'] = 'zh';
            }
        }
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $xPath = "//table/Field[@edit='Y']";
        $v = $this->data['xmlspec']->xpath($xPath);

        $epostid = '';
        $cc = '';
        for ($i = 0; $i < count($v); ++$i) {
            $epostid .= $cc.$v[$i]['name'];
            $cc = ',';
        }

        $rows = $this->epostRepo->selectRaw('*');
        $rows->myWhere('epostid|INS', $epostid, 'epostid', 'Y');

        if ('' != config('config.zh.name')) {
            $rows->myWhere('alg|S', $this->data['alg'], 'alg', 'Y');
        }

        //\PF::dbSqlPrint($rows);
        $rows = $rows->get();

        //print_r(DB::getQueryLog()); // Show results of log
        foreach ($rows as $rs) {
            $this->data[$rs->epostid] = $rs->epostbody;
        }

        $this->data['pmmodify'] = $this->pm->modify($this->data);

        return view('admin.epostxml.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validators = null;
        $xPath = "//table/Field[@edit='Y']";

        $v = $this->data['xmlspec']->xpath($xPath);
        for ($i = 0; $i < count($v); ++$i) {
            if ('Y' == $v[$i]['必填']) {
                $validators[strval($v[$i]['name'])] = ['required'];
            }
        }

        $inputs = null;
        //$input = $request->only(array_keys($fields));

        $xPath = "//table/Field[@edit='Y']";
        $v = $this->data['xmlspec']->xpath($xPath);

        $xPath = "//table/Field[@edit='Y']";
        $v = $this->data['xmlspec']->xpath($xPath);

        for ($i = 0; $i < count($v); ++$i) {
            $epostinputs = null;

            $epostinputs['epostid'] = strval($v[$i]['name']);
            $epostinputs['eposttitle'] = $this->data['eposttitle'];

            //PF::printr($epostinputs);
            // $inputs['created_at'] = $request->input('created_at');
            try {
                if ('' != strval($v[$i]['上傳目錄'])) {
                    $inputs = null;

                    /*檔案上傳*/
                    $upload = new UploadFile();
                    $upload->request = $request;
                    $upload->inputs = $inputs;
                    $upload->folder = 'images/epost';
                    //$upload->width = '800';
                    //$upload->height = '600';
                    //$upload->limitext = config('app.FileLimit');
                    $inputs = $upload->execute();
                    $epostinputs['epostbody'] = $inputs[strval($v[$i]['name'])];
                } else {
                    $epostinputs['epostbody'] = $request->input(strval($v[$i]['name']));
                }
            } catch (\Exception $e) {
                //var_dump($e);
                die($e->getMessage());
            }

            // DB::table('epost')->select()
            // ->myWhere('epostid|S', $this->data['edit'].'_'.$v[$i]['name'], 'epostid', 'Y')
            // ->delete();

            if ('' != config('config.zh.name')) {
                $this->epostRepo->updateOrCreate(['epostid' => $v[$i]['name'], 'alg' => $this->data['alg']], $epostinputs);
            } else {
                $this->epostRepo->updateOrCreate(['epostid' => $v[$i]['name']], $epostinputs);
            }
        }

        return back()->with('js', "_toast('更新成功',500)");

        return view('admin/layouts/postsubmit', [
            'data' => $this->data,
        ]);
    }
}
