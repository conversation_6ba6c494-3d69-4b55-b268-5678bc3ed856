<?php

namespace App\Console;

use DB;
use PF;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Services\ProductService;

class Kernel extends ConsoleKernel {
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        'App\Console\Commands\dayCommand',
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     */
    protected function schedule(Schedule $schedule) {
        //建立LOG檔案
        $schedule->call(function () {
            $logfile = storage_path('logs/laravel-' . date('Y-m-d') . '.log');
            \File::put($logfile, '');
            chmod($logfile, 0777);
        })->daily()->onSuccess(function () {
            \Log::notice('create logs successful.');
        })->onFailure(function (Stringable $output) {
            \Log::error('create logs failed.' . $output);
        });
        $schedule->call(function () {
            try {
                $validCommands = array('route:clear', 'config:clear', 'cache:clear', 'view:clear', 'clear-compiled', 'config:cache');
                foreach ($validCommands as $cmd) {
                    try {
                        $this->call('' . $cmd . '');
                    } catch (\Exception $e) {
                        \Log::error($cmd . ' > ' . $e->getMessage());
                    }
                }
            } catch (\Exception $e) {
                \Log::error($e->getMessage());
            }
        })->dailyAt('04:00')->onSuccess(function () {
            \Log::notice('day:clear successful.');
        })->onFailure(function (Stringable $output) {
            \Log::error('day:clear failed.' . $output);
        });
        //php.ini要支援 register_argc_argv=On 才可以在command加arg參數
        //備份資料庫
        //$schedule->command('backup:database 2')->everyFiveMinutes();
        // $schedule->call(function () {
        //     try {
        //         $notifyService = new \App\Services\notifyService(null);
        //         $notifyService->run();
        //         \Log::info('notifyService OK');
        //     } catch (\Exception $e) {
        //         \Log::error($e->getMessage());
        //     }
        // })->dailyAt('10:00');

        // $schedule->call(function () {


        // })->everyFiveMinutes();

        //每天凌晨檢查過期商品

        $schedule->call(function () {
            app(ProductService::class)->handleExpiredProducts();
        })->dailyAt('00:00')->onSuccess(function () {
            \Log::notice('ProductService handleExpiredProducts successful.');
        })->onFailure(function (Stringable $output) {
            \Log::error('ProductService handleExpiredProducts  failed.' . $output);
        });

        // $schedule->command('inspire')
        //          ->hourly();
        //$schedule->command('day:run')->everyFiveMinutes();
        //$schedule->command('clear:data')->dailyAt('11:00');
        // $schedule->command('makejs:update')
        // //->dailyAt('00:00')
        // ->appendOutputTo(storage_path('logs/schedule.log'));

        // $schedule->command('SyncAPIOrders:orders')
        // ->timezone('Asia/Kolkata')
        // ->dailyAt('00:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands() {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
