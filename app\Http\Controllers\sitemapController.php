<?php

namespace App\Http\Controllers;

use DB;
use PF;

//use Illuminate\Support\Facades\DB;

class sitemapController extends Controller {
    private $data;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    public function index() {
        $body = '';
        try {

            $xml = new \SimpleXMLElement('<urlset xmlns="http://www.google.com/schemas/sitemap/0.84"></urlset>');
            $this->url($xml, url('/'), 1);
            //$this->url($xml, url('news/today'), 0.4);
            $rows = DB::table('kind')->selectRaw('kindid');

            $rows->myWhere('kind|S', "newskind", 'kind', 'Y');
            //$rows->myWhere('board.online|N', "1", 'online', 'Y');
            $rows = $rows->get();
            foreach ($rows as $rs) {
                $this->url($xml, url('news/' . $rs->kindid), 0.4);
            }

            $rows = DB::table('board')->selectRaw('id');
            $rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
            $rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
            $rows->myWhere('board.kind|S', "news", 'kind', 'Y');
            //$rows->myWhere('board.online|N', "1", 'online', 'Y');
            $rows = $rows->get();
            foreach ($rows as $rs) {
                $this->url($xml, url('news/show/' . $rs->id), 0.4);
            }
            // $rows = DB::table('product')->selectRaw('productid');
            // $rows->myWhere('online|N', "1", 'kind', 'Y');
            // //$rows->myWhere('board.online|N', "1", 'online', 'Y');
            // $rows = $rows->get();
            // foreach ($rows as $rs) {
            //     $this->url($xml, url('product/show/' . $rs->id), 0.4);
            // }
            // $rows = DB::table('message')->selectRaw('id');

            // $rows = $rows->get();
            // foreach ($rows as $rs) {
            //     $this->url($xml, url('message/show/' . $rs->id), 0.4);
            // }

            $body = $xml->asXML();
        } catch (\Exception $e) {
            $body = $e->getMessage();
        }

        return  response($body)->header('Content-Type', 'text/xml');
    }
    public function list() {
        $body = '';
        try {
            $xml = new \SimpleXMLElement('<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></sitemapindex>');

            $rows = DB::table('product');
            $rows->myWhere('online|N', "1", 'kind', 'Y');
            $rows->selectRaw('count(*) as t');
            $rows->limit(1);

            if ($rows->count() > 0) {
                $rs = $rows->first();
                $t = $rs->t;
                for ($i = 1; $i < Ceil($t / 500) + 1; ++$i) {
                    $url = $xml->AddChild('sitemap', '');
                    $url->AddChild('loc', url('sitemap/page/' . $i));
                }
            }


            $body = $xml->asXML();
        } catch (\Exception $e) {
            $body = $e->getMessage();
        }

        return  response($body)->header('Content-Type', 'text/xml');
    }



    public function page($page) {
        $body = '';
        try {

            $xml = new \SimpleXMLElement('<urlset xmlns="http://www.google.com/schemas/sitemap/0.84"></urlset>');

            $rows = DB::table('product')->selectRaw('productid');
            $rows->myWhere('online|N', "1", 'kind', 'Y');
            $rows = $rows->paginate(500, ['*'], 'page', $page);
            foreach ($rows as $rs) {
                $this->url($xml, url('house/show/' . $rs->productid), 0.4);
            }

            $body = $xml->asXML();
        } catch (\Exception $e) {
            $body = $e->getMessage();
        }

        return  response($body)->header('Content-Type', 'text/xml');
    }


    public function url($xml, $loc, $priority) {
        $url = $xml->AddChild('url', '');
        $url->AddChild('loc', $loc);
        $url->AddChild('lastmod', date('Y-m-d'));
        $url->AddChild('changefreq', 'weekly');
        $url->AddChild('priority', $priority);
    }
}
