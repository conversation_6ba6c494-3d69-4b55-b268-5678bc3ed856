@extends('layouts.master')
@section('css')
    <!-- 自定義 CSS 樣式 -->
    <style>
        .btn-outline-myGray.active {
            @apply bg-blue-600 text-white border-blue-600;
        }

        .btn-outline-myGray {
            @apply border-gray-300 text-gray-700 hover:bg-gray-50;
        }

        .must {
            @apply text-red-500;
        }

        .must2 {
            @apply text-red-500;
        }
    </style>
@stop

@section('content')
    <script type="text/javascript">
        function opencity(s, city1) {
            jQuery(".btn-outline-myGray").removeClass('active');
            $(s).addClass('active');
            $('[rel^="city2"]').hide();
            jQuery("[rel='city2_" + city1 + "']").show();
        }
        document.addEventListener("DOMContentLoaded", () => {
            $(".county").eq(0).trigger("click");
        });
    </script>
    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">基本資訊</h2>


    <!-- 主要內容區 -->
    <div class="min-h-screen bg-gray-50 py-8">
        <div class="max-w-4xl mx-auto px-4">

            <!-- 頁面標題區域 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <span
                            class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">📝</span>
                        我的基本資訊
                    </h1>
                    @if (Auth::guard('member')->check())
                        <a href="{{ \request()->middlewareurl }}myproduct"
                            class="inline-flex items-center px-4 py-2 bg-yellow-500 text-white text-sm font-medium rounded-lg hover:bg-yellow-600 transition-colors">
                            ⭐ 我的收藏
                        </a>
                    @endif
                </div>
            </div>

            <!-- 基本資訊表單 -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <form class="needs-validation" action="{{ request()->url() }}/store" novalidate method="post">
                    <div class="px-6 py-6">

                        <!-- 個人基本資料 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">
                                    <span class="must">*</span>姓名
                                </label>
                                <input type="text" name="name"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    value="{{ $data['name'] }}" required placeholder="請輸入您的姓名">
                                <div class="mt-2">
                                    {{ Form::myUIXml([
                                        'xmldoc' => $data['xmldoc'],
                                        'type' => 'radio',
                                        'title' => '性別',
                                        'node' => '//參數設定檔/性別/KIND',
                                        'name' => 'sex',
                                        'value' => $data['sex'],
                                        'required' => true,
                                    ]) }}
                                </div>
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">LINE ID</label>
                                <input type="text" name="lineid"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    value="{{ $data['lineid'] }}" placeholder="請輸入您的 LINE ID">
                            </div>
                        </div>

                        <!-- 聯絡資訊 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">
                                    <span class="must">*</span>行動電話
                                </label>
                                <input type="tel" name="mobile" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    value="{{ $data['mobile'] }}" placeholder="ex 09123456789" />
                                <div class="text-sm text-red-600 mt-1">
                                    行動電話格式錯誤 ex 09123456789
                                </div>
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">市話</label>
                                <input type="text" name="tel"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                    value="{{ $data['tel'] }}" placeholder="03-236456">
                            </div>
                        </div>

                        <!-- 電子郵件 -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <span class="must">*</span>電子郵件
                            </label>
                            <input type="email" name="email" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                value="{{ $data['email'] }}" placeholder="<EMAIL>">
                        </div>

                        <!-- 分隔線 -->
                        <div class="border-t border-gray-200 my-8"></div>

                        <!-- 需求標題 -->
                        <div class="mb-6">
                            <div class="flex items-center space-x-4 mb-4">
                                <h2 class="text-xl font-bold text-gray-900 flex items-center">
                                    <span
                                        class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">🏠</span>
                                    需求
                                </h2>
                                <span
                                    class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">法拍屋</span>
                            </div>
                        </div>

                        <!-- 房屋類型 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">類型</label>
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                {{ Form::myUIXml([
                                    'xmldoc' => $data['xmldoc'],
                                    'type' => 'checkbox',
                                    'title' => '房屋類別',
                                    'node' => '//參數設定檔/房屋類別/KIND',
                                    'name' => 'patterns',
                                    'value' => $data['patterns'],
                                    'required' => true,
                                ]) }}
                            </div>
                        </div>

                        <!-- 購屋預算 -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-3">購屋預算</label>
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                {{ Form::myUIXml([
                                    'xmldoc' => $data['xmldoc'],
                                    'type' => 'checkbox',
                                    'title' => '購屋預算',
                                    'node' => '//參數設定檔/購屋預算/KIND',
                                    'name' => 'totalupsets',
                                    'value' => $data['totalupsets'],
                                    'required' => true,
                                ]) }}
                            </div>
                        </div>

                        <!-- 分隔線 -->
                        <div class="border-t border-gray-200 my-8"></div>

                        <!-- 區域選擇 -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-4">區域</label>

                            <!-- 縣市選擇 -->
                            <div class="mb-4">
                                <h3 class="text-sm font-medium text-gray-600 mb-3">請選擇縣市：</h3>
                                <div id="counties" class="flex flex-wrap gap-2">
                                    @if ($data['city1rows'] != null)
                                        @foreach ($data['city1rows'] as $rs)
                                            <button type="button"
                                                class="btn-outline-myGray county px-4 py-2 text-sm font-medium border rounded-lg transition-colors"
                                                onclick="opencity(this,'{{ $rs->city1title }}')">
                                                {{ $rs->city1title }}
                                            </button>
                                        @endforeach
                                    @endif
                                </div>
                            </div>

                            <!-- 鄉鎮區選擇 -->
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                @if ($data['city1rows'] != null)
                                    @foreach ($data['city1rows'] as $rs)
                                        <div rel="city2_{{ $rs->city1title }}" style="display:none" class="space-y-2">
                                            <h4 class="text-sm font-medium text-gray-600 mb-3">請選擇 {{ $rs->city1title }}
                                                的鄉鎮區：</h4>
                                            {{ Form::myUIDb([
                                                'type' => 'checkbox',
                                                'sql' => "select postal,city2title from city2 where city1title='" . $rs->city1title . "' order by city2title",
                                                'name' => 'postals',
                                                'value' => $data['postals'],
                                            ]) }}
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>

                        <!-- 分隔線 -->
                        <div class="border-t border-gray-200 my-8"></div>

                        <!-- 細項需求 -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">細項需求</label>
                            <textarea name="item_request" rows="5"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                placeholder="請詳細描述您的需求...">{{ $data['item_request'] }}</textarea>
                        </div>

                        <!-- 同意條款 -->
                        <div class="mb-6">
                            <div class="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                <input type="checkbox" required id="check-1"
                                    class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="check-1" class="text-sm text-gray-700">
                                    <span class="font-medium">我同意</span>收到台灣房訊網的電子報訊息
                                </label>
                            </div>
                        </div>

                        <!-- 提交按鈕 -->
                        <div class="flex justify-end">
                            <button type="submit"
                                class="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-500 focus:ring-opacity-50 transition-all duration-200 transform hover:scale-105">
                                💾 儲存資料
                            </button>
                        </div>

                        @if (Auth::guard('member')->check() == false)
                            <script src="https://www.google.com/recaptcha/api.js?render={{ config('recaptcha.id') }}"></script>
                            <script>
                                $("button").each(function(index) {
                                    $(this).prop('disabled', true);
                                });
                                grecaptcha.ready(function() {
                                    grecaptcha.execute("{{ config('recaptcha.id') }}", {
                                        action: 'homepage'
                                    }).then(function(token) {
                                        var recaptchaResponse = document.getElementById('recaptchaResponse');
                                        recaptchaResponse.value = token;
                                        $("button").each(function(index) {
                                            $(this).prop('disabled', false);
                                        });
                                    });
                                });
                            </script>
                            <input type="hidden" value="" name="google_recaptcha_token" id="recaptchaResponse">
                            {{ Form::hidden('password', $data['password']) }}
                        @endif
                    </div>
                </form>
            </div>

            <!-- 密碼設定區塊 (僅登入會員顯示) -->
            @if (Auth::guard('member')->check())
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <form name="oForm" class="needs-validation" id="oForm" method="post"
                        action="{{ \request()->middlewareurl }}changepassword/store" novalidate>
                        <div class="px-6 py-6">
                            <!-- 密碼設定標題 -->
                            <div class="mb-6">
                                <h2 class="text-xl font-bold text-gray-900 flex items-center">
                                    <span
                                        class="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">🔒</span>
                                    密碼設定
                                </h2>
                            </div>

                            <!-- 密碼輸入區域 -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">
                                        <span class="must2">*</span>新密碼
                                    </label>
                                    <input type="password" name="password"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                        autocomplete="new-password" placeholder="請輸入新密碼" required>
                                </div>
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">
                                        <span class="must2">*</span>確認新密碼
                                    </label>
                                    <input type="password" name="password_confirmation"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                        autocomplete="new-password" placeholder="請再一次輸入新密碼" required>
                                </div>
                            </div>

                            <!-- 修改密碼按鈕 -->
                            <div class="flex justify-end">
                                <button type="submit"
                                    class="px-8 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-500 focus:ring-opacity-50 transition-all duration-200 transform hover:scale-105">
                                    🔄 修改密碼
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            @endif
        </div>
    </div>

    <script type="text/javascript">
        document.addEventListener("DOMContentLoaded", () => {
            jQuery(".btn-outline-myGray").eq(0).trigger("click");
        });
    </script>

@stop
