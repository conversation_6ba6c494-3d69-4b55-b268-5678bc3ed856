<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Schema;

/**
 * Laravel Command：自動產生 Model 關聯方法
 *
 * 使用方式：php artisan make:model-relations
 *
 * 功能：
 * 1. 分析資料庫表結構
 * 2. 自動識別外鍵關聯
 * 3. 將關聯方法直接寫入對應的 Model 檔案
 * 4. 在指定的關聯標記之間插入關聯方法
 */
class generateModelRelationsCommand extends Command {
    /**
     * 命令簽名和參數
     *
     * @var string
     */
    protected $signature = 'generate:model-relations {--force : 強制覆蓋現有檔案} {--tables= : 指定要分析的資料表，多個表用逗號分隔}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '將關聯方法直接寫入 Model 檔案';

    /**
     * 資料庫連接
     */
    protected $connection;

    /**
     * 資料表資訊
     */
    protected $tables = [];

    /**
     * 外鍵關聯資訊
     */
    protected $foreignKeys = [];

    /**
     * Model 檔案目錄
     */
    protected $modelsDir;

    /**
     * 建構子
     */
    public function __construct() {
        parent::__construct();
        $this->connection = DB::connection();
        $this->modelsDir = app_path('Models');
    }

    /**
     * 執行命令
     */
    public function handle() {
        $this->info('🚀 開始產生 Model 關聯方法...');

        try {
            // 分析資料庫結構
            $this->analyzeDatabase();

            // 為每個有關聯的 Model 更新檔案
            $updatedFiles = 0;
            foreach ($this->tables as $tableName => $tableInfo) {
                if (!empty($tableInfo['foreign_keys']) || !empty($tableInfo['references'])) {
                    if ($this->updateModelFile($tableInfo)) {
                        $updatedFiles++;
                    }
                }
            }

            $this->info("✅ 成功更新 {$updatedFiles} 個 Model 檔案");
            $this->info("📊 共分析 " . count($this->tables) . " 個資料表");
            $this->info("🔗 發現 " . count($this->foreignKeys) . " 個外鍵關聯");
        } catch (\Exception $e) {
            $this->error("❌ 產生失敗：" . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * 分析資料庫結構
     */
    protected function analyzeDatabase() {
        $this->info('🔍 分析資料庫結構中...');

        // 取得要分析的資料表
        $specifiedTables = $this->option('tables');
        if ($specifiedTables) {
            $tableNames = array_map('trim', explode(',', $specifiedTables));
        } else {
            // 取得所有資料表
            $tableNames = $this->getAllTables();
        }

        // 分析每個資料表
        foreach ($tableNames as $tableName) {
            $this->analyzeTable($tableName);
        }

        // 分析外鍵關聯
        $this->analyzeForeignKeys();
    }

    /**
     * 取得所有資料表名稱
     */
    protected function getAllTables(): array {
        // 使用 Laravel DB Schema Builder 取得所有資料表
        $tables = \DB::getSchemaBuilder()->getAllTables();

        return array_map(function ($table) {
            // 取得第一個屬性值作為資料表名稱（通常是 table_name 或類似屬性）
            $tableArray = (array) $table;
            $tableName = reset($tableArray);

            // 確保與 getAllTables() 回傳格式一致，不做額外的小寫轉換
            return $tableName;
        }, $tables);
    }

    /**
     * 分析單個資料表結構
     */
    protected function analyzeTable(string $tableName) {
        try {
            // 使用原始的資料表名稱，不做小寫轉換

            // 使用 Laravel Schema 動態取得欄位清單
            $columns = Schema::getColumnListing($tableName);

            // 取得詳細的欄位資訊
            $columnDetails = $this->getColumnDetails($tableName, $columns);

            $this->tables[$tableName] = [
                'name' => $tableName,
                'model_name' => $this->getModelName($tableName),
                'columns' => $columnDetails,
                'primary_key' => $this->getPrimaryKeyFromSchema($tableName),
                'foreign_keys' => [],
                'references' => []
            ];

            $this->line("  ✓ 分析資料表：{$tableName}");
        } catch (\Exception $e) {
            $this->warn("  ⚠ 跳過資料表 {$tableName}：" . $e->getMessage());
        }
    }

    /**
     * 取得欄位詳細資訊
     */
    protected function getColumnDetails(string $tableName, array $columns): array {
        $columnDetails = [];

        foreach ($columns as $columnName) {
            try {
                // 使用 Schema Builder 取得欄位類型
                $columnType = Schema::getColumnType($tableName, $columnName);

                $columnDetails[] = (object) [
                    'Field' => $columnName,
                    'Type' => $columnType,
                    'Key' => $this->isColumnPrimaryKey($tableName, $columnName) ? 'PRI' : '',
                ];
            } catch (\Exception $e) {
                // 如果無法取得欄位詳細資訊，使用基本資訊
                $columnDetails[] = (object) [
                    'Field' => $columnName,
                    'Type' => 'unknown',
                    'Key' => '',
                ];
            }
        }

        return $columnDetails;
    }

    /**
     * 從 Schema 取得主鍵欄位
     */
    protected function getPrimaryKeyFromSchema(string $tableName): ?string {
        try {
            // 嘗試使用不同方法取得主鍵
            $databaseDriver = config('database.default');
            $connection = config("database.connections.{$databaseDriver}");

            switch ($connection['driver']) {
                case 'mysql':
                    $result = DB::select("SHOW KEYS FROM `{$tableName}` WHERE Key_name = 'PRIMARY'");
                    return !empty($result) ? $result[0]->Column_name : null;

                case 'pgsql':
                    $result = DB::select("
                        SELECT a.attname
                        FROM pg_index i
                        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
                        WHERE i.indrelid = '{$tableName}'::regclass AND i.indisprimary
                    ");
                    return !empty($result) ? $result[0]->attname : null;

                case 'sqlite':
                    $result = DB::select("PRAGMA table_info({$tableName})");
                    foreach ($result as $column) {
                        if ($column->pk == 1) {
                            return $column->name;
                        }
                    }
                    return null;

                default:
                    // 預設假設 id 是主鍵
                    $columns = Schema::getColumnListing($tableName);
                    return in_array('id', $columns) ? 'id' : null;
            }
        } catch (\Exception $e) {
            // 如果無法取得主鍵，假設 id 是主鍵
            $columns = Schema::getColumnListing($tableName);
            return in_array('id', $columns) ? 'id' : null;
        }
    }

    /**
     * 檢查欄位是否為主鍵
     */
    protected function isColumnPrimaryKey(string $tableName, string $columnName): bool {
        try {
            $primaryKey = $this->getPrimaryKeyFromSchema($tableName);
            return $primaryKey === $columnName;
        } catch (\Exception $e) {
            return $columnName === 'id';
        }
    }

    /**
     * 取得主鍵欄位（相容舊版方法）
     */
    protected function getPrimaryKey(array $columns): ?string {
        foreach ($columns as $column) {
            if (is_object($column) && property_exists($column, 'Key') && $column->Key === 'PRI') {
                return $column->Field;
            }
        }
        return null;
    }

    /**
     * 分析外鍵關聯
     */
    protected function analyzeForeignKeys() {
        $this->info('🔗 分析外鍵關聯中...');

        foreach ($this->tables as $tableName => $tableInfo) {
            // 使用原始的資料表名稱，不做小寫轉換

            // 使用動態取得的欄位清單
            $columns = Schema::getColumnListing($tableName);

            foreach ($columns as $columnName) {
                // 檢查是否為外鍵（以 _id 結尾）
                if (Str::endsWith($columnName, '_id') && $columnName !== 'id') {
                    $referencedTable = Str::before($columnName, '_id');

                    // 檢查關聯的資料表是否存在於我們分析的表格中
                    if (isset($this->tables[$referencedTable])) {
                        $this->addForeignKeyRelation($tableName, $columnName, $referencedTable);
                    } else {
                        // 檢查資料庫中是否真的有這個表格
                        if ($this->tableExists($referencedTable)) {
                            // 如果表格存在但未在分析清單中，先加入基本資訊
                            $this->addMissingTable($referencedTable);
                            $this->addForeignKeyRelation($tableName, $columnName, $referencedTable);
                        }
                    }
                }
            }
        }
    }

    /**
     * 檢查資料表是否存在
     */
    protected function tableExists(string $tableName): bool {
        try {
            // 使用原始的資料表名稱，不做小寫轉換
            return Schema::hasTable($tableName);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 新增遺漏的資料表基本資訊
     */
    protected function addMissingTable(string $tableName) {
        // 使用原始的資料表名稱，不做小寫轉換

        if (!isset($this->tables[$tableName])) {
            try {
                $columns = Schema::getColumnListing($tableName);
                $columnDetails = $this->getColumnDetails($tableName, $columns);

                $this->tables[$tableName] = [
                    'name' => $tableName,
                    'model_name' => $this->getModelName($tableName),
                    'columns' => $columnDetails,
                    'primary_key' => $this->getPrimaryKeyFromSchema($tableName),
                    'foreign_keys' => [],
                    'references' => []
                ];

                $this->line("  📄 自動新增關聯表格：{$tableName}");
            } catch (\Exception $e) {
                $this->warn("  ⚠ 無法分析關聯表格 {$tableName}：" . $e->getMessage());
            }
        }
    }

    /**
     * 新增外鍵關聯
     */
    protected function addForeignKeyRelation(string $fromTable, string $foreignKey, string $toTable) {
        // HasMany 關聯（一對多）
        $this->tables[$toTable]['references'][] = [
            'type' => 'hasMany',
            'table' => $fromTable,
            'foreign_key' => $foreignKey,
            'method_name' => $fromTable
        ];

        // BelongsTo 關聯（多對一）
        $this->tables[$fromTable]['foreign_keys'][] = [
            'type' => 'belongsTo',
            'table' => $toTable,
            'foreign_key' => $foreignKey,
            'method_name' => $toTable
        ];

        $this->foreignKeys[] = "{$fromTable}.{$foreignKey} -> {$toTable}.id";
    }

    /**
     * 根據資料表名稱產生 Model 名稱
     */
    protected function getModelName(string $tableName): string {
        // 移除複數形式並轉換為駝峰命名
        $modelName = Str::studly(Str::singular($tableName));

        // 特殊情況處理
        $specialCases = [
            'member_product' => 'MemberProduct',
            'adminuser_member' => 'AdminuserMember',
            'product_items' => 'ProductItems',
        ];

        return $specialCases[$tableName] ?? $modelName;
    }

    /**
     * 更新 Model 檔案，將關聯方法插入到指定位置
     */
    protected function updateModelFile(array $tableInfo): bool {
        $modelName = $tableInfo['model_name'];
        $tableName = $tableInfo['name'];

        // 查找對應的 Model 檔案
        $modelFile = $this->findModelFile($modelName, $tableName);

        if (!$modelFile) {
            $this->warn("  ⚠ 找不到 Model 檔案：{$modelName}");
            return false;
        }

        // 讀取檔案內容
        $content = File::get($modelFile);

        // 檢查是否有關聯標記
        if (!$this->hasRelationsMarkers($content)) {
            //$this->warn("  ⚠ {$modelName} 檔案中未找到 /*Relations start*/ 和 /*Relations end*/ 標記");
            return false;
        }

        // 產生關聯方法內容
        $relationsContent = $this->generateRelationsContent($tableInfo);

        // 替換關聯區塊內容
        $updatedContent = $this->replaceRelationsContent($content, $relationsContent);

        // 檢查並處理刪除關聯標記
        if ($this->hasDelRelationsMarkers($updatedContent)) {
            // 產生刪除關聯內容
            $delRelationsContent = $this->generateDelRelationsContent($tableInfo);
            // 替換刪除關聯區塊內容
            $updatedContent = $this->replaceDelRelationsContent($updatedContent, $delRelationsContent);
        }

        // 寫入檔案
        File::put($modelFile, $updatedContent);

        $this->info("  ✅ 更新檔案：app/Models/" . basename($modelFile));
        return true;
    }

    /**
     * 查找對應的 Model 檔案
     */
    protected function findModelFile(string $modelName, string $tableName): ?string {
        // 優先使用資料表名稱查找檔案
        $possibleFiles = [
            $this->modelsDir . "/{$tableName}.php",
            $this->modelsDir . "/{$modelName}.php",
            $this->modelsDir . "/" . strtolower($tableName) . ".php",
            $this->modelsDir . "/" . strtolower($modelName) . ".php",
        ];

        foreach ($possibleFiles as $file) {
            if (File::exists($file)) {
                return $file;
            }
        }

        return null;
    }

    /**
     * 檢查檔案是否有關聯標記
     */
    protected function hasRelationsMarkers(string $content): bool {
        return strpos($content, '/*Relations start*/') !== false &&
            strpos($content, '/*Relations end*/') !== false;
    }

    /**
     * 檢查檔案是否有刪除關聯標記
     */
    protected function hasDelRelationsMarkers(string $content): bool {
        return strpos($content, '/*Del Relations start*/') !== false &&
            strpos($content, '/*Del Relations end*/') !== false;
    }

    /**
     * 產生關聯方法內容
     */
    protected function generateRelationsContent(array $tableInfo): string {
        $content = "\n";
        $datetime = now()->format('Y-m-d H:i:s');

        $content .= "    /**\n";
        $content .= "     * {$tableInfo['model_name']} Model 關聯方法\n";
        $content .= "     * 資料表：{$tableInfo['name']}\n";
        $content .= "     * 自動產生時間：{$datetime}\n";
        $content .= "     */\n\n";

        // BelongsTo 關聯
        foreach ($tableInfo['foreign_keys'] as $relation) {
            $content .= $this->generateBelongsToMethod($relation);
        }

        // HasMany 關聯
        foreach ($tableInfo['references'] as $relation) {
            $content .= $this->generateHasManyMethod($relation);
        }

        // 查詢範例
        $content .= $this->generateQueryExamples($tableInfo);

        return $content;
    }

    /**
     * 替換關聯區塊內容
     */
    protected function replaceRelationsContent(string $content, string $relationsContent): string {
        $pattern = '/\/\*Relations start\*\/(.*?)\/\*Relations end\*\//s';
        $replacement = '/*Relations start*/' . $relationsContent . '    /*Relations end*/';

        return preg_replace($pattern, $replacement, $content);
    }

    /**
     * 產生 BelongsTo 關聯方法
     */
    protected function generateBelongsToMethod(array $relation): string {
        $methodName = $relation['method_name'];
        $relatedTable = $relation['table'];
        $relatedModel = $this->getModelName($relatedTable);
        $foreignKey = $relation['foreign_key'];

        $content = "    /**\n";
        $content .= "     * {$relatedModel} 關聯（多對一）\n";
        $content .= "     * 關聯說明：每個記錄屬於一個 {$relatedModel}\n";
        $content .= "     */\n";
        $content .= "    public function {$methodName}() {\n";
        $content .= "        return \$this->belongsTo('App\\Models\\{$relatedTable}', '{$foreignKey}');\n";
        $content .= "    }\n\n";

        return $content;
    }

    /**
     * 產生 HasMany 關聯方法
     */
    protected function generateHasManyMethod(array $relation): string {
        $methodName = $relation['method_name'];
        $relatedTable = $relation['table'];
        $relatedModel = $this->getModelName($relatedTable);
        $foreignKey = $relation['foreign_key'];

        $content = "    /**\n";
        $content .= "     * 對 {$relatedModel} 關聯（一對多）\n";
        $content .= "     * 關聯說明：一個記錄可以有多個 {$relatedModel}\n";
        $content .= "     */\n";
        $content .= "    public function {$methodName}() {\n";
        $content .= "        return \$this->hasMany('App\\Models\\{$relatedTable}', '{$foreignKey}');\n";
        $content .= "    }\n\n";

        return $content;
    }

    /**
     * 產生查詢範例
     */
    protected function generateQueryExamples(array $tableInfo): string {
        $modelName = $tableInfo['model_name'];
        $tableName = $tableInfo['name'];

        $content = "    /**\n";
        $content .= "     * 查詢範例：\n";
        $content .= "     *\n";

        // BelongsTo 查詢範例
        if (!empty($tableInfo['foreign_keys'])) {
            $relation = $tableInfo['foreign_keys'][0];
            $methodName = $relation['method_name'];
            $relatedTable = $relation['table'];
            $relatedModel = $this->getModelName($relatedTable);

            $content .= "     * // 查詢 {$modelName} 及其關聯的 {$relatedModel}\n";
            $content .= "     * \${$modelName} = \\App\\Models\\{$tableName}::selectRaw('*')\n";
            $content .= "     *     ->with(['{$methodName}' => function (\$query) {\n";
            $content .= "     *         \$query->selectRaw('id,name');\n";
            $content .= "     *     }])\n";
            $content .= "     *     ->get();\n";
            $content .= "     *\n";
        }

        // HasMany 查詢範例
        if (!empty($tableInfo['references'])) {
            $relation = $tableInfo['references'][0];
            $methodName = $relation['method_name'];
            $relatedTable = $relation['table'];
            $relatedModel = $this->getModelName($relatedTable);

            $content .= "     * // 查詢 {$modelName} 及其關聯的 {$relatedModel}\n";
            $content .= "     * \${$modelName} = \\App\\Models\\{$tableName}::selectRaw('*')\n";
            $content .= "     *     ->with(['{$methodName}' => function (\$query) {\n";
            $content .= "     *         \$query->selectRaw('id,name');\n";
            $content .= "     *     }])\n";
            $content .= "     *     ->get();\n";
        }

        $content .= "     */\n\n";

        return $content;
    }

    /**
     * 產生刪除關聯內容
     */
    protected function generateDelRelationsContent(array $tableInfo): string {
        $content = "\n";
        $datetime = now()->format('Y-m-d H:i:s');

        $content .= "            /**\n";
        $content .= "             * {$tableInfo['model_name']} Model 刪除關聯資料\n";
        $content .= "             * 資料表：{$tableInfo['name']}\n";
        $content .= "             * 自動產生時間：{$datetime}\n";
        $content .= "             */\n\n";

        // 只處理 HasMany 關聯 (references)，因為這些是需要在刪除時一併刪除的子資料
        foreach ($tableInfo['references'] as $relation) {
            $relatedTable = $relation['table'];
            $foreignKey = $relation['foreign_key'];
            $relatedModel = $this->getModelName($relatedTable);

            $content .= "            // 刪除關聯的 {$relatedTable} 資料\n";
            $content .= "            \\DB::delete('delete from {$relatedTable} where {$foreignKey} = ?', array(\$model->id));\n\n";
        }

        return $content;
    }

    /**
     * 替換刪除關聯區塊內容
     */
    protected function replaceDelRelationsContent(string $content, string $delRelationsContent): string {
        $pattern = '/\/\*Del Relations start\*\/(.*?)\/\*Del Relations end\*\//s';
        $replacement = '/*Del Relations start*/' . $delRelationsContent . '            /*Del Relations end*/';

        return preg_replace($pattern, $replacement, $content);
    }
}
