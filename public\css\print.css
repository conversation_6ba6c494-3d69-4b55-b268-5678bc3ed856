        body {
            font-family: <PERSON><PERSON>, "Microsoft JhengHei", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: white;
        }

        .header {
            background-color: #FF8800;
            color: white;
            padding: 5px 10px;
            text-align: right;
            font-weight: bold;
        }

        .location {
            padding: 10px;
            background-color: #f9f9f9;
            border-bottom: 1px solid #eee;
        }

        .location i {
            color: #4CAF50;
            margin-right: 5px;
        }

        .property-summary {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .summary-item {
            flex: 1;
            text-align: center;
            border-right: 1px solid #eee;
        }

        .summary-item:last-child {
            border-right: none;
        }

        .summary-label {
            font-size: 14px;
            color: #666;
        }

        .summary-value {
            font-size: 22px;
            color: #FF8800;
            font-weight: bold;
            margin-top: 5px;
        }

        .summary-unit {
            font-size: 14px;
            color: #666;
        }

        .content-container {
            display: flex;
            flex-direction: row;
        }

        .image-container {
            width: 50%;
            display: flex;
            flex-direction: column;
        }

        .property-image {
            width: 100%;
            height: auto;
            position: relative;
            margin-bottom: 10px;
        }

        .property-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        .watermark {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(255, 255, 255, 0.7);
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
        }

        .date-stamp {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
        }

        .info-container {
            width: 50%;
            padding-left: 10px;
        }

        .section-title {
            background-color: #FF8800;
            color: white;
            padding: 8px 10px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .info-table tr {
            border-bottom: 1px solid #eee;
        }

        .info-table td {
            padding: 8px 10px;
        }

        .info-table td:first-child {
            width: 40%;
            color: #666;
        }

        .info-table td:last-child {
            color: #333;
            text-align: right;
            font-weight: bold;
        }

        .print-button {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            padding: 8px 15px;
            text-align: center;
            margin: 15px;
            cursor: pointer;
        }

        .footer {
            background-color: #f9f9f9;

            border-top: 1px solid #eee;
            clear: both;
        }

        .footer-logo {
            width: 140px;
            margin-bottom: 10px;
        }

        .footer-address {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .footer-contact {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }

        .contact-item {
            width: 50%;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .contact-item i {
            width: 20px;
            text-align: center;
            margin-right: 5px;
        }

        .footer-email {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .footer-hours {
            font-size: 14px;
            color: #666;
        }

        .hotline {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 15px;
        }

        .hotline-number {
            font-size: 14px;
            color: #FF8800;
            font-weight: bold;
        }

        .contact-button {
            background-color: #FF8800;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            display: flex;
            align-items: center;
        }

        .contact-button i {
            margin-right: 5px;
        }

        @media print {
            body {
                width: 100%;
                max-width: 100%;
                box-shadow: none;
            }

            .contact-button {
                display: none;
            }
        }