<?php

namespace App\Libraries;

/***
"功能名稱":"共用類別-XML函式",
"建立時間":"2022-01-18 13:18:52",
***/
trait PF_xml
{
    public static function xmlDoc($XmlFile)
    {
        try {
            $XmlFile = storage_path('app/'.$XmlFile);
            if (file_exists($XmlFile)) {
                $xml = simplexml_load_file($XmlFile, null, LIBXML_NOCDATA);

                return $xml;
            } else {
                throw new \Exception($XmlFile.'檔案不存在');
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public static function xmlSearch($XmlDoc, $Note, $ReNote, $Value, $isdisplay = 1)
    {
        try {
            if ('NULL' == gettype($XmlDoc)) {
                $XmlDoc = PF::xmlDoc('Setup.xml');
            }
            if (count($XmlDoc->xpath('//Setup')) > 0) {
                $Note = str_replace('參數設定檔', 'Setup', $Note);
                $Note = str_replace('傳回值', 'ReturnValue', $Note);
                $Note = str_replace('資料', 'Data', $Note);
                $ReNote = str_replace('資料', 'Data', $ReNote);
                $ReNote = str_replace('傳回值', 'ReturnValue', $ReNote);
            }

            if (false == isset($Value) || '' == strval($Value)) {
                return null;
            }
            $array = explode(',', $Value);
            $xPath = '';
            $cc = '';
            $ValueTotal = '';
            try {
                for ($i = 0; $i < count($array); ++$i) {
                    $xPath = $Note."[.='".$array[$i]."']/parent::*";

                    if ('boolean' == gettype($XmlDoc->xpath($xPath))) {
                        return $Value;
                    }

                    foreach ($XmlDoc->xpath($xPath) as $v) {
                        $ValueTotal .= $cc.__(strval($v->$ReNote));
                        $cc = ',';
                    }
                }
            } catch (\Exception $e) {
                throw new \CustomException($e->getMessage().' > '.$xPath);
            }
            //PF_print($ValueTotal);
            if ('' == $ValueTotal && 1 == $isdisplay) {
                $ValueTotal = $Value;
            }

            return $ValueTotal;
        } catch (Exception $e) {
            throw $e;
        }
    }
}
