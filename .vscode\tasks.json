{
    "version": "2.0.0",
    "options": {
        "cwd": "${workspaceRoot}/",
        "workspaceFolderBasename": "${workspaceFolderBasename}"
    },
    "tasks": [
        {
            "label": "開啟專案目錄",
            "isBuildCommand": true,
            "command": "Shell",
            "windows": {
                "command": "explorer.exe"
            },
            "args": [
                "${workspaceFolder}"
            ],
            "problemMatcher": []
        },
        {
            "label": "kill-npm-first",
            "command": "taskkill",
            "args": [
                "/F",
                "/IM",
                "cmd.exe"
            ],
            "problemMatcher": [],
            "group": {
                "kind": "build",
                "isDefault": true
            }
        },
        {
            "label": "clear",
            "type": "shell",
            "command": "cmd /c clear",
            "options": {
                "cwd": "${workspaceFolder}/command"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "ftp 正式環境 filewatch", // 任務名稱，顯示在 VS Code 任務列表中
            "type": "shell", // 任務類型，這裡使用 shell 來執行命令
            "command": "command/ftp-production.bat", // 要執行的命令，這裡是運行一個批處理檔案
            "args": [
                ""
            ], // 傳遞給命令的參數，這裡是空的
            "options": {
                "cwd": "${workspaceFolder}" // 工作目錄，設定為當前工作區資料夾
            },
            "presentation": {
                "reveal": "never", // 執行時不顯示終端機視窗
                "panel": "shared" // 使用共用的終端機面板顯示輸出
            },
            "runOptions": {
                "instanceLimit": 1, // 限制同時執行的任務實例數為 1，若重複執行則終止前一個
                //"runOn": "folderOpen", // 觸發條件，當打開工作區資料夾時自動執行
                "reevaluateOnRerun": true // 重跑任務時重新評估，確保新實例替換舊實例
            },
            "problemMatcher": [] // 問題匹配器，這裡設為空表示不使用預設匹配行為
        },
        {
            "label": "code live-browser reload",
            "type": "shell",
            "command": "command/codelive.bat",
            "args": [
                ""
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999,
                "runOn": "folderOpen"
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "1laravel Copy",
            "type": "shell",
            "command": "cmd /c 1copy",
            "options": {
                "cwd": "${workspaceFolder}/command"
            },
            "args": [],
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999,
                "runOn": "folderOpen"
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "ftp 正式環境 python 下載",
            "type": "shell",
            "command": "command/ftp-production-download.bat",
            "args": [
                ""
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "ftp 正式環境 python backup db 下載",
            "type": "shell",
            "command": "command/ftp-production-downloaddbbackup.bat",
            "args": [
                ""
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "ftp 測試環境 filewatch",
            "type": "shell",
            "command": "command/ftp-test.bat",
            "args": [
                ""
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "ftp 測試環境 python 下載",
            "type": "shell",
            "command": "command/ftp-test-download.bat",
            "args": [
                ""
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "ftp 測試環境 python backup db下載",
            "type": "shell",
            "command": "command/ftp-test-downloaddbbackup.bat",
            "args": [
                ""
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "dependsOrder": "sequence"
        },
        {
            "label": "vue npm run 正式+build+watch",
            "type": "shell",
            "command": "npm run watch",
            "options": {
                "cwd": "${workspaceFolder}/command"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "mcp browser-tools-server",
            "type": "shell",
            "command": "browser-tools-server",
            "options": {
                "cwd": "${workspaceFolder}/command"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "vue npm run build+preview",
            "type": "shell",
            "command": "npm run build;npm run preview",
            "options": {
                "cwd": "${workspaceFolder}/command"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "npm run dev",
            "type": "shell",
            "command": "command/vite-dev.bat",
            "options": {
                "cwd": ""
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
            "runOptions": {
                "instanceLimit": 999,
                "runOn": "folderOpen"
            },
        },
        {
            "label": "apache start",
            "type": "shell",
            "command": "command/apache.bat",
            "options": {
                "cwd": ""
            }
        },
        {
            "label": "artisan serve",
            "type": "shell",
            "command": "php artisan serve --host=allennb.com.tw --port=8000",
            "options": {
                "cwd": ""
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            },
        },
        {
            "label": "vite npm run build",
            "type": "shell",
            "command": "npm run build",
            "options": {
                "cwd": "${workspaceFolder}/command"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "nuxt generate",
            "type": "shell",
            "command": "npm run generate",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "資料表 建立",
            "type": "shell",
            "command": "php",
            "group": "none",
            "args": [
                "artisan",
                "make:migration",
                "create_${input:name}_table "
            ],
            "options": {
                "cwd": "${workspaceFolder}/"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "建立 phpunit Test檔案",
            "type": "shell",
            "command": "php",
            "group": "none",
            "args": [
                "artisan",
                "makephpunit:file",
                "--workspaceFolder=${workspaceFolder}",
                "--fileName=${file}"
            ],
            "options": {
                "cwd": "${workspaceFolder}/"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "建立livewire",
            "type": "shell",
            "command": "php",
            "group": "none",
            "args": [
                "artisan",
                "make:livewire",
                "${input:name}"
            ],
            "options": {
                "cwd": "${workspaceFolder}/"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "建立Controller",
            "type": "shell",
            "command": "php",
            "group": "none",
            "args": [
                "artisan",
                "page:create",
                "--folder=${input:folders}",
                "--name=${input:name}",
                "--repositoryname=N"
            ],
            "options": {
                "cwd": "${workspaceFolder}/"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "資料表 migrate refresh(單一資料表)",
            "type": "shell",
            "command": "php",
            "group": "none",
            "args": [
                "artisan",
                "migrate_file:refresh",
                "${input:name}"
            ],
            "options": {
                "cwd": "${workspaceFolder}/"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "git push",
            "type": "shell",
            "command": "cmd /c push",
            "group": "none",
            "options": {
                "cwd": "${workspaceFolder}/command/git"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "資料表 migrate(產生未產生的資料表)",
            "type": "shell",
            "command": "php",
            "group": "none",
            "args": [
                "artisan",
                "migrate"
            ],
            "options": {
                "cwd": "${workspaceFolder}/"
            },
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "synchronize model",
            "type": "shell",
            "command": "cmd /c command_synchronizemodel",
            "options": {
                "cwd": "${workspaceFolder}/command"
            },
            "problemMatcher": [],
            "presentation": {
                "reveal": "always",
                "panel": "shared"
            }
        },
        {
            "label": "nuxt:dev",
            "type": "npm",
            "script": "dev",
            "isBackground": true,
            "problemMatcher": [
                {
                    "base": "$tsc-watch",
                    "background": {
                        "activeOnStart": true,
                        "beginsPattern": "√ Nitro",
                        "endsPattern": "built in"
                    }
                }
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            }
        }
    ],
    "inputs": [
        {
            "id": "openSimpleBrowser",
            "type": "command",
            "command": "simpleBrowser.show",
            "args": [
                "https://allennb.com.tw/bauschtrial/public/a"
            ]
        },
        {
            "type": "pickString",
            "id": "tables",
            "description": "select table name?",
            "options": [
                "adminuser",
                "adminuserloginlog",
                "board",
                "city1",
                "city2",
                "dblog",
                "epost",
                "formquerylog",
                "kind",
                "kindhead",
                "member",
                "product",
            ],
            "default": "component"
        },
        {
            "type": "pickString",
            "id": "folders",
            "description": "select folder name?",
            "options": [
                "/",
                "admin/",
                "api/",
                "api/admin/",
                "api/member/",
                "api/membercenter/",
                "layouts/",
                "line/",
                "membercenter/",
            ],
            "default": "component"
        },
        {
            "type": "promptString",
            "id": "name",
            "description": "你要新增的名稱",
        },
        {
            "type": "promptString",
            "id": "repositoryname",
            "description": "你要新增的repository name",
        },
    ]
}