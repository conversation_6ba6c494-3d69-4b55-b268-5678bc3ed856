<?php

namespace App\Macros;

use PF;

/***
"功能名稱":"共用類別-Html 產生控制項XML",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
 ***/
class MyFormUIXml {
    public $i = 0;
    protected $arr;

    public function __construct($form, $arr) {
        $this->arr = $arr;
    }

    public function createHtml() {
        $html = '';
        $i = 0;
        try {
            if ('' == $this->arr['fieldtitle']) {
                $this->arr['fieldtitle'] = '資料';
            }

            if ('' == $this->arr['xmldoc']) {
                $html .= 'xmldoc is null';

                return $html;
            }

            if (\Str::contains($this->arr['requiredclass'], ['required[1,'])) {
                $this->arr['required'] = true;
            }


            $this->arr['type'] = strtolower($this->arr['type']);

            $this->arr['v-modelname'] = $this->arr['name'];

            // if ('' == $this->arr['selectfirsttxt']) {
            //     $this->arr['selectfirsttxt'] = __('請選擇').'..';
            // }
            if ((in_array($this->arr['type'], ['checkbox', 'select3', 'select2multiple']))) {
                $html .= "<input type='hidden' name='" . $this->arr['name'] . "[]'>";
            }
            if ('' == $this->arr['firsttxt']) {
                $this->arr['firsttxt'] = __('請選擇') . '..';
            }
            if (\Str::contains($this->arr['type'], ['selectautocomplete', 'select2'])) {
                $this->arr['type'] = 'select';
                $html .= "<script language=JavaScript> \n";
                $html .= "$(function() {\n";
                $html .= "$(\"select[name='" . $this->arr['name'] . "']\").select2();\n";
                $html .= "});\n";
                $html .= "</script>\n";
                $html .= '<link href="' . asset('assets/select2/css/select2.min.css') . '" rel="stylesheet" />' . PHP_EOL;
                $html .= '<script src="' . asset('assets/select2/js/select2.min.js') . '"></script>';
            }
            if ('select' == $this->arr['type']) {
                $html .= '<select name="' . $this->arr['name'] . '"';

                if ('' != $this->arr['v-model']) {
                    $html .= ' v-model="' . $this->arr['v-model'] . '" ';
                }
                if ('' == $this->arr['class']) {
                    $this->arr['class'] = "form-control";
                }


                if (true == $this->arr['required']) {
                    $html .= ' required ';
                }

                foreach ($this->arr as $_key => $_value) {
                    if (false == in_array($_key, ['required', 'v-modelname', 'name', 'firsttxt', 'xmldoc', 'node', 'type', 'linecount', 'sql', 'value', 'firsttext', 'v-model'])) {
                        $html .= ' ' . $_key . '="' . $_value . '"';
                    }
                }
                $html .= ' id="' . $this->arr['name'] . '"';

                // if ('' != $this->arr['requiredclass']) {
                //     if (\Str::contains($this->arr['requiredclass'], ['required[1,'])) {
                //         $html .= ' required ';
                //     }
                // }
                // if ('' != $this->arr['classname']) {
                //     if ('required[1,' == substr($this->arr['className'], 0, 11)) {
                //         $html .= ' required ';
                //     }
                // }

                $html .= ">\n";
                if ('' != $this->arr['firsttxt']) {
                    $html .= '<option value="">' . $this->arr['firsttxt'] . '</option>' . chr(13) . chr(10);
                }
            }

            $id = \Str::random(5);

            foreach ($this->arr['xmldoc']->xpath($this->arr['node']) as $key => $v) {
                if (in_array($this->arr['type'], ['checkbox', 'radio'])) {
                    $html .= '<div class="form-check form-check-inline">' . PHP_EOL;
                }


                $ischeckstatus = 0;
                $keystr = strval($v->傳回值);
                $v1 = get_object_vars($v);
                if ('' == $keystr) {
                    $keystr = $v1[$this->arr['fieldtitle']];
                }

                $datastr = $v1[$this->arr['fieldtitle']];
                //}
                $text = __(strval($datastr));

                $ischeckstatus = 0;
                if ('checkbox' == $this->arr['type'] && '' != $this->arr['value']) {
                    $values = explode(',', $this->arr['value']);
                    if (in_array($keystr, $values)) {
                        $ischeckstatus = 1;
                    }
                } else {
                    if (trim($this->arr['value']) == trim(strval($keystr))) {
                        $ischeckstatus = 1;
                    }
                }

                if ('select' == $this->arr['type']) {
                    $html .= "<option value=\"$keystr\"";
                    if (1 == $ischeckstatus) {
                        $html .= ' selected ';
                    }
                    $html .= '>';
                    $html .= $text . "</option>\n";
                } else {

                    $html .= '<input ';
                    if ($this->arr['name'] != "") {
                        $html .= 'name="' . $this->arr['name'];
                        if ('checkbox' == $this->arr['type']) {
                            $html .= '[]';
                        }
                        $html .= '" ';
                    }

                    $html .= ' id="' . $id . "_" . $key . '"';
                    $html .= ' type="' . $this->arr['type'] . '" value="' . htmlspecialchars($keystr) . '"';

                    if ('checkbox' != $this->arr['type']) {
                        if ('' != $this->arr['required'] && true == $this->arr['required']) {
                            $html .= ' required ';
                        }
                    } else {
                        if ('' != $this->arr['required'] && true == $this->arr['required']) {
                            $html .= ' requiredclass="[1,TEXT]" ';
                        }
                    }
                    foreach ($this->arr as $_key => $_value) {
                        if (false == in_array($_key, ['rows', 'required', 'fieldtitle', 'firsttxt', 'v-modelname', 'name',  'xmldoc', 'node', 'type', 'linecount', 'sql', 'value', 'firsttxt', 'v-model', 'class'])) {
                            $html .= ' ' . $_key . '="' . $_value . '"';
                        }
                    }
                    $html .= ' class="form-check-input" ';
                    if ('' != $this->arr['v-model']) {
                        $html .= " v-model='" . $this->arr['v-model'] . "' ";
                        if ('checkbox' == $this->arr['type']) {
                            $html .= " :true-value=\"'" . htmlspecialchars($keystr) . "'\" ";
                        }
                    }
                    if (1 == $ischeckstatus) {
                        $html .= ' checked ';
                    }

                    if (true == $this->arr['islg']) {
                        $text = __($text);
                    }

                    $html .= '>' . PHP_EOL;
                    $html .= '<label class="form-check-label" for="' . $id . "_" . $key . '">';
                    $html .= $text . '</label>' . PHP_EOL;
                }
                if (in_array($this->arr['type'], ['checkbox', 'radio'])) {
                    $html .= '</div>' . PHP_EOL;
                    if ('' != $this->arr['linecount']) {
                        if (0 == ($i + 1) % $this->arr['linecount']) {
                            $html .= '<br/>';
                        }
                        ++$i;
                    }
                }
            }



            $html .= "\n";
            if ('select' == $this->arr['type']) {
                $html .= '</select>';
            }


            if (\Str::contains($this->arr['type'], ['select3', 'select2'])) {
                $html .= "<div class=\"invalid-tooltip\">" . __('請選擇一筆') . "</div>" . PHP_EOL;
            }
        } catch (\Exception $e) {
            $html .= $e->getMessage();
        }

        return $html;
    }
}
