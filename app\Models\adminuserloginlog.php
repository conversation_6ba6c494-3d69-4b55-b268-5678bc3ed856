<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use PF;
//管理者後入LOG
class adminuserloginlog extends baseModel
{
    

	public $tabletitle = '管理者後入LOG';
    public $table = 'adminuserloginlog';
    public $primaryKey = 'admiuserloginid';
    
    //欄位必填
    public $rules = [
		'admiuserloginid' => 'required',
'account' => 'required',

    ];
    public $fieldInfo = [
'admiuserloginid'=>['title'=>'自動編號','type'=>'int(11)'],//
'account'=>['title'=>'帳號','type'=>'varchar(255)'],//
'clientip'=>['title'=>'登入IP','type'=>'varchar(50)'],//
'created_at'=>['title'=>'登入時間','type'=>'datetime'],//
'loginstatus'=>['title'=>'登入狀態','type'=>'varchar(50)'],//
'logouttime'=>['title'=>'登出時間','type'=>'datetime'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    public $timestamps = false;
    protected $fillable = ['account','clientip','created_at','loginstatus','logouttime']; //可充許傳入的欄位
    protected $guarded =[];
    protected $dates = ['created_at','logouttime'];    
  

  public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          

        });
         static::deleting(function ($model) {
            //  DB::table('adminuserloginlog')->select()
            // ->myWhere('admiuserloginid|N', $model->admiuserloginid, "admiuserloginid", 'Y')
            // ->delete();



          
        });
        static::deleted(function ($model) {
            
        });

    }	


}
