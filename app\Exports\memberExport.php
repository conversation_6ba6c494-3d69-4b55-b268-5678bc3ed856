<?php


namespace App\Exports;

use PF;
use Illuminate\Support\Collection;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithEvents;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;

/***
"功能名稱":"Excel匯出-會員",
"資料表":"member",
"建立時間":"2025-05-25 10:40:07 ",
 ***/
class memberExport extends DefaultValueBinder implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithCustomValueBinder {
    protected $data;
    protected $fields;
    protected $fieldnicknames;


    /**
     *TODO 定義要顯示的欄位
     */
    public function __construct($data, $fieldnicknames, $rows, $issample = null) {
        ini_set('memory_limit', '512M');
        set_time_limit(0);
        //$this->memberRepo = app(\App\Repositories\memberRepository::class);
        //$this->fieldnicknames = $this->memberRepo->getFieldTitleArray();
        $this->fieldnicknames = $fieldnicknames;
        $this->data = $data;

        $this->fields = [
            "mobile" => "", //行動電話
            "name" => "", //姓名
            "lineid" => "", //LINE ID
            "sex" => "", //性別
            "tel" => "", //市話
            "email" => "", //電子信箱
            "patterns" => "", //法拍屋種類
            "totalupsets" => "", //總底價
            "postals" => "", //區域
            "item_request" => "", //細項需求
            //"myproducts" => "", //我的收藏
            "isepaper" => "", //是否訂閱電子報
            "memo" => "", //備註
            //"lastlogin_ip" => "", //登入IP
            //"lastlogin_dt" => "", //最後登入日期
            //"logincount" => "", //登入次數
            "online" => "", //會員狀態
            // "adminuser_account"=>"",//編輯人員
            // "created_at"=>"",//建立時間
        ];
        if ($issample != null) {
            //unset($this->fields["id"]);
            //unset($this->fields["created_at"]);
            $rows = $rows->limit(1);
        }
        if (false == ($rows instanceof \Illuminate\Support\Collection)) {
            $rows = $rows->get();
        }

        //dd($rows);
        $this->data['rows'] = $rows;
    }


    /**
     *TODO 組資料(陣列轉集合)
     */
    public function collection() {

        $arrays = [];
        foreach ($this->data['rows'] as $rs) {
            $dict = null;
            foreach ($this->fields as $key => $value) {
                $value = $rs->{$key};
                switch ($key) {
                    case 'sex':
                        $value = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/性別/KIND/傳回值', '資料', $value);
                        break;
                    case 'patterns':
                        $value = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/法拍屋種類/KIND/傳回值', '資料', $value);
                        break;
                    case 'totalupsets':
                        $value = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/總底價/KIND/傳回值', '資料', $value);
                        break;
                        // case 'lastlogin_dt':
                        //     $value = PF::formatDate($value);
                        //     break;
                        // case 'online':
                        //     $value = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/會員狀態/KIND/傳回值', '資料', $value);
                        //     break;
                }
                //if (PF::left(trim($value), 1) == "=") {
                //    $value = "'" . $value;
                //}
                $dict[$key] = $value;
            }
            $arrays[] = $dict;
        }



        return new Collection($arrays);
    }
    /**
     *TODO 調整欄位寬度
     */
    public function registerEvents(): array {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $i = 0;
                $chrnumber = 65;
                $chr1 = '';
                foreach ($this->fields as $key => $value) {
                    $width = mb_strlen($this->fieldnicknames[$key], "UTF-8") * 5;
                    if ($width < 15) {
                        $width = 15;
                    }
                    $event->sheet->getDelegate()->getColumnDimension(strval(chr(65 + $i)))->setAutoSize(false)->setWidth($width);
                    //$event->sheet->getDelegate()->getColumnDimension(strval(chr(65 + $i)))->setAutoSize(true);
                    $i++;
                    if ($i == 25) {
                        $i = 0;
                        $chr1 = 'A';
                    }
                }
            }
        ];
    }
    public function bindValue(Cell $cell, $value) {
        $column = $cell->getColumn();

        //$fieldName = array_keys($this->fields)[ord($column) - 65];
        //if ('SORDNO' == $fieldName) {
        //      $cell->setValueExplicit($value, DataType::TYPE_STRING);
        //}
        if (is_numeric($value) && (strlen($value) >= 9)) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);
            //$cell->setValueExplicit($value, DataType::TYPE_NUMERIC);
            return true;
        } elseif (PF::isDate($value)) {
            if (strpos($value, ':') > 0) {
                $cell->getStyle()->getNumberFormat()->setFormatCode('yyyy-mm-dd hh:mm:ss');
            } else {
                $cell->getStyle()->getNumberFormat()->setFormatCode('yyyy-mm-dd');
            }
        }
        return parent::bindValue($cell, $value);
    }
    /**
     *TODO 比對標題
     */
    public function headings(): array {
        if (null == $this->fields) {
            throw new \CustomException("查無欄位");
        }
        $heads = [];
        foreach ($this->fields as $key => $value) {
            if ($value != "") {
                $heads[] = $value;
            } elseif (array_key_exists($key, $this->fieldnicknames)) {
                $value = $this->fieldnicknames[$key];
                $heads[] = $value;
            } else {

                $heads[] = $key;
                //$this->fields[$key] = $key;
            }
        }

        return $heads;
    }
}
