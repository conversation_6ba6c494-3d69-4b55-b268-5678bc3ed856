/*index/css*/

/*----------------------------------*\
  # hero-slide
\*----------------------------------*/
.hero.swiper-container {
  margin: 0 auto;
  width: 81vw; width: 72vw;

}
.hero .swiper-slide {
  width: 100%;
  height: 100%;
}
.hero .bgSet {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center  center;
  background-repeat: no-repeat;
  transition: 3s all;
  transform: scale(1.2);
}
.hero .swiper-slide-active .bgSet {
  display: flex;
  align-items: center;
  justify-content: center;
}
/*分頁器*/
.hero .swiper-pagination-bullet {
  background: var(--weak);
  border-radius: 50%;
  width: .7rem;
  height: .7rem;
  opacity: .7;
}
.hero .swiper-pagination-bullet-active {
  background-color: var(--main,#FF8000);
  opacity: 1;
}
.hero.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 .63rem;
}

@media (max-width: 1199px){
  .hero.swiper-container {
    width: 100vw;
    /* height: calc(100vw / (1920 / 600)); */
  }
  .hero .swiper-pagination-bullet {
    width: .36rem;
    height: .36rem;
  }
  .hero.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 .36rem;
  }
  .swiper-container-horizontal > .swiper-pagination-bullets,
  .swiper-pagination-custom, .swiper-pagination-fraction {
    transform: translate(0, 45%);
  }
}


/*----------------------------------*\
  # index-subtitle
\*----------------------------------*/
.index-subtitle {
  position: relative;
  font-size: 1.8rem;
  font-weight: bold;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 2.7rem 0;
}
.index-subtitle::before,
.index-subtitle::after {
  content: "";
  flex: 1;
  height: 1px;
  background: #ccc;
  margin: 0 1rem;
}


/*——————————————————————————————————*\
  # findings-grid
    in findings.css
\*——————————————————————————————————*/
.findings-grid.hotGoods {
  margin-bottom: 5.4rem;
}