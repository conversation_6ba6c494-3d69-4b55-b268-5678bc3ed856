@foreach ($data['rows'] as $rs)
<div class="row item col-xs-12 col-sm-6 col-md-4 col-lg-3 col-xl-3">
    <div class="mdc-card property-item grid-item column-4 full-width-page m-3">
        <div class="property-item">
            <div class="property-image etest">
                <a href="{{ url('/') }}/house/show/{{$rs->productid}}" itle="{{$rs->city1title}}{{$rs->city2title}}{{$rs->producttitle}}">
                    {{
                     Html::myUIImage([
                 'folder' => "images/product",
                 'filename' => $rs->img,
                 'alt' => $rs->title,
                //  'width' => 300,
                //  'height' => 300,
                'style' => 'width:390px',
                 'class' => 'img-fluid',
                     ])
             }}
                </a>
                <div class="prices">
                    {{$rs->totalupset}}萬
                </div>
            </div>
        </div>
        <div class="property-content-wrapper">
            <div class="property-content">
                <div class="content">
                    {{-- <h1 class="title">
                        <a href="{{ url('/') }}/house/show/{{$rs->productid}}" title="{{$rs->city1title}}{{$rs->city2title}}{{$rs->producttitle}}">{{$rs->producttitle}}</a>
                    </h1>
                    <p class="row address flex-nowrap">
                        <i class="material-icons text-muted">location_on</i>
                        <span>
                            {{$rs->city1title}}
                            {{$rs->city2title}}
                            {{$rs->address}}
                        </span>
                    </p> --}}

                    <div class="features">
                        @include('layouts.houseitemrs')
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endforeach
@if (count($data['rows'] )==0)
No Data
@endif

<!--物件列表 end-->