# 資料表規格

## adminuser ()
 - `id` : 自動編號
 - `account` : 帳號
 - `name` : 姓名
 - `password` : 密碼
 - `api_token` : APITOKEN
 - `remember_token` : 忘記身份TOKEN
 - `email` : EMAIL
 - `role` : 角色
    - 900 : 董事
    - 999 : 管理者
 - `lastlogin_dt` : 最後登入時間
 - `lastlogin_ip` : 最後登入IP
 - `linenotify_token` : linenotify token
 - `online` : 開啟
 - `created_at` : 建立時間
 - `updated_at` : 編輯時間

## adminuserloginlog ()
 - `admiuserloginid` : 自動編號
 - `account` : 帳號
 - `clientip` : 登入IP
 - `created_at` : 登入時間
 - `loginstatus` : 登入狀態
 - `logouttime` : 登出時間

## board (訊息公告)
 - `id` : 自動編號
 - `kind` : 種類
    - 1 : 法拍屋
    - 2 : 中古屋
    - 3 : 土地專區
 - `kindid` : 次種類編號
 - `title` : 標題
 - `memo` : 備註
 - `body` : 本文
 - `begindate` : 開始時間
 - `closedate` : 結束時間
 - `created_at` : 建立時間
 - `updated_at` : 異動時間
 - `boardsort` : 排序號碼
 - `location` : 
 - `field1` : 
 - `field2` : 
 - `field3` : 
 - `field4` : 
 - `field5` : 
 - `field6` : 
 - `field7` : 
 - `field8` : 
 - `field9` : 
 - `hits` : 點閱次數
 - `userid` : 操作者編號
 - `online` : 上下架
    - 1 : 上架
    - 0 : 下架
 - `useraccount` : 操作者帳號
 - `alg` : 語系

## city1 ()
 - `city1title` : 
 - `sortnum` : 排序號碼
 - `city1id` : 自動編號
 - `online` : 
 - `partid` : 地區編號
 - `entitle` : 英文

## city2 ()
 - `city1title` : 縣市
 - `city2title` : 鄉鎮
 - `Postal` : 郵遞區號
 - `city1id` : 縣市編號
 - `city2id` : 自動編號

## epost (上稿系統)
 - `epostid` : 
 - `eposttitle` : 標題
 - `epostbody` : 本文
 - `alg` : 語系
 - `created_at` : 建立時間
 - `updated_at` : 異動時間

## feedback (聯絡我們)
 - `id` : 自動編號
 - `company` : 公司名稱
 - `name` : 姓名
 - `email` : 電子信箱
 - `tel` : 電話
 - `ext` : 分機
 - `sex` : 性別
    先生
    女士
 - `mobile` : 行動電話
 - `title` : 標題
 - `memo` : 留言內容
 - `created_at` : 建立時間
 - `updated_at` : 異動時間
 - `retitle` : 回覆標題
 - `rebody` : 回覆訊息
 - `redate` : 回覆日期
 - `memberid` : 會員編號
 - `alg` : 語系

## formquerylog ()
 - `id` : 自動編號
 - `pagename` : 
 - `pathinfo` : 
 - `formbody` : FORM欄位值
 - `querybody` : 
 - `created_at` : 建立時間
 - `updated_at` : 異動時間

## jobs ()
 - `id` : 自動編號
 - `queue` : 
 - `payload` : 
 - `attempts` : 
 - `reserved_at` : 
 - `available_at` : 
 - `created_at` : 建立時間

## kind ()
 - `kindid` : 自動編號
 - `kindtitle` : 種類標題
 - `kind` : 種類
    - 1 : 法拍屋
    - 2 : 中古屋
    - 3 : 土地專區
 - `kindfield1` : 
 - `kindfield2` : 
 - `kindfield3` : 
 - `created_at` : 建立時間
 - `updated_at` : 異動時間
 - `kindsortnum` : 排序號碼
 - `alg` : 語系

## kindhead ()
 - `kindheadid` : 自動編號
 - `kindheadtitle` : 種類標題
 - `kindheadsort` : 排序號碼
 - `kindheadbody` : 本文
 - `kindmaincount` : 第二層數量
 - `productcount` : 產品數量
 - `alg` : 語系

## kindmain ()
 - `kindmainid` : 自動編號
 - `kindheadid` : 第一層種類編號
 - `kindmaintitle` : 種類標題
 - `kindmainsort` : 排序號碼
 - `kindmainbody` : 本文
 - `kindmainimg` : 圖
 - `kinditemcount` : 第三層數量
 - `productcount` : 產品數量
 - `alg` : 語系

## member (會員)
 - `id` : 自動編號
 - `name` : 姓名
 - `lineid` : LINE ID
 - `password` : 密碼
 - `sex` : 性別
    先生
    女士
 - `mobile` : 行動電話
 - `tel` : 市話
 - `email` : 電子信箱
 - `patterns` : 法拍屋種類
 - `totalupsets` : 總底價
 - `postals` : 區域
 - `myproducts` : 我的收藏
 - `item_request` : 細項需求
 - `isepaper` : 是否訂閱電子報
 - `memo` : 備註
 - `api_token` : api_token
 - `remember_token` : remember_token
 - `lastlogin_ip` : 登入IP
 - `lastlogin_dt` : 最後登入日期
 - `logincount` : 登入次數
 - `online` : 會員狀態
    - 1 : 正常
    - 2 : 停權
 - `adminuser_id` : 編輯人員關聯 => adminuser 表
 - `adminuser_account` : 編輯人員
 - `created_at` : 建立時間
 - `updated_at` : 編輯時間

## message (留言版)
 - `id` : 自動編號
 - `kind` : 
 - `name` : 
 - `email` : 
 - `sex` : 
 - `title` : 
 - `body` : 
 - `created_at` : 建立時間
 - `updated_at` : 編輯時間
 - `rebody` : 

## migrations ()
 - `id` : 自動編號
 - `migration` : 
 - `batch` : 

## myproduct (我的物件)
 - `id` : 自動編號
 - `member_id` : 會員關聯 => member 表
 - `product_id` : 物件關聯 => product 表
 - `created_at` : 建立日期
 - `updated_at` : 編輯日期

## product (物件)
 - `productid` : 自動編號
 - `productkind` : 物件種類
    法院法拍屋
    銀行銀拍屋
    行政執行署
    國有財產局
    台北市政府
    新北市政府
    台灣菸酒公會
    台北餐飲公會
    屋主自售
 - `producttitle` : 標題
 - `number` : 編號
 - `court` : 法院
 - `city1title` : 縣市
 - `city2title` : 鄉鎮
 - `auctions` : 拍賣情況
    - 1 : 待標
    - 2 : 流標
    - 1.1 : 應買
    - 4 : 停拍
    - 5 : 撤回
    - 6 : 得標
    - 7 : 承受
 - `debtor` : 債務人
 - `proofreadingday` : 校對日
 - `tenderdate` : 投標日
 - `beattime` : 拍次
    1拍
    2拍
    3拍
    應買
    特拍
 - `nocross_point` : 點交否
    不點交
    點交
    部份點交
 - `mainlawnestablishment` : 主建坪
 - `attachedtolawnestablishment` : 附屬坪
 - `additionalping` : 增建坪
 - `postulateping` : 公設坪
 - `carping` : 車位坪
 - `noticelawnestablishment` : 公告建坪
 - `stakeholdersfloor` : 持分地坪
 - `pingtotalnumberof` : 總坪數
 - `other_ping` : 其他坪數
 - `land_total_ping` : 土地總坪
 - `floorprice` : 坪單價
 - `aftermakingvalue_added` : 拍後增值
 - `currentvalues` : 公告現值
 - `totalupset` : 總底價
 - `margin` : 保證金
 - `address` : 地址
 - `buildname` : 大樓名稱
 - `pattern` : 法拍屋種類
 - `houseage` : 屋齡
    - 0~10 : 10內新屋
    - 10~20 : 10~20年
    - 21~30 : 20~30年
    - 31~30 : 30~40年
    - 41~50 : 40~50年
    - 51~99 : 50年以上
 - `postulatemorethan` : 公設比
 - `architecture` : 結構建材
 - `storey` : 樓高
 - `landaddress` : 土地地址
 - `landprice` : 土地公現
 - `buildings` : 建物
 - `bidsrecords` : 流標記錄
 - `hisrightis` : 他項權利
 - `transcriptinformation` : 謄本資料
 - `courttranscript` : 法院筆錄
 - `landvalue1` : 土地增值金額1
 - `landvalue2` : 土地增值金額2
 - `createdate` : 建立時間
 - `management` : 管理方式
 - `fees` : 費用
 - `parkingmode` : 停車方式
 - `transportfunction` : 交通機能
 - `schooldistrict` : 學區
 - `hits` : 點率閱
 - `location` : 放置位置
    - deal : 賀成交
    - hot : HOT
    - push1 : 推
    - push2 : 強推
    - new : 新增物件
    - storefront : 黃金店面
    - allowed : 整棟透天
    - factories : 辦公廠房 
    - TaipeiMRT : 台北捷運
    - AirportExpressLine : 機場快線
    - TaichungMRT : 台中捷運
    - KaohsiungMRT : 高雄捷運
    - new : 新品上市
    - home1 : 首頁下方大圖
    - home2 : 首頁下方小圖
 - `online` : 上下架
    - 1 : 上架
    - 0 : 下架
 - `sealedhuman` : 查封人
 - `thenumberofbidders` : 投標人數
 - `bidwere` : 得標人
 - `thebidprice` : 得標價格
 - `increasetheamountof` : 加價金額
 - `facingthelaneis` : 面臨路寬
 - `memo` : 備註
 - `mrtland` : 捷運路線
 - `mrtstation` : 捷運站名
 - `locationadmin` : 放置位置2
 - `point` : 點數
 - `auctionssortnum` : 排序號碼
 - `lat` : 經度
 - `lng` : 緯度
 - `url` : 網址
 - `img` : 圖案
 - `pdf` : pdf
 - `user_code` : 用戶代碼
 - `branch_code` : 分店代碼
 - `commission_date_start` : 委託日期(起)
 - `commission_date_end` : 委託日期(迄)
 - `floor_start` : 樓層(起)
 - `floor_end` : 樓層(迄)
 - `room_count` : 格局(房)
 - `living_room_count` : 格局(廳)
 - `hall_count` : 格局(室)
 - `bathroom_count` : 格局(衛)
 - `balcony_count` : 格局(陽台)
 - `virtual_tour_video` : 線上賞屋影片
 - `created_at` : 建立時間
 - `updated_at` : 編輯時間

## viewcount ()
 - `kind` : 
 - `hits` : 點閱次數
 - `created_at` : 建立時間
 - `updated_at` : 異動時間
 - `viewcountdate` : 日期
