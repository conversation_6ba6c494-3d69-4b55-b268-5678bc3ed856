{"var_dump": {"prefix": "test pp print", "scope": "php", "body": ["var_dump(${1:${CLIPBOARD}});"]}, "assertTrue": {"prefix": "test check assertTrue", "scope": "php", "body": ["\\$this->assertTrue(true);"]}, "test ok": {"prefix": "test assertTrue(true)", "scope": "php", "body": ["\\$this->assertTrue(true);"]}, "assertFalse": {"prefix": "test check assertFalse", "scope": "php", "body": ["\\$this->assertFalse(false);"]}, "assertFileExists": {"prefix": "test check assertFileExists 檔案是否存在", "scope": "php,js,javascript", "body": ["\\$this->assertFileExists(", "            storage_path('app/${1:xx}/${2:xx.pdf}')       ", "        );"]}, "assertStringContainsString": {"prefix": "test check assertStringContainsString 存在字串模糊", "scope": "php", "body": ["\\$this->assertStringContainsString('${1:account}',\\$response->getContent());"]}, "assertSee": {"prefix": "test check assertSee response是否存在", "scope": "php", "body": ["\\$response->assertSee('新增成功');"]}, "assertDontSee": {"prefix": "test check assertDontSee response是否不存在", "scope": "php", "body": ["\\$response->assertDontSee('新增成功');"]}, "assertStringNotContainsString": {"prefix": "test check assertStringNotContainsString 不存在字串模糊", "scope": "php", "body": ["\\$this->assertStringNotContainsString('${1:account}',\\$response->getContent());"]}, "assertEquals": {"prefix": "test check assertEquals 字串相等", "scope": "php", "body": ["\\$this->assertEquals(\"${1:account}\", \"${2:account}\");"]}, "test json assertJsonCount": {"prefix": "test check json assertJsonCount JSON筆數", "scope": "php", "body": ["\\$response->assertJsonCount(\\$this->product->count(), '0.*');"]}, "test data assertJsonCount": {"prefix": "test check json data assertJsonCount JSON筆數", "scope": "php", "body": ["\\$response->assertJsonCount(1, 'data');"]}, "test data assertGreaterThan": {"prefix": "test check json data 大於一筆", "scope": "php", "body": ["\\$jsonArray = \\$response->json();", "\\$this->assertGreaterThan(1, count(\\$jsonArray));"]}, "test fail": {"prefix": "test fail 直接顯示自定義的錯誤訊息", "scope": "php", "body": ["\\$this->fail('${1:內容為空}');"]}, "test get url": {"prefix": "test http get url", "scope": "php", "body": ["\\$response = \\$this->withHeaders(\\$datas['header'])->get($datas['url'], []);"]}, "test html": {"prefix": "test http post html", "scope": "php", "body": ["\\$datas = [", "    'header' => array(", "      //'CONTENT_TYPE' => 'multipart/form-data'", "      'CONTENT_TYPE' => 'text/html; charset=UTF-8',", "    ),", "    'url' => '/${1:account}/store',", "    'raw' => null,", "    'post' => array(", "      'number' => 'db5o2HEX21',", "      'name' => 'allen lin',           ", "    ),", "         ];", "", "        \\$response =\\$this->withHeaders(\\$datas['header'])->post(\\$datas['url'], \\$datas['post']);", "\\$this->checkHtml(\\$response);", "        \\$response->assertSee('新增成功');", "\\$response->assertStatus(200)->assertJson(['resultcode' => 0]);", "        "]}, "test post file": {"prefix": "test http post upload file", "scope": "php", "body": ["", "        \\$datas = [", "            'header' => array(", "                'CONTENT_TYPE' => 'multipart/form-data'", "                //'CONTENT_TYPE' => 'text/html; charset=UTF-8',", "            ),", "            'url' => '/admin/${1:member}/store',", "            'raw' => null,", "            'post' => array(", "                'file1' => \\Illuminate\\Http\\UploadedFile::fake()->image('test.png'),", "            ),", "        ];", "", "        \\$response = \\$this->withHeaders(\\$datas['header'])->post(\\$datas['url'], \\$datas['post']);", "        \\$this->checkHtml(\\$response);", "", "\\$rs = \\App\\Models\\\\${1:member}::all()->last();", "\\$this->checkfile(public_path('images/${1:member}/' . \\$rs->files));"]}, "test upload file": {"prefix": "test check file", "scope": "php", "body": ["\\$rs = \\App\\Models\\\\${1:member}::all()->last();", "\\$this->checkfile(public_path('images/${1:member}/' . \\$rs->files));"]}, "test post file excel": {"prefix": "test http post upload file excel", "scope": "php", "body": ["\\$this->${1:member}s = \\App\\Models\\\\${1:member}::factory(3)->create([", "            // 'member_id' => \\$this->member_id->id,", "        ]);", "", "        \\$exports = new \\App\\Exports\\\\${1:member}Export(null, \\App\\Models\\\\${1:member}::getFieldTitleArray(), \\$this->ecdetails);", "        \\Excel::store(\\$exports, public_path('images/test_export.xlsx'));", "        \\$datas = [", "            'header' => array(", "                'CONTENT_TYPE' => 'multipart/form-data'", "                //'CONTENT_TYPE' => 'text/html; charset=UTF-8',", "            ),", "            'url' => '/admin/${1:member}/excelimportstore',", "            'raw' => null,", "            'post' => array(", "                'file1' => new \\Symfony\\Component\\HttpFoundation\\File\\UploadedFile(public_path('images/test_export.xlsx'), 'test_export.xlsx', 'application/x-www-form-urlencoded', null, true)", "            ),", "        ];", "", "        \\$response = \\$this->withHeaders(\\$datas['header'])->post(\\$datas['url'], \\$datas['post']);", "        \\$this->checkHtml(\\$response);", "        foreach (\\$this->${1:member}s as \\$k1 => \\$rs) {", "", "            \\$this->assertDatabaseHas('${1:member}', [", "                'EORDID'       => \\$rs->EORDID,", "                'EORDNO' => \\$rs->EORDNO,", "            ]);", "        }"]}, "test html add function": {"prefix": "test http post html + add function", "scope": "php", "body": ["/**", "     * 測試資料列表功能.", "     *", "     * @return void", "     */", "    public function test_index/${1:account}_列表() {", "", "", "        \\$datas = [", "", "            'header' =>", "            array(", "", "                'CONTENT_TYPE' => 'text/html; charset=UTF-8',", "            ),", "            'url' => '/${2:admin}',", "            'post' =>", "            array(", "                'page' => 1,", "                'pagesize' => 10,", "                'searchname' =>  'project_id',", "                'search' => \\$this->sign->project_id,", "            ),", "        ];", "", "        \\$response = \\$this->withHeaders(\\$datas['header'])->post(\\$datas['url'], \\$datas['post']);", "        // echo \\$response->getStatusCode();", "        //echo \"response\" . \\$response->getContent();", "", "\\$this->checkHtml(\\$response);", "", "        \\$this->assertStringContainsString(\\$datas['post']['search'], \\$response->getContent());", "    }"]}, "check  assertJsonStructure": {"prefix": "test check json assertJsonStructure 欄位是否正確", "scope": "php", "body": ["\\$response->assertStatus(200)->assertJsonStructure([", "                  'resultcode',", "                  'resultmessage',", "                  'data' => [", "                        'data' => [", "                              '*' => [ // '*' 表示 datas 陣列中的每個項目都應該包含以下結構", "                                    'id',", "                              ],", "                        ],", "", "                  ],", "            ]);"]}, "assertDatabaseHas": {"prefix": "test check db 存在assertDatabaseHas", "scope": "php", "body": ["\\$this->assertDatabaseHas('${1:member}', [", "            'member_id'       => \\$this->${1:member}->id,", "            'remail' => \\$datas['post']['remail'],", "            'remail' => \\$datas['raw']['remail'],", "]);"]}, "assertDatabaseHas like": {"prefix": "test check db 存在assertDatabaseHas like", "scope": "php", "body": ["\\$this->assertTrue(", "\\DB::table('${1:member}')", "->where('${2:body}', 'LIKE', '%${3:name}%')", "->exists()", ");"]}, "assertDatabaseMissing": {"prefix": "test check db 不存在assertDatabaseMissing", "scope": "php", "body": ["\\$this->assertDatabaseMissing('${1:member}', [", "  'id'       => \\$this->${1:member}->id,", "]);"]}, "assertJson": {"prefix": "test check assertJson 格式", "scope": "php", "body": ["\\$response->assertStatus(200)->assertJson(['resultcode' => 0]);"]}, "Hash": {"prefix": "test check Hash 檢查密碼", "scope": "php", "body": ["\\$rs = \\App\\Models\\member::findOrFail(\\$this->member->id);", "\\$this->assertTrue(\\Hash::check('Aa@123456', \\$rs->password));"]}, "check test json": {"prefix": "test check json 多筆內容", "scope": "php", "body": ["\\$response", "->assertStatus(200)", "->assertJsonPath('data.data.0.id', \\$this->${1:member}->id);"]}, "check test json1": {"prefix": "test check json 單筆內容", "scope": "php", "body": ["\\$response->assertStatus(200)", "->assertJsonPath('data.clesson_id', \\$this->clesson->id)", "->assertJsonPath('data.clesson.chapter.0.id', \\$this->chapter->id)", "->assertJsonPath('data.clesson.chapter.0.video.0.id', \\$this->video[0]->id);"]}, "check test json one": {"prefix": "test check json 單筆", "scope": "php", "body": ["\\$response", "->assertStatus(200)", "->assertJsonPath('data.id', \\$this->${1:member}->id);"]}, "test get": {"prefix": "test http get", "scope": "php", "body": ["\\$response = \\$this->get('/');", "\\$response->assertStatus(200);"]}, "test assertRedirect": {"prefix": "test check url assertRedirect", "scope": "php", "body": ["\\$response->assertRedirectContains('/admin/main');", "\\$response->assertStatus(302);"]}, "test assertStatus": {"prefix": "test check assertStatus", "scope": "php", "body": ["\\$response->assertStatus(200);", "\\$response->assertStatus(302);"]}, "test http json": {"prefix": "test http json", "scope": "php", "body": ["\\$datas = [", "'header' =>", "array(", "'HTTP_Authorization' => 'Bearer ' . \\$this->member->api_token,", "   'CONTENT_TYPE' => 'application/json',", "),", "'url' => 'api/membercenter/changepassword/store',", "'raw' =>", "array(", "   'password' =>  'xxx', ", "),", "'post' => NULL,", "];", "\\$response = \\$this->withHeaders(\\$datas['header'])", "      ->json('POST', \\$datas['url'], \\$datas['raw']);", "", "    // echo \\$response->getStatusCode();", "    echo \\$response->getContent();", "", "\\$this->checkJson(\\$response);", "\\$response", "->assertStatus(200)", "->assertJsonPath('data.data.0.title', \\$this->${1:member}->title);", "\\$response", "->assertStatus(200)", "->assertJsonPath('data.id', \\$this->${1:member}->id);"]}, "test mail class": {"prefix": "test check mail class", "scope": "php", "body": ["\\Mail::fake();", "", "", "", "", "\\Mail::assertQueued(\\App\\Mails\\\\${1:xx}Mail::class);", "\\Mail::assertSent(\\App\\Mails\\\\${1:xx}Mail::class);"]}, "test date": {"prefix": "test date change", "scope": "php", "body": ["\\Carbon::setTestNow(\\Carbon: :parse('2024/08/28'));"]}, "test not null": {"prefix": "test not null", "scope": "php", "body": ["\\$rs = \\App\\Models\\shopline: :findOrFail(\\$id);", "\\$this->assertNotNull(\\$rs->${1:xx});"]}, "test null": {"prefix": "test null", "scope": "php", "body": ["\\$rs = \\App\\Models\\shopline: :findOrFail(\\$id);", "\\$this->assertNull(\\$rs->${1:xx});"]}}