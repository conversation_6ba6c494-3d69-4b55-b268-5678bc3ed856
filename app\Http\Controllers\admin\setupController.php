<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;
use PF;
use PT;

class setupController extends adminController
{
    private $fieldnicknames;
    private $filename;
    private $data;

    /**
     * 建構值
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();

        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        //權限管理
        // \Auth::guard('admin')->user()->authorize([
        //     'xmldoc'=>$this->data['xmldoc'],
        //     //'role'=>'999',//角色(如果有傳xmldoc則不用傳role)
        //     'controller'=>'setup'//功能代碼
        // ]);

        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'setup');
        $this->filename = base_path('config/config.php');

        
        if ($this->data['alg']=="") {
            $this->data['alg']="zh";
        }
        
        if ('' != config('config.'.$this->data['alg'].'.name')) {
            $this->filename = base_path('config/config.'.$this->data['alg'].'.php');            
        } 
        

        $this->fieldnicknames['title'] = '網站標題';
        $this->fieldnicknames['name'] = '公司名稱';
        $this->fieldnicknames['email'] = '公司信箱,寄件者';
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        \Artisan::call('config:clear');
        \Artisan::call('config:cache');

        return view('admin/setup.edit', [
                'data' => $this->data,
            ]);
    }

    /**
     * 資料建立顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
    }

    /**
     * 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        try {
            $ebody = \File::get($this->filename);
        } catch (Illuminate\Contracts\Filesystem\FileNotFoundException $exception) {
            die("The file doesn't exist");
        }
        $arrs = null;
        foreach (explode(PHP_EOL, $ebody) as $key => $item) {
            $items1 = explode(' //', $item);
            if (count($items1) > 1) {
                $items2 = explode('\'', $items1[0]);
                if (count($items2) > 0) {
                    $arrs[$items2[1]]['key'] = $items2[1];
                    $arrs[$items2[1]]['title'] = $items1[1];
                    $arrs[$items2[1]]['value'] = $items2[3];
                }
            }
        }
        //PF::printr($arrs);
        $this->data['arrs'] = $arrs;

        return view('admin/setup.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validators = null;
        $validators['title'] = ['required']; /*訊息種類-	*/
        $validators['name'] = ['required']; /*訊息種類-	*/
        $validators['email'] = ['required']; /*標題-	*/

        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
//         <?php

        // return [
//     'name' => '淞澤國際整合顧問有限公司',   //公司名稱

        try {
            $ebody = \File::get($this->filename);
        } catch (Illuminate\Contracts\Filesystem\FileNotFoundException $exception) {
            die("The file doesn't exist");
        }
        $body = '<?php'.PHP_EOL;
        $body .= PHP_EOL;
        $body .= 'return ['.PHP_EOL;

        foreach (explode(PHP_EOL, $ebody) as $key => $item) {
            $items1 = explode(' //', $item);
            if (count($items1) > 1) {
                $items2 = explode('\'', $items1[0]);
                if (count($items2) > 0) {
                    $value = $request->input($items2[1]);
                    $value = str_replace("'", '', $value);
                    $value = str_replace('"', '', $value);
                    $body .= "   '".$items2[1]."' => '".$value."',  //".$items1[1].PHP_EOL;
                }
            }
        }
        $body .= '];'.PHP_EOL;

        try {
            \File::put($this->filename, $body);
        } catch (Illuminate\Contracts\Filesystem\FileNotFoundException $exception) {
            throw new \CustomException("The file doesn't exist");
        }

        \Artisan::call('config:clear');
        \Artisan::call('config:cache');
        //\Artisan::call('view:clear');

        return back()->with('js', "alert('更新成功')");

        return back()->with('msg', 'update ok');
        PF::printr(1);
    }
}
