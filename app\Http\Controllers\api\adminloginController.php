<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use PF;

//use Illuminate\Support\Facades\DB;

class adminloginController extends Controller
{
    private $data;
    private $db;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();

        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->db = new \App\Models\adminuser();
        $this->fieldnicknames =$this->db::getFieldTitleArray();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';
        
        try {
            $validators = null;

            $validators['account'] = 'required';
            $validators['password'] = 'required';
//PF::printr($request->all());
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->fieldnicknames);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
            $loginstatus = '';

            //$this->middleware('guest:vendor', ['except' => 'logout']);

            if (\Auth::guard('admin')->attempt(['account' => $request->input('account'), 'password' => $request->input('password'), 'online' => 1])) {
                //\DB::update('UPDATE place  SET  signcount=(select count(*) from sign where place.placeid=sign.placeid ) ');

                $inputs['lastlogin_dt'] = date('Y-m-d H:i:s');
                $inputs['lastlogin_ip'] = \Request::ip();
                $inputs['api_token'] =  hash('sha256', \Str::random(80));
                
                \App\Models\adminuser::where('account', '=', $request->input('account'))->update($inputs);
                $jsondata['resultmessage'] = __('登入成功');
                $jsondata['api_token'] = $inputs['api_token'];
            //return redirect()->intended('/admin/main');
            } else {
                $loginstatus = __('失敗');
                throw new \Exception(__('登入失敗').' (IP:'.request()->ip().')');
            }
        } catch (\Exception $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        } finally {
            $inputs = null;
            $inputs['account'] = $request->input('account');
            $inputs['clientip'] = request()->ip();
            $inputs['logintime'] = date('Y-m-d H:i:s');
            $inputs['loginstatus'] = $loginstatus;

            \App\Models\adminuserloginlog::insert($inputs);
        }
        //json = json_encode(body,JSON_UNESCAPED_UNICODE);
        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function logout(Request $request)
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';
        
        try {
            
        } catch (\Exception $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        } finally {
            $inputs = null;
            $inputs['account'] = $request->input('account');
            $inputs['clientip'] = request()->ip();
            $inputs['logintime'] = date('Y-m-d H:i:s');
            $inputs['loginstatus'] = $loginstatus;

            \App\Models\adminuserloginlog::insert($inputs);
        }
        //json = json_encode(body,JSON_UNESCAPED_UNICODE);
        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
