function UpFile(FieldName) {
	PF_WindowOpen('upload.php?tmpobj=' + FieldName, 400, 300);
}

//產生資料庫語法
function SearchoForm_onsubmit(form) {


	if (PF_FormMultiAll(form) == false) {
		return false
	};
	var searchname = '';
	var searchname_length = 0;
	if (typeof (form.searchname) != "undefined") {
		searchname = form.searchname.value;
		searchname_length = form.searchname.length
	}

	if (PF_IsNull(searchname)) {
		searchname = (form.searchname.value).toUpperCase();
		if (searchname.slice(-4) == "|INT" && searchname.slice(-6) == "|ININT" && searchname.indexOf("+") == -1) {
			if (PF_IsNum(form.Search.value) == false) {
				alert('搜尋字串請勿輸入非數字');
				form.Search.focus();
				return false;
			}
		}

		if (typeof (form.searchenddate) != "undefined") {
			if (PF_IsNull(form.searchenddate.value) && PF_IsNull(form.searchenddate.value)) {
				if (form.searchstartdate.value > form.searchenddate.value) {
					alert('起始日不得大於終止日');
					form.searchenddate.focus();
					return false;
				}
			}
			PF_FieldDisabled(form)
		}
	} else {

		var searchnameT = '';
		var cc = '';


		for (var i = 0; i < searchname_length; i++) {
			if (PF_IsNull(form.searchname[i].value)) {

				searchnameT = searchnameT + cc + form.searchname[i].value;
				cc = "^";
			}
			form.searchname[0].value = searchnameT;
		}

	}

	return true;
}

function XMLSearchSelect(Field) {
	var XMLTitle = '';
	var XMLField = '';
	var cc = '';
	for (var i = 0; i < Field.length; i++) {
		if (Field.options[i].value != '') {
			XMLTitle = XMLTitle + cc + Field.options[i].text;
			XMLField = XMLField + cc + Field.options[i].value;
			cc = ',';
		}
	}


	var obj = new Object();
	obj.XMLTitle = XMLTitle;
	obj.XMLField = XMLField;
	var myObject = new Object();
	myObject = OpenDialog(true, 'XMLSearch.' + extFile, '610', '400', '', '', obj)
	try {

		SearchoForm.Search.value = myObject.FieldValue;
		for (var i = 1; i < SearchoForm.searchname.options.length; i++) {
			if (SearchoForm.searchname.options[i].value == myObject.Field) {
				SearchoForm.searchname.selectedIndex = i;
				break;
			}

		}
		SearchoForm.searchname.options[0].value = myObject.Field;
		SearchoForm.submit();

	} catch (e) {}

}

function XMLSearchAdvise(form) {
	var myObject = new Object();
	var SFieldVale = '';
	var SFieldText = '';
	var DFieldVale = '';
	var DFieldText = '';
	var cc = '';
	var dd = '';
	var i = 1;
	var s = '0'
	var SearchURL = '';
	for (i = 1; i < form.searchname.options.length; i++) {
		SFieldVale = SFieldVale + cc + form.searchname.options[i].value;
		SFieldText = SFieldText + cc + form.searchname.options[i].text;
		cc = ",";
	}

	if (typeof (form.Search_DateName) != "undefined") {


		for (i = 0; i < form.Search_DateName.options.length; i++) {
			DFieldVale = DFieldVale + dd + form.Search_DateName.options[i].value;
			DFieldText = DFieldText + dd + form.Search_DateName.options[i].text;
			dd = ",";
		}
	}

	SearchURL = 'XMLSearchAdvise.' + extFile + '?SFieldVale=' + SFieldVale + '&SFieldText=' + PF_escape(SFieldText) + '&DFieldVale=' + DFieldVale + '&DFieldText=' + PF_escape(DFieldText);
	hWinSize = 'scrollbars=yes,width=410,height=200,status=no,toolbar=NO,menubar=NO,location=NO,resizable=yes,';
	hWin = window.open(SearchURL, 'SearchURL', hWinSize);

	//lefts = screen.width / 2 - 250; // 所開視窗保持水平置中
	//tops = screen.height / 2 -390; // 所開視窗保持垂直置中
	//hWin.moveTo(lefts,tops); // 調整視窗位置


}

jQuery(document).ready(function () {
	if (jQuery("#searchdiv").length > 0) {
		if (typeof (SearchoForm) == 'undefined') {
			
			return false;
		}
		
		if (typeof (SearchoForm.search) == 'undefined') {
			return false;
		}

		if (SearchoForm.search.value != '') {
			PF_ChgSearch();
		}
		SearchoForm.searchname.onchange = function () {
			PF_ChgSearch();
			SearchoForm.search.value = '';
		}
	}
});

function PF_ChgSearch() {
	

	if (typeof (SearchoForm.searchname) != 'undefined') {
		requestURL =FC_WebFolder +'admin/api/searchchange';
		
		pars = 'xmltitle=' + SearchoForm.searchname.options[SearchoForm.searchname.selectedIndex].text + '&search=' + SearchoForm.search.value;
		jQuery.ajax({
			type: "post",
			url: requestURL,			
			data: pars,
			error: function (resp) {
				console.log('Error: url : ' + requestURL + '?' + pars + '\n' + resp.responseText);
			},
			success: function (resp) {
				///console.log(resp);
				$("#searchdiv").html(resp);
			}
		})
	}
}

function CreateRow(classname) {
	var table = $("." + classname);
	var row = $(table).find('tbody tr:eq(0)').html();
	$(table).find('tbody:last').append('<tr>' + row + '</tr>');
}

function DeleteRow(elem) {
	$(elem).parent().parent().remove();
}

function MoveRow(s, action) {
	var table = $(s).closest('table');
	var row = $(s).parents("tbody tr:first");
	n = $(table).find("tbody > tr").length - 2;
	if (action == "up") {
		if (row.index() != 1) {
			row.insertBefore(row.prev());
		}
	} else if (action == "down") {
		if (row.index() != n) {
			row.insertAfter(row.next());
		}
	} else if (action == "top") {
		if (row.index() != 1) {
			row.insertBefore($(table).find("tbody tr:eq(1)"));
		}
	} else {
		if (row.index() != n) {
			row.insertAfter($(table).find("tbody tr:eq(" + n + ")"));
		}
	}
	return false;
}