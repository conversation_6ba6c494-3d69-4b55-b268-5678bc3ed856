<?php

namespace App\Http\Controllers\membercenter;

use PF;
use Config;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\View;

use App\Repositories\productRepository;
use App\Repositories\myproductRepository;



class myproductController extends Controller {
    private $data;
    private $productRepo;
    private $myproductRepo;
    public function __construct(productRepository $productRepo, myproductRepository $myproductRepo) {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->productRepo = $productRepo;
        $this->myproductRepo = $myproductRepo;
        $this->data['displaynames'] = $this->productRepo->getFieldTitleArray();
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {



        $rows = $this->productRepo->selectRaw('product.*');

        $rows->join('myproduct', 'myproduct.product_id', '=', 'product.productid');
        $rows->myWhere('member_id|N', \Auth::guard('member')->id(), 'member_id', 'Y');

        $rows->orderByRaw('tenderdate desc ,productid desc');
        $rows = $rows->get();

        //$rows = $rows->paginate(10);
        $this->data['rows'] = $rows;
        return view(
            'membercenter.myproduct.index',
            [
                'data' => $this->data
            ]
        );
    }
    public function store(Request $request) {

        $product_id = $request->input('product_id');


        //FIXME 那些欄位為必填判斷
        $validators = null;
        $validators['product_id'] = ['required'];


        if ($validators != null) {
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->data['displaynames']);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
        }
        $inputs = $request->all();
        if ($this->myproductRepo->select('id')->where([
            'member_id' => \Auth::guard('member')->id(),
            'product_id' => $request->input('product_id')
        ])->exists()) {
            $this->myproductRepo->delete([
                'product_id' => $request->input('product_id'),
                'member_id' => \Auth::guard('member')->id()
            ]);
            $resultmessage = '刪除成功';
        } else {
            $inputs['member_id'] = \Auth::guard('member')->id();
            $inputs['created_at'] = date('Y-m-d H:i:s'); //建立日期-
            //PF::printr($inputs);exit();
            $edit = $this->myproductRepo->create($inputs)->id;


            $resultmessage = '加入成功';
        }
        $rows = $this->myproductRepo->selectRaw('*');
        $rows->myWhere('member_id|N', \Auth::guard('member')->id(), "kind", 'Y');
        $rows = $rows->get();
        $myproducts = $rows->map(function ($rs) {
            return $rs->product_id;
        })->implode(',');
        \Session::put('myproducts', $myproducts);
        \Session::save();

        return back()->with('js', "_toast('" . $resultmessage . "',1000,'success')");
    }
    public function destroy(Request $request) {
        $this->myproductRepo->delete([
            'product_id' => $request->input('del'),
            'member_id' => \Auth::guard('member')->id()
        ]);
        return back()->with('js', "_toast('刪除成功',1000,'success')");
    }
}
