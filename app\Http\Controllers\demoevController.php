<?php

namespace App\Http\Controllers;

use DB;
use PF;
use Illuminate\Http\Request;
use Symfony\Component\Process\Process;
use App\Http\Controllers\Controller;
use Symfony\Component\Process\Exception\ProcessFailedException;

/***
"功能名稱":"環境檢測",
"備註":" ",
"建立時間":"2022-01-18 16:22:24",
 ***/
class demoevController extends Controller {
    private $data;

    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
    }

    public function dipslayerr($msg) {
        return '<font class=text-danger> ' . $msg . ' </font>';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        if ($request->input('code') != date('md')) {
            abort(403);
        }

        \PF::setConfig('title', '環境檢測');
        //PF::printr(getEnv('CACHE_DRIVER'));
        // PF::printr($_SERVER['APP_ENV']);
        // PF::printr($_ENV['APP_ENV'] );
        // //exit();
        // //\Artisan::call('cache:clear');
        // //\Artisan::call('config:clear');
        // PF::printr(getEnv('APP_ENV'));
        // PF::printr(\Request::server('HTTP_HOST'));
        // // PF::printr(\App::environment());
        // $inputs['account'] = \$request->input('account'); /*帳號-*/
        // $inputs['adddate'] = date('Y-m-d H:i:s'); /*建立時間-*/

        // $dbcount = DB::table('adminuser')->insert($inputs);

        try {

            $rows = \DB::select('select now() as t');

            if (count($rows)) {
                $rs = $rows[0];
                $this->data['dbstatus'] = 'OK';
                try {
                    $this->data['dbtime'] = $rs->t;
                    if (date('Y-m-d H:i:s') != $rs->t) {
                        throw new \Exception('主機與資料庫時間不一致');
                    }
                } catch (\Exception $e) {
                    $this->data['dbtime'] = $rs->t . $this->dipslayerr($e->getMessage());
                }

                $rows = DB::select('select version() as version');
                $mysql_version = $rows[0]->version;
                $this->data['dbversion'] = $mysql_version;
            } else {
                $this->data['dbstatus'] = 'error';
            }
        } catch (\Exception $e) {
            $this->data['dbstatus'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $this->data['nowstatus'] = now();
            if (now() != \Carbon::now()->toDateTimeString()) {
                throw new \CustomException(now() . " 時間不一致");
            }
        } catch (\Exception $e) {
            $this->data['nowstatus'] = $this->dipslayerr($e->getMessage());
        }



        try {
            \Cache::store('file')->put('key', 'YES', now()->addMinutes(10));

            $this->data['cachefile'] = \Cache::pull('key');
        } catch (\Exception $e) {
            $this->data['cachefile'] = $this->dipslayerr($e->getMessage());
        }

        if ('1' == extension_loaded('memcached')) {
            try {

                \Cache::store('memcached')->put('key', date('Y-m-d H:i:s'), now()->addMinutes(10));
                if (\Cache::store('memcached')->has('key') == false) {
                    throw new \CustomException('no memcached');
                }
                $this->data['cachememcached'] = \Cache::store('memcached')->get('key');
            } catch (\Exception $e) {
                $this->data['cachememcached'] = $this->dipslayerr($e->getMessage());
            }
        } else {
            $this->data['cachememcached'] = $this->dipslayerr('not extension_loaded mememcached');
        }
        try {
            if (class_exists('Predis')) {
                \Cache::store('redis')->put('key', date('Y-m-d H:i:s'), now()->addMinutes(10));
            } else {
                throw new \CustomException('No Predis class');
            }
            $this->data['cacheredis'] = \Cache::pull('key');
        } catch (\Exception $e) {
            $this->data['cacheredis'] = $this->dipslayerr($e->getMessage());
        }
        try {

            $process = Process::fromShellCommandline('ps aux | grep ' . base_path('/'));
            $process->run();
            $this->data['grep_queue_work'] = $process->getOutput();
        } catch (\Exception $e) {
            $this->data['grep_queue_work'] = $this->dipslayerr($e->getMessage());
            //throw $e;
        }
        try {
            if ('1' == $this->data['isqueueemail']) {

                $this->sendMailQueue();
                $this->data['mailqueuestatus'] = 'OK';
            }
        } catch (\Exception $e) {
            $this->data['mailqueuestatus'] = $this->dipslayerr($e->getMessage());
        }
        try {

            if ('1' == $this->data['isemail']) {
                $this->sendMail();

                $this->data['mailstatus'] = 'OK';
            }
        } catch (\Exception $e) {
            $this->data['mailstatus'] =  $this->dipslayerr($e->getMessage());
        }


        try {
            if (\Schema::hasTable('jobs')) {


                $this->data['queuecount'] = \DB::table('jobs')->count();
            } else {
                $this->data['queuecount'] = $this->dipslayerr('查無jobs資料表');
            }
        } catch (\Exception $e) {
            $this->data['queuecount'] = $this->dipslayerr($e->getMessage());
        }

        try {
            \Session::put('demoev', 'OK');

            $this->data['session'] = \Session::get('demoev');
        } catch (\Exception $e) {
            $this->data['session'] = $this->dipslayerr($e->getMessage());
        }

        try {
            $timezone = config('app.timezone');

            $this->data['timezone'] = $timezone;
        } catch (\Exception $e) {
            $this->data['timezone'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $debugmodel = \Config::get('app.debug');
            if ($debugmodel) {
                throw new \Exception('YES');
            } else {
                $debugmodel = 'NO';
            }
            //$debugmodel = config('app.debug');

            $this->data['debugmodel'] = $debugmodel;
        } catch (\Exception $e) {
            $this->data['debugmodel'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $dbname = \DB::connection()->getDatabaseName();

            $this->data['dbname'] = $dbname;
        } catch (\Exception $e) {
            $this->data['dbname'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $APP_ENV = \App::environment();

            $this->data['APP_ENV'] = $APP_ENV;
        } catch (\Exception $e) {
            $this->data['APP_ENV'] = $this->dipslayerr($e->getMessage());
        }

        try {
            $upload_max_filesize = ini_get('upload_max_filesize');

            $this->data['upload_max_filesize'] = $upload_max_filesize;
            if ($this->data['upload_max_filesize'] < 10) {
                throw new \CustomException('建議開大一點');
            }
        } catch (\Exception $e) {
            $this->data['register_argc_argv'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $max_execution_time = ini_get('max_execution_time');

            $this->data['max_execution_time'] = $max_execution_time;
            $this->data['max_execution_time'] = $this->data['max_execution_time'];
            if ((ini_get('max_execution_time')) < 300) {
                throw new \CustomException('建議開大一點');
            }
        } catch (\Exception $e) {
            $this->data['register_argc_argv'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $memory_limit = ini_get('memory_limit');

            $this->data['memory_limit'] = $memory_limit;
        } catch (\Exception $e) {
            $this->data['memory_limit'] = $this->dipslayerr($e->getMessage());
        }

        try {
            $register_argc_argv = ini_get('register_argc_argv');

            $this->data['register_argc_argv'] = $register_argc_argv;
            if (1 != $this->data['register_argc_argv']) {
                throw new \CustomException('請開啟');
            }
        } catch (\Exception $e) {
            $this->data['register_argc_argv'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $short_open_tag = ini_get('short_open_tag');

            $this->data['short_open_tag'] = $short_open_tag;
            if (1 != $this->data['short_open_tag']) {
                throw new \CustomException('請開啟');
            }
        } catch (\Exception $e) {
            $this->data['register_argc_argv'] = $this->dipslayerr($e->getMessage());
        }

        $array = ['exif', 'gd', 'curl', 'fileinfo'];
        for ($i = 0; $i < count($array); ++$i) {
            try {
                if ('1' == extension_loaded($array[$i])) {
                    $this->data[$array[$i] . 'status'] = 'OK';
                } else {
                    $this->data[$array[$i] . 'status'] = 'Error ';
                }
            } catch (\Exception $e) {
                $this->data[$array[$i] . 'status'] = $this->dipslayerr($e->getMessage());
            }
        }

        if (function_exists('simplexml_load_file')) {
            try {
                $this->data['xmlstatus'] = 'OK';
            } catch (\Exception $e) {
                $this->data['xmlstatus'] = 'error:(apt install php7.0-mbstring php7.0-zip php7.0-xml);' . $e->getMessage();
            }
        }
        $this->data['folderarray'] = ['storage/logs/', 'public/images'];
        for ($i = 0; $i < count($this->data['folderarray']); ++$i) {
            try {
                $folder = $this->data['folderarray'][$i];
                if (is_dir(base_path($folder))) {
                    if (false == is_writable(base_path($folder))) {
                        throw new Exception($folder . ' no write file');
                    }
                    //\File::put(base_path($folder.'/test.txt'), 'test');

                    $this->data[$folder . 'status'] = 'OK';
                } else {
                    throw new \CustomException('The directory is not ' . $folder . ' exists.');
                }
                //\File::put(base_path($folder.'/test.txt'), 'test');
                $this->data[$folder . 'status'] = 'OK';
            } catch (\Exception $e) {
                $this->data[$this->data['folderarray'][$i] . 'status'] = $this->dipslayerr($e->getMessage());
            }
        }
        $this->data['filearray'] = ['app/config.php'];
        for ($i = 0; $i < count($this->data['filearray']); ++$i) {
            try {

                $file = $this->data['filearray'][$i];
                $this->data[$file . 'status'] = "OK";
                if (file_exists(base_path($file))) {
                    if (false == is_writable(base_path($file))) {
                        throw new \Exception($file . ' no write file');
                    }
                }
            } catch (\Exception $e) {
                $this->data[$file . 'status'] = $this->dipslayerr($e->getMessage());
            }
        }
        try {
            $this->data['schedule.log'] = \File::get(base_path('storage/logs/schedule.log'));
        } catch (\Exception $e) {
            $this->data['schedule.log'] = $this->dipslayerr($e->getMessage());
        }
        try {
            $this->data['laravel.log'] = \File::get(base_path('storage/logs/laravel-' . date('Y-m-d') . '.log'));
        } catch (\Exception $e) {
            $this->data['laravel.log'] = $this->dipslayerr($e->getMessage());
        }

        try {
            $this->data['serverip'] = $this->getServerIp();
        } catch (\Exception $e) {
            $this->data['serverip'] = $e->getMessage();
        }
        try {
            $path = base_path('public');

            $matchingFiles = \File::glob("{$path}/*.php");



            $body = '(只能有public/index.php)<Br>' . implode('<BR>', $matchingFiles);



            $this->data['public_php'] = $body;
        } catch (\Exception $e) {
            $this->data['public_php'] = $e->getMessage();
        }



        try {
            $this->data['captcha_img'] = captcha_img();
        } catch (\Exception $e) {
            $this->data['captcha_img'] = $this->dipslayerr($e->getMessage());
            //throw $e;
        } finally {
        }

        return view(
            'demoev.index',
            [
                'data' => $this->data,
            ]
        );
    }

    public function getServerIp() {
        try {
            $body = "";
            $version = app()->version();
            if (version_compare($version, '8.0.0', '>=')) {
                $response = \Http::withHeaders([])->withOptions([
                    'connect_timeout' => 5,
                    'timeout' => 5,
                    'verify' => false,
                ])->get('https://myip.com.tw/');
                $body = $response->body();
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return PF::getStr($body, '<font color=green>', '</font>', true);
    }
    public function sendMailQueue() {
        \Mail::to('<EMAIL>') //收件人
            // ->cc($moreUsers) //副本
            // ->bcc($evenMoreUsers) //密件副本
            ->queue(new \App\Mails\sendMail(
                [
                    'subject' => "測試信queue-" . \Request::server('HTTP_HOST'),
                    'raw' => 'queue-' . date('Y-m-d H:i:s'),
                ]
            ));
    }
    public function sendMail() {
        //make send email


        \Mail::to('<EMAIL>') //收件人
            // ->cc($moreUsers) //副本
            // ->bcc($evenMoreUsers) //密件副本
            ->send(new \App\Mails\sendMail(
                [
                    'subject' => "測試信-" . \Request::server('HTTP_HOST'),
                    'raw' =>  date('Y-m-d H:i:s'),
                ]
            ));
    }
}
