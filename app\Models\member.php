<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Auth\Authenticatable as AuthenticableTrait;
use PF;
//會員
class member extends baseModel implements Authenticatable
{
    use AuthenticableTrait;

    public $tabletitle = '會員';
    public $table = 'member';
    public $primaryKey = 'id';

    //欄位必填
    public $rules = [
        'name' => 'required',
'password' => 'required',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'bigint(20) unsigned'],//
'name'=>['title'=>'姓名','type'=>'varchar(255)'],//
'lineid'=>['title'=>'LINE ID','type'=>'varchar(255)'],//
'password'=>['title'=>'密碼','type'=>'varchar(255)'],//
'sex'=>['title'=>'性別','type'=>'varchar(10)'],// //先生,女士,
'mobile'=>['title'=>'行動電話','type'=>'varchar(50)'],//
'tel'=>['title'=>'市話','type'=>'varchar(50)'],//
'email'=>['title'=>'電子信箱','type'=>'varchar(250)'],//
'patterns'=>['title'=>'法拍屋種類','type'=>'varchar(255)'],//
'totalupsets'=>['title'=>'總底價','type'=>'varchar(255)'],//
'postals'=>['title'=>'區域','type'=>'varchar(255)'],//
'myproducts'=>['title'=>'我的收藏','type'=>'mediumtext'],//
'item_request'=>['title'=>'細項需求','type'=>'mediumtext'],//
'isepaper'=>['title'=>'是否訂閱電子報','type'=>'tinyint(4)'],//
'memo'=>['title'=>'備註','type'=>'mediumtext'],//
'api_token'=>['title'=>'api_token','type'=>'varchar(100)'],//
'remember_token'=>['title'=>'remember_token','type'=>'varchar(100)'],//
'lastlogin_ip'=>['title'=>'登入IP','type'=>'varchar(255)'],//
'lastlogin_dt'=>['title'=>'最後登入日期','type'=>'datetime'],//
'logincount'=>['title'=>'登入次數','type'=>'int(11)'],//
'online'=>['title'=>'會員狀態','type'=>'tinyint(4)'],// //正常[1],停權[2],
'adminuser_id'=>['title'=>'編輯人員','type'=>'int(11)'],//
'adminuser_account'=>['title'=>'編輯人員','type'=>'varchar(50)'],//
'created_at'=>['title'=>'建立時間','type'=>'timestamp'],//
'updated_at'=>['title'=>'編輯時間','type'=>'timestamp'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['name','lineid','password','sex','mobile','tel','email','patterns','totalupsets','postals','myproducts','item_request','isepaper','memo','api_token','remember_token','lastlogin_ip','lastlogin_dt','logincount','online','adminuser_id','adminuser_account','created_at','updated_at']; //可充許傳入的欄位
    protected $guarded = [];
    protected $dates = ['lastlogin_dt','created_at','updated_at'];


    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
        });
        static::deleted(function ($model) {
        });
    }
}
