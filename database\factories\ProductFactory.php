<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory {
    /**
     * Factory對應的模型
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * 定義模型的預設狀態
     *
     * @return array
     */
    public function definition() {
        return [
            'productkind' => 2, // 中古屋
            'producttitle' => $this->faker->sentence(3),
            'number' => 'TEST' . $this->faker->unique()->numberBetween(100000, 999999),
            'city1title' => $this->faker->randomElement(['台北市', '新北市', '桃園市', '台中市', '高雄市']),
            'city2title' => $this->faker->randomElement(['大安區', '信義區', '中山區', '松山區', '內湖區']),
            'address' => $this->faker->address,
            'totalupset' => $this->faker->numberBetween(1000000, 50000000),
            'buildname' => $this->faker->company . '大樓',
            'pattern' => $this->faker->randomElement(['公寓', '大樓', '透天', '別墅']),
            'houseage' => $this->faker->numberBetween(1, 50),
            'pingtotalnumberof' => $this->faker->numberBetween(20, 100),
            'mainlawnestablishment' => $this->faker->numberBetween(15, 80),
            'attachedtolawnestablishment' => $this->faker->numberBetween(2, 10),
            'postulateping' => $this->faker->numberBetween(5, 20),
            'carping' => $this->faker->numberBetween(5, 15),
            'other_ping' => $this->faker->numberBetween(0, 10),
            'stakeholdersfloor' => $this->faker->numberBetween(5, 30),
            'storey' => $this->faker->numberBetween(3, 30) . '樓',
            'floor_start' => $this->faker->numberBetween(1, 10),
            'floor_end' => $this->faker->numberBetween(11, 20),
            'room_count' => $this->faker->numberBetween(1, 5),
            'living_room_count' => $this->faker->numberBetween(1, 3),
            'hall_count' => $this->faker->numberBetween(1, 3),
            'bathroom_count' => $this->faker->numberBetween(1, 4),
            'balcony_count' => $this->faker->numberBetween(0, 3),
            'parkingmode' => $this->faker->randomElement(['平面車位', '機械車位', '地下車位']),
            'memo' => $this->faker->paragraph,
            'online' => 1,
            'hits' => $this->faker->numberBetween(0, 1000),
            'user_code' => 'U' . $this->faker->numberBetween(10000, 99999),
            'branch_code' => 'BR' . $this->faker->numberBetween(100, 999),
            'commission_date_start' => $this->faker->date(),
            'commission_date_end' => $this->faker->date(),
            'img' => 'test1.jpg,test2.jpg,test3.jpg',
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
