document.addEventListener("DOMContentLoaded", () => {
    try {
        if ($(window).width() < 725) {

            $(".table-pc").each((i, item) => {
                var isopen = false;
                var min_count = 5;
                if (typeof ($(item).attr('mobile-count')) != "undefined") {
                    min_count = $(item).attr('mobile-count');
                }

                // 建立一個空字串來裝新的 HTML 內容
                var newContent = '';

                // 針對表格的每一行進行操作
                $(item).find("tbody tr").each(function (x, itemx) {

                    var $row = $(itemx);
                    // 每一列開始都先加上一個 div
                    newContent += '<div class="card">';
                    newContent += '<div class="card-body">';

                    // 對該列的每一格進行操作
                    $row.find('td').each(function (y, itemy) {
                        var $cell = $(itemy);
                        var dataTitle = $('th').eq(y).text();
                        if (y == min_count) {
                            key = x.toString() + y.toString();
                            newContent +=
                                '<div class="more-icon" data-toggle="collapse" href="#collapse' +
                                key +
                                '" data-target="#collapse' + key +
                                '" role="button" aria-expanded="false" ><i class="fas fa-chevron-down"></i></div>';
                            newContent += '<div class="collapse" id="collapse' + key +
                                '">';
                        }

                        newContent += '<label class="title">' + dataTitle +
                            ' : </label> ' + $cell
                                .html() + '<br>';

                    });
                    // 當我們的欄位數超過三欄時，在這裡添加一個 div 以結束折疊區塊
                    if ($row.find('td').length > min_count) {
                        isopen = true;
                        newContent += '</div>'; // 結束 .collapse div
                    }
                    // 每一列結束都附上一個結束的 div
                    newContent += '</div>';
                    newContent += '</div>';
                });

                var div = document.createElement("div");
                div.setAttribute("class", 'table-mobile');

                $(item).after($(div).html(newContent));
                if (isopen) {
                    $(item).after("<a href='javascript:;' align=right class='allopen' onclick=\"if($(this).find('.fa-sort-up').length>0){$('div .collapse').collapse('hide');this.innerHTML='<i class=\\'fas fa-sort\\'></i>';}else{$('div .collapse').collapse('show');this.innerHTML='<i class=\\'fas fa-sort-up\\'></i>';}return false;\"><i class='fas fa-sort'></i></a>");
                }
                //$(item).show();

            });
        }
    } catch (error) {
        console.log(["error", error]);
    }
});