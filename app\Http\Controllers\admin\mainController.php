<?php

namespace App\Http\Controllers\admin;

use PF;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\View;

//use Illuminate\Support\Facades\DB;

class mainController extends Controller {
    public $userlimit;
    public $role;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        \Config::set('config.title', __('管理者介面'));
        if (isset($_SERVER['HTTPS']) && 'off' !== $_SERVER['HTTPS']) {
            header('Content-Security-Policy: upgrade-insecure-requests');
        }
        $xmldoc = PF::xmlDoc('Setup.xml');
        $this->data['xmldoc'] = $xmldoc;

        $basetarget = 'maincontent';
        $this->userlimit = \Auth::guard('admin')->user()->userlimit;
        $this->role = \Auth::guard('admin')->user()->role;
        $Menuxml = $xmldoc->xpath('//參數設定檔/權限/選單');
        $x = 0;

        $i2 = 0;
        $i3 = 0;
        $menu = '';
        foreach ($Menuxml as $key => $rs1) {
            ++$i2;
            $isopen = '1' == $rs1['IsOpen'] ? 'menu-open' : '';
            //IsOpen
            $menu .= <<<EOF
            <li class="nav-item $isopen" level="1">
            <a href="#" class="nav-link">
            <p>
                {$rs1['主選單名稱']}
                <i class="right fas fa-angle-left"></i>
            </p>
            </a>
            <ul class="nav nav-treeview">
            EOF;
            $menu .= PHP_EOL;

            $MenuContext2 = $rs1->xpath('KIND');

            if (count($MenuContext2) > 0) {
                $menu .= <<<EOF
                  <li class="nav-item" level="2">

                EOF;
                $i3 = 0;
                foreach ($MenuContext2 as $key2 => $rs2) {
                    $nodisplay = strval($rs2['nodisplay']);

                    if ('1' == $nodisplay) {
                        continue;
                    }

                    //$MenuContext3 = $rs2->xpath('KIND[count(@nodisplay)=0]');
                    $MenuContext3 = $rs2->xpath('KIND');
                    $displayflag = 0;

                    if ($this->ismenulimit($rs2['角色'], $rs2->傳回值)) {
                        if ('' != trim($rs2->網址) || count($MenuContext3) > 0) {
                            ++$i3;
                            $MenuContext3 = $rs2->xpath("KIND[網址!='']");

                            if ('' != trim($rs2->網址)) {
                                $menu .= <<<EOF
                                    <li class="nav-item" level="3">
                                    <a href="{$rs2->網址}" class="nav-link">
                                    <p>{$rs2->資料}</p>
                                    </a>
                                    </li>
                                EOF;
                                $menu .= PHP_EOL;
                            } else {
                                if (count($MenuContext3) > 0) {
                                    $menu .= <<<EOF
                                    <a href="#" class="nav-link" level="4">
                                    <p>{$rs2->資料}</p>
                                    <i class="right fas fa-angle-left"></i>
                                    </a>
                                    <ul class="nav nav-treeview">
                                    EOF;
                                    $i4 = 0;
                                    foreach ($MenuContext3 as $key3 => $rs3) {
                                        if ($this->ismenulimit($rs3['角色'], $rs3->傳回值)) {
                                            //if (('999' == \Auth::guard('admin')->user()->role || '0' ==strval($rs3['角色']) ) || (PF::splitCompare( \Auth::guard('admin')->user()->userlimit, strval($rs3->傳回值)) && '' != strval($rs3->傳回值))) {
                                            $menu .= <<<EOF
                                            <li class="nav-item">
                                            <a href="{$rs3->網址}" class="nav-link">
                                            <p>{$rs3->資料}</p>
                                            </a>
                                            </li>
                                            EOF;
                                            ++$i4;
                                            ++$i3;
                                        }
                                    }
                                    $menu .= <<<EOF
                                </ul>
                                EOF;
                                    $menu .= PHP_EOL;
                                    // if (0 == $i4) {
                                    //     $menu .= "<script>$('#i3".strval($rs2->傳回值)."').hide();</script>\n";
                                    // }
                                }
                            }
                        }
                    }
                    //$menu .= '</li>'.PHP_EOL;
                }
                $menu .= '  </li>' . PHP_EOL;
            }

            $menu .= '</ul>' . PHP_EOL;
            $menu .= '</li>' . PHP_EOL;
            if (0 == $i3) {
                //$menu .= "<script>$('#i2".$i2."').hide();</script>";
            }
        }

        $this->data['menu'] = $menu;

        return view('admin/main/index', [
            'data' => $this->data,
        ]);
    }

    public function ismenulimit($rolenumber, $menunumber) {
        if ('999' == $this->role) {
            return true;
        }
        //角色-角色
        $rolenumber = strval($rolenumber);
        if ('0' == $rolenumber || 'ALL' == $rolenumber) {
            if ('' != $menunumber) {
                if ('' != $this->userlimit) {
                    return in_array(strval($menunumber), explode(',', $userlimit));
                }
            }

            return true;
        }

        if (in_array(strval($this->role), explode(',', $rolenumber))) {
            return true;
        } else {
            return false;
        }

        //傳回值
        $menunumber = strval($menunumber);

        if ('' == $this->userlimit) {
            if ('' != $this->userlimit) {
                return in_array(strval($menunumber), explode(',', $userlimit));
            }
        }

        return false;
    }

    public function updateusetime(Request $request) {
        if (false == PF::isEmpty(\Session::get('admiuserloginid'))) {
            $inputs = null;
            $inputs['logouttime'] = date('Y-m-d H:i:s');
            \App\Models\adminuserloginlog::find(\Session::get('admiuserloginid'))->update($inputs);
            \Session::forget('admiuserloginid');
        }
        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function logout(Request $request) {
        if (array_search('AuthStore', $request->route()->middleware())) {
            \Auth::guard('company')->logout();

            return redirect('store/');
        }
        $this->updateusetime($request);
        \Auth::guard('admin')->logout();

        return redirect('admin/');
    }
}
