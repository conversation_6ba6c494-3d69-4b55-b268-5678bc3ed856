@import 'oldStyle.css';
.properties-wrapper.homePropertiesWrapper {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: auto;
  grid-gap: 15px;
  max-width: 98%;
  width: 1920px;
  margin: auto;
  margin-bottom: 50px;
}
.properties-wrapper.homePropertiesWrapper > .item {
  grid-column-start: span 3;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
}
.properties-wrapper.homePropertiesWrapper > .item .property-item {
  margin: 0 !important;
  width: 100%;
  transform: unset;
}
.properties-wrapper.homePropertiesWrapper > .item .property-item .img-fluid {
  width: 100%;
}
.properties-wrapper.homePropertiesWrapper > .item.makeAdeal {
  grid-row: 1/4;
  max-height: 1230px;
  flex-wrap: unset;
  gap: 15px;
}
.properties-wrapper.homePropertiesWrapper > .item.makeAdeal .makeAdealBody {
  justify-content: flex-start;
  gap: 5px;
}
.properties-wrapper.homePropertiesWrapper > .item.makeAdeal .makeAdealBody .item {
  padding: 0;
  align-items: center;
}
.properties-wrapper.homePropertiesWrapper > .item.makeAdeal .makeAdealBody .item + .item {
  padding-top: 12px;
}
@media (max-width: 1240px) {
  .properties-wrapper.homePropertiesWrapper > .item {
    grid-column-start: span 4;
  }
}
@media (min-width: 640px) {
  .properties-wrapper.homePropertiesWrapper > .item.makeAdeal .makeAdealBody .item {
    height: 16.6666666667%;
  }
}
@media (max-width: 640px) {
  .properties-wrapper.homePropertiesWrapper > .item {
    grid-column-start: span 12;
  }
  .properties-wrapper.homePropertiesWrapper > .item.makeAdeal { /*💛*/
    grid-row: 14/14;
    grid-row: 100/auto;
  }
  .properties-wrapper.homePropertiesWrapper { /*💛*/
    grid-gap: 15px;  grid-gap: 0px;
  }
  .newClass { /*💛*/
    border-bottom: 15px solid transparent;
  }
}





