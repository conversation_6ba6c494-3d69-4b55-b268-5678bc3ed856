<?php

namespace Tests\Feature\admin;

use Tests\Feature\admin\baseTest;
use App\Models\product;

/**
 * 產品管理功能測試
 */
class productTest extends baseTest {
    protected $product;

    /**
     * 測試初始化，建立測試用產品資料
     */
    public function setUp(): void {
        parent::setUp();
        // 使用 factory 建立一筆產品資料
        $this->product = product::factory()->create();
    }

    /**
     * 測試取得產品列表 index
     * 驗證列表頁面回傳狀態與 HTML
     */
    public function test_取得產品列表_index() {
        $response = $this->get('/admin/product');
        $response->assertStatus(200);
        $this->checkHtml($response);
    }

    /**
     * 測試取得產品資料 getRows
     * 驗證取得資料 API 回傳狀態與 JSON 結構
     */
    public function test_取得產品資料_getRows() {
        $response = $this->post('/admin/product/getRows', [
            'searchname' => '',
            'search' => '',
        ]);
        $response->assertStatus(200);
        $this->checkJson($response);
        $response->assertJsonPath('data.0.productid', $this->product->productid);
    }

    /**
     * 測試顯示新增產品頁面 create
     * 驗證新增頁面回傳狀態與 HTML
     */
    public function test_顯示新增產品頁面_create() {
        $response = $this->get('/admin/product/create');
        $response->assertStatus(200);
        $this->checkHtml($response);
    }

    /**
     * 測試顯示產品詳細資料 show
     * 驗證詳細頁面回傳狀態與 HTML
     */
    public function test_顯示產品詳細資料_show() {
        $response = $this->get('/admin/product/show?productid=' . $this->product->productid);
        $response->assertStatus(200);
        $this->checkHtml($response);
    }

    /**
     * 測試顯示儲存頁面 showstore
     * 驗證儲存頁面回傳狀態與 HTML
     */
    public function test_顯示儲存頁面_showstore() {
        $response = $this->get('/admin/product/showstore?productid=' . $this->product->productid);
        $response->assertStatus(200);
        $this->checkHtml($response);
    }

    /**
     * 測試顯示編輯產品頁面 edit
     * 驗證編輯頁面回傳狀態與 HTML
     */
    public function test_顯示編輯產品頁面_edit() {
        $response = $this->get('/admin/product/edit?edit=' . $this->product->productid);
        $response->assertStatus(200);
        $this->checkHtml($response);
    }

    /**
     * 測試儲存產品 store
     * 驗證儲存 API 回傳狀態、JSON 結構與資料庫
     */
    public function test_儲存產品_store() {
        $data = [
            'number' => 'TST' . rand(1000, 9999),
            'city1title' => '台北市',
            'city2title' => '中正區',
            'address' => '測試地址',
            // 其他必要欄位可依 migration/factory 補齊
        ];
        $response = $this->post('/admin/product/store', $data);
        $response->assertStatus(200);
        $this->checkHtml($response);
        $this->assertDatabaseHas('product', [
            'number' => $data['number'],
            'address' => $data['address'],
        ]);
    }

    /**
     * 測試刪除產品 destroy
     * 驗證刪除 API 回傳狀態與資料庫
     */
    public function test_刪除產品_destroy() {
        $response = $this->post('/admin/product/destroy', [
            'del' => $this->product->productid,
        ]);
        $response->assertStatus(200);
        $this->checkHtml($response);
        $this->assertDatabaseMissing('product', [
            'productid' => $this->product->productid,
        ]);
    }

    /**
     * 測試匯出產品 Excel excelexport
     * 驗證匯出功能回傳狀態
     */
    public function test_匯出產品Excel_excelexport() {
        $response = $this->get('/admin/product/excelexport');
        $response->assertStatus(200);
        // 匯出通常為檔案下載，可檢查 header
        $response->assertHeader('content-disposition');
    }

    /**
     * 測試解析產品資料 parse
     * 驗證解析 API 回傳狀態與 JSON 結構
     */
    public function test_解析產品資料_parse() {
        $response = $this->post('/admin/product/parse', [
            'body' => '{"number":"TST1234"}',
            'url' => 'http://test.url',
        ]);
        $response->assertStatus(200);
        $this->checkHtml($response);
    }
}
