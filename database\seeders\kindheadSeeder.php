<?php

use Illuminate\Database\Seeder;

class kindheadSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $tablename = 'kindhead';
     
        DB::table($tablename)->delete();
        //DB::table($tablename)->truncate();

        $faker = \Faker\Factory::create('zh_TW');

        for ($i = 0; $i < 10; ++$i) {
            $data[] = [
                'kindheadtitle' => "第一層種類-".strval($i),
                'kindheadbody' =>  $faker->realText(),
                'kindheadsort' =>strval($i)
            ];
        }

        DB::table($tablename)->insert($data);

        // $this->call(UsersTableSeeder::class);
    }
}
