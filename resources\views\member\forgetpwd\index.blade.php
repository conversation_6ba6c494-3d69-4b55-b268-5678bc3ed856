@extends('layouts.master')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md mx-auto">
            <!-- 卡片容器 -->
            <div class="bg-white rounded-xl shadow-2xl overflow-hidden">
                <!-- 頭部區域 -->
                <div class="bg-gradient-to-r from-primary to-red-700 px-6 py-8">
                    <div class="text-center">
                        <div class="mx-auto h-12 w-12 bg-white rounded-full flex items-center justify-center mb-4">
                            <svg class="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v-2H7v-2H4a1 1 0 01-1-1v-4c0-5.523 4.477-10 10-10s10 4.477 10 10z">
                                </path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-white">忘記密碼</h2>
                        <p class="text-red-100 mt-2">請輸入您的電子信箱，我們將發送重設密碼連結給您</p>
                    </div>
                </div>

                <!-- 表單區域 -->
                <div class="px-6 py-8">

                    <form name="oForm2" class="space-y-6 needs-validation" method="post" language="javascript"
                        action="{{ \request()->middlewareurl }}forgetpwd/store" novalidate>
                        @csrf

                        <!-- 電子信箱輸入框 -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                電子信箱 <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                                        </path>
                                    </svg>
                                </div>
                                <input name="email" type="email" id="email" placeholder="請輸入您的電子信箱"
                                    class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition duration-200 placeholder-gray-400"
                                    required>
                            </div>
                            <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">
                                請輸入有效的電子信箱地址
                            </div>
                        </div>

                        <!-- Google reCAPTCHA -->
                        <script src="https://www.google.com/recaptcha/api.js?render={{ config('recaptcha.id') }}"></script>
                        <script>
                            // 初始化時禁用按鈕
                            $("button[type='submit']").prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

                            grecaptcha.ready(function() {
                                grecaptcha.execute("{{ config('recaptcha.id') }}", {
                                    action: 'homepage'
                                }).then(function(token) {
                                    var recaptchaResponse = document.getElementById('recaptchaResponse');
                                    recaptchaResponse.value = token;

                                    // 啟用按鈕
                                    $("button[type='submit']").prop('disabled', false).removeClass(
                                        'opacity-50 cursor-not-allowed');
                                });
                            });
                        </script>
                        <input type="hidden" value="" name="google_recaptcha_token" id="recaptchaResponse">

                        <!-- 提交按鈕 -->
                        <div class="pt-4">
                            <button type="submit"
                                class="w-full bg-gradient-to-r from-primary to-red-700 text-white font-semibold py-3 px-4 rounded-lg hover:from-red-700 hover:to-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transform transition duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                                <span class="flex items-center justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                        </path>
                                    </svg>
                                    發送重設密碼連結
                                </span>
                            </button>
                        </div>

                        <!-- 返回登入連結 -->
                        <div class="text-center pt-4 border-t border-gray-200">
                            <p class="text-sm text-gray-600">
                                記起密碼了嗎？
                                <a href="{{ url('/member/login') }}"
                                    class="font-medium text-primary hover:text-red-700 transition duration-200">
                                    返回登入
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 安全提示 -->
            <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">安全提示</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <li>重設密碼連結將在15分鐘內有效</li>
                                <li>如果您沒有收到郵件，請檢查垃圾信件夾</li>
                                <li>請勿與他人分享重設密碼連結</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            /* 自定義動畫效果 */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }

                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .animate-fadeInUp {
                animation: fadeInUp 0.6s ease-out;
            }

            /* 表單驗證樣式 */
            .was-validated .form-control:invalid {
                border-color: #ef4444;
            }

            .was-validated .form-control:valid {
                border-color: #10b981;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            $(document).ready(function() {
                // 添加淡入動畫
                $('.max-w-md').addClass('animate-fadeInUp');

                // 表單驗證
                $('form[name="oForm2"]').on('submit', function(e) {
                    var form = this;
                    if (form.checkValidity() === false) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    $(form).addClass('was-validated');
                });

                // 即時email驗證
                $('input[name="email"]').on('input', function() {
                    var email = $(this).val();
                    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                    if (email && !emailRegex.test(email)) {
                        $(this).removeClass('border-gray-300').addClass('border-red-300');
                        $(this).next('.invalid-feedback').removeClass('hidden');
                    } else {
                        $(this).removeClass('border-red-300').addClass('border-gray-300');
                        $(this).next('.invalid-feedback').addClass('hidden');
                    }
                });
            });
        </script>
    @endpush

@stop
