/**
 * 自動輪播控制器
 * 提供進階的自動輪播功能，包括暫停/恢復、速度控制等
 */

// 自動輪播控制類
class AutoCarouselController {
    constructor() {
        this.isPlaying = true; // 播放狀態
        this.mainSlider = null; // 主輪播器實例
        this.thumbSlider = null; // 縮圖輪播器實例
        this.playButton = null; // 播放/暫停按鈕
        this.init();
    }

    /**
     * 初始化控制器
     */
    init() {
        this.createControlButtons();
        this.bindEvents();
        this.setupKeyboardControls();
    }

    /**
     * 創建控制按鈕
     */
    createControlButtons() {
        const sliderWrap = document.querySelector('.slider__wrap');
        if (!sliderWrap) return;

        // 創建控制面板
        const controlPanel = document.createElement('div');
        controlPanel.className = 'carousel-controls';
        controlPanel.innerHTML = `
            <button class="control-btn play-pause-btn" title="播放/暫停">
                <i class="fa-solid fa-pause"></i>
            </button>
            <button class="control-btn speed-btn" title="切換速度">
                <span class="speed-text">標準</span>
            </button>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        `;

        sliderWrap.appendChild(controlPanel);
        this.playButton = controlPanel.querySelector('.play-pause-btn');
        this.speedButton = controlPanel.querySelector('.speed-btn');
        this.progressBar = controlPanel.querySelector('.progress-fill');
    }

    /**
     * 綁定事件監聽器
     */
    bindEvents() {
        // 播放/暫停按鈕事件
        if (this.playButton) {
            this.playButton.addEventListener('click', () => {
                this.togglePlayPause();
            });
        }

        // 速度切換按鈕事件
        if (this.speedButton) {
            this.speedButton.addEventListener('click', () => {
                this.toggleSpeed();
            });
        }

        // 滑鼠懸停事件
        const sliderWrap = document.querySelector('.slider__wrap');
        if (sliderWrap) {
            sliderWrap.addEventListener('mouseenter', () => {
                this.pauseAutoplay();
            });

            sliderWrap.addEventListener('mouseleave', () => {
                if (this.isPlaying) {
                    this.resumeAutoplay();
                }
            });
        }
    }

    /**
     * 設置鍵盤控制
     */
    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousSlide();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.nextSlide();
                    break;
                case ' ': // 空白鍵
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.pauseAutoplay();
                    break;
            }
        });
    }

    /**
     * 設置輪播器實例
     */
    setSliders(mainSlider, thumbSlider) {
        this.mainSlider = mainSlider;
        this.thumbSlider = thumbSlider;
        this.startProgressAnimation();
    }

    /**
     * 切換播放/暫停狀態
     */
    togglePlayPause() {
        if (this.isPlaying) {
            this.pauseAutoplay();
        } else {
            this.resumeAutoplay();
        }
    }

    /**
     * 暫停自動播放
     */
    pauseAutoplay() {
        this.isPlaying = false;
        if (this.mainSlider && this.mainSlider.autoplay) {
            this.mainSlider.autoplay.stop();
        }
        if (this.thumbSlider && this.thumbSlider.autoplay) {
            this.thumbSlider.autoplay.stop();
        }
        
        // 更新按鈕圖示
        if (this.playButton) {
            this.playButton.innerHTML = '<i class="fa-solid fa-play"></i>';
            this.playButton.title = '播放';
        }
    }

    /**
     * 恢復自動播放
     */
    resumeAutoplay() {
        this.isPlaying = true;
        if (this.mainSlider && this.mainSlider.autoplay) {
            this.mainSlider.autoplay.start();
        }
        if (this.thumbSlider && this.thumbSlider.autoplay) {
            this.thumbSlider.autoplay.start();
        }
        
        // 更新按鈕圖示
        if (this.playButton) {
            this.playButton.innerHTML = '<i class="fa-solid fa-pause"></i>';
            this.playButton.title = '暫停';
        }
    }

    /**
     * 切換播放速度
     */
    toggleSpeed() {
        const speeds = [
            { delay: 4000, text: '標準' },
            { delay: 2000, text: '快速' },
            { delay: 6000, text: '慢速' }
        ];
        
        const currentDelay = this.mainSlider?.autoplay?.delay || 4000;
        const currentIndex = speeds.findIndex(speed => speed.delay === currentDelay);
        const nextIndex = (currentIndex + 1) % speeds.length;
        const nextSpeed = speeds[nextIndex];

        // 更新主輪播器速度
        if (this.mainSlider && this.mainSlider.autoplay) {
            this.mainSlider.autoplay.delay = nextSpeed.delay;
        }

        // 更新縮圖輪播器速度
        if (this.thumbSlider && this.thumbSlider.autoplay) {
            this.thumbSlider.autoplay.delay = nextSpeed.delay - 1000; // 縮圖稍快一點
        }

        // 更新按鈕文字
        if (this.speedButton) {
            this.speedButton.querySelector('.speed-text').textContent = nextSpeed.text;
        }
    }

    /**
     * 上一張圖片
     */
    previousSlide() {
        if (this.mainSlider) {
            this.mainSlider.slidePrev();
        }
    }

    /**
     * 下一張圖片
     */
    nextSlide() {
        if (this.mainSlider) {
            this.mainSlider.slideNext();
        }
    }

    /**
     * 開始進度條動畫
     */
    startProgressAnimation() {
        if (!this.progressBar) return;

        const updateProgress = () => {
            if (this.mainSlider && this.isPlaying) {
                const delay = this.mainSlider.autoplay?.delay || 4000;
                const elapsed = Date.now() - (this.mainSlider.autoplay?.paused ? 0 : this.mainSlider.autoplay?.timeLeft || 0);
                const progress = Math.min((elapsed / delay) * 100, 100);
                this.progressBar.style.width = `${progress}%`;
            }
            requestAnimationFrame(updateProgress);
        };

        updateProgress();
    }
}

// 當 DOM 載入完成後初始化控制器
document.addEventListener('DOMContentLoaded', () => {
    // 等待 Swiper 初始化完成
    setTimeout(() => {
        window.autoCarouselController = new AutoCarouselController();
        
        // 如果 Swiper 實例已存在，設置它們
        if (window.sliderImages && window.sliderThumbs) {
            window.autoCarouselController.setSliders(window.sliderImages, window.sliderThumbs);
        }
    }, 500);
});
