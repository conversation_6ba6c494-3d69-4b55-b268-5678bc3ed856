<?php

namespace App\Repositories;

use DB;
use PF;

class Repository
{
    /**
     * @var
     */
    public $model;

    /**
     * @param App $app
     *
     * @throws \Bosnadev\Repositories\Exceptions\RepositoryException
     */
    public function __construct($model)
    {
        $this->model = $model;
    }

    public function select($field = '*')
    {
        $rows = $this->selectRaw($field);

        return $rows;
    }

    public function selectRaw($field = '')
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    /**
     *TODO 透過KEY找一筆資料.
     */
    public function find($id, $column = '*')
    {
        $rows = $this->model->find($id);

        return $rows;
    }

    public function getFieldTitleArray()
    {
        return $this->model::getFieldTitleArray();
    }

    /**
     * @param int   $perPage
     * @param array $columns
     *
     * @return mixed
     */
    public function paginate($perPage = 15, $columns = array('*'))
    {
        return $this->model->paginate($perPage, $columns);
    }

    public function firstOrCreate(array $wheres, array $inputs = null)
    {
        if (null != $inputs) {
            return $this->model::firstOrCreate($wheres, $inputs);
        }

        return $this->model::firstOrCreate($wheres, $wheres);
    }

    public function findOrFail($wheres)
    {
        return $this->model::findOrFail($wheres);
    }

    public function firstOrFail($wheres)
    {
        return $this->model::firstOrFail($wheres);
    }

    public function where($wheres)
    {
        $rows = $this->model::where($wheres);

        return $rows;
    }

    public function updateOrCreate(array $wheres, array $inputs)
    {
        return $this->model::updateOrCreate($wheres, $inputs);
    }

    /**
     * @param array $data
     *
     * @return mixed
     */
    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function insert(array $datas)
    {
        $this->model->insert($datas);
    }

    /**
     * @param array $data
     * @param $id
     * @param string $attribute
     *
     * @return mixed
     */
    public function update(array $inputs, $id, $attribute = 'id')
    {
        if (PF::isEmpty($id)) {
            throw new \CustomException('update [id] is empty');
        }
        if (null == $inputs) {
            throw new \CustomException('update inputs is null');
        }
        if (is_array($id)) {
            //$this->xxrepo->update($inputs, ['id'=>explode(",",$id)]);
            return $this->model::where($id)->update($inputs);
        } else {
            return $this->model::findOrFail($id)->update($inputs);
        }
    }

    /**
     * @param $id
     *
     * @return mixed
     */
    public function delete($id = null)
    {
        if (null == $id) {
            return $this->model->delete();
        }
        if (is_array($id)) {
            $this->model::where($id)->delete();
        } else {
            $this->model->destroy($id);
        }
    }

    public function deleteIds($ids)
    {
        if (PF::isEmpty($ids)) {
            throw new \CustomException('delete [ids] is empty');
        }
        if (is_array($ids)) {
            $this->model->where($ids)->delete();
        } else {
            $this->model->whereIn($this->model->primaryKey, explode(',', $ids))->delete();
        }
    }

    public function truncate()
    {
        $this->model->truncate();
    }
}
