<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class ProductService {
    /**
     * 檢查並處理拍定、撤回狀態的商品
     * 一筆一筆將拍定、撤回的商品下架並清理相關圖片檔案
     *
     * @return void
     */
    public function handleExpiredProducts() {
        try {
            // 查詢拍定(6)、撤回(5)狀態的上架商品
            $auctionCompletedProducts = Product::where('online', 1)
                ->whereIn('auctions', ['撤回', '拍定']) // 5: 撤回, 6: 得標(拍定)
                ->get();

            $deletedCount = 0;

            // 一筆一筆處理拍定、撤回狀態的商品
            foreach ($auctionCompletedProducts as $product) {
                try {
                    // 解析並刪除圖片檔案
                    $this->deleteProductImages($product);

                    // 更新商品狀態為下架
                    $product->online = 0;
                    $product->save();

                    $deletedCount++;

                    \Log::info("已下架拍定/撤回商品", [
                        'productid' => $product->productid,
                        'number' => $product->number,
                        'title' => $product->producttitle,
                        'auctions' => $product->auctions,
                        'auction_status' => $product->auctions == 5 ? '撤回' : '拍定'
                    ]);
                } catch (\Exception $e) {
                    \Log::error("下架拍定/撤回商品時發生錯誤", [
                        'productid' => $product->productid,
                        'auctions' => $product->auctions,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if ($deletedCount > 0) {
                \Log::info("拍定/撤回商品下架完成，共下架 {$deletedCount} 筆商品");
            }
        } catch (\Exception $e) {
            \Log::error("處理拍定/撤回商品時發生錯誤", [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 刪除商品相關的圖片檔案
     *
     * @param product $product
     * @return void
     */
    private function deleteProductImages(product $product) {
        if (empty($product->img)) {
            return;
        }

        try {
            // 解析 img 欄位，可能是逗號分隔的檔案名稱
            $imageFiles = array_merge(
                explode(',', $product->img),
                explode(',', $product->pdf)
            );

            foreach ($imageFiles as $imageFile) {
                $imageFile = trim($imageFile);

                if (empty($imageFile)) {
                    continue;
                }

                // 建構完整的檔案路徑
                $imagePath = public_path('images/product/' . $imageFile);

                // 檢查檔案是否存在並刪除
                if (File::exists($imagePath)) {
                    File::delete($imagePath);
                    \Log::info("已刪除圖片檔案", [
                        'file' => $imagePath,
                        'productid' => $product->productid
                    ]);
                } else {
                    \Log::warning("圖片檔案不存在", [
                        'file' => $imagePath,
                        'productid' => $product->productid
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Log::error("刪除圖片檔案時發生錯誤", [
                'productid' => $product->productid,
                'img' => $product->img,
                'error' => $e->getMessage()
            ]);
        }
    }
}
