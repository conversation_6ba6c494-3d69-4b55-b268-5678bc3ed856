<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use DB;
use App\Repositories\boardRepository;

class placeSeeder extends Seeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        
        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->boardRepo->select()
        ->myWhere('kind|S', 'place', "del", 'Y')
        ->delete();
        
        $faker = \Faker\Factory::create('zh_TW');

        for ($i = 0; $i < 10; ++$i) {
            $data = [
                'kind' => 'place',
                //'title' =>  $faker->sentence,
                'title' => '臺灣臺北地方法院-'.$i,
                'memo' => '100206 臺北市博愛路 131 號',

                //'field1' => $faker->file('C:\AppServ\laravel\1\Pictures\banner.jpg', 'C:\AppServ\laravel\e\storage\app\public\images\news', false),
                'field1' => $faker->image(public_path('images/place'), 800, 800, 'cats',false),

                'field2' => '(02)2314-6871',
                'field3' => '(02)2331-8047',
                //'body' => $faker->randomHtml(2, 3),

                //'body' => $faker->text(2000),

                'begindate' => date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month')))),
            ];
            $this->boardRepo->updateOrCreate($data);
        }
        
        

        

        // $this->call(UsersTableSeeder::class);
    }

}
