<?php

namespace App\Http\Controllers\admin;

use DB;
use PF;
use PT;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class viewcountController extends adminController
{
    private $fieldnicknames;
    private $data;
    private $xmlDoc;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        //導覽列
        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'viewcount');

        //共用的hidden變數
        $this->data['hiddens'] = [];

        $this->db = new \App\Models\viewcount();
        $this->fieldnicknames = $this->db->fieldnicknames;
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ('' == $this->data['yyyymm']) {
            $this->data['yyyymm'] = date('Y-m');
        }

        $this->data['yyyymm1'] = (intval(PF::left($this->data['yyyymm'], 4)) - 1).'-'.PF::right($this->data['yyyymm'], 2);

        $rows = DB::table('viewcount')->selectRaw('hits,created_at');
        $rows->myWhere('left(created_at,7)|S', $this->data['yyyymm'], 'yyyymm', 'Y');

        $rows = $rows->get();
        $this->data['monthtotal'] = collect($rows)->sum('hits');

        foreach ($rows as $rs) {
            $hits[PF::formatDate($rs->created_at)] = $rs->hits;
        }
        $this->data['total'] = $rows->sum('hits');
        $rows = DB::table('viewcount')->selectRaw('hits,created_at');
        $rows->myWhere('left(created_at,7)|S', $this->data['yyyymm1'], 'yyyymm', 'Y');

        $rows = $rows->get();
        $hits1[] = null;
        foreach ($rows as $rs) {
            $hits1[PF::formatDate($rs->created_at)] = $rs->hits;
        }

        $startdate = $this->data['yyyymm'].'-01';
        $startdate1 = $this->data['yyyymm1'].'-01';
        $enddate = date('Y-m-d', strtotime("$startdate +1 month -1 day"));
        $a = PF::dateDiff($enddate, $startdate, 'd');
        //PF_print($hit1);
        $weeks = array('日', '一', '二', '三', '四', '五', '六');
        $this->data['dates'] = [];
        $this->data['dates'][] = ['Year', '今年人次', '去年人次'];

        for ($i = 0; $i <= $a; ++$i) {
            $d = date('Y/m/d', strtotime($i.' day', strtotime($startdate)));
            $d1 = date('Y/m/d', strtotime('-1 year', strtotime($d)));
            $w = $weeks[date('w', strtotime($d))];
            $this->data['dates'][] = [$d.'('.$w.')', (null == $hits[$d]) ? 0 : $hits[$d], (null == $hits1[$d1]) ? 0 : $hits1[$d1]];
        }
        $this->data['dates'] = json_encode($this->data['dates'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

        return view('admin.viewcount.index', [
                'data' => $this->data,
            ]);
    }
}
