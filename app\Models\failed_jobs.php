<?php
namespace App\Models;
use DB;
use PF;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
/*swagger api document start*/
/**
 * @OA\Schema(
 *   schema="failed_jobs",
 *      allOf={
 *         @OA\Schema( @OA\Property(property="id", type="",description="自動編號", example=""  )),
*         @OA\Schema( @OA\Property(property="connection", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="queue", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="payload", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="exception", type="",description="", example=""  )),
*         @OA\Schema( @OA\Property(property="failed_at", type="",description="", example=""  )),

 *      }
 *)
 */
/*swagger api document end*/
class failed_jobs extends baseModel
{
    use HasFactory;
    
    public $tabletitle = '';
    public $table = 'failed_jobs';
    public $primaryKey = 'id';
    //public $incrementing = false;//取消自動編號
      //欄位必填
      public $rules = [
		'id' => 'required',

    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'bigint(20) unsigned'],//
'connection'=>['title'=>'','type'=>'text'],//
'queue'=>['title'=>'','type'=>'text'],//
'payload'=>['title'=>'','type'=>'longtext'],//
'exception'=>['title'=>'','type'=>'longtext'],//
'failed_at'=>['title'=>'','type'=>'timestamp'],//
];
    //public $timestamps = false;//不使用 timestamps 相關字段
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    protected $fillable = ['connection','queue','payload','exception','failed_at']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    
    protected $dates = ['failed_at'];
  
//   public function __construct() {
//         $this->fillable = parent::getfillables();//接受$request->all();
//         //$this->fillable =array_keys($this->fieldInfo);
//         parent::__construct();        
//         parent::setFieldInfo($this->fieldInfo);
//   }         
    
    // public function ordergroup()//父對子 一對多
    // {
    //     return $this->hasMany('App\Models\ordergroupitem');
    // }
    // public function kindhead()//子對父 多對一
    // {
    //  return $this->belongsTo(kindhead::class,'kindheadid');
    // }
    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          
        });
         static::deleting(function ($model) {

          
        });
        static::deleted(function ($model) {
            
        });
    }	

}