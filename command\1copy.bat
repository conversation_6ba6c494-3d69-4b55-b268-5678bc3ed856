chcp 65001
cd..
echo "start copy"
set "current_dir=%~dp0"
echo Current directory: %current_dir%


xcopy C:\AppServ\laravel\1\1laravel\.vscode .vscode\ /EXCLUDE:command\excludes.txt /y /Q
xcopy C:\AppServ\laravel\1\1laravel\command command\ /EXCLUDE:command\excludes.txt /y /Q /s
xcopy C:\AppServ\laravel\1\1laravel\.cursor .cursor\ /y /Q /s
xcopy C:\AppServ\laravel\1\1laravel\stubs stubs\ /y /Q /s
xcopy C:\AppServ\laravel\1\1laravel\app\Console\Commands app\Console\Commands\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\app\Exceptions app\Exceptions\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\phpunit.xml  /y /Q

xcopy C:\AppServ\laravel\1\1laravel\.cursorignore  /y /Q

REM xcopy C:\AppServ\laravel\1\1laravel\app\Models\baseModel.php app\Models\ /y /Q
REM xcopy C:\AppServ\laravel\1\1laravel\app\Exceptions\Handler.php app\Exceptions\ /y /Q

REM xcopy C:\AppServ\laravel\1\1laravel\app\Repositories\Repository.php app\Repositories\ /y /Q


xcopy C:\AppServ\laravel\1\1laravel\app\Providers\RouteServiceProvider.php app\Providers\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\routes\artisan.php routes\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\app\Services\artisanService.php app\Services\ /y /Q



xcopy C:\AppServ\laravel\1\1laravel\app\Libraries app\Libraries\ /EXCLUDE:command\excludes.txt /y /Q

xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\api\aController.php app\Http\Controllers\api /y /Q

xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\demoevController.php app\Http\Controllers\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\zipcodeController.php app\Http\Controllers\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\aController.php app\Http\Controllers\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\demoevController.php app\Http\Controllers\ /y /Q


xcopy C:\AppServ\laravel\1\1laravel\app\Services\formquerylogService.php app\Services\ /y /Q

xcopy C:\AppServ\laravel\1\1laravel\app\Traits app\Traits\ /y /Q /s
xcopy C:\AppServ\laravel\1\1laravel\app\Rules app\Rules\ /y /Q /s
xcopy C:\AppServ\laravel\1\1laravel\app\Listeners app\Listeners\ /y /Q /s


REM xcopy C:\AppServ\laravel\1\1laravel\resources\views\errors resources\views\errors\ /y /Q /s
xcopy C:\AppServ\laravel\1\1laravel\resources\views\demoev resources\views\demoev\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\resources\views\a resources\views\a\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\resources\views\demoev resources\views\demoev\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\resources\views\errors resources\views\errors\ /y /Q

REM xcopy C:\AppServ\laravel\1\1laravel\resources\views\layouts\raw.blade.php resources\views\layouts\ /y /Q

xcopy C:\AppServ\laravel\1\1laravel\storage\views\email\errmsg.blade.php storage\views\email\ /y /Q

xcopy C:\AppServ\laravel\1\1laravel\database\seeders\DatabaseSeeder.php database\seeders\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\database\seeders\myFaker.php database\seeders\ /y /Q


xcopy C:\AppServ\laravel\1\1laravel\tests\TestCase.php tests\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\tests\CreatesApplication.php tests\ /y /Q
xcopy C:\AppServ\laravel\1\1laravel\.env.testing \ /y /Q

if exist resources/js/node_modules (
    echo "start copy nuxt3"
    xcopy C:\AppServ\laravel\1\1nuxt\.clinerules  /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\command resources\js\command\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\views\ld_json resources\views\ld_json\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\.vscode\nuxt3.code-snippets .vscode\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\.vscode\bootstrap5.code-snippets .vscode\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\components resources\js\components\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\stores resources\js\stores\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\utils resources\js\utils\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\pages\logs resources\js\pages\logs\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\pages\sample resources\js\pages\sample\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\pages\admin\epost resources\js\pages\admin\epost\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\pages\admin\formquerylog resources\js\pages\admin\formquerylog\ /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\pages\admin\viewfile resources\js\pages\admin\viewfile\ /y /Q

    xcopy C:\AppServ\laravel\1\1nuxt\tsconfig.json /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\package.json /y /Q

    xcopy C:\AppServ\laravel\1\1nuxt\resources\js\error.vue resources\js /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\app\Http\Controllers\api\errorController.php app\Http\Controllers\api /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\app\Http\Controllers\api\admin\formquerylog.php app\Http\Controllers\api\admin /y /Q
    xcopy C:\AppServ\laravel\1\1nuxt\app\Http\Controllers\api\admin\uploadController.php app\Http\Controllers\api\admin /y /Q
) else (
    if exist node_modules (
        echo "start copy vue3"
        xcopy C:\AppServ\laravel\1\1vue\resources\views\ld_json resources\views\ld_json\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\.vscode\vue3.code-snippets .vscode\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\components resources\js\components\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\directives resources\js\directives\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\services resources\js\services\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\stores resources\js\stores\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\utils resources\js\utils\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\views\admin\epost resources\js\views\admin\epost\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\views\admin\viewfile resources\js\views\admin\viewfile\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\views\admin\formquerylog resources\js\views\admin\formquerylog\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\app.js resources\js\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\vite.config.js /y /Q
        xcopy C:\AppServ\laravel\1\1vue\resources\js\app.js resources\js\ /y /Q
        xcopy C:\AppServ\laravel\1\1vue\app\Http\Controllers\api\errorController.php app\Http\Controllers\api /y /Q
    )
)




if exist app\Http\Controllers\admin (
    echo "start copy admin"
    xcopy C:\AppServ\laravel\1\1laravel\.clinerules  /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\layouts\empty.blade.php resources\views\admin\layouts\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\boardController.php app\Http\Controllers\admin\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\failed_jobsController.php app\Http\Controllers\admin\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\ckeditorController.php app\Http\Controllers\admin\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\epostController.php app\Http\Controllers\admin\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\epostxmlController.php app\Http\Controllers\admin\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\viewfileController.php app\Http\Controllers\admin\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\viewcountController.php app\Http\Controllers\admin\ /y /Q
    REM xcopy  C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\frameController.php app\Http\Controllers\admin\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\app\Http\Controllers\admin\setupController.php app\Http\Controllers\admin\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\errors resources\views\admin\errors\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\setup resources\views\admin\setup\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\epost resources\views\admin\epost\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\viewfile resources\views\admin\viewfile\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\formquerylog resources\views\admin\formquerylog\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\failed_jobs resources\views\admin\failed_jobs\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\frame resources\views\admin\frame\ /y /Q
    REM xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\layouts\search.blade.php resources\views\admin\layouts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\layouts\hidden.blade.php resources\views\admin\layouts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\layouts\delall.blade.php resources\views\admin\layouts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\resources\views\admin\layouts\add.blade.php resources\views\admin\layouts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\app\Macros app\Macros\ /y /Q /s
    xcopy C:\AppServ\laravel\1\1laravel\public\Scripts\PJSFunc.js public\Scripts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\public\Scripts\PJSFunVue3.js public\Scripts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\public\Scripts\mobile-table.js public\Scripts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\public\Scripts\vueprod3.js public\Scripts\ /y /Q
    xcopy C:\AppServ\laravel\1\1laravel\public\Scripts\vue-components.js public\Scripts\ /y /Q

) else (
    echo "start copy no admin"
)







REM 刪除Logs
del /F /S /Q storage\logs
php artisan migrate:fresh --env=testing
php artisan optimize:clear
command/db_backup.bat
REM 清除暫存
REM cd command
REM clear.bat
cmd /k
goto end

:end