@extends('admin.layouts.master')


@section('css')
@endsection

@section('js')
@endsection
@section('nav')
    {!! $data['nav'] !!}
@endsection


@section('content')
    {!! Session::get('msg') !!}


    <div class="container">
        <form name="SearchoForm" class="form-inline" method="post" language="javascript"
            action="{{ request()->getRequestUri() }}" onsubmit="return SearchoForm_onsubmit(this);">
            @include('admin.layouts.search', [])
        </form>
    </div>



    <!--排序的參數-->
    <form name="SortoForm" method="post">
        @include('admin.layouts.hidden', ['method' => 'SortoForm', 'data' => $data])
    </form>

    <div class="table-responsive">
        <table class="table table-striped table-hover  table-bordered table-fixed table-pc" mobile-count="5">

            <thead>
                <tr valign="top" align="left">

                    <th width="100" id="pagename">功能名稱</th>
                    <th width="" id="pathinfo">程式位置</th>

                    <th width="100" id="created_at">建立時間</th>
                    <th width="100">模擬測試</th>

                </tr>
            </thead>



            <tbody>
                @foreach ($data['rows'] as $rs)
                    <!--傳給下一頁的參數-->



                    <tr>



                        <td>

                            {{ $rs->pagename }}

                        </td>

                        <td>

                            {{ $rs->pathinfo }}

                            <textarea class="form-control" cols="37" rows="5" style='width:100%;height:100px'>
@if ($rs->formbody != '')
{!! $rs->formbody !!}
@else
{!! $rs->raw !!}
@endif
</textarea>



                        </td>


                        <td>

                            {{ $rs->created_at }}

                        </td>
                        <td>
                            @if ($rs->formbody != '')
                                <form method="post" language="javascript" name="oForm" action="{{ $rs->pathinfo }}"
                                    target="_blank">
                                    @foreach (explode('&', $rs->formbody) as $item)
                                        @php
                                            $arr = explode('=', $item);
                                        @endphp
                                        <textarea style="display:none" name="{{ $arr[0] }}">{!! $arr[1] !!}</textarea>
                                    @endforeach
                                    <button class="btn btn-primary">模擬測試</button>
                                </form>
                            @endif



                        </td>


                    </tr>
                @endforeach

            </tbody>



        </table>

        @if (count($data['rows']) == 0)
            No Data
        @endif
        {{ $data['rows'] != null ? $data['rows']->links('layouts.paginate') : '' }}


    </div>
@endsection
