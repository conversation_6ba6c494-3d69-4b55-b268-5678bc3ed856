<?php

namespace App\Repositories;

use App\Models\adminuser;
use DB;

class adminuserRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(adminuser $model)
    {
        $this->model = $model;
    }

    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }



    public function create($inputs)
    {

        parent::create($inputs);
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        parent::update($inputs,$id,$attribute) ;      
    }

    public function deleteIds($ids)
    {
        parent::deleteIds($ids);
        //$this->model->whereIn($this->model->primaryKey, explode(",",$ids))->delete();
       
        // $rows = $this->db->myWhere('userid|ININT', $this->data['del'], 'del', 'Y');
        // //PF::dbSqlPrint($rows);
        // $rows->get()->each(function ($row) {
        //     $row->delete();
        // });
        
    }
}
