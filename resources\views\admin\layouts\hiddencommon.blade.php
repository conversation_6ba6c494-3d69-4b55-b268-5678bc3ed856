@foreach (Request::all() as $key => $item)    
    @if ($item!="" && (in_array($key,config('app.globalhidden')) || $key=="page"))
        @if (is_array($item))
        <input type="hidden" name="{{$key}}"  value="{{implode(",",$item)}}" />
        @elseif ($key!="page")
        <input type="hidden" name="{{$key}}"  value="{{strval($item)}}" />
        @endif
    @endif
@endforeach
@if ($data['hiddens']!="")    
    @foreach ($data['hiddens'] as $key)
            <input type="hidden" name="{{$key}}" value="{{$data[$key]}}">
    @endforeach
@endif