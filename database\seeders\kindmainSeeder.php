<?php

use Illuminate\Database\Seeder;

class kindmainSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $tablename = 'kindmain';
     
        //DB::table($tablename)->delete();
        //DB::table($tablename)->truncate();

        $faker = \Faker\Factory::create('zh_TW');
//\DB::enableQueryLog();// 顯示sqlcmd
        $rows = DB::table('kindhead')->select('*');
        $rows->orderBy('kindheadid');
        //print_r(DB::getQueryLog());
        $rows=$rows->get();
        foreach ($rows as $rs) {
            //PF::printr($rs->title);        
            for ($i = 0; $i < 10; ++$i) {
                $data[] = [
                    'kindheadid' => $rs->kindheadid,
                    'kindmaintitle' => "第二層種類-".strval($i),
                    'kindmainbody' => $faker->realText(),
                    'kindmainsort' =>strval($i),
                ];
            }
        }

        DB::table($tablename)->insert($data);

        // $this->call(UsersTableSeeder::class);
    }
}
