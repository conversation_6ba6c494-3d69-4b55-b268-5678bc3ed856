<?php

namespace App\Http\Controllers\membercenter;

use DB;
use PF;
use Config;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\View;
use App\Repositories\memberRepository;

class changepasswordController extends Controller
{
    private $data;
    private $memberRepo;

    public function __construct(memberRepository $memberRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->memberRepo = $memberRepo;

        $this->data['displaynames'] = $this->memberRepo->getFieldTitleArray();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $rows = DB::table('member');
        $rows->selectRaw('member.*');
        $rows->myWhere('id|N', \Auth::guard('member')->id(), 'memberid', 'Y');

        $rows->limit(1);
        //$rows = $rows->get();
        //dd($row);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('No data');
        }
        // Config::set('config.title', config('config.title'));
        // Config::set('config.keyword', '');
        // Config::set('config.description', '');

        return view('membercenter.changepassword.index', [
            'data' => $this->data,
            ]
       );
    }

    public function store(Request $request)
    {
        $validators = null;

        $validators['password'] = ['required', 'confirmed', 'min:8']; //密碼

        $validator = \Validator::make($request->all(), $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        $inputs = [];

        $inputs['password'] = \Hash::make($request->input('password')); //密碼-

        $this->memberRepo->update($inputs, \Auth::guard('member')->id());

        return back()->with('js', "_toast('更新成功',500)");
    }
}
