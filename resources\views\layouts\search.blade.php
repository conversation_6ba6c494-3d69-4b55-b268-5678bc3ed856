<section class="hero swiper-container ">
    <!-- 搜尋表單 - 使用 Tailwind CSS 重新設計 -->

    <div class="container mx-auto px-4 py-1">

        <form name="oForm" id="oForm" method="post" language="javascript" action="{{ url('/') }}/house#house"
            novalidate class="bg-white rounded-lg p-2 w-full">
            <div class="newSearch">

                <!-- 標籤分頁 (法拍屋/中古屋/土地專區) -->
                <div class="flex mb-2 border-b">
                    @foreach ($data['xmldoc']->xpath('//參數設定檔/種類/KIND') as $key => $v)
                        <label
                            class="property-tab cursor-pointer px-4 py-2 font-bold rounded-t-lg mr-1 transition-colors duration-200 {{ $data['productkind'] == strval($v->傳回值) ? 'active bg-orange-500 text-white' : 'bg-gray-200 text-gray-600 hover:bg-gray-300' }}">
                            <input type="radio" name="productkind" value="{{ strval($v->傳回值) }}"
                                {{ $data['productkind'] == strval($v->傳回值) ? 'checked' : '' }} class="hidden">
                            {{ $v->資料 }}
                        </label>
                    @endforeach
                </div>

                <!-- 城市選擇按鈕 -->
                <div class="mb-2">
                    <div class="flex flex-wrap gap-2">
                        @if ($data['city1rows'] != null)
                            @foreach ($data['city1rows'] as $rs)
                                <button type="button"
                                    class="city-btn px-3 py-1 border border-gray-300 rounded font-medium transition-colors duration-200 {{ $data['city1title'] == $rs->city1title ? 'active bg-orange-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }}"
                                    onClick="onCity1(this,'{{ $rs->city1title }}')">
                                    {{ $rs->city1title }}
                                </button>
                            @endforeach
                        @endif
                    </div>
                </div>

                <!-- 區域選擇 -->
                <div class="mb-2">

                    <input name="city1title" type="hidden" value="{{ $data['city1title'] }}">
                    @if ($data['city1rows'] != null)
                        @foreach ($data['city1rows'] as $rs)
                            <div class="district-container {{ $data['city1title'] == $rs->city1title ? '' : 'hidden' }}"
                                rel='city2' id='city1_{{ $rs->city1title }}'>
                                <div class="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-10 gap-2">
                                    @php
                                        $city2rows = collect(\Cache::get('city2rows'))->where(
                                            'city1title',
                                            $rs->city1title,
                                        );
                                    @endphp
                                    @foreach ($city2rows as $city2)
                                        <label
                                            class="district-checkbox flex items-center px-2 py-1 border rounded hover:bg-gray-50 cursor-pointer transition-colors duration-200 whitespace-nowrap">

                                            <input name="postal[]" type="checkbox" value="{{ $city2->postal }}"
                                                {{ in_array($city2->postal, explode(',', $data['postal'] ?? '')) ? 'checked' : '' }}
                                                class="mr-2 text-orange-500 focus:ring-orange-500 flex-shrink-0">
                                            <span class="text-sm">{{ $city2->city2title }}</span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>

                <!-- 房屋型態 -->
                <div class="mb-2 flex flex-col md:flex-row md:items-start">
                    <div class="w-full md:w-24 flex-shrink-0 mb-2 md:mb-0 md:mr-6">
                        <label
                            class="block text-sm font-bold text-gray-700 border border-gray-300 rounded px-2 py-1 text-center bg-gray-50">房屋型態</label>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-9 gap-2">
                            @foreach ($data['xmldoc']->xpath('//參數設定檔/房屋類別/KIND') as $key => $v)
                                <label
                                    class="option-checkbox flex items-center px-2 py-1 border rounded hover:bg-gray-50 cursor-pointer transition-colors duration-200 whitespace-nowrap">
                                    <input name="pattern[]" type="checkbox" value="{{ strval($v->傳回值) }}"
                                        {{ in_array(strval($v->傳回值), explode(',', $data['pattern'] ?? '')) ? 'checked' : '' }}
                                        class="mr-2 text-orange-500 focus:ring-orange-500 flex-shrink-0">
                                    <span class="text-sm">{{ $v->資料 }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- 權狀坪數 -->
                <div class="mb-2 flex flex-col md:flex-row md:items-start">
                    <div class="w-full md:w-24 flex-shrink-0 mb-2 md:mb-0 md:mr-6">
                        <label
                            class="block text-sm font-bold text-gray-700 border border-gray-300 rounded px-2 py-1 text-center bg-gray-50">權狀坪數</label>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                            @foreach ($data['xmldoc']->xpath('//參數設定檔/坪數/KIND') as $key => $v)
                                <label
                                    class="option-checkbox flex items-center px-2 py-1 border rounded hover:bg-gray-50 cursor-pointer transition-colors duration-200 whitespace-nowrap">
                                    <input name="pingtotalnumberof[]" type="checkbox" value="{{ strval($v->傳回值) }}"
                                        {{ in_array(strval($v->傳回值), explode(',', $data['pingtotalnumberof'] ?? '')) ? 'checked' : '' }}
                                        class="mr-2 text-orange-500 focus:ring-orange-500 flex-shrink-0">
                                    <span class="text-sm">{{ $v->資料 }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- 購屋預算 -->
                <div class="mb-2 flex flex-col md:flex-row md:items-start">
                    <div class="w-full md:w-24 flex-shrink-0 mb-2 md:mb-0 md:mr-6">
                        <label
                            class="block text-sm font-bold text-gray-700 border border-gray-300 rounded px-2 py-1 text-center bg-gray-50">購屋預算</label>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                            @foreach ($data['xmldoc']->xpath('//參數設定檔/購屋預算/KIND') as $key => $v)
                                <label
                                    class="option-checkbox flex items-center px-2 py-1 border rounded hover:bg-gray-50 cursor-pointer transition-colors duration-200 whitespace-nowrap">
                                    <input name="totalupset[]" type="checkbox" value="{{ strval($v->傳回值) }}"
                                        {{ in_array(strval($v->傳回值), explode(',', $data['totalupset'] ?? '')) ? 'checked' : '' }}
                                        class="mr-2 text-orange-500 focus:ring-orange-500 flex-shrink-0">
                                    <span class="text-sm">{{ $v->資料 }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- 屋齡 -->
                <div class="mb-2 flex flex-col md:flex-row md:items-start">
                    <div class="w-full md:w-24 flex-shrink-0 mb-2 md:mb-0 md:mr-6">
                        <label
                            class="block text-sm font-bold text-gray-700 border border-gray-300 rounded px-2 py-1 text-center bg-gray-50">屋齡</label>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                            @foreach ($data['xmldoc']->xpath('//參數設定檔/屋齡/KIND') as $key => $v)
                                <label
                                    class="option-checkbox flex items-center px-2 py-1 border rounded hover:bg-gray-50 cursor-pointer transition-colors duration-200 whitespace-nowrap">
                                    <input name="houseage[]" type="checkbox" value="{{ strval($v->傳回值) }}"
                                        {{ in_array(strval($v->傳回值), explode(',', $data['houseage'] ?? '')) ? 'checked' : '' }}
                                        class="mr-2 text-orange-500 focus:ring-orange-500 flex-shrink-0">
                                    <span class="text-sm">{{ $v->資料 }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- 關鍵字搜尋 -->
                <div class="mb-3 flex flex-col md:flex-row md:items-start">
                    <div class="w-full md:w-24 flex-shrink-0 mb-2 md:mb-0 md:mr-6">
                        <label
                            class="block text-sm font-bold text-gray-700 border border-gray-300 rounded px-2 py-1 text-center bg-gray-50">關鍵字</label>
                    </div>
                    <div class="flex-1 min-w-0">
                        <input type="text" name="search" value="{{ $data['search'] }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors duration-200 text-base"
                            placeholder="路段，車站，捷運，商圈">
                    </div>
                </div>

                <!-- 搜尋按鈕 -->
                <div class="text-center mb-2">
                    <button type="submit"
                        class="bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-6 rounded-lg text-base transition-colors duration-200">
                        <i class="fas fa-search mr-2"></i>搜尋
                    </button>
                </div>

            </div><!-- //newSearch -->
        </form>
    </div>


    <script>
        document.addEventListener("DOMContentLoaded", () => {
            // 標籤切換功能
            const tabs = document.querySelectorAll(".property-tab");
            tabs.forEach(tab => {
                tab.addEventListener("click", () => {
                    // 移除所有標籤的 active 狀態
                    tabs.forEach(t => {
                        t.classList.remove("active", "bg-orange-500", "text-white");
                        t.classList.add("bg-gray-200", "text-gray-600");
                    });
                    // 添加當前標籤的 active 狀態
                    tab.classList.add("active", "bg-orange-500", "text-white");
                    tab.classList.remove("bg-gray-200", "text-gray-600");
                });
            });

            // 選項點擊效果 - 更新為 Tailwind CSS 樣式
            const checkboxLabels = document.querySelectorAll(".option-checkbox, .district-checkbox");
            checkboxLabels.forEach(label => {
                const input = label.querySelector("input");

                // 初始化已選中項目的樣式
                if (input && input.checked) {
                    label.classList.add("bg-red-100", "border-red-300");
                }

                // 點擊事件
                label.addEventListener("click", function() {
                    setTimeout(() => {
                        if (input.checked) {
                            this.classList.add("bg-red-100", "border-red-300");
                        } else {
                            this.classList.remove("bg-red-100", "border-red-300");
                        }
                    }, 10);
                });
            });

            // 城市按鈕初始化
            let activeIndex = 0;
            const activeButton = document.querySelector(".city-btn.active");
            if (activeButton) {
                activeIndex = Array.from(document.querySelectorAll(".city-btn")).indexOf(activeButton);
            }

            // 觸發預設城市選擇
            const cityButtons = document.querySelectorAll(".city-btn");
            if (cityButtons[activeIndex]) {
                cityButtons[activeIndex].click();
            }
        });

        // 標記是否為第一次載入
        let isFirstLoad = true;

        function onCity1(button, city1title) {
            // 隱藏所有區域容器
            document.querySelectorAll(".district-container").forEach(container => {
                container.classList.add("hidden");
            });

            // 顯示選中城市的區域容器
            const targetContainer = document.getElementById("city1_" + city1title);
            if (targetContainer) {
                targetContainer.classList.remove("hidden");
            }

            // 更新隱藏輸入框的值
            document.querySelector("input[name='city1title']").value = city1title;

            // 只有在非第一次載入時才清除所有區域的選擇
            if (!isFirstLoad) {
                document.querySelectorAll(".district-container input[type='checkbox']").forEach(input => {
                    input.checked = false;
                });
                document.querySelectorAll(".district-checkbox").forEach(label => {
                    label.classList.remove("bg-red-100", "border-red-300");
                });
            }

            // 第一次載入後設定為 false
            isFirstLoad = false;

            // 更新城市按鈕樣式
            document.querySelectorAll(".city-btn").forEach(btn => {
                btn.classList.remove("active", "bg-orange-500", "text-white");
                btn.classList.add("bg-white", "text-gray-700");
            });
            button.classList.add("active", "bg-orange-500", "text-white");
            button.classList.remove("bg-white", "text-gray-700");
        }
    </script>
</section>
