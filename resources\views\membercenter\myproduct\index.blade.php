@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')
    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">我的收藏</h2>

    <!-- 主要內容區 -->
    <div class="min-h-screen bg-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- 標題區 -->
            <div class="mb-8">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">
                            <span class="text-primary">{{ Auth::guard('member')->user()->name }}</span>，您好
                        </h1>
                        <p class="mt-2 text-lg text-gray-600">
                            法拍收藏（<span class="font-semibold text-primary">{{ count($data['rows']) }}</span>）
                        </p>
                    </div>
                </div>
            </div>

            <form name="oForm" id="oForm" method="post" language="javascript"
                action="{{ \request()->middlewareurl }}myproduct/destroy" novalidate>

                @if (count($data['rows']) > 0)
                    <!-- 桌面版表格 -->
                    <div class="hidden lg:block bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            基本資料</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            拍次</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            開標日</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            開標結果</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            法拍屋地址</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            類型</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            總坪數</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            公告底價</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            單價/萬</th>
                                        <th
                                            class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach ($data['rows'] as $rs)
                                        <tr class="hover:bg-gray-50 transition-colors">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <a href="{{ url('/') }}/house/show/{{ $rs->productid }}"
                                                    title="{{ $rs->city1title }}{{ $rs->city2title }}{{ $rs->producttitle }}"
                                                    class="block w-20 h-20 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                                                    {{ Html::myUIImage([
                                                        'folder' => 'https://www.ebayhouse.com.tw/images/product',
                                                        'filename' => collect(explode(',', $rs->img))->last(),
                                                        'alt' => $rs->title,
                                                        'noimg' => 'no-picture.gif',
                                                        'class' => 'w-full h-full object-cover',
                                                    ]) }}
                                                </a>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                                {{ $rs->beattime }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $rs->tenderdate }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span
                                                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                    {{ $rs->auctions }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900 max-w-xs">
                                                <div class="truncate">
                                                    {{ $rs->city1title }}{{ $rs->city2title }}{{ $rs->address }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $rs->pattern }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $rs->pingtotalnumberof }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                                                {{ $rs->totalupset }}萬</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $rs->floorprice }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button type="button"
                                                    onclick="$('#oForm input[name=\'del\']').val({{ $rs->productid }}); $('#oForm').submit();"
                                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                                    <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                        </path>
                                                    </svg>
                                                    取消收藏
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 手機版卡片布局 -->
                    <div class="lg:hidden space-y-4">
                        @foreach ($data['rows'] as $rs)
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                                <div class="p-6">
                                    <div class="flex space-x-4">
                                        <!-- 圖片 -->
                                        <div class="flex-shrink-0">
                                            <a href="{{ url('/') }}/house/show/{{ $rs->productid }}"
                                                title="{{ $rs->city1title }}{{ $rs->city2title }}{{ $rs->producttitle }}"
                                                class="block w-20 h-20 rounded-lg overflow-hidden shadow-sm">
                                                {{ Html::myUIImage([
                                                    'folder' => 'https://www.ebayhouse.com.tw/images/product',
                                                    'filename' => collect(explode(',', $rs->img))->last(),
                                                    'alt' => $rs->title,
                                                    'noimg' => 'no-picture.gif',
                                                    'class' => 'w-full h-full object-cover',
                                                ]) }}
                                            </a>
                                        </div>

                                        <!-- 內容 -->
                                        <div class="flex-1 min-w-0">
                                            <div class="space-y-2">
                                                <div class="flex items-center justify-between">
                                                    <span class="text-sm font-medium text-gray-900">第 {{ $rs->beattime }}
                                                        拍</span>
                                                    <span
                                                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                        {{ $rs->auctions }}
                                                    </span>
                                                </div>

                                                <div class="text-sm text-gray-600">
                                                    <div class="flex items-center space-x-1 mb-1">
                                                        <svg class="h-4 w-4 text-gray-400" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                                            </path>
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z">
                                                            </path>
                                                        </svg>
                                                        <span
                                                            class="truncate">{{ $rs->city1title }}{{ $rs->city2title }}{{ $rs->address }}</span>
                                                    </div>
                                                    <div class="flex items-center space-x-1 mb-1">
                                                        <svg class="h-4 w-4 text-gray-400" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                                            </path>
                                                        </svg>
                                                        <span>開標日：{{ $rs->tenderdate }}</span>
                                                    </div>
                                                    <div class="flex items-center space-x-1">
                                                        <svg class="h-4 w-4 text-gray-400" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                                            </path>
                                                        </svg>
                                                        <span>{{ $rs->pattern }} · {{ $rs->pingtotalnumberof }} 坪</span>
                                                    </div>
                                                </div>

                                                <div class="flex items-center justify-between">
                                                    <div class="flex flex-col">
                                                        <span
                                                            class="text-lg font-bold text-green-600">{{ $rs->totalupset }}萬</span>
                                                        <span class="text-xs text-gray-500">單價 {{ $rs->floorprice }}
                                                            萬/坪</span>
                                                    </div>
                                                    <button type="button"
                                                        onclick="$('#oForm input[name=\'del\']').val({{ $rs->productid }}); $('#oForm').submit();"
                                                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                                                        <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                            </path>
                                                        </svg>
                                                        取消收藏
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <!-- 空狀態 -->
                    <div class="text-center py-12">
                        <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">尚無收藏物件</h3>
                        <p class="text-gray-500 mb-6">您還沒有收藏任何法拍屋物件，快去瀏覽精選物件吧！</p>
                        <a href="{{ url('/') }}"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            瀏覽物件
                        </a>
                    </div>
                @endif

                {{ Form::hidden('del', $data['del']) }}
            </form>

            <!-- 分頁 -->
            @if (method_exists($data['rows'], 'links') && $data['rows']->hasPages())
                <div class="mt-8">
                    {{ $data['rows']->links('layouts.paginate') }}
                </div>
            @endif
        </div>
    </div>
@endsection
