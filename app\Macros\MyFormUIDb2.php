<?php

namespace App\Macros;

use DB;

/***
"功能名稱":"共用類別-Html 產生控制項",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
 ***/

/*
<style>
    .select2-results__option {
        float: left;
        width: 20%;
    }
</style>
{{
Form::myUIDb([
    'type' => 'select2multiple',
    'title' =>'不能評分作者',
    'sql' => "select id,name from judge where ratingproject_id=".$data['ratingproject_id']." order by name",
    'name' => 'no_rating_authors',
    'value' => $data['no_rating_authors'],
    'id'=>'no_rating_authors',

    'required'=>true,
 ])
}}
*/

class MyFormUIDb2 {
    public $i = 0;
    protected $arr;

    public function __construct($form, $arr) {
        $this->arr = $arr;
    }

    public function createHtml() {
        $html = '';
        // if ('' == $this->arr['sql'] && '' == $this->arr['url']) {
        //     $html .= 'sql command or url is null';

        //     return;
        // }
        $this->arr['type'] = strtolower($this->arr['type']);

        $i = 0;

        if (\Str::contains($this->arr['requiredclass'], ['required[1,'])) {
            $this->arr['required'] = true;
        }
        if (in_array($this->arr['type'], ['select3']) == false) {
            if ('' == $this->arr['class']) {
                $this->arr['class'] = 'form-control';
            }
        }
        if ('' == $this->arr['firsttxt']) {
            $this->arr['firsttxt'] = __('請選擇') . '..';
        }
        if ((in_array($this->arr['type'], ['checkbox', 'select3']))) {
            $html .= "<input type='hidden' name='" . $this->arr['name'] . "[]'>";
        }

        $this->arr['type'] = strtolower($this->arr['type']);

        $this->arr['v-modelname'] = $this->arr['name'];

        $isselect = false;
        if (substr_count($this->arr['type'], 'select') > 0 || substr_count($this->arr['type'], 'multiple') > 0) {
            $isselect = true;
            $name1 = $this->arr['name'];
            $name1 = str_replace('[]', '', $name1);
            if (\Str::contains($this->arr['type'], ['selectautocomplete', 'select2'])) {
                $this->arr['select2'] = 'true';
                if ('' == $GLOBALS['isselet2']) {
                    $html .= '<link href="' . asset('select2/css/select2.css') . "?d=2\" rel=\"stylesheet\" />\n";

                    $html .= '<script src="' . asset('select2/js/select2.min.js') . "?d=2\"></script>\n";
                }
                $GLOBALS['isselet2'] = true;
            } elseif (in_array($this->arr['type'], ['select3'])) {
                $this->arr['select3'] = 'true';
                $this->arr['firstdisplayflag'] = 'N';
                if ('' == $GLOBALS['isselet3']) {
                    $html .= '<link href="' . asset('assets/select3/jquery.multiselect.css') . "\" rel=\"stylesheet\" />\n";

                    $html .= '<script src="' . asset('assets/select3/jquery.multiselect.js') . "\"></script>\n";
                }
                $GLOBALS['isselet3'] = true;
            }
            if (in_array($this->arr['type'], ['select3']) || \Str::contains($this->arr['type'], ['multiple'])) {
                $html .= '<select multiple style="min-width:250px;" name="' . $this->arr['name'] . '[]"';
            } else {
                $html .= '<select name="' . $this->arr['name'] . '"';
                $html .= ' id="' . $this->arr['name'] . '"';
            }
            if ('' != $this->arr['required'] && true == $this->arr['required']) {
                $html .= ' required ';
            }
            //if (false == \Str::contains($this->arr['type'], ['selectautocomplete', 'select2'])) {
            // if ('' == $this->arr['v-model']) {
            //     $html .= " v-model='inputs.".$name1."' ";
            // }
            // }

            foreach ($this->arr as $_key => $_value) {
                if (false == in_array($_key, ['rows', 'required', 'v-modelname', 'name', 'firsttxt', 'xmldoc', 'node', 'type', 'linecount', 'sql', 'value'])) {
                    $html .= ' ' . $_key . '="' . $_value . '"';
                }
            }

            $html .= ">\n";

            if ('' == $this->arr['firstdisplayflag']) {
                $html .= '<option value="">' . __($this->arr['firsttxt']) . '</option>' . PHP_EOL;
            }
        }

        if ('' != $this->arr['sql']) {
            if ('' != $this->arr['connection']) {
                $pdo = DB::connection($this->arr['connection'])->getPdo();
            } else {
                $pdo = DB::getPdo();
            }
            $rows = $pdo->prepare($this->arr['sql']);
            $rows->setFetchMode(\PDO::FETCH_NUM);
            $rows->execute();
            $rows = $rows->fetchAll();
        } elseif ('' != $this->arr['rows']) {
            $rows = $this->arr['rows'];
        } else {
            throw new \CustomException('no sql or rows data');
        }

        $n = 0;
        $vales = [];
        if (is_array($this->arr['value'])) {
            $vales = $this->arr['value'];
        } else if ($this->arr['value']) {
            $vales = explode(',', $this->arr['value']);
        }

        foreach ($rows as $rs) {
            if (in_array($this->arr['type'], ['checkbox', 'radio'])) {
                $html .= '<div class="form-check form-check-inline">' . PHP_EOL;
            }
            if ($rs instanceof \stdClass) {
                $rs = get_object_vars($rs); //to rs['XX'];
            }

            $ischeckstatus = 0;
            $keystr = array_values($rs)[0];
            //\PF::printr(['keystr', $rs, array_values($rs)[0], $keystr]);

            if ('' == $keystr) {
                $keystr = $rs[1];
            }
            if (1 == count($rs)) {
                $text = array_values($rs)[0];
            } else {
                $text = array_values($rs)[1];
            }

            if ('' != $this->arr['value']) {
                if (count($vales) > 0) {
                    if (in_array(trim($keystr), $vales)) {
                        $ischeckstatus = 1;
                    }
                }
            }

            if ($isselect) {
                $html .= "<option value=\"$keystr\"";

                if (1 == $ischeckstatus) {
                    $html .= ' selected';
                }
                $html .= '>';
                $html .= $text . '</option>' . PHP_EOL;
            } else {

                $html .= '<input ';
                $html .= 'name="' . $this->arr['name'];
                if ('checkbox' == $this->arr['type']) {
                    $html .= '[]';
                }
                $html .= '" type="' . $this->arr['type'] . '" value="' . htmlspecialchars($keystr) . '"';
                $html .= ' id="' . $this->arr['name'] . '_' . $n . '"';


                if ('checkbox' != $this->arr['type']) {
                    if ('' != $this->arr['required'] && true == $this->arr['required']) {
                        $html .= ' required ';
                    }
                } else {
                    if ('' != $this->arr['required'] && true == $this->arr['required']) {
                        $html .= ' requiredclass="[1,TEXT]" ';
                    }
                }
                foreach ($this->arr as $_key => $_value) {
                    if (false == in_array($_key, ['rows', 'required', 'fieldtitle', 'firsttxt', 'v-modelname', 'name',  'xmldoc', 'node', 'type', 'linecount', 'sql', 'value', 'firsttxt', 'v-model', 'class'])) {
                        $html .= ' ' . $_key . '="' . $_value . '"';
                    }
                }
                $html .= ' class="form-check-input" ';
                if ('' != $this->arr['v-model']) {
                    $html .= " v-model='" . $this->arr['v-model'] . "' ";
                    if ('checkbox' == $this->arr['type']) {
                        $html .= " :true-value=\"'" . htmlspecialchars($keystr) . "'\" ";
                    }
                }
                if (1 == $ischeckstatus) {
                    $html .= ' checked ';
                }

                if (true == $this->arr['islg']) {
                    $text = __($text);
                }

                $html .= '>' . PHP_EOL;

                $html .= '<label class="form-check-label" for="' . $this->arr['name'] . '_' . $n . '">' . $text . '</label>' . PHP_EOL;
                ++$n;
            }

            if (in_array($this->arr['type'], ['checkbox', 'radio'])) {
                $html .= '</div>' . PHP_EOL;
                if ('' != $this->arr['linecount']) {
                    if (0 == ($i + 1) % $this->arr['linecount']) {
                        $html .= '<br/>';
                    }
                    ++$i;
                }
            }
        }

        $html .= "\n";
        if ($isselect) {
            $html .= '</select>' . PHP_EOL;
        }
        if (\Str::contains($this->arr['type'], ['select3', 'select2'])) {
            $html .= "<div class=\"invalid-tooltip\">" . __('請選擇一筆') . "</div>" . PHP_EOL;
        }

        // if ('checkbox' == $this->arr['type']) {
        //     $html .= "<input type='hidden' name='" . $this->arr['name'] . "'>" . PHP_EOL;
        // }

        return $html;
    }
}
