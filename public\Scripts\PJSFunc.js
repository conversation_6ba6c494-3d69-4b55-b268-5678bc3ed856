try {
    const { replace } = require("lodash");
} catch (error) { }
var FC_WebFolder = "";
var FC_TableCssModule = "2";
var extFile = "php";
var FC_LG = "zh";

function PF_print(str) {
    try {
        console.log(str);
    } catch (ex) { }
}
//去除左邊空白
function lTrim(str) {
    if (typeof str != "undefined") {
        if (str.charAt(0) == " ") {
            str = str.slice(1);
            str = lTrim(str);
        }
        return str;
    }
}

//去除右邊空白
function rTrim(str) {
    var iLength;
    if (typeof str != "undefined") {
        iLength = str.length;

        if (str.charAt(iLength - 1) == " ") {
            str = str.slice(0, iLength - 1);
            str = rTrim(str);
        }
        return str;
    }
}
//去除兩邊空白
function trim(str) {
    return lTrim(rTrim(str));
}

//checkBox 全選
function checkAll(field) {
    field.checked = true;
    for (i = 0; i < field.length; i++) field[i].checked = true;
}
//checkBox 全不選
function uncheckAll(field) {
    field.checked = false;
    for (i = 0; i < field.length; i++) field[i].checked = false;
}

function checkbox(TxnID) {
    if (typeof TxnID.checked == "undefined") {
        var count = 0;
        for (i = 0; i < TxnID.length; i++) {
            if (jQuery(TxnID[i]).is(":visible")) {
                if (TxnID[i].checked == true && TxnID[i].disabled == false) {
                    count++;
                }
            }
        }
        if (count <= 0) {
            return false;
        } else {
            return true;
        }
    } else {
        if (!TxnID.checked) {
            return false;
        } else {
            return true;
        }
    }
}

function PF_isDate(dateStr) {
    dateStr = dateStr.replace("-", "/");
    var dateObj = dateStr.split("/"); // yyyy/mm/dd

    //列出12個月，每月最大日期限制
    var limitInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    var theYear = parseInt(dateObj[0]);
    var theMonth = parseInt(dateObj[1]);
    var theDay = parseInt(dateObj[2]);
    var isLeap = new Date(theYear, 1, 29).getDate() === 29; // 是否為閏年?

    if (isLeap) {
        // 若為閏年，最大日期限制改為 29
        limitInMonth[1] = 29;
    }

    // 比對該日是否超過每個月份最大日期限制
    return theDay <= limitInMonth[theMonth - 1];
}
/*
/var m1 = PF_WindowOpen(title, url, width, height) ;
m1.on("hidden.bs.modal", function (e) {
window.location.reload();
});
*/

// ------------------------------
// 數字檢核函數
// ------------------------------

function PF_IsNum(sString) {
    sString = sString.replace(",", "");
    if (isNaN(sString)) {
        return false;
    }
    sString = Math.abs(sString);
    for (var i = 0; i < sString.length; i++) {
        if (sString.charCodeAt(i) < 48 || sString.charCodeAt(i) > 57) {
            return false;
        }
    }
    return true;
}

function PF_IsFloat(sString) {
    if (isNaN(sString)) {
        return false;
    }

    for (var i = 0; i < sString.length; i++) {
        if (
            (sString.charCodeAt(i) < 48 || sString.charCodeAt(i) > 57) &&
            sString.charCodeAt(i) != 46
        ) {
            return false;
        }
    }
    return true;
}
// ------------------------------
// 英文字母檢核函數
// ------------------------------

function PF_IsLetter(sString) {
    sString = sString.toUpperCase();
    for (var i = 0; i < sString.length; i++) {
        if (sString.charCodeAt(i) < 65 || sString.charCodeAt(i) > 90) {
            return false;
        }
    }
    return true;
}

// ------------------------------
// 數字與英文字母檢核函數
// ------------------------------

function PF_IsChar(sString) {
    for (var i = 0; i < sString.length; i++) {
        if (
            (sString.charCodeAt(i) < 48 || sString.charCodeAt(i) > 57) &&
            (sString.charCodeAt(i) < 65 || sString.charCodeAt(i) > 90) &&
            (sString.charCodeAt(i) < 97 || sString.charCodeAt(i) > 122)
        ) {
            return false;
        }
    }
    return true;
}

function PF_IsNull(Str) {
    x = "" + trim(Str);

    if (x == "") {
        return false;
    } else {
        return true;
    }
}

function PF_VerifyEMail(strEMail) {
    var charCanUse =
        "-.0123456789@ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz";
    var intIndex = strEMail.indexOf("@");
    if (strEMail.length < 5) {
        return false;
    }

    if (intIndex < 1) {
        return false;
    }

    if (intIndex != strEMail.lastIndexOf("@")) {
        return false;
    }

    if (strEMail.charAt(intIndex - 1) == ".") {
        return false;
    }

    var strTemp = strEMail.substr(intIndex + 1);
    if (strTemp.indexOf(".") < 1) {
        return false;
    }

    if (strTemp.indexOf("..") != -1) {
        return false;
    }

    var i;
    for (i = 0; i < strEMail.length; i++) {
        if (charCanUse.indexOf(strEMail.charAt(i)) == -1) {
            return false;
        }
    }
    return true;
}

//checkBox 全選
function checkAll(field) {
    field.checked = true;
    for (i = 0; i < field.length; i++) field[i].checked = true;
}
//checkBox 全不選
function uncheckAll(field) {
    field.checked = false;
    for (i = 0; i < field.length; i++) field[i].checked = false;
}

//鎖右鍵
//document.oncontextmenu=eventfalse;
//鎖左鍵
//document.onselectstart=eventfalse;
function eventfalse() {
    window.event.returnValue = false;
}

//檢查生日年月日
function IsLeapyear(Year) {
    if ((Year % 4 == 0 && Year % 100 != 0) || Year % 400 == 0) return true;
    else return false;
}

function PF_BirthDay(Year, Month, Day) {
    if (Month > 12) return false;
    var lDay = 0;
    MonthArray = new Array(
        "31",
        "28",
        "31",
        "30",
        "31",
        "30",
        "31",
        "31",
        "30",
        "31",
        "30",
        "31"
    );
    lDay = MonthArray[Month - 1];
    if (Month == 2 && IsLeapyear(Year)) lDay = lDay + 1;
    if (Day > lDay) {
        return false;
    } else return true;
}

// ------------------------------
// 檢查UID
// 檢查國民身份證編號、營利事業統一編號、護照號碼
// ------------------------------

function PF_IsUid(strUid) {
    if (strUid.length < 8 || strUid.length == 9) {
        return false;
    }

    if (strUid.length == 8) {
        if (!PF_CheckBAN(strUid)) {
            return false;
        }
    } else {
        if (strUid.length == 10) {
            if (!PF_CheckID(strUid)) {
                return false;
            }
        } else {
            if (PF_IsLetter(strUid.charAt(0))) {
                if (strUid.charAt(10) != "3") {
                    return false;
                }

                if (!PF_CheckID(strUid.substr(0, 10))) {
                    return false;
                }
            } else {
                if (!PF_IsNum(strUid.substr(0, 8))) {
                    strReason = "護照號碼第一碼至第八碼必須全部為數字";
                    return false;
                }

                if (!PF_IsLetter(strUid.substr(8, 2))) {
                    strReason = "護照號碼第九碼和第十碼必須是英文字母";
                    return false;
                }

                if (!PF_IsNum(strUid.charAt(10))) {
                    strReason = "護照號碼最後一碼必須是數字";
                    return false;
                }
            }
        }
    }
    return true;
}

// ------------------------------
// 國民身份證編號驗證
// ------------------------------

function PF_CheckID(strUserID) {
    var intAreaNo; //區域碼變數
    var intCheckSum; //檢核碼變數
    var intCount; //計數變數
    var strAreaCode; //區域碼變數
    // var blnCheckID = false;    //設定起始值

    strUserID = strUserID.toUpperCase(); //轉換為大寫
    strAreaCode = strUserID.charAt(0); //取得首碼字母

    //確定身份證有10碼
    if (strUserID.length != 10) {
        strReason = "國民身份證號碼必須是十碼";
        return false;
    }

    //確定首碼在A-Z之間
    if (strAreaCode < "A" || strAreaCode > "Z") {
        strReason = "國民身份證號碼第一碼必須是英文字母";
        return false;
    }

    //確定2-10碼是數字
    for (intCount = 1; intCount < 10; intCount++) {
        if (
            strUserID.charAt(intCount) < "0" ||
            strUserID.charAt(intCount) > "9"
        ) {
            strReason = "國民身份證號碼第二碼至第十碼必須全部為數字";
            return false;
        }
    }

    intAreaNo = "ABCDEFGHJKLMNPQRSTUVXYWZIO".indexOf(strAreaCode) + 10; //取得英文字母對應編號，A->10,B->11等等
    strUserID = intAreaNo + strUserID.substr(1, 9); //組合字串
    intCheckSum =
        parseInt(strUserID.charAt(0)) + parseInt(strUserID.charAt(10)); //計算首尾二者之和

    //計算第二碼至第十碼之積
    for (intCount = 1; intCount < 10; intCount++) {
        intCheckSum += parseInt(strUserID.charAt(intCount)) * (10 - intCount);
    }

    //檢查是否為10整除
    if (intCheckSum % 10 == 0) {
        return true;
    } else {
        strReason = "國民身份證號碼輸入錯誤，請再檢查";
        return false;
    }
}

// ------------------------------
// 營利事業統一編號邏輯檢查
// ------------------------------

function PF_CheckBAN(strBAN) {
    var intMod; //餘數變數
    var intSum; //合計數變數
    var intX = new Array(1, 2, 1, 2, 1, 2, 4, 1);
    var intY = new Array(7);
    // var blnCheckBAN = false;
    var intCount; //計數變數

    if (strBAN.length != 8) {
        strReason = "營利事業統一編號必須是八碼";
        return false;
    }

    for (intCount = 0; intCount < 8; intCount++) {
        if (strBAN.charAt(intCount) < "0" || strBAN.charAt(intCount) > "9") {
            strReason = "輸入之營利事業統一編號中有非數字";
            return false;
        }
    }

    for (intCount = 0; intCount < 8; intCount++) {
        intX[intCount] *= parseInt(strBAN.charAt(intCount));
    }

    intY[0] = parseInt(intX[1] / 10);
    intY[1] = intX[1] % 10;
    intY[2] = parseInt(intX[3] / 10);
    intY[3] = intX[3] % 10;
    intY[4] = parseInt(intX[5] / 10);
    intY[5] = intX[5] % 10;
    intY[6] = parseInt(intX[6] / 10);
    intY[7] = intX[6] % 10;

    intSum = intX[0] + intX[2] + intX[4] + intX[7] + intY[0] + intY[1] + intY[2] + intY[3] + intY[4] + intY[5] + intY[6] + intY[7];

    intMod = intSum % 10;

    if (strBAN.charAt(6) == "7") {
        if (intMod == 0) {
            return true;
        } else {
            intSum = intSum + 1;
            intMod = intSum % 10;
            if (intMod == 0) {
                return true;
            } else {
                strReason = "營利事業統一編號輸入錯誤，請再檢查";
                return false;
            }
        }
    } else {
        if (intMod == 0) {
            return true;
        } else {
            strReason = "營利事業統一編號輸入錯誤，請再檢查";
            return false;
        }
    }
}

//依資料排序
function SortoForm_onsubmit(e) {
    var sortname = "";
    if (typeof e != "undefined") {
        var srcEl = e.srcelement ? e.srcelement : e.target;
        sortname = srcEl.id;
        if (sortname == "") {
            sortname = e.currentTarget.id;
        }
    } else {
        e = window.event;
        sortname = e.srcElement.id;
    }
    if (typeof document.forms["SortoForm"].sorttype != "undefined") {
        document.forms["SortoForm"].sortname.value = sortname;
        if (document.forms["SortoForm"].sorttype.value == "desc") {
            document.forms["SortoForm"].sorttype.value = "";
        } else {
            document.forms["SortoForm"].sorttype.value = "desc";
        }
    } else if (
        typeof document.forms["SortoForm"].elements["sorttype"] != "undefined"
    ) {
        document.forms["SortoForm"].elements["sortname"].value = sortname;
        if (document.forms["SortoForm"].elements["sorttype"].value == "desc") {
            document.forms["SortoForm"].elements["sorttype"].value = "asc";
        } else {
            document.forms["SortoForm"].elements["sorttype"].value = "desc";
        }
    }
    if (document.forms["SortoForm"].elements["sortname"].value == "") {
        return;
    }
    document.forms["SortoForm"].submit();
}

function SortD(box) {
    var temp_opts = new Array();
    var temp = new Object();
    for (var i = 0; i < box.options.length; i++) {
        temp_opts[i] = box.options[i];
    }

    for (var x = 0; x < temp_opts.length - 1; x++) {
        for (var y = x + 1; y < temp_opts.length; y++) {
            if (temp_opts[x].text > temp_opts[y].text) {
                temp = temp_opts[x].text;
                temp_opts[x].text = temp_opts[y].text;
                temp_opts[y].text = temp;
            }
        }
    }

    for (var i = 0; i < box.options.length; i++) {
        box.options[i].value = temp_opts[i].value;
        box.options[i].text = temp_opts[i].text;
    }
}

function PJ_AjaxSelectJson(ob, url, selectedvalue) {
    jQuery(function ($) {
        jQuery.ajax({
            type: "post",
            url: url,
            dataType: "json",
            data: jQuery(ob[0].form).serialize(),
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
                Authorization:
                    "Bearer " + $('meta[name="api_token"]').attr("content"),
            },
            error: function (e) {
                console.log("PJ_AjaxSelectJson Error: url : " + url + "\n" + e);
            },
            success: function (obj) {
                var firsttxt = jQuery(ob).attr("firsttxt");
                if (typeof firsttxt == "undefined") {
                    firsttxt = "請選擇";
                }
                jQuery(ob).empty();
                jQuery(ob).append(
                    $("<option></option>").attr("value", "").text(firsttxt)
                );
                $.each(obj.data, function (sn, item) {
                    //    console.log(item.value);
                    if (selectedvalue == item.value) {
                        jQuery(ob).append(
                            $("<option selected></option>")
                                .attr("value", item.value)
                                .text(item.title)
                        );
                    } else {
                        jQuery(ob).append(
                            $("<option></option>")
                                .attr("value", item.value)
                                .text(item.title)
                        );
                    }
                });
            },
        });
    });
}

function PJ_AjaxSelectXml(ob, url) {
    jQuery(function ($) {
        jQuery.ajax({
            type: "post",
            url: url,
            dataType: "xml",
            error: function (e) {
                alert("Error: url : " + url + "\n" + e);
            },
            success: function (xml) {
                property = $(xml).find("Data > Record");
                jQuery(ob).empty();
                jQuery(ob).append(
                    $("<option></option>").attr("value", "").text("請選擇")
                );
                for (var i = 0, x = 1; i < property.length; i++, x++) {
                    jQuery(ob).append(
                        $("<option></option>")
                            .attr("value", $("傳回值", property[i]).text())
                            .text($("傳回值", property[i]).text())
                    );
                }
            },
        });
    });
}
//多層式下拉式(一對一)
function PJ_SelectThird(AxajUrl, KeyValue1, str, Field1, Field2, KeyValue2) {
    if (KeyValue1 == "") {
        return false;
    }
    var FieldTitle;
    if (AxajUrl == "") {
        AxajUrl = "PJ_SelectThird." + extFile;
    }

    var url = "";
    if (AxajUrl.indexOf("http") == -1) {
        url = FC_WebFolder;
    }

    url += AxajUrl;

    if (url.indexOf("?") > 0) {
        url += "&str=" + str + "&Key=" + PF_escape(KeyValue1);
    } else {
        url += "?str=" + str + "&Key=" + PF_escape(KeyValue1);
    }
    FieldTitle = $(Field1).find("option").eq(0).text();
    jQuery.ajax({
        type: "POST",
        url: url,
        dataType: "xml",
        error: function (e) {
            alert("Error: url : " + url + "\n" + e);
        },
        beforeSend: function () {
            $(Field1).empty();
            $(Field1).append(
                $("<option></option>").attr("value", "").text("Loading")
            );
        },
        success: function (xml) {
            try {
                property = $(xml).find("Data > Record");
                $(Field1).empty();
                $(Field1).append(
                    $("<option></option>")
                        .attr("value", "")
                        .text(PF_Lg("請選擇"))
                );

                if (property.length > 0) {
                    for (var i = 0, x = 1; i < property.length; i++, x++) {
                        aname = $("資料", property[i]).text();
                        avalue = $("傳回值", property[i]).text();
                        $(Field1).append(
                            $("<option></option>")
                                .attr("value", avalue)
                                .text(aname)
                        );
                        if (trim(avalue) == trim(KeyValue2)) {
                            $(Field1)[0].selectedIndex = i + 1;
                        }
                    }
                    if (typeof KeyValue2 != "undefined") {
                        KeyValue2split = KeyValue2.split(",");
                        for (i = 0; i < KeyValue2split.length; i++) {
                            if (trim(KeyValue2split[i]) != "") {
                                $(Field1).children("option[value=" + KeyValue2split[i] + "]").prop("selected", true);
                            }
                        }
                    }
                }
                if (typeof Field2 != "undefined" && Field2 != "") {
                    $(Field2).empty();
                    $(Field2).append($("<option></option>").attr("value", "").text(PF_Lg("請選擇")));
                }
            } catch (error) {
                console.log("error:" + error);
            }
        },
    });
}

function PF_Lg(str) {
    return str;
}

function PF_FormMulti(S1, S2, field, Text) {
    try {
        var SS = "";
        var Pform;
        if (typeof field == "undefined") {
            return true;
        }
        var fieldname = field.name;
        if (typeof fieldname == "undefined") {
            fieldname = $(field).attr("name");
        }
        var fieldtype = field.type;
        if (typeof fieldtype == "undefined") {
            fieldtype = $(field).attr("type");
        }
        if (typeof Text != "undefined") {
            if (Text != "") {
                Text = Text.replace(new RegExp("<BR>", "g"), " ");
                Text = Text.replace(new RegExp("<br>", "g"), " ");
            }
        }

        if (typeof Text == "undefined") {
            Text = "";
        }

        if (S1 == "1") {
            switch (fieldtype) {
                case "hidden":
                    break;
                case "text":
                case "tel":
                case "url":
                case "password":
                case "textarea":
                case "email":
                case "number":
                case "date":
                case "datetime-local":
                case "time":
                    if (PF_IsNull(field.value) == false) {
                        try {
                            field.focus();
                        } catch (e) { }
                        throw Text + " " + PF_Lg("未填");
                    }
                    if (jQuery(field).attr("maxlength")) {
                        if (field.value.length > jQuery(field).attr("maxlength") && jQuery(field).attr("maxlength") != -1) {
                            throw (Text + " " + PF_Lg("限制字數") + ":" + jQuery(field).attr("maxlength"));
                        }
                    }
                    break;
                case "file":
                    if (PF_IsNull(field.value) == false) {
                        throw PF_Lg("請上傳") + " " + Text;
                    }

                    break;
                case "select-one":
                case "select-multiple":
                    if (PF_IsNull(field.value) == false) {
                        field.focus();
                        throw PF_Lg("請選擇其一") + " " + Text;
                    }
                    break;
                default:
                    if (typeof field.name == "undefined") {
                        if (checkbox(field) == false) {
                            try {
                                field[0].focus();
                            } catch (e) {
                                field.focus();
                            }
                            try {
                                for (i = 0; i < field.length; i++) {
                                    if (jQuery(field[i]).is(":visible") && jQuery(field[i]).is(":disabled") == false) {
                                        field[i].focus();
                                        break;
                                    }
                                }
                            } catch (e) {
                                field.focus();
                            }
                            throw PF_Lg("請選擇其一") + " " + Text;
                        }
                        SS = "1";
                        break;
                    } else {
                        if (field.type == "hidden" || field.type == "range") {
                            break;
                        }
                        if (field.type != "checkbox" && typeof eval("field.form.hiddenfile" + fieldname + "_year") != "undefined") {
                            try {
                                if (eval("field.form." + field.name + "_year").value == "") {
                                    eval("field.form." + field.name + "_year").focus();
                                    throw "請選擇" + Text + "日期";
                                }
                                if (eval("field.form." + field.name + "_month").value == "") {
                                    eval("field.form." + field.name + "_month").focus();
                                    throw "請選擇" + Text + "日期";
                                }
                                if (eval("field.form." + field.name + "_day").value == "") {
                                    eval("field.form." + field.name + "_day").focus();
                                    throw "請選擇" + Text + "日期";
                                }
                                if (PF_isDate(eval("field.form." + field.name + "_year").value + "/" + eval("field.form." + field.name + "_month").value + "/" + eval("field.form." + field.name + "_day").value) == false) {
                                    eval("field.form." + field.name + "_day").focus();
                                    throw Text + "  日期輸入錯誤";
                                }
                            } catch (e) { }
                        } else {
                            if (checkbox(field) == false) {
                                for (i = 0; i < field.length; i++) {
                                    if (field[i].disabled == false) {
                                        field[i].focus();
                                        break;
                                    }
                                }
                                throw PF_Lg("請勾取其一") + " " + Text;
                            }
                            SS = "1";
                            break;
                        }
                    }
            }
        }

        if (SS == "") {
            // switch (fieldtype) {
            // 	case 'file':
            // 		if (PF_CheckFileType(field, S2) == false) {
            // 			throw PF_Lg('請上傳格式') + ' ' + Text + ' : ' + S2;
            // 		}
            // 		break;
            // }
            switch (S2) {
                case "INT": //數字
                case "FLOAT": //數字
                    if (fieldtype == "text" || fieldtype == "number") {
                        if (S2 == "FLOAT") {
                            if (PF_IsFloat(field.value) == false) {
                                field.focus();
                                throw PF_Lg("請輸入數字") + " " + Text;
                            }
                        } else if (S2 == "INT") {
                            if (PF_IsNum(field.value) == false) {
                                field.focus();
                                throw PF_Lg("請輸入數字") + " " + Text;
                            }
                        }
                        if (jQuery(field).attr("min")) {
                            if (parseInt(field.value, 10) < parseInt(jQuery(field).attr("min"), 10)) {
                                throw (Text + " " + PF_Lg("不得小於") + ":" + jQuery(field).attr("min"));
                            }
                        }
                        if (jQuery(field).attr("max")) {
                            if (parseInt(field.value, 10) > parseInt(jQuery(field).attr("max"), 10)) {
                                throw (Text + " " + PF_Lg("不得大於") + ":" + jQuery(field).attr("max"));
                            }
                        }
                    }
                    break;
                case "ENGINT": //英文數字
                    var validchar01 =
                        "1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
                    for (var i = 0; i < field.value.length; i++) {
                        var ch = field.value.charAt(i);
                        if (validchar01.indexOf(ch) < 0) {
                            field.focus();
                            throw Text + " " + PF_Lg("格式錯誤");
                        }
                    }
                    break;
                case "DATE": //日期
                    if (fieldtype == "text") {
                        if (PF_IsNull(field.value) == true) {
                            if (PF_isDate(field.value) == false) {
                                field.focus();
                                throw Text + " " + PF_Lg("格式錯誤");
                            }
                        }
                        if (jQuery(field).attr("min")) {
                            if (PF_isDate(field.value)) {
                                if (new Date(field.value) < new Date(jQuery(field).attr("min"))) {
                                    throw (Text + " " + PF_Lg("不得小於") + ":" + jQuery(field).attr("min"));
                                }
                            }
                        }
                        if (jQuery(field).attr("max")) {
                            if (PF_isDate(field.value)) {
                                if (new Date(field.value) > new Date(jQuery(field).attr("max"))) {
                                    throw (Text + " " + PF_Lg("不得大於") + ":" + jQuery(field).attr("max"));
                                }
                            }
                        }
                    } else {
                        try {
                            if (eval("field.form." + field.name + "_year").value != "" || eval("field.form." + field.name + "_month").value != "" || eval("field.form." + field.name + "_day").value != "") {
                                if (
                                    PF_isDate(eval("field.form." + field.name + "_year").value + "/" + eval("field.form." + field.name + "_month").value + "/" + eval("field.form." + field.name + "_day").value) == false) {
                                    eval("field.form." + field.name + "_day").focus();
                                    throw Text + PF_Lg("格式錯誤");
                                }
                            }
                        } catch (e) { }
                    }
                    break;
                case "EMAIL": //EMAIL
                    if (PF_IsNull(field.value) == true) {
                        if (PF_VerifyEMail(field.value) == false) {
                            field.focus();
                            throw Text + " " + PF_Lg("格式錯誤");
                        }
                    }
                    break;
                case "URL":
                    if (PF_IsNull(field.value) == true) {
                        var aurl = field.value.toLowerCase();
                        if (aurl.substring(0, 7) != "http://" && aurl.substring(0, 8) != "https://") {
                            field.focus();
                            throw PF_Lg("網址前面請輸入http://或https://格式");
                        }
                        var urlreg = /^[A-Za-z0-9\-]+\.[A-Za-z0-9]+[\/=\?%\-&_~`@[\]\':+!]*([^<>\"\"])*$/;
                        aurl = aurl.replace("http://", "");
                        aurl = aurl.replace("https://", "");
                        if (!urlreg.test(aurl)) {
                            field.focus();
                            throw PF_Lg("網址錯誤");
                        }
                    }
                    break;
                case "PASSWORD": //密碼
                    if (field.value == "") {
                        return;
                    }
                    if (field.value.length < 8 || field.value.length > 20) {
                        field.focus();
                        throw Text + " " + PF_Lg("長度必須為8~20位");
                    }
                    if (
                        PF_IsNum(field.value) ||
                        PF_IsChar(field.value.toUpperCase()) == false
                    ) {
                        field.focus();
                        throw (
                            Text +
                            " " +
                            PF_Lg(" 其中必須包含至少一位數字、一位英文")
                        );
                    }
                    break;
                case "ACCOUNT": //帳號
                    if (field.value != "") {
                        if (PF_IsLetter(field.value.charAt(0)) == false) {
                            field.focus();
                            throw Text + " " + PF_Lg("第一個字需為英文字母");
                        }
                        if (field.value.length < 3 || field.value.length > 20) {
                            field.focus();
                            throw (Text + " " + PF_Lg("請用3~20碼之英文字母或數字，英文有大小寫之分，切勿用全形和其它特殊符號，如.,!@#$%^&*()等"));
                        }
                        if (PF_IsChar(field.value.toUpperCase()) == false) {
                            field.focus();
                            throw Text + " 格式錯誤";
                        }
                    }
                    break;
                case "MOBILE": //手機
                    if (PF_IsNull(field.value) == true) {
                        if (PF_IsNum(field.value) == false) {
                            field.select();
                            throw PF_Lg("請輸入數字") + " " + Text;
                        }
                        if (field.value.length != 10) {
                            field.focus();
                            throw "對不起!" + Text + "長度必須等於十位.";
                        }
                        if (field.value.substring(0, 2) != "09") {
                            field.focus();
                            throw Text + " " + PF_Lg("格式錯誤");
                        }
                    }
                    break;
                case "TEL": //電話
                    if (PF_IsNull(field.value) == true) {
                        if (PF_CheckCompareStr("0123456789-()#", field.value) == false) {
                            //if (field.value.search(/^[0][1-9]{1,3}-([0-9]{7,8})+((#([0-9]){1,5}){0,1})$/) == -1) {
                            field.focus();
                            throw (Text + " " + PF_Lg("format : xxx-xxxxxxxx#分機"));
                        }
                    }
                    break;
                case "UID": //身份証
                    if (PF_IsNull(field.value) == true) {
                        strReason = "";
                        if (!PF_IsUid(field.value)) {
                            if (strReason == "") {
                                field.focus();
                                throw "請輸入正確的身分證或統編";
                            } else {
                                field.focus();
                                throw strReason;
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    } catch (err) {
        _alert(err);
        return false;
    }
}

function PF_CheckCompareStr(strTemp, str) {
    if (typeof str != "undefined") {
        for (var i = 0; i < str.length; i++) {
            if (strTemp.indexOf(str.charAt(i)) == -1) {
                return false;
            }
        }
    }
    return true;
}
function checkValidity(event, form) {
    var isOk = true;
    event.preventDefault()
    event.stopPropagation()
    var isTab = false;
    if ($("#nav-tab").length > 0) {
        isTab = true;
    }
    $(form).find('select, textarea, input:not([type="checkbox"])').each(function () {
        if (!this.checkValidity()) {
            if ($(this).is(":visible") == false) {
                if (isTab) {
                    var id = $(this).closest('[class^="tab-pane"]').attr('id');
                    $("a[href='#" + id + "']").trigger("click");
                }
            }
            console.log($(this).attr('name') + " 未填");
            $(this).addClass('is-invalid');
            isOk = false;
        } else {

            $(this).removeClass('is-invalid');
        }
    });
    var checkboxNames = [];

    $(form).find('input[type="checkbox"][required]').each(function () {
        var name = $(this).attr('name');
        if (typeof (name) != "undefined") {
            if (checkboxNames.includes(name) == false) {
                checkboxNames.push(name);
            }
        }

    });
    checkboxNames.forEach(name => {
        var cbxvalue = (jQuery(form).find("input:checkbox:checked[name='" + name + "']").map(function () { return $(this).val(); }).get()).toString();
        if (cbxvalue == "") {
            //console.log(["name", name]);
            jQuery(form).find("input[name='" + name + "']").addClass('is-invalid');
            isOk = false;
        } else {
            jQuery(form).find("input[name='" + name + "']").removeClass('is-invalid');
        }
    });

    var element = document.querySelector('input:invalid,select:invalid');
    if (element) {
        document.querySelector('input:invalid,select:invalid').focus();
    }

    return isOk;
}
//if (PF_FormMultiAll(form)==false){return false};
function PF_FormMultiAll(form) {
    var required = 0;
    var requiredstr = "";
    var requiredflag = "";
    var requiredtype = "";
    var controlname = "";
    var title = "";
    var fieldname = "";
    jQuery(form).find("[class^=required],[required],[requiredclass^=required]").each(function (i) {
        if (jQuery(this).is(":visible") && jQuery(this).is(":disabled") == false) {
            if (controlname != jQuery(this).attr("name")) {
                fieldname = jQuery(this).attr("name");

                if (typeof fieldname != "undefined") {
                    title = jQuery(this).attr("title");
                    if (typeof title == "undefined" || title == "") {
                        if (fieldname.indexOf("$") > -1) {
                            //asp.net
                            title = jQuery(".title_" + jQuery(this).attr("id").split("_")[0]).text();
                        } else {
                            title = jQuery(".title_" + fieldname.replace("[]", "")).text();
                        }
                    }

                    title = trim(title);

                    if (jQuery(this).attr("class") == "required") {

                        if (PF_FormMulti("1", "TEXT", form.elements[fieldname], title) == false) {
                            required = 1;
                            return false;
                        }
                    } else {
                        requiredstr = jQuery(this).attr("class");
                        if (jQuery(this).attr("requiredclass") != null && jQuery(this).attr("requiredclass").length > 0) {
                            requiredstr = jQuery(this).attr("requiredclass");
                        }
                        requiredstr = requiredstr.substring(
                            requiredstr.indexOf("[") + 1,
                            requiredstr.lastIndexOf("]")
                        );
                        var controlobj = form.elements[jQuery(this).attr("name")];

                        if (requiredstr.indexOf(",") > -1) {
                            requiredflag = requiredstr.split(",")[0];
                            requiredtype = requiredstr.split(",")[1];
                            if (fieldname.indexOf("$") > -1 && jQuery(this).attr("type") == "checkbox") {
                                //asp.net
                                controlobj = jQuery("input[id^='" + jQuery(this).attr("id").split("_")[0] + "']:checkbox");
                            } else {
                                if (requiredtype == "DATE") {
                                    if (fieldname.slice(-5) == "_year" && typeof form.elements[fieldname.substring(0, fieldname.indexOf("_year"))] != "undefined") {
                                        if (PF_FormMulti(requiredflag, "DATE", form.elements[fieldname.substring(0, fieldname.indexOf("_year"))], title) == false) {
                                            required = 1;
                                            return false;
                                        }
                                    }
                                }
                            }
                        } else {
                            requiredflag = 1;
                            requiredtype = requiredstr;
                        }

                        if (PF_FormMulti(requiredflag, requiredtype, controlobj, title) == false) {
                            required = 1;
                            return false;
                        }

                        //判斷是否重覆
                        if (typeof jQuery(this).attr("unique") != "undefined") {
                            if (jQuery(this).attr("unique") != jQuery(this).val()) {
                                if (PF_dbUnique(form, jQuery(this).attr("table"), jQuery(this), jQuery(this).attr("title")) == false) {
                                    required = 1;
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
            if (typeof jQuery(this).attr("name") != "undefined") {
                controlname = jQuery(this).attr("name");
            }
        }
    });
    jQuery(form).find("select[name*='_yy']").each(function (i) {
        fieldname = jQuery(this).attr("name");
        var name = fieldname.replace("_yy", "");
        if ($(this).val() != "" && form.elements[name + "_mm"].value != "" && form.elements[name + "_dd"].value != "") {
            form.elements[name].value = $(this).val() + "-" + form.elements[name + "_mm"].value + "-" + form.elements[name + "_dd"].value;
        }
    });

    if (required == 1) {
        return false;
    }
}
//取得控制項的值
function PF_GetFormValue(formId, name) {

    const form = document.getElementById(formId);
    const elements = form.elements;
    const values = {};

    for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const elementName = element.name;
        const elementType = element.type;

        if (elementType !== 'submit' && elementType !== 'reset' && elementType !== 'button') {

            if (name && elementName !== name) {
                continue;
            }

            if (elementType === 'checkbox') {
                if (element.checked) {
                    if (!values[elementName]) {
                        values[elementName] = [];
                    }
                    values[elementName].push(element.value);
                }
            } else if (elementType === 'select-multiple') {
                const selectedValues = [];

                for (let j = 0; j < element.options.length; j++) {
                    const option = element.options[j];

                    if (option.selected) {
                        selectedValues.push(option.value);
                    }
                }

                values[elementName] = selectedValues;
            } else {
                values[elementName] = element.value;
            }

            // if (name && elementName === name) {
            //     break;
            // }
        }
    }

    if (name != "") {
        return values[name];
    }

    return values;

}
//設定得控制項的值
function PF_SetFormValue(f, v) {
    switch (f.type) {
        case "text":
        case "tel":
        case "password":
        case "textarea":
            f.value = v;
            break;
        case "file":
            break;
        case "select-one":
        case "select-multiple":
            for (var i = 0; i < f.length; i++) {
                if (f.options[i].value != "" && f.options[i].value == v) {
                    f.selectedIndex = i;
                    break;
                }
            }
            break;
        default:
            if (f.length > 1) {
                for (i = 0; i < f.length; i++) {
                    if (PF_SplitCompare(v, f[i].value)) {
                        f[i].checked = true;
                    }
                }
            } else {
                if (f.value == v) {
                    f.checked = ture;
                }
            }
            break;
    }
}
//字串對比PF_SplitCompare(大量值,單一值)
function PF_SplitCompare(SplitValue, CompareValue) {
    SplitValue = SplitValue.toString();
    if (SplitValue == "") {
        return false;
    }
    var f = SplitValue.replace(";", ",");
    f = f.split(",");
    for (i = 0; i < f.length; i++) {
        if (f[i] == CompareValue) {
            return true;
            break;
        }
    }
    return false;
}

//if (PF_dbCheckQual(form,'xx',form.xx,'xx')==false){return false;}
function PF_dbCheckQual(form, DbTable, Field, FieldText) {
    var tmp = true;
    if (typeof Field == "undefined") {
        return null;
    }
    if (typeof Field == "undefined") {
        return null;
    }
    if (Field.value == "") {
        return;
    }
    if (eval("form." + Field.name + "1").value != Field.value) {
        var url = FC_WebFolder + "api/db/isunique";
        var sendData = "DbTable=" + DbTable + "&Field=" + Field.name + "&Fieldvalue=" + Field.value;

        jQuery.ajax({
            type: "POST",
            url: url,
            data: sendData,
            dataType: "xml",
            async: false,
            error: function (e) {
                alert("Error: url : " + url + "?" + sendData + "\n" + e);
            },
            beforeSend: function () {
                $("#loading").toggle();
            },
            success: function (xml) {
                $("#loading").toggle();
                property = $(xml).find("Data");
                value = $("item", property).text();
                if (value == "Y") {
                    alert(FieldText + " " + PF_Lg("已經存在"));
                    Field.focus();
                    tmp = false;
                }
            },
        });
    }
    return tmp;
}
//if (PF_dbUnique(form,'xx',form.xx,'xx')==false){return false;}
function PF_dbUnique(form, DbTable, Field, FieldText) {
    var tmp = true;
    if (typeof Field == "undefined") {
        return null;
    }
    var url = FC_WebFolder + "api/db/isunique";
    // var sendData = "DbTable=" + DbTable + "&Field=" + $(Field).attr('name') + "&Fieldvalue=" + $(Field).val();
    var dict = {
        url: url,
        dataType: "xml",
        async: false,
        data: {
            DbTable: DbTable,
            Field: $(Field).attr("name"),
            Fieldvalue: $(Field).val(),
        },
    };
    if (typeof FieldText == "undefined") {
        FieldText = "";
    }
    PF_ajax(dict)
        .done(function (obj) {
            property = $(obj).find("Data");
            value = $("item", property).text();
            if (value == "Y") {
                _alert(FieldText + " " + PF_Lg("已經存在"));
                Field.focus();
                tmp = false;
            }
        })
        .fail(function (resp) {
            tmp = false;
            _alert(resp.statusText);
        });
    return tmp;
}

function PF_SortoFormClassName(form) {
    jQuery(".oFormTable th[id], .oFormTableRwd th[id]").addClass(
        "TitleBgcolorSort"
    );
    jQuery(".oFormTable th[id], .oFormTableRwd th[id]").click(
        SortoForm_onsubmit
    );
    if (jQuery("form input[name='Sort_Name']").length > 0) {
        if (form.Sort_Name.value != "") {
            if (form.Sort_Name.value != "" && form.Sort_Type.value == "desc") {
                document.getElementById(form.Sort_Name.value).className =
                    "TitleBgcolorSort2";
            } else if (form.Sort_Name.value != "") {
                document.getElementById(form.Sort_Name.value).className =
                    "TitleBgcolorSort1";
            }
        }
    }
    jQuery(".table th[id], .oFormTable th[id], .oFormTableRwd th[id]").click(
        SortoForm_onsubmit
    );
    if (jQuery("form input[name='sortname']").length > 0) {
        if (form.sortname.value != "") {
            var title = jQuery("#" + form.sortname.value).html();
            //console.log(title);
            if (form.sortname.value != "" && form.sorttype.value == "desc") {
                jQuery("#" + form.sortname.value).html(
                    title + ' <i class="fa fa-fw fa-sort-down"></i>'
                );
                //document.getElementById(form.sortname.value).className = 'TitleBgcolorSort2';
            } else if (form.sortname.value != "") {
                jQuery("#" + form.sortname.value).html(
                    title + ' <i class="fa fa-fw fa-sort-up"></i>'
                );
                //document.getElementById(form.sortname.value).className = 'TitleBgcolorSort1';
            }
        }
    }
    jQuery(".table th[id], .oFormTable th[id], .oFormTableRwd th[id]").each(
        function (i) {
            if ($(this).find("i").length == 0) {
                var title = $(this).html();
                $(this).html(title + '<i class="fa fa-fw fa-sort"></i>');
            }
        }
    );
}

function PF_CheckFileType(f, limitFile) {
    //limitFile = $(f).attr('limitext').replace(/,/g, ";");
    filename = f.value;
    if (filename == "") {
        return true;
    }

    vaild_ext = limitFile.split(";");
    var ext = filename.substring(filename.lastIndexOf(".") + 1);

    ext = ext.toUpperCase();

    for (var i = 0; i < vaild_ext.length; i++) {
        if (ext.toLowerCase() == vaild_ext[i].toLowerCase()) return true;
    }
    return false;
}
//cookies設置
function setCookie(name, value) {
    var argv = setCookie.arguments;
    var argc = setCookie.arguments.length;
    var expires = argc > 2 ? argv[2] : null;
    if (expires != null) {
        var LargeExpDate = new Date();
        LargeExpDate.setTime(LargeExpDate.getTime() + expires * 1000 * 3600 * 24);
    }
    document.cookie = name + "=" + escape(value) + (expires == null ? "" : "; expires=" + LargeExpDate.toGMTString());
}
//cookies讀取
function getCookie(Name) {
    var search = Name + "=";
    if (document.cookie.length > 0) {
        offset = document.cookie.indexOf(search);
        if (offset != -1) {
            offset += search.length;
            end = document.cookie.indexOf(";", offset);
            if (end == -1) end = document.cookie.length;
            return unescape(document.cookie.substring(offset, end));
        } else return "";
    }
}

function PF_SortByKey(array, key, sortype) {
    return array.sort(function (a, b) {
        var x = a[key];
        var y = b[key];
        if (sortype == "desc") {
            return x > y ? -1 : x < y ? 1 : 0;
        } else {
            return x < y ? -1 : x > y ? 1 : 0;
        }
    });
}

function PF_uiLoadingStart() {
    //console.log('start');
    jQuery("#circle").show();
    jQuery("#loading").show();
}

function PF_uiLoadingEnd() {
    //console.log('end');
    jQuery("#circle").hide();
    jQuery(".loading").hide();
}
//將全部button Disabled
function PF_FieldDisabled(isOpen) {
    // $("button").each(function (index) {
    //     $(this).prop('disabled', true);
    // });
    PF_uiLoadingStart();
}
function PF_editToPreview() {

    $(".preview").each((key, form) => {
        //    console.log(form);
        //$(form).find("input[type='checkbox'],input[type='radio']").attr('disabled', true);
        $(form).find("select").each((key, item) => {
            var text = $(item).find(':selected').text();
            if (text.indexOf('請選擇') == -1) {
                $(item).after(text);
            }
            $(item).remove();
        });
        $(form).find("input[type='checkbox'],input[type='radio']").each((key, item) => {
            if ($(item).prop('checked')) {
                var text = $(item).next().text();
                $(item).after(text);
                $(item).next().remove();
                $(item).remove();
            } else {
                $(item).parent().remove();
                $(item).next().remove();
                $(item).remove();
            }
            $("input[type='hidden'][name='" + $(item).attr('name') + "']").remove();

        });
        $(form).find("input[type='text'],input[type='tel'],input[type='date'],input[type='number'],input[type='url'],input[type='email'],textarea").each((key, item) => {
            var value = $(item).val();

            if ($(item).attr('type') == "number" && value != "") {
                try {
                    //value = Number(parseFloat(value).toFixed(3)).toLocaleString("en", { minimumFractionDigits: 3 });
                    value = Number(parseFloat(value).toFixed(3)).toLocaleString("en", {});
                } catch (error) {

                }
            }
            if ($(item).css('display') != "none") {
                $(item).after(value + "&nbsp;");
                $(item).remove();
            }

        });
        $(form).find('.select2-container--default').hide();
    });
    //$(form).find(".form-check").hide();

    // $(form).find("input:checked").each((key, item) => {
    //     $(item).removeAttr("disabled");
    // });
}
function PF_print(id) {
    var headstr = "<html><head><title></title></head><body>";
    var footstr = "</body>";

    var newstr = $("#" + id).html();
    var oldstr = document.body.innerHTML;
    document.body.innerHTML = headstr + newstr + footstr;
    window.print();
    document.body.innerHTML = oldstr;
    return false;
}
//v-model->map
function PF_getVmodelArray(doc, originals = null) {
    if (typeof doc == "undefined" || doc == null) {
        throw "getVmodelArray : not v-model id";
    }
    var tmpinputs = {};
    var tmpcheckboxmap = [];
    var beforefieldname = "";
    doc.querySelectorAll("select[v-model] , input[v-model]:not([type=hidden]),textarea").forEach(function (field) {
        var name = field.getAttribute("v-model");
        if (name != null) {


            name = name.replace("inputs.", "");
            name = name.replace("rs.", "");
            switch (field.type) {
                case "radio":
                    if (field.checked) {
                        tmpinputs[name] = "";
                    }
                    break;
                case "checkbox":
                    if (beforefieldname != field.name) {
                        tmpinputs[name.replace("[]", "").replace("item.", "")] = [];
                    }
                    break;
                default:
                    tmpinputs[name] = "";
                    break;
            }

            beforefieldname = name;
        }
    });
    if (originals != null) {
        Object.keys(tmpinputs).forEach((key) => {
            originals[key] = tmpinputs[key];
        });
    }
    return tmpinputs;
}
//name->map
function PF_getFormModelArray(doc, originals = null) {
    var tmpinputs = {};
    var tmpcheckboxmap = [];
    var beforefieldname = "";
    doc.querySelectorAll("select[v-model] , input:not([type=hidden])[v-model],textarea").forEach(
        function (field) {
            if (field.name != "") {

                tmpinputs[field.name.replace("[]", "")] = null;
            }
        }
    );
    doc.querySelectorAll("select[v-model] , input:not([type=hidden])[v-model],textarea").forEach(
        function (field) {
            switch (field.type) {
                case "radio":
                    if (field.checked) {
                        tmpinputs[field.name] = field.value;
                    }
                    break;
                case "checkbox":
                    if (beforefieldname != field.name) {
                        tmpinputs[field.name.replace("[]", "")] = [];
                        tmpcheckboxmap = [];
                    }
                    if (field.checked) {
                        tmpcheckboxmap.push(field.value);
                        tmpinputs[field.name.replace("[]", "")] =
                            tmpcheckboxmap;
                    }
                    break;
                default:
                    tmpinputs[field.name] = field.value;
                    break;
            }

            beforefieldname = field.name;
        }
    );
    if (originals != null) {
        Object.keys(tmpinputs).forEach((key) => {
            if (originals[key] == null) {
                originals[key] = tmpinputs[key];
            }
        });
    }
    //console.log(tmpinputs);
    return tmpinputs;
}
function PF_formatNumber(n, dp) {
    var s = '' + (Math.floor(n)), d = n % 1, i = s.length, r = '';
    while ((i -= 3) > 0) {
        r = ',' + s.substr(i, 3) + r;
    }
    var rest = Math.round(d * Math.pow(10, dp || 2));
    var rest_len = rest.toString().length;
    if (rest_len < dp) {
        rest = '0'.repeat(dp - rest_len) + rest;
    }
    return s.substr(0, i + 3) + r + (rest ? '.' + rest : '');
}
// resizeImage(e.target.result, 800, 800).then((res) => {
//     console.log(["res", res]);
// })
function resizeImage(result, MAX_WIDTH, MAX_HEIGHT) {
    return new Promise(function (a) {
        const img = new Image();
        img.src = result;
        img.onload = function () {
            var width = img.width;
            var height = img.height;
            if (width > height) {
                if (width > MAX_WIDTH) {
                    height = height * (MAX_WIDTH / width);
                    width = MAX_WIDTH;
                }
            } else {
                if (height > MAX_HEIGHT) {
                    width = width * (MAX_HEIGHT / height);
                    height = MAX_HEIGHT;
                }
            }
            var canvas = document.createElement("canvas");
            canvas.width = width;
            canvas.height = height;
            var ctx = canvas.getContext("2d");
            ctx.drawImage(img, 0, 0, width, height);
            var dataurl = canvas.toDataURL('image/jpeg', 1);
            //console.log(["dataurl", dataurl]);
            a(dataurl);
        };
    });
}
//<img rel="photo0" src="" class="img-thumbnail" alt=""></img>
//<input type="file" onchange="UploadPrviewImg(this)" class="form-control" name="bankimg" accept=".png,.jpg,*.gif" >
function UploadPrviewImg(e) {

    const previewImage = $("[rel='" + e.name + "']")[0];

    let file = e.files[0];
    const reader = new FileReader();
    reader.onload = (e) => {
        previewImage.src = e.target.result;
    };
    reader.readAsDataURL(file);

}
function _toast(msg, timer = 1500, icon = "success") {
    Swal.fire({
        position: "top-end",
        icon: icon,
        title: msg,
        //text: title,
        showConfirmButton: false,
        timer: timer,
    });
}

function _alert(msg, icon = "warning") {
    Swal.fire({
        icon: icon,
        html: msg,
    });

    //Swal.fire(msg, "", "warning");
}

function _confirm(message, callback) {
    Swal.fire({
        title: message,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        cancelButtonText: "取消",
        confirmButtonText: "確定",
    }).then((confirmed) => {
        callback(confirmed && confirmed.value == true);
    });
}

function bootstrapModal(title, url, width, height, rel = "") {


    var iframeheight = height;
    vtitle = "";
    if (typeof title != "undefined") {
        vtitle = title;
    }

    return `<div id="myModal" rel="${rel}" class="modal centered-modal" role="dialog" style="height:${height}px;">
    <div class="modal-dialog" role="document" style="width:${width}px;">
        <!-- Modal content-->
        <div class="modal-content" >
            <div class="modal-header" style="height:50px;margin-top:-10px">
                <h4 class="modal-title">${vtitle}</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <iframe class="modal-url"  id="modal-iframe" name="modal-iframe" marginwidth=0 marginheight=0 src="${url}" frameborder=0 width="100%" scrolling="auto" height="${iframeheight - 100}"></iframe>
            </div>
        </div>
    </div>
    </div>`;
}

function PF_formSearch(name, value) {
    document.forms["oForm"].page.value = 1;
    document.forms["oForm"].searchname.value = name;
    document.forms["oForm"].search.value = value;
    document.forms["oForm"].submit();
}
function PF_formDel(form, url) {
    (!_confirm('確定要刪除？', function (confirmed) {
        if (confirmed) {
            form.action = url;
            form.submit();
        }
    }));

}

function PF_ajax(dict) {
    if (dict == null) {
        return null;
    }
    if (dict["url"].indexOf("http") == -1) {
        dict["url"] = FC_WebFolder + dict["url"];
    }
    if (dict["headers"] == null) {
        dict["headers"] = {};
    }
    if (typeof dict["data"] != "undefined" && dict["data"] != "") {
        //console.log(dict["data"]);
        try {
            var c = $.parseJSON(dict["data"]);
            dict["headers"]["content-type"] = "application/json; charset=utf-8";
        } catch (err) {
            // Do something about the exception here
        }
    }

    if (dict["url"].indexOf("/api/") > -1) {

        dict["headers"]["X-CSRF-TOKEN"] = $('meta[name="csrf-token"]').attr(
            "content"
        );
        if (typeof $('meta[name="api_token"]').attr("content") != "undefined") {
            api_token = $('meta[name="api_token"]').attr("content");
        } else if (localStorage.api_token != null) {
            api_token = localStorage.api_token;
        }
        if (typeof api_token != "undefined") {
            dict["headers"]["Authorization"] = "Bearer " + api_token;
        }
    }
    //console.log(dict['headers']);
    //if (dict['async']==null){dict['async']=true;}
    if (dict["type"] == null) {
        dict["type"] = "post";
    }
    //if (dict['dataType']==null){dict['dataType']='json';}
    if (dict["beforeSend"] == null) {
        dict["beforeSend"] = function (xhr) {
            if (dict["noloading"] == null) {
                PF_uiLoadingStart();
            }
        };
    }
    if (dict["debug"]) {
        console.log(dict);
    }
    return jQuery
        .ajax(dict)
        .always(function (jqXHR, textStatus) {
            PF_uiLoadingEnd();
            if (textStatus != "success") {
                console.log(jqXHR);
                //_alert("Error: " + jqXHR.statusText)
            }
        })
        .fail(function (resp) {
            console.log("fail:" + resp.statusText);
            PF_uiLoadingEnd();
        });
}

function copyText(value) {
    var tempInput = document.createElement("input");
    tempInput.style = "position: absolute; left: -1000px; top: -1000px";
    tempInput.value = value;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);
    document.execCommand("copy");
    Swal.fire({
        position: "top-end",
        icon: "success",
        title: "複製成功",
        //text: title,
        showConfirmButton: false,
        timer: 500,
    });
}
function select2() {
    setTimeout(function () {
        $("select[select2]").each(function () {
            if (typeof ($(this).attr('tabindex')) == "undefined") {
                $(this).select2();
                $(this).bind('change', function () {
                    if (typeof (app.$root) != "undefined" && typeof (app.$root.$data) != "undefined") {
                        var modelName = $(this).attr('data-select2-id');
                        app.$root.$data.inputs[modelName] = $(this).val();
                        $(this).trigger('blur');
                    }
                });
                if (typeof (app.$root) != "undefined" && typeof (app.$root.$data) != "undefined") {
                    var modelName = $(this).attr('data-select2-id');
                    app.$root.$data.inputs[modelName] = $(this).val();
                }
            }
        });
    }, 500);
}

function select3() {
    setTimeout(function () {
        $("select[select3]").each(function () {
            if (typeof ($(this).attr('tabindex')) == "undefined") {

                $(this).multipleSelect({
                    multiple: true,
                    multipleWidth: $(this).attr('multipleWidth'),
                    placeholder: $(this).attr('firsttxt') != "" ? $(this).attr('firsttxt') : 'Select',
                });

            }
        });
    }, 500);
}
function copy(id) {
    var copyText = document.getElementById(id);
    copyText.select();
    copyText.setSelectionRange(0, 99999);
    document.execCommand("copy");
    Swal.fire({
        position: "top-end",
        icon: "success",
        title: "複製成功",
        //text: title,
        showConfirmButton: false,
        timer: 500,
    });
}
//copyText(window.location.href)
function copyText(text) {
    var dummy = document.createElement("input");

    document.body.appendChild(dummy);
    dummy.value = text;
    dummy.select();
    document.execCommand("copy");
    document.body.removeChild(dummy);
    Swal.fire({
        position: "top-end",
        icon: "success",
        title: "複製成功",
        //text: title,
        showConfirmButton: false,
        timer: 500,
    });
}
function PF_WindowOpen(title, url, width, height, isreload = false) {
    var myModal_rel = "myModal_" + Math.floor(new Date().getTime() / 1000);

    var m = bootstrapModal(title, url, width, height, rel = myModal_rel); // I would want to skip creating an HTML element with jQuery.
    var m1;
    var isframeset = false;
    var isiframe = false;
    if ($('#frameset', window.top.document).length > 0 && $('#maincontent', window.top.document).length > 0) {
        isframeset = true;
    } else if ($("iframe", window.top.document).length > 0) {
        isiframe = true;
    }
    if (isframeset) {
        $(window.top.frames["maincontent"].document.body).append(m);
        $('#frameset', window.top.document).attr('cols', '0,*')
        $('#framesetright', window.top.document).attr('rows', '0,*')
        m1 = $(window.top.frames["maincontent"].document.querySelectorAll("div[rel='" + myModal_rel + "']")[0]);
    } else if (isiframe) {
        $(window.top.document.body).append(m);
        m1 = $(window.top.document.querySelectorAll("div[rel='" + myModal_rel + "']")[0]);
    } else {
        var m1 = $(m);
    }


    m1.modal({
        title: title,
        show: true,
        keyboard: true,
        url: url,
    });
    m1.on("hidden.bs.modal", function (e) {
        //$(this).find("iframe").html("").attr("src", "");

        if (isframeset) {
            var myModal_reldoms = $(window.top.frames["maincontent"].document.querySelectorAll("div[rel='" + myModal_rel + "']"));
            $(myModal_reldoms[0]).remove();
            if ($(window.top).width() > 768 && $(window.top.frames["maincontent"].document.querySelectorAll("div[id='myModal']")).length == 0) {
                $('#frameset', window.top.document).attr('cols', '200,*');
            }
            $('#framesetright', window.top.document).attr('rows', '45,*');
        } else if (isiframe) {
            $(window.top.document.querySelectorAll("div[rel='" + myModal_rel + "']")[0]).remove();
        } else {
            $('#myModal').remove();
        }
        if (isreload) {
            window.location.reload();
        }
    });
    $(".modal-dialog").css({ "max-width": "98%" });
    $(".modal-dialog").css({ "max-height": "98%" });
}
//HTML載入就執行
jQuery(document).ready(function () {
    try {
        try {
            if (jQuery("form[name='SortoForm']").length > 0) {
                PF_SortoFormClassName(document.forms["SortoForm"]);
            }
        } catch (ex) {
            console.log(ex);
        }
        try {
            jQuery('[data-toggle="tooltip"]').tooltip();
        } catch (e) {
            //console.log(e);
        }
        jQuery('[data-toggle="modal"]').click(function () {
            let el = jQuery(this);
            var url = el.attr("href");

            var data_url = $(this).attr("data-url");
            if (typeof data_url !== typeof undefined) {
                url = data_url;
            }
            var title = el.attr("data-title");
            if (typeof url == "undefined" || url == "" || url == "#") {
                return;
            }
            var isreload = $(this).attr("reload");

            if (typeof isreload !== typeof undefined && isreload !== false) {
                isreload = true;
            } else {
                isreload = false;
            }

            //console.log(isreload);
            var width = el.attr("data-width");
            var target = el.attr("data-target");
            //var height = $(window).height();
            //if (target == "_top") {
            height = $(top).height();
            // }
            height *= 0.98;
            var data_height = el.attr("data-height");
            if (typeof data_height !== typeof undefined && data_height != "") {
                height = data_height;
            }
            PF_WindowOpen(title, url, width, height, isreload);
        });

        setTimeout(function () {
            PF_uiLoadingEnd();
        }, 500);
        try {

            PF_editToPreview();
            jQuery.browser.msie = false;
            jQuery.browser.version = 0;
            if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
                jQuery.browser.msie = true;
                jQuery.browser.version = RegExp.$1;
            }
            select2();
            select3();

        } catch (ex) {
            //console.log(ex);
        }

        try {
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.reportValidity()) {
                        checkValidity(event, form);
                        return false;
                    }
                    PF_FieldDisabled(form); //將全部button Disabled
                    //form.submit();
                    return true;

                }, false)
            })
        } catch (ex) {
            console.log(ex);
        }

    } catch (ex) {
        console.log(ex);
    }
});
window.addEventListener("pageshow", function (e) {
    try {
        $("button").each(function (index) {
            $(this).prop('disabled', false);
        });
        PF_uiLoadingEnd();
    } catch (ex) { }
});
