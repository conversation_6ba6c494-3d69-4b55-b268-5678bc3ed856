<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房產資訊 - {{ $data['city1title'] }}
        {{ $data['city2title'] }}
        {{ $data['address'] }}</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#ef4444',
                        'primary-light': '#fecaca',
                        'accent': '#f97316',
                    },
                    fontFamily: {
                        'chinese': ['Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', 'Helvetica', 'Arial',
                            'sans-serif'
                        ],
                    },
                }
            }
        }
    </script>
    <style>
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .no-print {
                display: none !important;
            }
        }

        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
        }
    </style>
</head>

<body class="bg-white font-chinese text-gray-800">
    <!-- 頁面容器 -->
    <div class="max-w-4xl mx-auto p-6 bg-white">

        <!-- 頂部標題區域 -->
        <div class="bg-gradient-to-r from-red-400 to-red-500 text-white px-6 py-4 rounded-t-lg mb-0">
            <h1 class="text-2xl font-bold flex items-center">
                {{ $data['producttitle'] ?? '房產資訊' }}
            </h1>
        </div>

        <!-- 地址區域 -->
        <div class="bg-red-100 px-6 py-3 border-l-4 border-red-500">
            <div class="flex items-center text-gray-700">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                <span class="text-lg">
                    {{ $data['city1title'] }} {{ $data['city2title'] }} {{ $data['address'] }}
                    {{ $data['storey'] != '' ? '(' . $data['storey'] . ')' : '' }}
                    @if ($data['buildname'] != '')
                        [{{ $data['buildname'] }}]
                    @endif
                </span>
            </div>
            <div class="text-sm text-gray-600 mt-1">
                物件編號：{{ $data['number'] ?? 'N/A' }}
            </div>
        </div>

        <!-- 主要內容區域 -->
        <div class="bg-white border border-gray-200 rounded-b-lg overflow-hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">

                <!-- 左側圖片區域 -->
                <div class="bg-gray-50 p-6">
                    <div class="space-y-4">
                        @foreach ($data['images'] as $key => $item)
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                {{ Html::myUIImage([
                                    'folder' => 'https://www.ebayhouse.com.tw/images/product',
                                    'filename' => $item,
                                    'class' => 'w-full h-48 object-cover',
                                ]) }}
                            </div>
                            @if ($key >= 1)
                                @break
                            @endif
                        @endforeach

                        <!-- 戶型圖 -->
                        <div class="bg-white rounded-lg shadow-sm p-4">
                            <div class="border-2 border-dashed border-gray-300 h-64 flex items-center justify-center">
                                <div class="text-center text-gray-500">
                                    <i class="fas fa-home text-4xl mb-2"></i>
                                    <p>戶型圖</p>
                                </div>
                            </div>
                        </div>

                        <!-- QR Code 區域 -->
                        <div class="bg-white rounded-lg shadow-sm p-4 text-center">
                            <div class="w-24 h-24 bg-gray-200 mx-auto mb-2 flex items-center justify-center">
                                <i class="fas fa-qrcode text-2xl text-gray-500"></i>
                            </div>
                            <p class="text-sm text-gray-600">掃描看更多<br>房屋資訊</p>
                        </div>
                    </div>
                </div>

                <!-- 右側資訊區域 -->
                <div class="p-6">
                    <!-- 價格摘要 -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-red-500">
                                {{ PF::formatNumber($data['totalupset'], 0) }}
                                <span class="text-lg">萬</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">公告底價</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-800">
                                {{ PF::formatNumber($data['floorprice'], 2) }}
                                <span class="text-lg">萬/坪</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">單價</div>
                        </div>
                    </div>

                    <!-- 建物概要 -->
                    <div class="bg-red-50 rounded-lg p-4 mb-6">
                        <h3 class="flex items-center text-lg font-semibold text-red-700 mb-4">
                            <i class="fas fa-home mr-2"></i>
                            建物概要
                        </h3>
                        <div class="grid grid-cols-2 gap-y-3 gap-x-6 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">型態</span>
                                <span class="font-medium">{{ $data['pattern'] ?? '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">樓層/樓高</span>
                                <span class="font-medium">
                                    @if ($data['floor_start'] && $data['floor_end'])
                                        {{ $data['floor_start'] }}/{{ $data['floor_end'] }}
                                    @else
                                        {{ $data['storey'] ?? '-' }}
                                    @endif
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">屋齡</span>
                                <span class="font-medium">{{ $data['houseage'] ?? '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">格局</span>
                                <span class="font-medium">
                                    @if ($data['room_count'] || $data['living_room_count'] || $data['bathroom_count'])
                                        {{ $data['room_count'] ?? 0 }}房{{ $data['living_room_count'] ?? 0 }}廳{{ $data['bathroom_count'] ?? 0 }}衛
                                    @else
                                        -
                                    @endif
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">管理費</span>
                                <span class="font-medium">{{ $data['fees'] ?? '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">車位</span>
                                <span
                                    class="font-medium">{{ $data['carping'] ? number_format($data['carping'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">房屋管理員</span>
                                <span class="font-medium">{{ $data['management'] ?? '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">總戶數</span>
                                <span class="font-medium">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">單位價格</span>
                                <span class="font-medium">{{ PF::formatNumber($data['floorprice'], 2) }}萬/坪</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">單位坪數</span>
                                <span
                                    class="font-medium">{{ $data['noticelawnestablishment'] ? number_format($data['noticelawnestablishment'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">單位總金</span>
                                <span class="font-medium">{{ PF::formatNumber($data['totalupset'], 0) }}萬</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">單位管理費</span>
                                <span class="font-medium">{{ $data['fees'] ?? '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">單位編號</span>
                                <span class="font-medium"></span>
                            </div>
                        </div>
                    </div>

                    <!-- 詳細資料 -->
                    <div class="bg-red-50 rounded-lg p-4 mb-6">
                        <h3 class="flex items-center text-lg font-semibold text-red-700 mb-4">
                            <i class="fas fa-info-circle mr-2"></i>
                            詳細資料
                        </h3>
                        <div class="grid grid-cols-2 gap-y-3 gap-x-6 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">建物坪數</span>
                                <span
                                    class="font-medium">{{ $data['noticelawnestablishment'] ? number_format($data['noticelawnestablishment'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">主建</span>
                                <span
                                    class="font-medium">{{ $data['mainlawnestablishment'] ? number_format($data['mainlawnestablishment'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">主建</span>
                                <span
                                    class="font-medium">{{ $data['mainlawnestablishment'] ? number_format($data['mainlawnestablishment'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">附屬</span>
                                <span
                                    class="font-medium">{{ $data['attachedtolawnestablishment'] ? number_format($data['attachedtolawnestablishment'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">公設</span>
                                <span
                                    class="font-medium">{{ $data['postulateping'] ? number_format($data['postulateping'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">土地總坪</span>
                                <span
                                    class="font-medium">{{ $data['land_total_ping'] ? number_format($data['land_total_ping'], 2) . '坪' : '-' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">土地持分</span>
                                <span
                                    class="font-medium">{{ $data['stakeholdersfloor'] ? number_format($data['stakeholdersfloor'], 2) . '坪' : '-' }}</span>
                            </div>
                        </div>

                        <!-- 建物特色 -->
                        <div class="mt-4 pt-4 border-t border-red-200">
                            <h4 class="text-red-700 font-medium mb-2">建物特色</h4>
                            <div class="space-y-1 text-sm text-gray-600">
                                @if ($data['memo'])
                                    <div>{!! nl2br(e($data['memo'])) !!}</div>
                                @else
                                    <p>暫無特色說明</p>
                                @endif
                            </div>
                        </div>


                    </div>

                    <!-- 營業員資訊 -->
                    <div class="bg-gradient-to-r from-orange-400 to-orange-500 text-white rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-orange-500"></i>
                                </div>
                                <div>
                                    <div class="font-medium">營業員：張志成</div>
                                    <div class="text-sm opacity-90">電話：0913959113</div>
                                    <div class="text-sm opacity-90">LINE ID：0913959113</div>
                                    <div class="text-xs opacity-75">台灣地產股份有限公司</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs opacity-75">經紀證：(103)新北市經字第003636號</div>
                                <div class="text-xs opacity-75">發證人：蘇政雄</div>
                                <div class="text-xs opacity-75">經紀業：台灣地產投資顧問有限公司</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 列印按鈕 -->
        <div class="text-center mt-6 no-print">
            <button onclick="window.print()"
                class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fas fa-print mr-2"></i>
                列印房產資訊
            </button>
        </div>
    </div>
</body>

</html>
