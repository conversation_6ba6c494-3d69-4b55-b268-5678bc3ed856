/*----------------------------------*\
  # home slider
\*----------------------------------*/
.home.swiper-container {
  width: 1260px;
  max-width: 100%;
  margin: 0 auto;
}
.home .swiper-slide {
  width:100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.home .swiper-slide img {
	width: 100%;
  height: 100%;
	object-fit: cover;
  object-position: 50% 50%;
}
.home .swiper-slide.swiper-slide-active {
  transform: scale3d(1.2, 1.2, 1.2);
  transition: transform .9s ease;
}
.home .swiper-button-prev {color: transparent;}
.home .swiper-button-next {color: transparent;}
.home .swiper-button-prev {
  height:36px;
  width: 36px;
  background: url(../images/aw-right.svg) no-repeat center center/100% auto;
  transform: rotate(180deg);
}
.home .swiper-button-next {
  height:36px; width: 36px;
  background: url(../images/aw-right.svg) no-repeat center center/100% auto;
}
@media (max-width: 415px){
  .home .swiper-button-prev {
    height:12.6px; width: 12.6px;
  }
  .home .swiper-button-next {
    height:12.6px; width: 12.6px;
  }
}
/*----------------------------------*\
  # 調整
\*----------------------------------*/
.carousel-control-next,
.carousel-control-prev {
  /*position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 15%;
  color: #fff;
  text-align: center;*/
  opacity: .5;
  opacity: 0;
  /*transition: opacity .15s ease;*/
}

/*----------------------------------*\
  # index (搜尋)
\*----------------------------------*/
.newSearch {
	background: #f6f8fb;
	max-width: 98%;
	/* width: 1920px; */
	/* margin: -90px auto 21px; */
	padding: 12px 15px;
	border-radius: 0 0 10px 10px;
	box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.1);
}
.newSearch hr {
  margin-top: 0rem;
  margin-bottom: .6rem;  margin-bottom: .78rem;
  border: 0;
  border-top: 1px solid rgba(0,0,0,.3);
}
.newSearch .input_wrap {
	font-size: 18px;
	flex-wrap: wrap;
	display: flex;
	margin:5px 0 1px;
	padding: 0 9px;
	justify-content: flex-start;
}
.newSearch .input_wrap.districts {
	margin: 9px 0;
}
.newSearch .input_wrap label.subject {
	height: 42px;
	line-height: 42px;
	width: 111px;
}
.newSearch .input_wrap .form-check-box {
	display: flex;
	flex-wrap: wrap;
}
/*---------2023-0714 checkbox改版*/
.newSearch .form-check-box .form-check {
  padding: 0 12px 0 33px;
  /* height: 42px; */
  display: flex;
  align-items: center;
  margin-right: 18px;
  border-radius: 5.1px;
  margin-bottom: 5.4px;
  margin-top: 5.4pxpx;
}
.newSearch .districts .form-check-box .form-check {
	/* margin-bottom: 12px;
  margin-top: 12pxpx; */
}
.newSearch .form-check-box label {
  cursor: pointer;
  /*font-size: 18px;*/
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
  position: relative;
}
.newSearch .form-check-box label::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 18px;
  height: 18px;
  top: 0;
  left: -24px;
  border: 1.8px solid #000;
  border-radius: 1.5px;
  background-color: #fff;
}
.newSearch .form-check-box label::after {
  display: inline-block;
  position: absolute;
  width: 18px;
  height: 18px;
  left: -21.9px;
  top: 1.5px;
  color: #454545;
}
.newSearch .form-check-box input[type="checkbox"] {
  opacity: 0;
  z-index: 1;
  margin-top: 0;
  margin-left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.newSearch .form-check-box input[type="checkbox"]:focus + label::before {
  outline: none;
}
.newSearch .form-check-box input[type="checkbox"]:checked + label::after{
  /*font-size: 18px;*/
  content: "\2713";
  transform: rotate(0deg) scaleX(1.2);
}
.newSearch .form-check-box input[type="checkbox"]:checked + label {
  color: white;
}
/*---------// 2023-0714 checkbox改版*/

/*------關鍵字欄位*/
.newSearch input {
	width: 315px;
	height: 42px;
	line-height: 42px;
	padding: 0 9px;
}
.newSearch .btn_search {
	width: 144px;
	background: #f87c7e;
	color: #FFF;
	margin: auto;
}
.newSearch .btn_search img {
	width: 18px;
	margin-right: 6px;
}

@media (max-width: 1199px){
	.newSearch {
		margin: 0 auto 21px;
		padding:7.2px 0px;
	}
	.newSearch hr {
	  margin-bottom: .36rem;
	}
	.newSearch .input_wrap {
		font-size: 14.4px;
		margin:0;
		padding: 0;
	}
	.newSearch .input_wrap label.subject {
		height: 27px;
		line-height: 39px;
	}
	/*---------2023-0714 checkbox改版*/
	.newSearch .form-check-box .form-check {
		padding: 0 9px 0 27.9px;
	  height: 27px;
	  margin-right: 14.4px;
	  margin-bottom: 9px;
	}
	.newSearch .form-check-box label {
	  /*font-size: 14.4px;*/
	}
	.newSearch .form-check-box label::before {
	  content: "";
	  display: inline-block;
	  position: absolute;
	  width: 14.4px;
	  height: 14.4px;
	  left: -18.9px;
	  border: .5px solid #000;
	}
	.newSearch .form-check-box label::after {
	  width: 14.4px;
	  height: 14.4px;
	  top: 0px;
	  left: -16.8px;
	}
	.newSearch .form-check-box input[type="checkbox"]:checked + label::after{
	  /*font-size: 14.4px;*/
	}
	/*------關鍵字欄位*/
	.newSearch input {
		width: 100%;
		height: 27px;
		line-height: 27px;
	}
	.newSearch .btn_search {
		width: 111px;
	}
	.newSearch .btn_search img {
		width: 14.4px;
	}
	.newSearch hr.stop_mo {
		display: none;
	}
}

/*---------pick_counties (newSearch) 2023-0714*/
.pick_counties {
	width: 100%;
	margin: 3px auto;
  display: flex;
  flex-wrap: wrap;
}
.pick_counties .btn {
	margin: 6px 18px 6px 0;
	border-radius:5.1px;
	height: 42px;
	line-height: 42px;
	font-size: 18px;
	padding: 0 18.9px;
}
.btn-outline-myGray {
  color: #000;
  border-color: #aaa;
}
.btn-outline-myGray:hover {
  color: #fff;
  background-color: #F87C7E;
}
.btn-outline-myGray:focus,
.btn-outline-myGray.focus ,
.pick_counties .btn.active{
	color: #fff;
  background-color: #F87C7E;
  box-shadow: none;
}

@media (max-width: 1199px){
	/*---------pick_counties (newSearch) 2023-0714*/
	.pick_counties {
		width: 100%;
		margin: 3px auto;
	  display: flex;
	  flex-wrap: wrap;
	}
	.pick_counties .btn {
		height: 27px;
		line-height: 27px;
		font-size: 14.4px;
		padding: 0 9px;
		margin: 3px 15px 3px 0;
	}
	.btn-outline-myGray {
	  color: #000;
	  border-color: #aaa;
	}
	.btn-outline-myGray:hover {
	  color: #fff;
	  background-color: #F87C7E;
	}
	.btn-outline-myGray:focus,
	.btn-outline-myGray.focus {
		color: #fff;
	  background-color: #F87C7E;
	  box-shadow: 0 0 0 1px rgba(248, 124, 126, 0.5);
	}
}
@media (max-width: 321px){
	.pick_counties .btn {
		margin: 3px 9.9px 3px 0;
	}
}

/*----------------------------------*\
  # myData
\*----------------------------------*/
.mydata_wrap {
	--mydataFont: 18px;
	--mydataBlue :#0793e2;
	--mydataCaption: 21px;
	width: 96%;
	margin:1rem auto 30px;
}
.mydata_wrap label{
	font-size: var(--mydataFont);
}
.mydata_wrap input {
	color: var(--mydataBlue);
}
.gender_box {
	display: flex;
}
.gender_box .form-check {
	margin-right: 21px;
	display: flex;
	align-items: center;
}
.gender_box .form-check-input {
  margin-top: 0px;
  margin-left: -18px;
}
.mydata_wrap .must {
	color: red;
	margin-right: 3px;
}
.mydata_wrap .must2 {
	font-size: 21px;
	color: red;
	margin-bottom: 0;
	transform: translate(0, 36%);
}
.input_wrap {
	display: flex;
	justify-content: space-between;
	margin:30px 0;
}
.input_wrap.liftUp {
	transform: translate(0, -36%);
	padding-bottom: 18px;
	border-bottom: 1px solid rgba(0, 0, 0, .1);
	margin: 0 0 -15px;
}
/*類型*/
.input_wrap2 {
	display: flex;
	margin:30px 0;
	flex-wrap: wrap;
}
/*價錢*/
.input_wrap3 { flex-wrap: wrap;
	display: flex;
	margin:30px 0;
}
.input_box {
	width: calc(50% - 9px);
}

/*---------類型*/
.input_wrap2 label.subject {
	width: 150px;
	font-size: var(--mydataCaption);
	letter-spacing: 36px;
}
.input_wrap2 .form-check-box {
	width: 672px;
	display: flex;
	flex-wrap: wrap;
}
.input_wrap2 .form-check-box .form-check {
	width: 150px;
	letter-spacing: 3px;
}
.input_wrap2 input[type="checkbox"]{
	width: 16.2px;
	height: 16.2px;
}

/*---------購屋預算*/
.input_wrap3 label.subject {
	width: 150px;
	font-size: 24px;
	font-size: var(--mydataCaption);
	letter-spacing: 8.1px;
}
.input_wrap3 .form-check-box {
	display: flex;
	flex-wrap: wrap;
}
.input_wrap3 .form-check-box .form-check {
	margin-right: 27px;
}
.input_wrap3 .form-check-box .form-check:last-child{
	margin-right: 0px;
}
.input_wrap3 input[type="checkbox"]{
	width: 16.2px;
	height: 16.2px;
}
/*---------我同意(僅一行)*/
.input_wrap4 {
	font-size: var(--mydataFont);
	margin-bottom: 36px;
}
.input_wrap4 input[type="checkbox"]{
	width: 16.2px;
	height: 16.2px;
}
.mydata_wrap label.aria-label {
	font-size: var(--mydataCaption);
	letter-spacing: normal;
}
.mydata_wrap textarea {
	font-size: var(--mydataFont);
}
.btn_saveMyData {
	background: #00aaff;
	color: white;
	padding: 0 30px;
	font-size: 18px;
	font-size: var(--mydataFont);
	height: 54px;
	line-height: 54px;
	margin-bottom: 42px;
}
.btn_saveMyData:hover {
	color: white;
}
@media (max-width: 1199px){
	.mydata_wrap {
		--mydataFont: 15px;	--mydataFont: 14.4px;
		--mydataCaption: 18px;
	}
	.mydata_wrap .form-check-inline .form-check-input {
    margin-right:4.5px;
	}
	.input_wrap {
		margin:30px 0;
		flex-direction: column;
	}
	.input_box {
		width: 100%;
	}
	.input_wrap .input_box:first-child {
		margin-bottom: 21px;
	}
	.input_wrap.liftUp {
		transform: translate(0, -12%);
		margin: 30px 0 -9px;
	}
	/*---------類型*/
	.input_wrap2 label.subject {
		width: 100%;
		letter-spacing: 3px;
	}
	.input_wrap2 .form-check-box .form-check {
		width: 90px;
	}
	.input_wrap2 input[type="checkbox"]{
		width: 15px;
		height: 15px;
	}
	.input_wrap2 .form-check {
		margin: 3px 0;
	}
	/*---------購屋預算*/
	.input_wrap3 label.subject {
		letter-spacing: 3px;
	}
	.input_wrap3 input[type="checkbox"]{
		width: 15px;
		height: 15px;
	}
	.input_wrap3 .form-check {
		margin: 3px 18px 3px 0;
	}
	/*---------我同意*/
	.input_wrap4 .form-check-box span {
		/*margin-left: 3.6px;*/
	}
	.input_wrap4 input[type="checkbox"]{
		width: 15px;
		height: 15px;
	}
	.mydata_wrap label.aria-label {
		letter-spacing: 3px;
	}
}
/*----------------------------------*\
  # myData：chooseArea
\*----------------------------------*/
.myData_chooseArea {
	width: 100%;
}
.myData_chooseArea .pick_counties {
	width: 100%;
	margin: 3px auto;
  display: flex;
  flex-wrap: wrap;
}
.myData_chooseArea .pick_counties .btn {
	margin: 0;
	border-radius:5.1px;
	height: 42px;
	line-height: 42px;
	font-size: var(--mydataFont);
	padding: 0 18.9px;
}
.myData_chooseArea .btn-outline-myGray {
  color: #000;
  border-color: #acacac;
}
.myData_chooseArea .btn-outline-myGray:hover {
  color: #fff;
  background-color: var(--mydataBlue);
}
.myData_chooseArea .btn-outline-myGray:focus,
.myData_chooseArea .btn-outline-myGray.focus ,
.myData_chooseArea .pick_counties .btn.active{
	color: #fff;
  background-color: var(--mydataBlue);
  box-shadow: none;
}
.myData_chooseArea .form-check-box {
	display: flex;
	flex-wrap: wrap;
	border: 1px solid #acacac;
	padding: 9px;
}
.myData_chooseArea .form-check-box .form-check {
	display: inline-block;
	display: grid;
  place-items: center;
	padding: 0 12px 0 33px;
	height: 42px;
  margin-right: 18px;
  border-radius: 5.1px;
  margin-top: 9px;
  margin-bottom: 9px;
}
.myData_chooseArea .form-check-box label {
  cursor: pointer;
  font-size: var(--mydataFont);
  line-height: 1;
  display: inline-block;
  vertical-align: middle;
  position: relative;
}
.myData_chooseArea .form-check-box label::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 18px;
  height: 18px;
  top: 0;
  left: -24px;
  border: 1px solid #000;
  border-radius: 1.5px;
  background-color: #ffffff;
}
.myData_chooseArea .form-check-box label::after {
  display: inline-block;
  position: absolute;
  width: 18px;
  height: 18px;
  left: -21.9px;
  top: 0px;
  color: #000000;
}
.myData_chooseArea .form-check-box input[type="checkbox"] {
  opacity: 0;
  z-index: 1;
  margin-top: 0;
  margin-left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.myData_chooseArea .form-check-box input[type="checkbox"]:focus + label::before {
  outline: none;
}
.myData_chooseArea .form-check-box input[type="checkbox"]:checked + label::after{
  content: "\2713";
  transform: scaleX(1.2);
}
.myData_chooseArea .form-check-box input[type="checkbox"]:checked + label {
  color: white;
}
@media (max-width: 1199px){
	.myData_chooseArea .pick_counties .btn {
		height: 27px;
		line-height: 27px;
		padding: 0 12px;
		border-radius: 1.5px;
	}
	.myData_chooseArea .form-check-box {
		padding: 4.5px;
	}
	.myData_chooseArea .form-check-label {
		/*margin-bottom: 6px;*/
	}
	.myData_chooseArea .form-check-box .form-check {
		padding: 0 9px 0 27.9px;
		height: 27px;
	  margin-right: 9px;
	  margin-top: 4.5px;
	  margin-bottom: 4.5px;
	}
	.myData_chooseArea .form-check-box label::before {
	  width: 14.4px;
	  height: 14.4px;
	  top: 0;
	  left: -18.9px;
	  border-radius: 1.5px;
	}
	.myData_chooseArea .form-check-box label::after {
	  width: 14.4px;
	  height: 14.4px;
	  left: -16.2px;
	}
	.myData_chooseArea .form-check-box input[type="checkbox"]:checked + label::after{
	  font-size: 14.4px;
	}
}
@media (max-width: 376px){
	.myData_chooseArea .pick_counties .btn {
		padding: 0 6px;
	}
}

/*----------------------------------*\
  # 登入/出
\*----------------------------------*/
.user {
	z-index: 99999;
	position: fixed;
	top: calc((63px - 18px) / 2);
	right: 18px;
	font-size: 18px;
}
.user a {
	position: relative;
	white-space:nowrap;
}
.user a:before{
	position: absolute;
	content: '';
	left: -33px;
	top: -1.8px;
	width: 27px;
	height: 27px;
}
.user a.logIn:before{
	background: url(../images/logIn.svg) no-repeat center center/100% auto;
}
.user a.logOut:before{
	background: url(../images/logOut.svg) no-repeat center center/100% auto;
}
@media (max-width: 1199px){
	.user {
		top: 7.2px;
		right: 18px;
		font-size: 14px;
	}
	.user a {
		color: #fff;
	}
	.user a:before{
		left: -23px;
		top: 0px;
		width: 18px;
		height: 18px;
	}
	.user a.logIn:before{
		background: url(../images/logIn_mo.svg) no-repeat center center/100% auto;
	}
	.user a.logOut:before{
		background: url(../images/logOut_mo.svg) no-repeat center center/100% auto;
	}
}
/*----------------------------------*\
  # 收藏
\*----------------------------------*/
.favorite_title {
	border-bottom: 1px solid #ccc;
	margin-bottom: 27px;
}
.favorite_title .userName {
	font-size: 29.4px;
	position: relative;
}
.favorite_title .userName:before {
	position: absolute;
	content: '';
	width: 6px;
	height: 100%;
	background: #000;
	top: 0;
	left: 0;
}
.favorite_title .userName span {
	margin-left: 15px;
}
.favorite_title .number {
	height: 40px;
	text-align: center;
	padding: 9px 12px;
	background: #0793e2;
	color: white;
	font-size: 16px;
}
.favorite_title.myData {
	display: flex;
	border-bottom: none;
}
.favorite_title.need {
	display: flex;
	flex-direction: column;
	border-bottom: none;
}
.favorite_title.myData .myFavo{
	height: 36px;
	line-height: 36px;
	text-align: center;
	padding: 0 12px;
	background: #fff;
	border: 1px solid #bbb ;
	color: #ccc;
	font-size: 16px;
	margin-left: 18px;
}
/*----------------------------*/
.favorite_wrap {
	min-height: 360px;
	width: 100%;
	margin:1rem auto;
	font-size: 16px;
	margin-bottom: 30px;
}
.favoriteTable {
	text-align: center;
	width: 100%;
	border-collapse: collapse;
	border: solid 1px #bbb;
}
.favoriteTable tr {
	display: flex;
	border-bottom: 1px solid #bbb;
}
.favoriteTable th {
	background: #FFF;
	width: calc(100% / 10);
}
.favoriteTable td {
	width: calc(100% / 10);
	display: grid;
  place-items: center;
}
.favoriteTable td:nth-child(5) {
	color: #0793e2;
}
.favoriteTable td:nth-child(8),
.favoriteTable td:nth-child(9) {
	color: #e71a21;
}
.favoriteTable tr:nth-child(2n) {
	background:#f9f9f9;
}
.favoriteTable tr:nth-child(2n+1) {
	background:white;
}
.favoriteTable td,
.favoriteTable th {
	padding:12px 9px;
}
.favoriteTable img {
	max-width: 100px;
	margin: auto;
	width: 123px;
}
.favoriteTable .delBtn {
	position: relative;
	display: block;
	height: 48px;
	line-height: 48px;
	padding: 0 12px 0 27px;
	background: #e71a21;
	color: white;
	max-width: 120px;
	font-size: 15px;
}
.favoriteTable .delBtn:before {
	position: absolute;
	content: '';
	width: 15px;
	height: 18px;
	background: url(../images/delete.svg) no-repeat center center/100% auto;
	top: calc(50% - 9px);
	left: 9px;
}
@media (max-width: 1199px) {
	.favorite_title .userName {
		font-size: 29.4px; font-size: 23.1px;
	}
	.favorite_title.myData .myFavo{
		height: 30px;
		line-height: 30px;
		font-size: 14.1px;
		margin-left: 12px;
	}
	.favorite_wrap {
		width:100%;
		padding: 9px; padding: 0 9px;
	}
	.favoriteTable {
		border: solid 0px #aaa;
		/*box-shadow:1px 1px 3px #aaa;*/
	}
	.favoriteTable th {
		display: none;
	}
	.favoriteTable tr {
		border: solid 1px #ccc;
		display: block;
		margin-bottom: 18px;
		box-shadow:1px 1px 2px #ccc;
	}
	.favoriteTable tr:nth-child(1){
		margin-bottom: 0px;
	}
	.favoriteTable tr:last-child {
		margin-bottom: 0px;
	}
	.favoriteTable td {
		width: 100%;
		display:block;
		position:relative;
		padding-left:120px;
    border-bottom: 1px dotted #aaa;
	}
	.favoriteTable td:nth-child(1),
	.favoriteTable td:last-child {
		position: static;
		padding-left:0px;
		padding: 18px;
	}
	.favoriteTable td:last-child {
		padding:18px 0 18px 18px;
	}
	.favoriteTable td:before {
		position: absolute;
		content: attr(data-th);
		display: inline-block;
    width:100px;
    left: 18px;
    font-weight: bold;
    text-align: left;
	}
	.favoriteTable td:nth-child(5){
		line-height: 1.5;
		text-align: left;
	}
	.favoriteTable td:nth-child(5):before,
	.favoriteTable td:nth-child(8):before,
	.favoriteTable td:nth-child(9):before {
		color: black;
	}
	.favoriteTable img {
		max-width: 100%;
		width: auto;
	}
	.favoriteTable .delBtn {
		margin: 0 ;
	}
}

/*----------------------------頁數*/
.favorite_paging {
	text-align: center;
	font-size: 15px;
	margin-bottom: 30px;
}
.favorite_paging .count {
	margin-bottom: 6px;
}
.favorite_paging .count span,
.favorite_paging .isPage span {
	padding: 0 6px;
}
.favorite_paging .count span:nth-child(2){
	color: red;
}
.favorite_paging .isPage a {
	margin: 0 12.6px;
}
/*----------------------------------*\
	# caption_wrap
\*----------------------------------*/
.caption_wrap {
	width: 98%;
	height: 141px;
	margin: -60px auto 0;
	text-align: center;
	background: var(--mdc-theme-background);
	background: rgba(250, 250, 250, .96);
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 9px;
}
.caption_wrap h1 {
	color: var(--gray); color: #747474;
	font-size: 51px;
	font-weight: 600;
	line-height: 1.1;
	margin-bottom: 0
}
@media (max-width: 1199px){
	.caption_wrap {
		height: 93px;
		margin: -46.5px auto 0;
	}
	.caption_wrap h1 {
		font-size: 30px;
	}
}
/*----------------------------------*\
	# login_wrap
\*----------------------------------*/
.login_wrap {
	width: 98%;
	margin: 51px auto;
	padding: 24px 0 51px;
	display: flex;
	flex-wrap: wrap;
}
.login_wrap h3 {
	font-size: 35px;
	font-weight: 600;
	text-align: center;
	color: #3e3e3e;
}
.register {
	padding: 0 9% 0 7.2%;
	width: calc(50% - 0.5px);
	border-right: 1px solid #b3b3b3;
}
.logIn{
	padding: 0 16.5% 0 9%;
	width: calc(50% - 0.5px);
}
@media (max-width: 1199px){

}
@media (max-width: 600px){
	.login_wrap {
		width: 90%;
		margin: 21px auto 51px; margin: 21px auto 30px;
		padding: 24px 0 51px; padding: 24px 0 0px;
		display: flex;
		flex-wrap: wrap;
	}
	.login_wrap h3 {
		font-size: 24px;
	}
	.register {
		order: 2;
		padding: 0;
		width: 100%;
		border-right: 0px solid #b3b3b3;
		border-top: 1px solid #b3b3b3;
		padding-top: 60px;
	}
	.logIn {
		order: 1;
		padding: 0;
		width: 100%;
	}
}

/*----------------------------------*\
	# form
\*----------------------------------*/
.register form,
.logIn form {
	margin: 60px 0 45px;
}
.inputBox,
.inputBox2 {
	display: flex;
	margin:15px 0;
}
.register .inputBox:last-child {
	padding-left: 150px;
}
.inputBox label {
	font-size: 27px;
	white-space:nowrap;
	width: 150px;
	line-height: 60px;
	height: 60px;
	text-align:right;
	margin-bottom: 0;
}
.inputBox input {
	font-size: 27px;
	padding: 0 12px;
	width:calc(100% - 150px);
	line-height: 60px;
	height: 60px;
}
.inputBox button,
.inputBox2 button {
	display: block;
	margin:15px auto 0;
	min-width:150px;
	background: #5c80fb;
	color:white;
	font-size: 21px;
}
.inputBox button:hover,
.inputBox2 button:hover{
	color: white;
}
/*------------------*/
.inputBox2 input {
	width:100%;
	line-height: 60px;
	height: 60px;
	padding: 0 12px;
}
.inputBox2  ::placeholder {
  color: #ccc;
  opacity: 1;
}

@media (max-width: 600px) {
	.register form,
	.logIn form {
		margin: 30px 0  40px;
	}
	.inputBox,
	.inputBox2 {
		margin:10px 0;
	}
	.inputBox2 {
		padding: 0 15%;
	}
	.register .inputBox:last-child {
		padding-left: 0px;
	}
	.inputBox label {
		font-size: 16px;
		white-space:nowrap;
		width: 90px;
		line-height: 45px;
		height: 45px;
		text-align:right;
	}
	.inputBox input {
		font-size: 16px;
		width:calc(100% - 90px);
		line-height: 45px;
		height: 45px;
		padding: 0 9px;
	}
	.inputBox2 input {
		line-height: 45px;
		height: 45px;
		padding: 0 9px;
	}
}