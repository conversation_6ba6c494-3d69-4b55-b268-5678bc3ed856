{
    "center": {
        "prefix": "bo center",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"container\">",
            "<div class=\"text-center\">",
            "",
            "</div>",
            "</div>"
        ]
    },
    "bo left": {
        "prefix": "bo left",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"float-left\">"
        ]
    },
    "bo right": {
        "prefix": "bo right",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"float-right\">"
        ]
    },
    "row col": {
        "prefix": "bo row col 平均分散",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"row\">",
            "                    <div class=\"col\"> </div>",
            "                    <div class=\"col\"> </div>",
            "</div>"
        ]
    },
    "左邊會留白p-X距離fluid不留距離": {
        "prefix": "bo container-fluid ",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"container-fluid \">",
            "</div>"
        ]
    },
    "依PC與手機尺寸不同顯示sm:手機 md:平板 lg:電腦": {
        "prefix": "bo rwd row col 依PC與手機尺寸不同顯示sm:手機 md:平板 lg:電腦",
        "scope": "html,blade,vue-html",
        "description": "",
        "body": [
            "<div class=\"row\">",
            "<div class=\"col-12 col-sm-6 col-md-4 col-lg-3 mb-3\">",
            "</div>",
            "</div>"
        ]
    },
    "radio 多行": {
        "prefix": "bo radio 多筆",
        "scope": "html,blade",
        "body": [
            "<div class=\"form-check form-check-inline\">",
            " <input class=\"form-check-input\" type=\"radio\" name=\"exampleRadios\" id=\"exampleRadios3\" value=\"c\">",
            " <label class=\"form-check-label\" for=\"exampleRadios3\">",
            " XX",
            " </label>",
            " <input class=\"form-check-input\" type=\"radio\" name=\"exampleRadios\" id=\"exampleRadios3\" value=\"c\">",
            " <label class=\"form-check-label\" for=\"exampleRadios3\">",
            " XX",
            " </label>",
            "</div>"
        ]
    },
    "table": {
        "prefix": "bo table",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"table-responsive-md\">",
            " <table class=\"table table-striped table-hover table-bordered table-fixed\">",
            " <tr>",
            " <th>",
            "",
            " </th>",
            " </tr>",
            " <tr>",
            " <td>",
            "",
            " </td>",
            " </tr>",
            " </table>",
            "</div>"
        ]
    },
    "button_block": {
        "prefix": "button 滿版",
        "scope": "html,blade,vue-html",
        "body": [
            " btn-block"
        ]
    },
    "button_href": {
        "prefix": "botton href url",
        "scope": "html,blade,vue-html",
        "description": "button submit btn btn-info",
        "body": [
            "<a href=\"{{ url('/') }}/${1:name}\" class=\"btn btn-info\" target=\"_blank\">${2:瀏覽}</a>"
        ]
    },
    "button_large 大版": {
        "prefix": "botton big",
        "scope": "html,blade,vue-html",
        "body": [
            " btn-lg"
        ]
    },
    "button_small 小版": {
        "prefix": "button small",
        "scope": "html,blade,vue-html",
        "body": [
            " btn-sm"
        ]
    },
    "button_block width 100%": {
        "prefix": "button block width 100%",
        "scope": "html,blade,vue-html",
        "body": [
            " btn-block"
        ]
    },
    "class": {
        "prefix": "bo class from control ",
        "scope": "html,blade,vue-html",
        "description": " class input-class",
        "body": [
            "form-control"
        ]
    },
    "card": {
        "prefix": "bo card success",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"card card-success\">",
            "<div class=\"card-header\">",
            "<h3 class=\"card-title\">${1:title}</h3>",
            "</div>",
            "<div class=\"card-body\">",
            "",
            "</div>",
            "</div>"
        ]
    },
    "card warning": {
        "prefix": "bo card warning",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"card card-warning\">",
            "<div class=\"card-header\">",
            "<h3 class=\"card-title\">${1:title}</h3>",
            "</div>",
            "<div class=\"card-body\">",
            "",
            "</div>",
            "</div>"
        ]
    },
    "checkbox1": {
        "prefix": "bo class checkbox",
        "scope": "html,blade",
        "description": "checkbox class name",
        "body": [
            " calss=\"form-check-input\" "
        ]
    },
    "tab": {
        "prefix": "bo tab 主框架",
        "scope": "html,blade",
        "description": "ultab",
        "body": [
            "",
            "<script language=\"JavaScript\">\n    ",
            "    \\$(function() {",
            "        var ul=\\$(\"ul[role='tablist']\");        ",
            "        \\$(\"div[class^=tab-pane]\").each(function(i) {",
            "            var title=\\$(this).attr('id');",
            "            var active = ($(this).attr('class') == 'tab-pane fade show active' || i == 0) ? 'active' : '';",
            "            ul.append('<li class=\"nav-item\"><a class=\"nav-link ' + active + '\" id=\"name-'+title+'\"  data-toggle=\"tab\" role=\"tab\"  href=\"#'+title+'\">'+title+'</a></li>');",
            "            ",
            "        });",
            "    });",
            "</script>",
            "",
            " <ul class=\"nav nav-tabs\" id=\"nav-tab\" role=\"tablist\">",
            " ",
            " </ul>",
            " ",
            "",
            " <div class=\"tab-content\">",
            " <div id=\"資訊\" class=\"tab-pane fade show active p-2\">",
            " ",
            " </div>",
            "",
            " <div id=\"推播/標籤\" class=\"tab-pane fade p-2\">",
            " ",
            " </div>",
            " </div>"
        ]
    },
    "tab_subt": {
        "prefix": "bo tab 子項內容",
        "scope": "html,blade",
        "description": "ultab",
        "body": [
            " <div id=\"${1:標題}\" class=\"tab-pane fade p-2\">",
            " ",
            " </div>"
        ]
    },
    "checkbox_inline_vue-html": {
        "prefix": "checkbox inline vue-html model",
        "scope": "html,blade",
        "description": "bootstrap checkbox",
        "body": [
            "   <div class=\"form-check form-check-inline\">",
            "<input  name=\"${1:name}[]\" id=\"${1:name}_0\" type=\"checkbox\" ",
            " value=\"${2:title}\" v-model=\"inputs.${1:name}\"  class=\"form-check-input\" ",
            " :true-value=\"'${2:title}'\"  title=\"${3:title}\" ",
            " requiredclass=\"required[0,TEXT]\" >",
            "<label class=\"form-check-label\" for=\"${1:name}_0\">${3:title}</label>",
            " </div>"
        ]
    },
    "button-primary": {
        "prefix": "button primary",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"button\" class=\"btn btn-primary\">${1:確定}</button>"
        ]
    },
    "button-secondary": {
        "prefix": "button secondary",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"button\" class=\"btn btn-secondary\">${1:確定}</button>"
        ]
    },
    "button-success": {
        "prefix": "button success",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"submit\" class=\"btn btn-success\">${1:確定}</button>"
        ]
    },
    "button-danger": {
        "prefix": "button danger",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"button\" class=\"btn btn-danger\">${1:確定}</button>"
        ]
    },
    "button-warning": {
        "prefix": "button warning",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"button\" class=\"btn btn-warning\">${1:確定}</button>"
        ]
    },
    "button-info": {
        "prefix": "button info",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"button\" class=\"btn btn-info\">${1:確定}</button>"
        ]
    },
    "button-light": {
        "prefix": "button light",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"button\" class=\"btn btn-light\">${1:確定}</button>"
        ]
    },
    "button-dark": {
        "prefix": "button dark",
        "scope": "html,blade,vue-html",
        "body": [
            "<button type=\"button\" class=\"btn btn-dark\">${1:確定}</button>"
        ]
    },
    "form": {
        "prefix": "bo form left 2 8 一行 左右",
        "scope": "html,blade",
        "body": [
            "<div class=\"form-group row\">",
            "    <div class=\"col-md-2\">bot_token<label class=\"text-danger p-1\"></label></div>",
            "    <div class=\"col-md-10\">",
            "          <input type=\"text\" class=\"form-control\" name=\"${1:name}\"",
            "          value=\"{{\\$data['${1:name}']}}\"  ",
            "          required  placeholder=\"\" />",
            "    ",
            "    </div>",
            "</div>"

        ]
    },
    "form vue": {
        "prefix": "bo form left 2 8 一行 左右",
        "scope": "vue-html",
        "body": [
          "<div class=\"form-group row\">",
        "    <div class=\"col-md-2\">${1:title}<font class=\"text-danger p-1\"></font></div>",
        "    <div class=\"col-md-10\">",
        "<el-form-item",
        "                                prop=\"${2:name}\"",
        "                                :rules=\"[",
        "                                    {",
        "                                        required: false,",
        "                                        message: '${1:title} 未填',",
        "                                    },",
        "                                ]\"",
        "                            >",
        "                                <el-input",
        "                                    v-model=\"inputs.${2:name}\"",
        "                                    type=\"text\"",
        "                                />",
        "                            </el-form-item>",
        "    </div>",
        "</div>"
        ]
    },
    "form 50 50": {
        "prefix": "bo form 左右各50%",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\" row\">",
            "    <div class=\"col-md-6\">",
            "    ",
            "    </div>",
            "    <div class=\"col-md-6\">",
            "    ",
            "    </div>",
            "</div>"
        ]
    },
    "表單 上下 form top bottom vue": {
        "prefix": "bo form 一行上下",
        "scope": "vue-html",
        "body": [
            " <div class=\"form-group \">",
            " <label>${1:title}：<font class=\"text-danger p-1\"></font></label>",
            "",
            "<el-form-item",
            "                                prop=\"${2:name}\"",
            "                                :rules=\"[",
            "                                    {",
            "                                        required: false,",
            "                                        message: '${1:title} 未填',",
            "                                    },",
            "                                ]\"",
            "                            >",
            "                                <el-input",
            "                                    v-model=\"inputs.${2:name}\"",
            "                                    type=\"text\"",
            "                                />",
            "                            </el-form-item>",
            "",
            " </div>"
        ]
    },
    "表單 上下 form top bottom": {
        "prefix": "bo form 一行上下",
        "scope": "html,blade",
        "body": [
            " <div class=\"form-group \">",
            " <label>${1:title}：<font class=\"text-danger p-1\"></font></label>",
            "",
            "  <input type=\"text\" class=\"form-control\" name=\"${2:name}\" ",
            "   value=\"{{\\$data['${2:name}']}}\" size=\"40\" />",
            "",
            " </div>"
        ]
    },
    "form input-group": {
        "prefix": "bo form 一行連在一起 line",
        "scope": "html,blade",
        "body": [
            "<div class=\"input-group\">",
            "",
            "</div>"
        ]
    },
    "form 6 6": {
        "prefix": "bo form 一行平均 line",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"form-group row\">",
            "    <div class=\"col-md-6\">",
            "    ",
            "    </div>",
            "    <div class=\"col-md-6\">",
            "    </div>",
            "</div>"
        ]
    },
    "sample form": {
        "prefix": "bo sample form",
        "scope": "html,blade",
        "description": "sample boostrap form",
        "body": [
            "<script type=\"text/javascript\">",
            "    function oForm_onsubmit(form) {    ",
            "        if (PF_FormMultiAll(form) == false) {",
            "            return false",
            "        };",
            "        PF_FieldDisabled(form)",
            "        return true;",
            "    }",
            "    </script>",
            "    <div class=\"card\">",
            "        <div class=\"card-body\">",
            "            <form name=\"oForm\" class=\"needs-validation\" novalidate id=\"oForm\" method=\"post\" language=\"javascript\"",
            "                action=\"{{ url('/') }}/test/botbonniestore\" target=\"botbonniestore\" onsubmit=\"return oForm_onsubmit(this);\">",
            "                <div class=\"form-group d-md-flex align-items-center\">",
            "                    <h4>訊息推播</h4>",
            "                </div>",
            "                <div class=\"form-group row\">",
            "                    <label class=\"col-md-2\">bot_token<font class=\"text-danger p-1\"></font></label>",
            "                    <div class=\"col-md-10\">",
            "                        <input type=\"text\" class=\"form-control\" name=\"${1:name}\"",
            "                         value=\"{{\\$data['${1:name}']}}\" title=\"title\"   ",
            "                         required  placeholder=\"\" />",
            "    ",
            "                    </div>",
            "                    <label class=\"col-md-2\">push json<font class=\"text-danger p-1\"></font></label>",
            "                    <div class=\"col-md-10\">",
            "                        <input type=\"text\" class=\"form-control\" name=\"${2:name}\"",
            "                         value=\"{{\\$data['${2:name}']}}\" title=\"title\"   ",
            "                         required  placeholder=\"\" />",
            "                    </div>",
            "                </div>",
            "                <div class=\"container\">",
            "                    <div class=\"row justify-content-center align-items-center\">",
            "                        <button class=\"btn btn-success\">送出</button>",
            "                    </div>",
            "                </div>",
            "    ",
            "    ",
            "            </form>",
            "        </div>",
            "    </div>"
        ]
    },
    "container_br": {
        "prefix": "bo container 斷行",
        "scope": "html,blade,vue-html",
        "description": "container + form-row align-items-center",
        "body": [
            " <div class=\"container-fluid \">",
            "",
            "            <div class=\"form-row align-items-center\">",
            "",
            "                ${1:種類} : ${2:title}",
            "            </div>",
            "</div>"
        ]
    },
    "text+連著後面加控制項": {
        "prefix": "bo text append button",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"input-group\">",
            "    <input class=\"form-control\" type=\"tel\" name=\"mobile\" required title=\"手機號碼\"",
            "      requiredclass=\"required[1,MOBILE]\">",
            "    <div class=\"input-group-append\">",
            "                            <button type=\"button\" class=\"btn btn-info\">寄發</button>",
            "    </div>",
            "</div>"
        ]
    },
    "text-primary": {
        "prefix": "bo font color text-primary",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-primary'>${1:}</font>"
        ]
    },
    "text-secondary": {
        "prefix": "bo font color  text-secondary",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-secondary'>${1:}</font>"
        ]
    },
    "text-success": {
        "prefix": "bo font color text-success",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-success'>${1:}</font>"
        ]
    },
    "text-danger": {
        "prefix": "bo font color text-danger",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-danger'>${1:}</font>"
        ]
    },
    "text-warning": {
        "prefix": "bo font color text-warning",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-warning'>${1:}</font>"
        ]
    },
    "text-info": {
        "prefix": "bo font color text-info",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-info'>${1:}</font>"
        ]
    },
    "text-dark": {
        "prefix": "bo font color text-dark",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-dark'>${1:}</font>"
        ]
    },
    "text-light": {
        "prefix": "bo font color text-light",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='text-light'>${1:}</font>"
        ]
    },
    "font-weight-bold": {
        "prefix": "bo font  weight bold",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='font-weight-bold'>${1:}</font>"
        ]
    },
    "font-weight-light": {
        "prefix": "bo font  font weight light",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='font-weight-light'>${1:}</font>"
        ]
    },
    "font-italic": {
        "prefix": "bo font italic",
        "scope": "html,blade,vue-html",
        "body": [
            "<font class='font-italic'>${1:}</font>"
        ]
    },
    "modal": {
        "prefix": "modal 使用同頁的HTML下的modal",
        "scope": "html,blade",
        "body": [
            "<a data-toggle=\"modal\" data-target=\"#exampleModalCenter\">",
            "\\${1:name}</a>",
            "                ",
            "",
            "                    <!-- Modal -->",
            "                    <div class=\"modal fade\" id=\"exampleModalCenter\" tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"exampleModalCenterTitle\" aria-hidden=\"true\">",
            "                        <div class=\"modal-dialog modal-dialog-centered\" role=\"document\">",
            "                            <div class=\"modal-content\">",
            "                                <div class=\"modal-header\">",
            "                                    <h5 class=\"modal-title\" id=\"exampleModalLongTitle\">${1:title}</h5>",
            "                                    <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\">",
            "                                        <span aria-hidden=\"true\">&times;</span>",
            "                                    </button>",
            "                                </div>",
            "            <script type=\"text/javascript\">",
            "                function oFormmodal(form) {",
            "                    if (PF_FormMultiAll(form) == false) { return false };",
            "                    //PF_FieldDisabled(form)",
            "                    \\$(\"[data-dismiss='modal']\", document).trigger('click');",
            "                    return true;",
            "                }",
            "            </script>",
            "            <form name=\"oFormmodal\" id=\"oFormmodal\" method=\"post\" language=\"javascript\" action=\"{{request()->middlewareurl}}${1:member}/store\" onsubmit=\"return oFormmodal(this);\" >",
            "                                <div class=\"modal-body\">",
            "                                    {{\\$item['body']}}",
            "                                </div>",
            "                                <div class=\"modal-footer\">",
            "                                    <!--<button  class=\"btn btn-success\">確定</button>-->",
            "                                    <button type=\"button\" class=\"btn btn-success\" data-dismiss=\"modal\">確定</button>",
            "",
            "                                </div>",
            "            </form>",
            "                            </div>",
            "                        </div>",
            "                    </div>"
        ]
    },
    "modal_button": {
        "prefix": "modal button",
        "scope": "html,blade",
        "description": "herf",
        "body": [
            "<button type=\"button\"  class=\"btn btn-primary btn-sm\"",
            "data-url=\"{{\\request()->middlewareurl}}${2:product}?__${2:product}_id={{\\$rs->id}}\" data-toggle=\"modal\"",
            "data-title=\"{{\\$rs->title}}\" data-width=\"\" data-height=\"\">",
            "${1:新增}</button>"
        ]
    },
    "modal_target": {
        "prefix": "modal submit target",
        "scope": "html,blade",
        "body": [
            "<form name=\"oForm\" id=\"oForm\" method=\"post\" type=\"text/javascript\" action = \"{{request()->url()}}/../excelimportstore\"",
            "onsubmit=\"return oForm_onsubmit(this);\" target=\"modal-iframe\"",
            "<button type=\"button\"  class=\"btn btn-primary\"",
            "data-url=\"{{ url('/')}}raw\" data-toggle=\"modal\"",
            "data-title=\"{{\\$rs->title}}\" data-width=\"\" data-height=\"\">",
            "${1:新增}</button>",
            "</form>"
        ]
    },
    "modal_href": {
        "prefix": "modal href",
        "scope": "html,blade",
        "description": "herf",
        "body": [
            "<a ",
            "href=\"{{request()->middlewareurl}}${1:product}?__${1:product}_id={{\\$rs->id}}\" data-toggle=\"modal\"",
            "data-title=\"{{\\$rs->title}}\"  data-width=\"\" data-height=\"\">",
            "${2:新增}</a>"
        ]
    },
    "radio": {
        "prefix": "radio 一個",
        "scope": "html,blade",
        "body": [
            "<div class=\"form-check form-check-inline\">",
            "    <input name=\"{1:product}\" id=\"{1:product}_a\" type=\"radio\" value=\"不限\" class=\"form-check-input\">",
            "    <label class=\"form-check-label mr-2\" for=\"{1:product}_a\">不限</label>",
            "</div>"
        ]
    },
    "checkbox": {
        "prefix": "checkbox 一個",
        "scope": "html,blade",
        "body": [
            "{{ Form::hidden('${1:is}', '') }}",
            "<div class=\"form-check form-check-inline\">",
            "  <input name=\"${1:is}\" {{ \\$data['${1:is}'] == '1' ? 'checked' : '' }} id=\"${1:is}_a\" type=\"checkbox\" value=\"1\" class=\"form-check-input\">",
            "  <label class=\"form-check-label mr-2\" for=\"${1:is}_a\">${2:上架}</label>",
            "</div>"
        ]
    },
    "checkbox_larvel": {
        "prefix": "checbox + hidden",
        "scope": "html,blade",
        "body": [
            "<div class=\"form-check form-check-inline\">",
            "{{Form::hidden('${1:ischecked}', '')}}",
            "{{Form::checkbox('${1:ischecked}', 1,\\$data['${1:ischecked}'],['v-model'=>'inputs.${1:ischecked}','class'=>\"form-check-input\"])}}",
            "</div>"
        ]
    },
    "hidden": {
        "prefix": "hidden html",
        "scope": "html,blade",
        "body": [
            "<input type=\"hidden\" name=\"${1:status}\" v-model=\"inputs.${1:status}\" value=\"{{\\$data['${1:status}']}}\">"
        ]
    },
    "textarea vue-html": {
        "prefix": "textarea text 是vue-html的版本",
        "scope": "html,blade",
        "body": [
            "<textarea name=\"${1:body}\" v-model=\"inputs.${1:status}\" class=\"form-control\" cols=\"37\" rows=\"5\" style='width:90%;height:200px'>{{\\$data['${1:body}']}}</textarea>"
        ]
    },
    "textarea no vue-html": {
        "prefix": "textarea text 不是vue-html的版本",
        "scope": "html,blade",
        "body": [
            "<textarea name=\"${1:body}\"  class=\"form-control\" cols=\"37\" rows=\"5\" style='width:90%;height:200px'>{{\\$data['${1:body}']}}</textarea>"
        ]
    },
    "input_text": {
        "prefix": "text 不是vue-html的版本",
        "scope": "html,blade",
        "body": [
            "<input type=\"text\" class=\"form-control\" name=\"${1:title}\"",
            " value=\"{{\\$data['${1:SYMBOL}']}}\"  ",
            " required placeholder=\"\" />"
        ]
    },
    "input_text number": {
        "prefix": "text number 不是vue-html的版本",
        "scope": "html,blade",
        "body": [
            "<input type=\"number\" class=\"form-control\" name=\"${1:title}\"",
            " value=\"{{\\$data['${1:SYMBOL}']}}\"  ",
            " required inputmode=\"decimal\" />"
        ]
    },
    "input_text_vue-html": {
        "prefix": "text 是vue-html的版本",
        "scope": "html,blade",
        "body": [
            "<input type=\"${2:text}\" class=\"form-control\"",
            "  ",
            " required v-model=\"inputs.${1:status}\" placeholder=\"\" />"
        ]
    },
    "text-center": {
        "prefix": "text center",
        "scope": "html,blade,vue-html",
        "body": [
            "text-center"
        ]
    },
    "copy": {
        "prefix": "copy",
        "scope": "html,blade",
        "description": "herf search name",
        "body": [
            "<div class=\"input-group\">",
            "                                <input class=\"form-control\" id=\"${1:url}\" type=\"text\"  ",
            "                                    value=\"{{ url('/')}}/zoom?id={{\\$data['edit']}}\" readonly>",
            "                                <div class=\"input-group-append\">",
            "",
            "                                    <button type=\"button\" onclick=\"copy('${1:url}')\" class=\"btn btn-info btn-sm\">複製</button>",
            "                                </div>",
            "</div>"
        ]
    },
    "copy text": {
        "prefix": "copy text",
        "scope": "html,blade",
        "description": "herf search name",
        "body": [
            "<i class=\"far fa-copy\" onclick=\"copyText('{{\\$rs->url}}')\"></i>"
        ]
    },
    "img-thumbnail": {
        "prefix": "bo img-thumbnail",
        "scope": "html,blade,vue-html",
        "body": [
            "class=\"img-thumbnail\""
        ]
    },
    "img-fluid": {
        "prefix": "bo img-fluid",
        "scope": "html,blade,vue-html",
        "body": [
            "class=\"img-fluid\""
        ]
    },
    "table_html": {
        "prefix": "table",
        "scope": "html,blade,vue-html",
        "body": [
            "<table style='border:3px #cccccc solid;' cellpadding='10' border='1'>",
            " <tr>",
            " <th>",
            "",
            " </th>",
            " </tr>",
            " <tr>",
            " <td>",
            "",
            " </td>",
            " </tr>",
            " </table>"
        ]
    },
    "password": {
        "prefix": "password",
        "scope": "html,blade",
        "body": [
            "data-toggle=\"tooltip\" data-placement=\"top\" title=\"密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ # \\$ % & *\"",
            "                        requiredclass=\"[1,PASSWORD]\""
        ]
    },
    "loading": {
        "prefix": "loading",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"d-flex justify-content-center\">",
            "<div class=\"spinner-border\" role=\"status\" id=\"ajaxloading\">",
            "</div>",
            "</div>"
        ]
    },
    "submenu": {
        "prefix": "bo button 按鈕選單 menu",
        "scope": "html,blade",
        "body": [
            "<div class=\"btn-group btn-sm\">",
            "                            <button type=\"button\" class=\"btn btn-primary btn-sm\">功能</button>",
            "                            <button type=\"button\" class=\"btn btn-primary btn-sm dropdown-toggle dropdown-toggle-split\" data-toggle=\"dropdown\" aria-expanded=\"false\">",
            "                                <span class=\"sr-only\">Toggle Dropdown</span>",
            "                            </button>",
            "                            <div class=\"dropdown-menu\">",
            "",
            "                                <a class=\"dropdown-item\" href=\"#\" data-toggle=\"modal\" data-title=\"{{\\$rs->title}}\" data-width=\"\" data-height=\"\">代登</a>",
            "                                <a class=\"dropdown-item\" href=\"#\" >個人名片</a>",
            "",
            "                            </div>",
            "                        </div>"
        ]
    },
    "form-row": {
        "prefix": "bo form-row 超過往下排",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"form-row\">",
            "    <div class=\"col border p-2\" align=\"center\">",
            "    </div>",
            "</div>"
        ]
    },
    "disabled": {
        "prefix": "disabled",
        "scope": "html,blade,vue-html",
        "body": [
            "disabled"
        ]
    },
    "topbuttom": {
        "prefix": "bo div 上下二行",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"container\">",
            "      <div class=\"row\">",
            "                   ",
            "       </div>",
            "       <div class=\"row\">",
            "                                ",
            "       </div>",
            "     </div>"
        ]
    },
    "ul li": {
        "prefix": "bo ul li",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"container\">",
            "  <ul class=\"list-unstyled row\">",
            "    <li class=\"col-sm-4\">項目 1</li>",
            "    <li class=\"col-sm-4\">項目 2</li>",
            "    <li class=\"col-sm-4\">項目 3</li>",
            "    <li class=\"col-sm-4\">項目 4</li>",
            "    <li class=\"col-sm-4\">項目 5</li>",
            "    <li class=\"col-sm-4\">項目 6</li>",
            "  </ul>",
            "</div>"
        ]
    },
    "invalid-tooltip": {
        "prefix": "error invalid-tooltip",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"invalid-tooltip\">",
            "${1:}",
            " </div>"
        ]
    },
    "invalid-feedback": {
        "prefix": "error invalid-feedback",
        "scope": "html,blade,vue-html",
        "body": [
            "<div class=\"invalid-feedback\">",
            "${1:}",
            " </div>"
        ]
    },
    "radio group": {
        "prefix": "radio group ",
        "scope": "html,blade",
        "body": [
            "<SCRIPT language=JavaScript>",
            "    function checkin(s, classt) {",
            "        var name = \\$(s).attr('rel');",
            "        \\$(\"[rel='\" + name + \"']\").each((key, item) => {",
            "            \\$(item).removeClass();",
            "            \\$(item).addClass('btn btn-default');",
            "",
            "        });",
            "        \\$(s).removeClass();",
            "        \\$(s).addClass('btn btn-' + classt);",
            "    }",
            "</SCRIPT>",
            "<div class=\"btn-group btn-group-toggle\" role=\"group\" aria-label=\"Basic radio toggle button group\">",
            "    @foreach (\\$data['xmldoc']->xpath(\"//參數設定檔/出席狀況/KIND\") as \\$v)",
            "    <label class=\"btn btn-{{\\$kind==strval(\\$v->傳回值) ? \\$v->class : 'default'}}\" onclick=\"checkin(this,'{{\\$v->class}}')\" rel=\"kind_{{\\$rs->id}}\">",
            "        <input type=\"radio\" id=\"{{\\$rs->id}}_1\" name=\"kind_{{\\$rs->id}}\" autocomplete=\"off\" value=\"{{strval(\\$v->傳回值)}}\" {{\\$kind==strval(\\$v->傳回值) ? 'checked' : ''}}> {{\\$v->資料}}",
            "    </label>",
            "",
            "    @endforeach",
            "</div>"
        ]
    },
    "rwd": {
        "prefix": "rwd",
        "scope": "html,blade,vue-html",
        "body": [
            "/*mobile  */",
            "        @media screen and (max-width: 724px) and (min-width: 0) {",
            "            video {",
            "                width: 100%",
            "            }",
            "",
            "",
            "        }",
            "",
            "        /*pc*/",
            "        @media screen and (min-width: 725px) {",
            "            video {",
            "                width: 460px",
            "            }",
            "        }",
        ]
    },
}