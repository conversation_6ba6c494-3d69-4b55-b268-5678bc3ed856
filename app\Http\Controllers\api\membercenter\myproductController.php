<?php

namespace App\Http\Controllers\api\membercenter;

use PF, PT;
use Exception, DB;
use Illuminate\Http\Request;
use App\Repositories\memberRepository;
use App\Repositories\myproductRepository;

use Illuminate\Support\Facades\Validator;

/***
"功能名稱":"我的物件",
"資料表":"myproduct",
"建立時間":"2023-07-09 09:57:42 ",
 ***/
//include('myproductRepository.php');
//include('myproduct.php')
class myproductController extends Controller {

    private $data;
    private $xmlDoc;
    private $myproductRepo;
    private $memberRepo;


    /**
     *TODO 建構子
     */
    public function __construct(myproductRepository $myproductRepo, memberRepository $memberRepo) {

        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->myproductRepo = $myproductRepo;
        $this->memberRepo = $memberRepo;

        //$this->data['nav'] = PT::nav($this->data['xmldoc'],"myproduct",$this->data['nav']);
        $this->data['displaynames'] = $this->myproductRepo->getFieldTitleArray();
    }


    /**
     * @OA\Post(
     *     path="/api/membercenter/myproduct",security={{"bearerAuth":{}}},operationId="",tags={"會員專區/我的物件"},summary="列表",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *         @OA\Schema(@OA\Property(property="searchstartdate",description="開始時間",type="string",example="2021-01-01",)),
     *         @OA\Schema(@OA\Property(property="searchenddate",description="結束時間",type="string",example="2099-12-31",)),
     *     })

     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),

     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/myproduct"),
     *         @OA\Schema(@OA\Property(property="title", type="string",description="", example="") ),
     *     }))

     *      ),)
     * ),)
     */

    /**
     * TODO 資料列表
     *
     * @return  \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $rows = $this->getRows($request);
        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $pagesize = (is_numeric($request->input('pagesize'))  ? $request->input('pagesize') : 10);
        $rows = $rows->paginate($pagesize);
        // 顯示sqlcmd
        $this->jsondata['data'] = $rows;
        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
    /**
     * 資料Rows
     *
     * @return  \Illuminate\Http\Response
     */
    public function getRows($request) {
        //
        $rows = $this->myproductRepo->selectRaw('myproduct.*');


        //依條件搜尋資料的SQL語法
        $rows->myWhere('member_id', \Auth::guard('member')->id(), $this->data['displaynames'], 'Y');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(myproduct.created_at,DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert(myproduct.created_at,DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');
        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), $request->input('sorttype') == "desc"  ? $request->input('sorttype') : "asc");
        } else {
            $rows->orderByRaw('myproduct.id desc');
        }
        return $rows;
    }


    /**
     * @OA\Post(
     *     path="/api/membercenter/myproduct/show",security={{"bearerAuth":{}}},operationId="",tags={"會員專區/我的物件"},summary="單筆顯示",description="",
     *     @OA\RequestBody(required=true,
     *      @OA\JsonContent(
     *      allOf={

     *         @OA\Schema(@OA\Property(property="id",description="編號",type="integer",example="1",)),

     *     })
     *   ,),

     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),

     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/myproduct"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),

     *     })

     *     ,)
     *),)
     */

    /**
     * 資料單一詳細頁
     * @return  \Illuminate\Http\Response
     */
    public function show($request) {
        //

        $rows = $this->myproductRepo->select('myproduct.*');
        $rows->myWhere('member_id', \Auth::guard('member')->id(), $this->data['displaynames'], 'Y');
        $rows->myWhere('id|N', $request->input('id'), "id", "Y");
        $rows = $rows->take(1);
        //  PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            $rs = $rows->first();
            $this->jsondata['data'] = $rs;
        } else {
            throw new \CustomException("no data");
        }

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }


    /**
     * @OA\Post(
     *     path="/api/membercenter/myproduct/store",security={{"bearerAuth":{}}},operationId="",tags={"會員專區/我的物件"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
     *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/myproduct"),
     *         @OA\Schema(type="object",@OA\Property(property="", type="string",description="系列", example="") ),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="id", type="integer",description="編號", ),
     *      )
     *     ),)
     *),)
     */

    /**
     * TODO 資料新增編輯儲存
     * @return  \Illuminate\Http\Response
     */
    public function store(Request $request) {

        $product_id = $request->input('product_id');
        $online = $request->input('online');

        //FIXME 那些欄位為必填判斷
        $validators = null;
        $validators['product_id'] = ['required'];
        $validators['online'] = ['required'];

        if ($validators != null) {
            $validator = \Validator::make($request->all(), $validators);
            $validator->setAttributeNames($this->data['displaynames']);
            if ($validator->fails()) {
                throw new \CustomException(implode(',', $validator->messages()->all()));
            }
        }
        $inputs = $request->all();

        if ('1' == $online) {

            $inputs['member_id'] = \Auth::guard('member')->id();
            $inputs['created_at'] = date('Y-m-d H:i:s'); //建立日期-
            //PF::printr($inputs);exit();
            $edit = $this->myproductRepo->create($inputs)->id;

            $this->jsondata['resultmessage'] = '加入成功';
        } else {
            $this->myproductRepo->delete([
                'product_id' => $request->input('product_id'),
                'member_id' => \Auth::guard('member')->id()
            ]);
            $this->jsondata['resultmessage'] = '刪除成功';
        }

        $rows = $this->myproductRepo->selectRaw('*');
        $rows->myWhere('member_id|N', \Auth::guard('member')->id(), "member_id", 'S');
        $rows = $rows->limit(10);
        $rows = $rows->get();
        $myproducts = "";
        foreach ($rows  as $key => $rs) {
            $myproducts .= $rs->product_id . ",";
        }
        $myproducts = ltrim(rtrim($myproducts, ","), ",");
        $inputs = null;
        $inputs['myproducts'] = $myproducts;
        $this->memberRepo->update($inputs, \Auth::guard('member')->id());
        $this->jsondata['data']['id'] = $edit;
        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
    /**
     * @OA\Post(
     *     path="/api/membercenter/myproduct/destroy",security={{"bearerAuth":{}}},operationId="",tags={"會員專區/我的物件"},summary="刪除",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="del",description="要刪除的編號",type="integer",example="1",description="多筆中間用逗號",),

     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *     ),)
     *),)
     */
    /**
     * TODO 資料刪除
     * @return  \Illuminate\Http\Response
     */
    public function destroy(Request $request) {
        $this->myproductRepo->deleteIds($this->data['del']);
        $this->jsondata['resultmessage'] = '刪除成功';
        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
