<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\api\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use App\Models\Product;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use PF;
use Str;

class houseflowController extends Controller {

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
    }

    /**
     * @OA\Post(
     *     path="/api/houseflow/store",
     *     security={{"bearerAuth":{}}},
     *     tags={"前台/房屋流程"},
     *     summary="新增/編輯房屋物件",
     *     description="同number的資料會被更新",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="user_code", type="string", description="用戶代碼", example="12345"),
     *             @OA\Property(property="branch_code", type="string", description="分店代碼", example="BR001"),
     *             @OA\Property(property="commission_date_start", type="string", description="委託日期(起)", example="2024-01-01"),
     *             @OA\Property(property="commission_date_end", type="string", description="委託日期(迄)", example="2024-12-31"),
     *             @OA\Property(property="number", type="string", description="物件編號", example="P001"),
     *             @OA\Property(property="producttitle", type="string", description="物件名稱", example="豪華公寓"),
     *             @OA\Property(property="city1title", type="string", description="縣市", example="台北市"),
     *             @OA\Property(property="city2title", type="string", description="區域", example="大安區"),
     *             @OA\Property(property="address", type="string", description="路名", example="忠孝東路四段100號"),
     *             @OA\Property(property="totalupset", type="integer", description="總價", example="8000000"),

     *             @OA\Property(property="pattern", type="string", description="建物型態", example="公寓"),
     *             @OA\Property(property="floor_start", type="string", description="樓層(起)", example="3"),
     *             @OA\Property(property="floor_end", type="string", description="樓層(迄)", example="5"),
     *             @OA\Property(property="storey", type="string", description="總樓層", example="12"),
     *             @OA\Property(property="houseage", type="integer", description="屋齡", example="15"),
     *             @OA\Property(property="room_count", type="string", description="格局(房)", example="3"),
     *             @OA\Property(property="living_room_count", type="string", description="格局(室)", example="1"),
     *             @OA\Property(property="hall_count", type="string", description="格局(廳)", example="2"),
     *             @OA\Property(property="bathroom_count", type="string", description="格局(衛)", example="2"),
     *             @OA\Property(property="balcony_count", type="string", description="格局(陽台)", example="1"),
     *             @OA\Property(property="buildname", type="string", description="社區名稱", example="信義豪宅"),
     *             @OA\Property(property="parkingmode", type="string", description="車位型式", example="地下室車位"),
     *             @OA\Property(property="pingtotalnumberof", type="integer", description="建物坪", example="39.0"),
     *             @OA\Property(property="mainlawnestablishment", type="string", description="主建物坪", example="25.5"),
     *             @OA\Property(property="attachedtolawnestablishment", type="string", description="附屬坪", example="5.2"),
     *             @OA\Property(property="postulateping", type="string", description="公設坪", example="8.3"),
     *             @OA\Property(property="carping", type="string", description="車位坪", example="5.0"),
     *             @OA\Property(property="other_ping", type="string", description="其他坪數", example="2.5"),
     *             @OA\Property(property="land_total_ping", type="integer", description="土地總坪", example="100.1"),
     *             @OA\Property(property="stakeholdersfloor", type="string", description="土地持分坪", example="10.5"),
     *             @OA\Property(property="memo", type="string", description="物件特色", example="採光佳，交通便利"),
     *             @OA\Property(property="layout_image", type="string", description="格局圖", example="layout.jpg"),
     *             @OA\Property(property="property_image1", type="string", description="物件照片1", example="image1.jpg"),
     *             @OA\Property(property="property_image2", type="string", description="物件照片2", example="image2.jpg"),
     *             @OA\Property(property="property_image3", type="string", description="物件照片3", example="image3.jpg"),
     *             @OA\Property(property="property_image4", type="string", description="物件照片4", example="image4.jpg"),
     *             @OA\Property(property="property_image5", type="string", description="物件照片5", example="image5.jpg"),
     *             @OA\Property(property="property_image6", type="string", description="物件照片6", example="image6.jpg"),
     *             @OA\Property(property="property_image7", type="string", description="物件照片7", example="image7.jpg"),
     *             @OA\Property(property="property_image8", type="string", description="物件照片8", example="image8.jpg"),
     *             @OA\Property(property="property_image9", type="string", description="物件照片9", example="image9.jpg"),
     *             @OA\Property(property="property_image10", type="string", description="物件照片10", example="image10.jpg"),
     *             @OA\Property(property="property_image11", type="string", description="物件照片11", example="image11.jpg"),
     *             @OA\Property(property="property_image12", type="string", description="物件照片12", example="image12.jpg"),
     *             @OA\Property(property="property_image13", type="string", description="物件照片13", example="image13.jpg"),
     *             @OA\Property(property="property_image14", type="string", description="物件照片14", example="image14.jpg"),
     *             @OA\Property(property="property_image15", type="string", description="物件照片15", example="image15.jpg"),
     *             @OA\Property(property="property_image16", type="string", description="物件照片16", example="image16.jpg"),
     *             @OA\Property(property="property_image17", type="string", description="物件照片17", example="image17.jpg"),
     *             @OA\Property(property="property_image18", type="string", description="物件照片18", example="image18.jpg"),
     *             @OA\Property(property="virtual_tour_video", type="string", description="線上賞屋影片", example="tour_video.mp4")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="回覆",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="resultcode", type="integer", description="訊息代碼", example=0),
     *             @OA\Property(property="resultmessage", type="string", description="訊息內容", example="新增成功"),
     *         )
     *     )
     * )
     */
    public function store(Request $request) {


        // 取得所有請求資料
        $inputs = $request->all();

        // 資料驗證規則 - 根據指定欄位
        $validator = Validator::make($inputs, [
            'user_code' => 'string|max:50',
            'branch_code' => 'string|max:50',
            'commission_date_start' => 'date',
            'commission_date_end' => 'date',
            'number' => 'required|string|max:50',
            'producttitle' => 'string|max:500',
            'city1title' => 'string|max:50',
            'city2title' => 'string|max:50',
            'address' => 'string|max:300',
            'totalupset' => 'integer',
            'pingtotalnumberof' => 'integer',
            'land_total_ping' => 'numeric', // 允許浮點數

        ]);


        // 如果驗證失敗
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        // 組合所有物件照片路徑到 img 欄位
        $propertyImages = [];
        $productImageDir = public_path('images/product');

        // 確保產品圖片目錄存在
        if (!File::exists($productImageDir)) {
            File::makeDirectory($productImageDir, 0755, true);
        }

        for ($i = 1; $i <= 18; $i++) {
            $imageKey = 'property_image' . $i;
            if (isset($inputs[$imageKey]) && !empty($inputs[$imageKey])) {
                // 處理圖片URL下載
                $fileName = $this->downloadImageIfNotExists($inputs[$imageKey], $productImageDir);
                if ($fileName) {
                    $propertyImages[] = $fileName;
                }
            }
        }

        // 將所有照片路徑用逗號串接
        $inputs['img'] = implode(',', $propertyImages);
        // 如果有格局圖,加入到圖片字串中
        if (isset($inputs['layout_image']) && !empty($inputs['layout_image'])) {
            $layoutFileName = $this->downloadImageIfNotExists($inputs['layout_image'], $productImageDir);
            if ($layoutFileName) {
                if (!empty($inputs['img'])) {
                    $inputs['img'] .= ',' . $layoutFileName;
                } else {
                    $inputs['img'] = $layoutFileName;
                }
            }
        }

        // 處理線上賞屋影片
        // if (isset($inputs['virtual_tour_video']) && !empty($inputs['virtual_tour_video'])) {
        //     $videoFileName = $this->downloadVideoIfNotExists($inputs['virtual_tour_video'], $productImageDir);
        //     if ($videoFileName) {
        //         $inputs['virtual_tour_video'] = $videoFileName;
        //     }
        // }

        $inputs['productkind'] = 2;

        $rows = \App\Models\product::updateOrCreate(
            [
                'number' => $inputs['number'],
                //'city1title' => $inputs['city1title'],
            ],
            $inputs
        );
        $productid = $rows->productid;
        if ($rows->wasRecentlyCreated) {
            $this->jsondata['resultmessage'] = '新增成功';
        } else {
            $this->jsondata['resultmessage'] = '更新成功';
        }

        \File::put(public_path('images/json/' . $productid . ".json"), json_encode($request->all(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));







        return $this->apiResponse($this->jsondata);
    }

    /**
     * 下載圖片到指定目錄（如果檔案不存在）
     *
     * @param string $imageUrl 圖片URL
     * @param string $targetDir 目標目錄
     * @return string|null 返回檔名或null（如果失敗）
     */
    private function downloadImageIfNotExists($imageUrl, $targetDir) {
        try {
            // 從URL取得檔名
            $pathInfo = pathinfo(parse_url($imageUrl, PHP_URL_PATH));
            $fileName = $pathInfo['basename'];
            $extension = isset($pathInfo['extension']) ? $pathInfo['extension'] : 'jpg';

            // 如果沒有副檔名，添加預設副檔名
            if (!isset($pathInfo['extension'])) {
                $fileName .= '.jpg';
            }

            $filePath = $targetDir . DIRECTORY_SEPARATOR . $fileName;

            // 檢查檔案是否已存在
            if (File::exists($filePath)) {
                // 檔案已存在，不需要下載，直接返回檔名
                return $fileName;
            }

            // 檔案不存在，下載圖片
            $imageContent = file_get_contents($imageUrl);

            if ($imageContent === false) {
                // 下載失敗
                return null;
            }

            // 儲存檔案
            File::put($filePath, $imageContent);

            return $fileName;
        } catch (\Exception $e) {
            // 錯誤處理，可以記錄日誌
            \Log::error('圖片下載失敗: ' . $e->getMessage() . ' URL: ' . $imageUrl);
            return null;
        }
    }

    /**
     * 下載影片到指定目錄（如果檔案不存在）
     *
     * @param string $videoUrl 影片URL
     * @param string $targetDir 目標目錄
     * @return string|null 返回檔名或null（如果失敗）
     */
    private function downloadVideoIfNotExists($videoUrl, $targetDir) {
        try {
            // 從URL取得檔名
            $pathInfo = pathinfo(parse_url($videoUrl, PHP_URL_PATH));
            $fileName = $pathInfo['basename'];
            $extension = isset($pathInfo['extension']) ? $pathInfo['extension'] : 'mp4';

            // 如果沒有副檔名，添加預設副檔名
            if (!isset($pathInfo['extension'])) {
                $fileName .= '.mp4';
            }

            $filePath = $targetDir . DIRECTORY_SEPARATOR . $fileName;

            // 檢查檔案是否已存在
            if (File::exists($filePath)) {
                // 檔案已存在，不需要下載，直接返回檔名
                return $fileName;
            }

            // 檔案不存在，下載影片
            $videoContent = file_get_contents($videoUrl);

            if ($videoContent === false) {
                // 下載失敗
                return null;
            }

            // 儲存檔案
            File::put($filePath, $videoContent);

            return $fileName;
        } catch (\Exception $e) {
            // 錯誤處理，可以記錄日誌
            \Log::error('影片下載失敗: ' . $e->getMessage() . ' URL: ' . $videoUrl);
            return null;
        }
    }
}
