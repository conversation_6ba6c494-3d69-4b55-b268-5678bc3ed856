<?php

namespace App\Http\Controllers\admin;

use DB;
use PT;
use ExcelExport;
use App\Facades\PF;
use Illuminate\Http\Input;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

use App\Repositories\adminuserRepository;
use Illuminate\Support\Facades\Validator;

class usermodifyController extends adminController
{
    private $fieldnicknames;
    private $data;
    private $xmldoc;
    private $db;
    private $adminuserRepo;

    /**
     *建構子.
     */
    public function __construct(adminuserRepository $adminuserRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        

        $this->data = PF::requestAll($this->data);

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'usermodify');

        $this->data['hiddens'] = array();
        $this->adminuserRepo=$adminuserRepo;
        $this->data['displaynames'] =$this->adminuserRepo->getFieldTitleArray();
     
    }

    /**
     * 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        
        
        
        $rows = $this->adminuserRepo->select('*');
        $rows->where('id', '=', \Auth::guard('admin')->user()->id);

        $rows = $rows->get();
        if ($rows->count() > 0) {
            foreach ($rows->first() as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
        }

        return view('admin.usermodify.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $edit = \Auth::guard('admin')->user()->id;

        $validators = null;
        

        $validators['name'] = 'required';
        $validators['email'] = 'nullable|email';

        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = null;
        if ($this->data['password']!=""){$inputs['password']=\Hash::make($this->data['password']);}/*密碼-*/	
        $inputs['name'] = $this->data['name']; /*姓名-*/
        $inputs['email'] = $request->input('email'); /*E-Mail-*/

        
        $this->adminuserRepo->update($inputs,$edit);
        
        return redirect('admin/usermodify/edit')->with('js', "_alert('更新成功')");
    }

}
