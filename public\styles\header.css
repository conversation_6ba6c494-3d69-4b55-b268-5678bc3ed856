/*----------------------------------*\ 
  # header 小>大         
\*----------------------------------*/
.header_wrap {
	position: fixed;
	z-index: 10;
	width: 100%;
	height: 51px;	
	padding: 0 18px 0 66px;
	background: var(--header);
	display: flex;
	align-items: center;
	box-shadow: var(--box-sd);
}
.header_wrap .brand {
	height: 30PX;
	width: auto;
}
.header_wrap .nav { 
	font-size: var(--p);
	width: 100%;
	height: calc(100% - 51px); 
	display: none;
	position: fixed;
	top: 51px;
	left: 0;	
	overflow-x: hidden;
	overflow-y: auto;
	line-height: 2.7rem;	
	background: var(--panel); 	
}
.header_wrap .nav ul { 
	height: calc(100% - 30px - 36px - 4.5rem); 
	overflow-y: auto;
	display: block;
	position: relative;	
}
.header_wrap .nav a,
.header_wrap .nav a:hover,
.header_wrap .nav a:active,
.header_wrap .nav a:visited {
	display: block;
	position: relative;
	padding: 0 21px;	
	color: var(--font);	
	text-decoration:none;
}
.header_wrap .nav a.active {
	color: var(--main);
}
/*.header_wrap .nav a.active {
	color: var(--main);
}*/

/*---------各層bg*/
.header_wrap .nav ul {	
	background: transparent;
}
.header_wrap .nav ul ul {	
	background: rgba(255,255,255,.9);;
}
.header_wrap .nav li {	
	border-top: 1px solid var(--border);
}

.header_wrap .nav li:hover > a,
.header_wrap .nav li.nav-active > a {	
	color: var(--weak);
	background: var(--main);
	background: rgba(255,128,0,.9);;
}
.header_wrap .nav .nav-submenu > a:after { 
	position: absolute;
	display: block;
	right:1rem;
	top: 1px;
	content: "\221F";
	transition: 400ms ease-in-out;
	transform: rotate(-45deg);
}
.header_wrap .nav li.nav-active > a:after {
	content: "\221F";
	transform: rotate(135deg);
}
.header_wrap .nav .nav-submenu > ul li a {
	padding: 0 0 0 36px; 
}
.header_wrap .nav .nav-submenu > ul ul li a {	
	padding: 0 0 0 45px;	
}
/*---------nav-button (burger)*/
.header_wrap .nav-button { z-index: 4;
  position: fixed;
  top: -5px;  
  left: 18px;
  width: 30px;
  height: 55px;
  transition-duration: 0.5s;
}
.header_wrap .nav-button .icon-left {
  transition-duration: 0.5s;
  position: absolute;
  height: 4px;
  width: 15px;
  top: 30px;
  background-color: var(--burger);
}
.header_wrap .nav-button .icon-left:before {
  transition-duration: 0.5s;
  position: absolute;
  width: 15px;
  height: 4px;
  background-color: var(--burger);
  content: "";
  top: -10px;
}
.header_wrap .nav-button .icon-left:after {
  transition-duration: 0.5s;
  position: absolute;
  width: 15px;
  height: 4px;
  background-color: var(--burger);
  content: "";
  top: 10px;
}
.header_wrap .nav-button .icon-left:hover {
  cursor: pointer;
}
.header_wrap .nav-button .icon-right {
  transition-duration: 0.5s;
  position: absolute;
  height: 4px;
  width: 15px;
  top: 30px;
  background-color: var(--burger);
  left: 15px;
}
.header_wrap .nav-button .icon-right:before {
  transition-duration: 0.5s;
  position: absolute;
  width: 15px;
  height: 4px;
  background-color: var(--burger);
  content: "";
  top: -10px;
}
.header_wrap .nav-button .icon-right:after {
  transition-duration: 0.5s;
  position: absolute;
  width: 15px;
  height: 4px;
  background-color: var(--burger);
  content: "";
  top: 10px;
}
.header_wrap .nav-button.toggle .icon-left {
  transition-duration: 0.5s;
  background: transparent;
}
.header_wrap .nav-button.toggle .icon-left:before {
  transform: rotateZ(45deg) scaleX(1.4) translate(2px, 2px);
}
.header_wrap .nav-button.toggle .icon-left:after {
  transform: rotateZ(-45deg) scaleX(1.4) translate(2px, -2px);
}
.header_wrap .nav-button.toggle .icon-right {
  transition-duration: 0.5s;
  background: transparent;
}
.header_wrap .nav-button.toggle .icon-right:before {
  transform: rotateZ(-45deg) scaleX(1.4) translate(-2px, 2px);
}
.header_wrap .nav-button.toggle .icon-right:after {
  transform: rotateZ(45deg) scaleX(1.4) translate(-2px, -2px);
}
.header_wrap .nav-button:hover {
  cursor: pointer;
}
/*---------log*/
.header_wrap .log { 
	display: flex;	
	position: absolute;	
	top: calc((100% - 18px) / 2);
	right: 9px;
	align-items: center;
}
.header_wrap .log img {			
	height: 18px;	
	width: auto;					
}	
.header_wrap .log span {
	font-size: 14px;
	padding-left: .18rem;	
	font-weight: 400;	
	color: var(--font);
}
@media (min-width: 360px){
	.header_wrap .brand {
		height: 33px;		
	}
	.header_wrap .log { 		
		top: calc((100% - 21px) / 2);
		right: 15px;
	}
	.header_wrap .log span {
		font-size: 16px;			
		/*font-weight: 300;*/
	}	
	.header_wrap .log img {			
		height: 21px;								
	}	
}

/*-----手機選單透明背板出現,下面頁面固定*/
.overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 055, 0.111);
  z-index: 9;
}
.overlay.active {
  display: block;
}

@media (min-width: 1200px){
	.header_wrap  {		
		height: 4.5rem; 
		padding: 0 5.4%; 		
		justify-content: flex-end;	
		justify-content: flex-start;	
		font-weight: 500;		
	}	
	.header_wrap .brand { 		
		--brand-h: 2rem; 
		height: var(--brand-h);
		padding-right: 3.6rem;
	}	
	.header_wrap .nav { 
		width: auto;  
		display: block;
		position: static; 
		height:3.6rem;
		top: auto;
		left: auto;		
		overflow: visible;		
		line-height: 3.6rem;
	}
	.header_wrap .nav ul { 
		height: auto; 
		overflow-y: visible;		
	}
	.header_wrap .nav ul ul li {
		line-height: 2.4rem; line-height: 1.98rem; 
	}
	.header_wrap .nav li { 
		position: relative;
		white-space: nowrap;
	}
	.header_wrap .nav > ul > li {
		display: inline-block;
	}
	.header_wrap .nav ul {
		display: block;
	}
	.header_wrap .nav ul ul { 
		display: none;
		position: absolute;
		top: 100%;
		left: 0;		
		z-index: 2;
		box-shadow: 0 1px 2px 0 rgba(115, 100, 99, 0.72);
		box-shadow: var(--box-sd);
	}	
	.header_wrap .nav li {			
		border-top: none;
	}
	.header_wrap .nav .nav-submenu li {
		min-width: 5.4rem;
	}
	.header_wrap .nav .nav-submenu > ul {
		margin: 0; 
		margin: .36rem 0 0;
	}
	.header_wrap .nav .nav-submenu > ul li a {
		padding: 0 1.5rem;	
	}
	.header_wrap .nav .nav-submenu > ul ul li a {
		padding: 0 1.5rem;	
	}
	.header_wrap .nav a,
	.header_wrap .nav a:hover,
	.header_wrap .nav a:active,
	.header_wrap .nav a:visited {			
		padding: 0 .72rem; /*選單間距*/
		transition: 300ms ease-in-out;		
	}
	.header_wrap .nav li:hover > a,
	.header_wrap .nav li.nav-active > a {	
		color: var(--main);
		background: transparent;
	}	
	.header_wrap .nav > ul > .nav-submenu > a:after { 
		content: "\221F"; 
		right: 0rem; 
		font-size: .6rem;
		transform: rotate(-45deg) scale(.63);		
	}	
	.header_wrap .nav > ul > .nav-submenu:hover > a:after { 
		transform: rotate(135deg) scale(.63);		
	}	
	.header_wrap .nav ul ul .nav-submenu > a:after {	
		content: '\221F';
		right: .45rem;
		font-size: .6rem;
		transform: rotate(-135deg) scale(.63);	
	}
	.header_wrap .nav ul ul .nav-submenu:hover > a:after {	
		transform: translate(.18rem, 0) rotate(-135deg) scale(.63);
	}
	/*---------各層bg*/
	.header_wrap .nav { 	
		background: transparent;	
	}
	.header_wrap .nav ul {		
		background: transparent;
	}
	.header_wrap .nav ul ul {		
		background: var(--header);
	}	
	/*---------display: none*/	
	.header_wrap .nav-button {
		display: none;
	}
	.header_wrap .nav a.sign_mo { 
		display: none;
	}
	.header_wrap .nav .sns_mo {
		display: none;
	}
	.header_wrap .nav .show_mo {
		display: none;
	}
	.header_wrap .nav .copyright {
		display: none;
	}	
	/*---------log*/
	.header_wrap  .log {		
		right: 1.35%;
	}
	.header_wrap  .log img {			
		width: 1.35rem;	
		height: auto;					
	}
	.header_wrap  .log span {		
		padding-left: .27rem;
	}	
}

