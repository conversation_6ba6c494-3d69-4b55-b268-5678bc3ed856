<?php

namespace Tests\Feature\api;

use Tests\TestCase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use App\Models\Product;

class woo104Test extends baseTest {

    public function setUp(): void {
        parent::setUp();

        $this->faker = \Faker\Factory::create('zh_TW');
        $this->myFaker = new \Database\Seeders\myFaker();
    }


    /**
     * 測試新增或更新法拍屋資料
     */
    public function test_新增或更新法拍屋資料_store() {
        // 模擬管理員登入
        //$admin = $this->getAdminUser();

        $datas = [
            'header' => [
                //'HTTP_Authorization' => 'Bearer ' . $admin->api_token,
                'CONTENT_TYPE' => 'application/json',
            ],
            'url' => '/api/woo104/store',
            'raw' => [
                'case_number' => 'A123456789',
                'address' => '台北市中正區仁愛路',
                'price' => 5000,
                'auction_round' => '第1拍',
                'bid_datetime' => '2025-05-01 10:00:00',
                'land_area' => '30',
                'land_unit_price' => '100',
                'deposit' => '500',
                'auction_result' => '流標',
                'announced_value' => '6000',
                'post_auction_appreciation' => '200',
                'building_age' => 10,
                'community_name' => '仁愛華廈',
                'land' => '台北市中正區仁愛段',
                'building' => '華廈',
                'seizure_record' => '查封紀錄',
                'auction_record' => '拍賣紀錄',
                'url' => 'https://example.com/item/123',
                'image_urls' => [
                    'https://via.placeholder.com/150',
                    'https://via.placeholder.com/200',
                ],
            ],
        ];

        // 發送 POST 請求
        $response = $this->withHeaders($datas['header'])
            ->json('POST', $datas['url'], $datas['raw']);

        // 檢查回傳 JSON 格式
        $this->checkJson($response);

        // 檢查 HTTP 狀態碼
        $response->assertStatus(200);

        // 驗證資料庫是否有此筆資料
        $this->assertDatabaseHas('product', [
            'number' => $datas['raw']['case_number'],
            'address' => $datas['raw']['address'],
            'totalupset' => 5000 * 10000,
            'city1title' => mb_substr($datas['raw']['address'], 0, 3),
            'city2title' => mb_substr($datas['raw']['address'], 3, 3),
            'url' => $datas['raw']['url'],
        ]);
    }
}
