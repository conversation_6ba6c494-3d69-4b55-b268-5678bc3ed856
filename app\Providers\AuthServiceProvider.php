<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider {
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot() {
        $this->registerPolicies();

        Gate::define('isAdmin', function () {
            if (\Auth::guard('admin')->check()) {
                return 999 == \Auth::guard('admin')->user()->role;
            }
        });
        Gate::define('isAdminRole', function ($adminuser = null, ...$roles) {
            if (in_array(\Auth::guard('admin')->user()->role, $roles)) {
                return true;
            }

            return false;
        });

        Gate::define('isAdminLimit', function ($adminuser = null, ...$limits) {
            foreach ($limits as $k => $v) {
                if (in_array($v, \Auth::guard('admin')->user()->limitss)) {
                    return true;
                }
            }

            return false;
        });

        Gate::define('isMemeberRole', function ($member, ...$roles) {
            if (in_array(\Auth::guard('member')->user()->role, $roles)) {
                return true;
            }

            return false;
        });

        Gate::define('isSeller', function () {
            return \Auth::guard('admin')->check();
        });

        Gate::define('admin', function ($adminuser, $rs) {
            if (\Auth::guard('admin')->check()) {
                return $adminuser->user()->userid == $rs->id;
            }

            return $false;
        });
        Gate::define('isCompany', function () {
            return \Auth::guard('company')->check();
        });
    }
}
