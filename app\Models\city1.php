<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use PF;
//縣市
class city1 extends baseModel
{
    

	public $tabletitle = '縣市';
    public $table = 'city1';
    public $primaryKey = 'city1id';
    
    //欄位必填
    public $rules = [
		'city1title' => 'required',
'city1id' => 'required',

    ];
    public $fieldInfo = [
'city1title'=>['title'=>'','type'=>'varchar(50)'],//
'sortnum'=>['title'=>'排序號碼','type'=>'float(5,2)'],//
'city1id'=>['title'=>'自動編號','type'=>'int(11)'],//
'online'=>['title'=>'','type'=>'int(11)'],//
'partid'=>['title'=>'地區編號','type'=>'int(11)'],//
'entitle'=>['title'=>'英文','type'=>'varchar(50)'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    
    protected $fillable = ['city1title','sortnum','online','partid','entitle']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = [];    
  /*
  public function __construct($attr = array(), $exists = false) {
        $this->fillable = parent::getfillables();//接受$request->all();
        parent::__construct($attr, $exists);
        
        parent::setFieldInfo($this->fieldInfo);
  }
*/         
  public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          

        });
         static::deleting(function ($model) {
            //  DB::table('city1')->select()
            // ->myWhere('city1id|N', $model->city1id, "city1id", 'Y')
            // ->delete();



          
        });
        static::deleted(function ($model) {
            
        });

    }	


}
