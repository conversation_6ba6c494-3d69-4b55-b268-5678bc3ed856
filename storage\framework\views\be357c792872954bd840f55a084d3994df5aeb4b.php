<?php $__env->startSection('css'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script type="application/ld+json">
      {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": "法拍-<?php echo e($data['city1title']); ?><?php echo e($data['city2title']); ?><?php echo e($data['address']); ?> - <?php echo e(PF::getConfig('name')); ?>",
        <?php if($data['img'] != null): ?>
        "image": [
          "<?php echo e(url('/')); ?>/images/product/<?php echo e(end(explode(",",$data['img']))); ?>"
        ],
        <?php endif; ?>
        "description": "<?php echo e(PF::noHtml($data['courttranscript'])); ?>",
        "sku": "",
        "mpn": "",
        "brand": {
          "@type": "Brand",
          "name": "<?php echo e(PF::getConfig('name')); ?>"
        },
        "review": {
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": 4,
            "bestRating": 5
          },
          "author": {
            "@type": "Person",
            "name": "<?php echo e(PF::getConfig('name')); ?>"
          }
        },
        <?php if($data['totalupset']!=""): ?>
        "offers": {
            "@type": "Offer",
            "priceValidUntil": "<?php echo e(date('Y-m-d', strtotime(PF::rocDateToWestDate($data['tenderdate'])))); ?>",
            "price": <?php echo e($data['totalupset']); ?>,
            "priceCurrency": "TWD"
        },
        <?php endif; ?>
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": 4.4,
          "reviewCount": <?php echo e($data['hits']!=''? $data['hits'] :0); ?>

        }

      }
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

    <section class="fc-address">
        <h2><i class="fa-solid fa-location-dot"></i>
            <?php echo e($data['number']); ?>

            &nbsp;&nbsp;&nbsp;
            <?php echo e($data['producttitle']); ?>


        </h2>
        <h3><span><?php echo e(PF::formatNumber($data['totalupset'], 0)); ?></span>萬</h3>
    </section>
    <div class="main-layout">
        <div class="main">
            <section class="slider__wrap">
                <div class="slider__flex">
                    <!-- sliders (bigPic) start-->
                    <div class="sliders">
                        <div class="swiper-container main-slider">
                            <div class="swiper-wrapper">
                                <?php if($data['img'] != ''): ?>
                                    <?php $__currentLoopData = array_reverse(explode(',', $data['img'])); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="swiper-slide">
                                            <div class="imgBox">
                                                <img src="https://www.ebayhouse.com.tw/images/product/<?php echo e($item); ?>"
                                                    alt="<?php echo e($data['producttitle']); ?>" />
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                            <!-- 添加分頁指示器 -->
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                    <!-- sliders (bigPic) end-->
                    <!-- sliders (thumbnail) start - 修改為多張上下排列且自動輪播 -->
                    <div class="slider__col">
                        <div class="slider__prev"></div>
                        <div class="slider__thumbs">
                            <div class="swiper-container thumb-slider">
                                <div class="swiper-wrapper">
                                    <?php if($data['img'] != ''): ?>
                                        <?php $__currentLoopData = array_reverse(explode(',', $data['img'])); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="swiper-slide">
                                                <div class="imgBox">
                                                    <img src="https://www.ebayhouse.com.tw/images/product/<?php echo e($item); ?>"
                                                        alt="<?php echo e($data['producttitle']); ?>" />
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="slider__next"></div>
                    </div>
                    <!-- sliders (thumbnail) end-->
                </div>
            </section>

            <section class="btnBox">
                <?php if($data['pdf'] != ''): ?>
                    <button class="pdf-button"
                        onclick="window.open('<?php echo e(url('/')); ?>/images/product/<?php echo e($data['pdf']); ?>', '_blank')">拍賣單位公告</button>
                <?php endif; ?>
                <button class="print-button"
                    onclick="window.open('<?php echo e(url('/')); ?>/house/print?productid=<?php echo e($data['productid']); ?>', '_blank')"><i
                        class="fa-solid fa-print"></i>列印</button>
            </section>

            <section class="map_wrap">
                <div class="topic">
                    <p>【 電子地圖 】</p>
                </div>
                <div class="map-container">
                    <iframe
                        src="https://maps.google.com.tw/maps?f=q&source=s_q&hl=zh-TW&geocode=&q=<?php echo e(urlencode($data['city1title'] . $data['city2title'] . $data['address'])); ?>&ie=UTF8&z=16&output=embed"
                        width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
            </section>

            <section>
                <div class="topic">
                    <p>【 免費諮詢 】</p>
                </div>
                <form class="queryForm" action="<?php echo e(url('/')); ?>/house/store" method="post">
                    <?php echo csrf_field(); ?>
                    <div class="form-row">
                        <label class="required" for="name">姓名：</label>
                        <input type="text" id="name" name="name" required placeholder="請輸入您的姓名"
                            value="<?php echo e($data['name'] ?? ''); ?>">
                    </div>

                    <div class="form-row">
                        <label for="tel">室內電話：</label>
                        <input type="tel" id="tel" name="tel" placeholder="ex xxx-xxxxxxxx#ext"
                            value="<?php echo e($data['tel'] ?? ''); ?>">
                    </div>

                    <div class="form-row">
                        <label class="required" for="mobile">行動電話：</label>
                        <input type="tel" id="mobile" name="mobile" required placeholder="ex 09123456789"
                            value="<?php echo e($data['mobile'] ?? ''); ?>" pattern="09[1-8][0-9]([\-|\s]?)[0-9]{3}\1[0-9]{3}">
                    </div>

                    <div class="form-row">
                        <label for="address">案件地址：</label>
                        <input type="text" id="address" name="memo"
                            value="<?php echo e($data['city1title']); ?><?php echo e($data['city2title']); ?> <?php echo e($data['address']); ?>">
                    </div>

                    <div class="form-row">
                        <label class="required" for="email">電子信箱：</label>
                        <input type="email" id="email" name="email" required placeholder="ex <EMAIL>"
                            value="<?php echo e($data['email'] ?? ''); ?>">
                    </div>

                    <input type="hidden" value="" name="google_recaptcha_token" id="recaptchaResponse">
                    <button type="submit">送出</button>
                </form>
            </section>

            <section class="bestvalue_wrap">
                <div class="topic">
                    <p>【 超值物件 】</p>
                </div>
                <!-- bestvalue-grid Start -->
                <div class="bestvalue-grid">

                    <?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rs): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo $__env->make('layouts.houseitem', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                </div>
                <!-- //bestvalue-grid End -->
            </section>
        </div>
        <!-- //.main End-->
        <div class="side">
            <div class="topic">
                <p>【 物件資料 】</p>
            </div>
            <div class="info-box">

                <?php if(in_array($data['productkind'], ['1', '3'])): ?>


                    <div class="info-item"><span>公告底價</span><span
                            class="eye-catch"><?php echo e(PF::formatNumber($data['totalupset'], 0)); ?> 萬</span></div>
                    <div class="info-item"><span>公告建坪</span><span><?php echo e(PF::formatNumber($data['pingtotalnumberof'], 2)); ?>

                            坪</span></div>
                    <div class="info-item"><span>每坪單價</span><span class="eye-catch"><?php echo e($data['floorprice']); ?> 萬</span>
                    </div>
                    <div class="info-item"><span>保 證 金</span><span><?php echo e(PF::formatNumber($data['totalupset'] * 0.2, 0)); ?>

                            萬</span></div>
                    <div class="info-item"><span>投標日期</span><span class="eye-catch"><?php echo e($data['tenderdate']); ?></span>
                    </div>
                    <div class="info-item"><span>狀　　態</span><span class="eye-catch"><?php echo e($data['auctions']); ?></span></div>
                    <div class="info-item"><span>拍　　次</span><span><?php echo e($data['beattime']); ?></span></div>
                    <div class="info-item"><span>主建坪數</span><span><?php echo e($data['mainlawnestablishment']); ?> 坪</span></div>
                    <div class="info-item">
                        <span>附屬建物</span><span><?php echo e(str_replace('坪', '', $data['attachedtolawnestablishment'])); ?> 坪</span>
                    </div>
                    <div class="info-item"><span>公共設施</span><span><?php echo e($data['postulateping']); ?> 坪</span></div>
                    <div class="info-item"><span>增建面積</span><span><?php echo e($data['additionalping']); ?> 坪</span></div>
                    <div class="info-item"><span>地坪</span><span><?php echo e($data['stakeholdersfloor']); ?> 坪</span></div>
                <?php else: ?>
                    
                    <div class="info-item"><span>型　　態</span><span><?php echo e($data['pattern'] ?? '未提供'); ?></span></div>
                    <div class="info-item"><span>樓層/樓高</span><span>
                            <?php if($data['floor_end']): ?>
                                <?php echo e($data['floor_end']); ?>

                            <?php endif; ?>
                            <?php if($data['storey']): ?>
                                / <?php echo e($data['storey']); ?>

                            <?php endif; ?>
                        </span></div>
                    <div class="info-item"><span>屋　　齡</span><span><?php echo e($data['houseage'] . '年' ?? ''); ?></span></div>
                    <div class="info-item"><span>格　　局</span><span>
                            <?php if($data['room_count'] || $data['living_room_count'] || $data['hall_count'] || $data['bathroom_count']): ?>
                                <?php echo e($data['room_count'] ?? '0'); ?>房
                                <?php echo e($data['living_room_count'] ?? '0'); ?>廳

                                <?php echo e($data['bathroom_count'] ?? '0'); ?>衛
                                <?php if($data['balcony_count']): ?>
                                    <?php echo e($data['balcony_count']); ?>陽台
                                <?php endif; ?>
                            <?php else: ?>
                                未提供
                            <?php endif; ?>
                        </span></div>
                    <div class="info-item"><span>社區名稱</span><span><?php echo e($data['buildname'] ?? '未提供'); ?></span></div>
                    <div class="info-item"><span>總坪數</span><span class="eye-catch">
                            <?php echo e(PF::formatNumber(
                                ($data['mainlawnestablishment'] ?? 0) +
                                    (str_replace('坪', '', $data['attachedtolawnestablishment']) ?? 0) +
                                    ($data['postulateping'] ?? 0) +
                                    ($data['carping'] ?? 0),
                                2,
                            )); ?>

                            坪</span></div>
                    <div class="info-item"><span>主建坪數</span><span><?php echo e($data['mainlawnestablishment']); ?> 坪</span></div>

                    <div class="info-item">

                        <span>附屬坪數</span><span><?php echo e(str_replace('坪', '', $data['attachedtolawnestablishment'])); ?> 坪</span>
                    </div>
                    <div class="info-item"><span>公設坪數</span><span><?php echo e($data['postulateping']); ?> 坪</span></div>
                    <div class="info-item"><span>車位坪數</span><span><?php echo e($data['carping']); ?> 坪</span></div>
                    <div class="info-item"><span>土地持分</span><span><?php echo e($data['stakeholdersfloor']); ?> 坪 </span></div>
                    <div class="info-item"><span>車位型式</span><span><?php echo e($data['parkingmode']); ?></span></div>
                    <div class="info-item"><span>建物結構</span><span><?php echo e($data['architecture']); ?></span></div>

                <?php endif; ?>
            </div>
        </div>
        <!-- //.side End-->
    </div>

    <!-- Google reCAPTCHA Scripts -->
    <script src="https://www.google.com/recaptcha/api.js?render=<?php echo e(config('recaptcha.id')); ?>"></script>
    <script>
        $("button").each(function(index) {
            $(this).prop('disabled', true);
        });
        grecaptcha.ready(function() {
            grecaptcha.execute("<?php echo e(config('recaptcha.id')); ?>", {
                action: 'homepage'
            }).then(function(token) {
                var recaptchaResponse = document.getElementById('recaptchaResponse');
                recaptchaResponse.value = token;
                $("button").each(function(index) {
                    $(this).prop('disabled', false);
                });
            });
        });
    </script>
    <style>
        .grecaptcha-badge {
            visibility: hidden;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\AppServ\laravel\ebayhouse\resources\views/house/show.blade.php ENDPATH**/ ?>