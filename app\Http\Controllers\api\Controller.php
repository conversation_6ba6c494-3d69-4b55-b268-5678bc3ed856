<?php

namespace App\Http\Controllers\api;

use Illuminate\Routing\Controller as BaseController;

/***
"功能名稱":"被Controller繼承",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 16:42:06",
 ***/
/**
 * @OA\Server(url="https://allennb.com.tw:440/ebayhouse/public/",description="測試")
 * @OA\Server(url="https://www.ebayhouse.com.tw/",description="正式"),
 * @OA\Info(title="API document", version="0.1")
 */

class Controller extends BaseController {
  public $jsondata;
  public function __construct() {
    $this->jsondata['resultcode'] = 0;
    $this->jsondata['resultmessage'] = '';
    if ('production' == \config('app.env')) {
      header('Access-Control-Allow-Origin: ' . $_SERVER['HTTP_ORIGIN']);
      header('Access-Control-Allow-Credentials: true');
      header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
      header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Accept');
      header('P3P: CP=ALL ADM DEV PSAi COM OUR OTRo STP IND ONL');
    }
    //PF::printr(4);
    //PF::printr($this->limit);
  }
  public function apiResponse($data, $code = 200) {

    return response()->json($data, $code, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
  }
}
