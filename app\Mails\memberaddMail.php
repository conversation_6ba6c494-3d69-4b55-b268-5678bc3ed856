<?php

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use DB;
use PF;

class memberaddMail extends Mailable
{
    public $tries = 3;
    use Queueable;
    use SerializesModels;
    public $id;
    public $password;

    // 讓外部可以將參數指定進來
    public function __construct($id,$password)
    {
        $this->id = $id;
        $this->password = $password;
    }

    public function build()
    {
        $rows = DB::table('member');
        $rows->selectRaw('*');
        $rows->myWhere('id|N', $this->id, 'id', 'Y');
        $rows->limit(1);
        if ($rows->count() > 0) {
            $rs = $rows->first();            
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
            $this->data['password'] = $this->password;
            
            $this->from(PF::getFromEmail(), config('config.name'));

            $this->subject(config('config.name')."-加入會員");
            //$this->view('email.'.$rs->alg.'.memberadd');
            $this->view('email.memberadd');
            $this->with($this->data);
            $this->to($rs->email);
        //    $this->cc(explode(';', config('config.email')));
        //$this->bcc(config('config.'.$rs->alg.'.email'));
        } else {
            throw new \CustomException('查無此記錄');
        }

        //PF::dbSqlPrint($rows);

        return $this;
    }
}
