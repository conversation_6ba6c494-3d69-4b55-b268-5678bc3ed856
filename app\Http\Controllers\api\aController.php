<?php

namespace App\Http\Controllers\api;

use PF;
use Illuminate\Http\Request;
use DB;

class aController extends Controller {
    private $data;
    private $xmldoc;

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        if ('allennb.com.tw' != request()->getHost()) {
            throw new \CustomException('no limit');
        }
    }
    public function index(Request $request) {
        $this->jsondata['resultcode'] = 0;
        $this->jsondata['resultmessage'] = '更新成功';
        return $this->apiResponse($this->jsondata);
    }
}
