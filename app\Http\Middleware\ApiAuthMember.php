<?php

namespace App\Http\Middleware;

use PF;
use Closure;

// $request->member['memberid']
class ApiAuthMember {
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next) {
        if ('production' != \config('app.env')) {
            \Log::info([
                'header' => ['HTTP_Authorization' => 'Bearer ' . $request->bearerToken(), 'CONTENT_TYPE' => 'application/json'],
                'url' => \Str::after($request->getRequestUri(), 'public/'),
                'raw' => null != $request->getContent() ? json_decode($request->getContent(), true) : null,
                'post' => $request->all(),
            ]);
        }
        $token = $request->input('token');
        if ('' == $token) {
            $token = $request->bearerToken();
        }

        if ('production' != \config('app.env')) {
            $member = \App\Models\member::where('mobile', '0960014725')->first();
        } else {
            if (PF::isEmpty($token)) {
                abort(403);
            }

            $member = \App\Models\member::where('api_token', $token)->first();
        }

        if (null != $member) {
            \Auth::guard('member')->login($member, true);
        } else {
            abort(403);
        }

        return $next($request);
    }
}
