<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Input;
use PF;
use PT;
use DB;

class adminuserloginlogController extends adminController
{
    private $fieldnicknames;
    private $data;
    private $xmldoc;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'adminuserloginlog');

        $this->fieldnicknames = [
'account' => '帳號',
'clientip' => 'Clientip',
'created_at' => '登入時間',
'loginstatus' => '登入狀態',
'logouttime' => '登出時間',
        ];
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
     
        
            $fieldsearchname = [
                '' => '請選擇',
                'account' => $this->fieldnicknames['account'],
'clientip' => $this->fieldnicknames['clientip'],
'loginstatus' => $this->fieldnicknames['loginstatus'],
            ];
            $fieldsearchdatename = [
'created_at' => $this->fieldnicknames['created_at'],
'logouttime' => $this->fieldnicknames['logouttime'],
            ];
            $this->data['fieldsearchname'] = $fieldsearchname;
            $this->data['fieldsearchdatename'] = $fieldsearchdatename;

            // 開啟記錄sqlcmd顯示
            //DB::enableQueryLog();
            $rows = DB::table('adminuserloginlog')->select('adminuserloginlog.*');
            if ('999' != \Auth::guard('admin')->user()->role) {
                $rows->myWhere('account|S', \Auth::guard('admin')->user()->account, 'account', 'Y');
            }

            $rows->myWhere($this->data['searchname'], $this->data['search'], $this->fieldnicknames, 'N');

            //依條件時間搜尋資料的SQL語法
            $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|>=', $request->input('searchstartdate'), $this->fieldnicknames, 'N');
            $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|<=', $request->input('searchenddate'), $this->fieldnicknames, 'N');

            if ($this->data['sortname']) {
                $rows->orderBy($request->input('sortname'), $request->input('sorttype')=="desc"  ? $request->input('sorttype') : "asc"  );
            } else {
                $rows->orderBy('admiuserloginid', 'desc');
            }
            $rows = $rows->paginate(10);

            // 顯示sqlcmd
            //print_r(DB::getQueryLog());

            $this->data['hidden'] = array();

            array_push($this->data['hidden'], 'account');

            $this->data['rows'] = $rows;
            //PF::printr($this->data['rows']);
            return view('admin.adminuserloginlog.index', [
                'data' => $this->data,
            ]);
    
    }

    /**
     * 資料建立顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        try {
            return view('admin.adminuserloginlog.edit', [
                'data' => $this->data,
            ]);
        } catch (\Exception $e) {
            //throw $e;
            die($e->getMessage());
        }
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
    }
}

