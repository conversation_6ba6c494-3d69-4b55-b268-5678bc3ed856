/*----------------------------------*\
  # foreclosure-address                     
\*----------------------------------*/
.fc-address { 
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  max-width: 63rem;
  margin: 0 auto;
}
.fc-address h2 {  
  font-size: var(--big);
  font-family: var(--base);
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 0rem;
  padding: .9rem 0;
}
.fc-address h2 i {
  padding-right: .36rem;
  color: var(--main);
}
.fc-address h3 { 
  font-size: var(--big);
  font-family: var(--mix);
  color: var(--main);
  font-weight: 600;
  line-height: 1;
  margin-bottom: 0rem;
  white-space: nowrap;
  padding: .9rem 0;  
}
.fc-address h3 span {
  font-size: 180%;
  font-weight: 700;
  padding-right: .36rem;
  font-style: italic;
}
@media (max-width: 415px){
  .fc-address h3 {
    font-size: var(--p);    
  }
}
/*----------------------------------*\
  # main-layout                      
\*----------------------------------*/
.main-layout {   
  display: flex; 
  width: 90%;
  max-width: 63rem;
  margin: 0 auto;  
}
.main-layout .main {  
  width: calc(100% - 15.3rem - 1.8rem);
  flex: 1 0 auto;   
  margin-right:1.8rem;  
}
.main-layout .side {  
  --rowH:1.8rem; 
  position: sticky;
  top:calc(var(--header-h) + .54rem);
  right: 4.5%;  right: 7.55%;
  width: 15.3rem;  
  height: calc(var(--rowH) * 13 + .9rem); 
  background: #FFF;
  border-radius: .36rem;
  box-shadow: var(--box-sd);  
  margin-bottom: 4.5rem;
}
@media (max-width: 1199px){  
  .main-layout {
    flex-direction: column;    
  }
  .main-layout .main {
    order: 2;
    width: 100%;
    margin-right: 0px;    
  }
  .main-layout .side {
    order: 1; 
    position: static;
    height: auto;
    width: 100%;
    right: 0px;     
  }
}

/*----------------------------------*\
  # side info                     
\*----------------------------------*/
.main-layout .side .info-box {
  width: 81%;
  padding: .45rem 0;
  margin: 0 auto;  
  font-size: var(--tiny); 
  font-weight: 400;  
}
.main-layout .side .info-item {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;   
  height: var(--rowH);
  line-height: var(--rowH);
}
.main-layout .side .info-item:last-child{  
  border-bottom: none;  
}
.main-layout .side .info-item span.eye-catch {
  color: var(--mate);  
}

/*----------------------------------*\
  # slider                     
\*----------------------------------*/
.slider__wrap {  
  max-width: 630px;
  width: 100%;
}
.slider__wrap .swiper-container { 
  border-radius: .36rem;
  box-shadow: var(--box-sd);  
  width: 100%;
  height: 100%;
}
.slider__flex {
  display: flex;
  align-items: flex-start;
}
.slider__col {
  display: flex;
  flex-direction: column;
  width: 99px;
  margin-left: 9px;  
}
.slider__prev,
.slider__next { 
  cursor: pointer;
  text-align: center;
  height:24px; 
  width: 30px; 
  margin-left: 36px;
  background: url(../images/aw.svg) no-repeat center center/100% auto; 
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; 
}
.slider__next {  
  margin-top: 6px;  
}
.slider__prev{  
  margin-bottom: 6px;   
  transform:  rotate(180deg) ;
}
.slider__prev:focus,
.slider__next:focus {
  outline: none;
}
.slider__wrap .swiper-button-disabled { 
  opacity: .18;
  cursor: not-allowed;
}
.slider__thumbs {  
  height: calc(378px - 60px);
}
.slider__thumbs .imgBox {
  transition: 0.25s;
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
  opacity: 0.5;
}
.slider__thumbs .imgBox:hover {
  opacity: 1;
}
.slider__thumbs .swiper-slide-thumb-active .imgBox {
  -webkit-filter: grayscale(0%);
  filter: grayscale(0%);
  opacity: 1;
}
.slider__wrap .sliders {  
  box-shadow: 0 1px 2px 2px rgba(0, 0, 0, 0.24);
  border-radius: .36rem;
  width: calc(100% - 100px);
  height: 378px;
}
.slider__wrap .sliders .imgBox img { 
  filter: brightness(1.0) contrast(1.2);
  transition: 3s;
}
.slider__wrap .sliders .imgBox:hover img {
  transform: scale(1.1);
}
.slider__wrap .imgBox {
  width: 100%;
  height: 100%;  
  overflow: hidden;
}
.slider__wrap .imgBox img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
@media (max-width: 1199px) {
   .slider__wrap {  
    max-width: 100%;    
  }
}
@media (max-width: 767.98px) { 
  .slider__wrap .sliders { 
    height: 243px;
  }
  .slider__thumbs { 
    height: calc(243px - 60px);
  }
  .slider__flex {    
    flex-direction: column;
  }
  .slider__col {
    flex-direction: row;
    align-items: center;
    margin-left: 0;
    margin-top: 4.5px;
    width: 100%;
  }
  .slider__wrap .sliders { 
    width: 100%;
  }
  .slider__thumbs { 
    height: 45px;  
    width: 100%; 
  }  
  .slider__next {    
    margin-left: 0px;    
    transform:  rotate(-90deg) ; 
  }
  .slider__prev{    
    margin-left: 0px;
    margin-right: 3px;    
    transform:  rotate(90deg);
  }
}

/*----------------------------------*\
  # pdf-button                     
\*----------------------------------*/
.btnBox {
  width: 100%;
  max-width: 630px;
  display: flex;
  justify-content: space-between;
}
.btnBox button {
  margin-top: .63rem;
  padding: .36rem 1.35rem;
  background-color: var(--mate);
  color: white;
  font-size:var(--tiny);
  border: none;
  border-radius: .36rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.btnBox button:hover {  
  background-color: #db4613;  
}
.btnBox button.print-button {
  margin-left: .9rem;
  background-color: var(--gold);
}
.btnBox button.print-button:hover {  
  background-color: #a47938;
}
.print-button i {
  padding-right: .36rem;
}
@media (max-width: 1199px) {  
  .btnBox {    
    max-width: 100%;    
  }
}
/*----------------------------------*\
  # topic                     
\*----------------------------------*/
.main-layout .topic { 
  border-radius: .36rem .36rem 0 0;
  margin-top: .63rem;
  width: 100%;
  background: var(--main);
  height: 2rem;
  line-height: 2rem;
  text-align: center;
  color: white;
  font-weight: 500; 
}
.main-layout .side .topic{
  margin-top: 0rem;
}
/*----------------------------------*\
  # google map                     
\*----------------------------------*/
.map-container {
  border-radius:0 0 .36rem .36rem;
  padding: .63rem;
  width: 100%;
  height: 18rem;
  background: white;
  box-shadow: var(--box-sd);
}
.map-container iframe {
  width: 100%;
  height: 100%;
  border: 0;
}
@media (max-width: 1199px) {  
  .map-container {   
    height: 13.5rem;    
  }
}
@media (max-width: 768px) {  
  .map-container {   
    height: 12rem;    
  }
}
/*----------------------------------*\
  # form                     
\*----------------------------------*/
.queryForm {
  background: white;
  padding:.9rem 1.8rem;
  width: 100%; 
  border-radius: 0 0 .36rem .36rem;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  box-shadow: var(--box-sd);
  font-size: var(--tiny);
}
.queryForm .form-row {
  display: flex; 
  align-items: center; align-items: flex-end;
  margin-bottom: 1rem;
}
.queryForm .form-row label { border: 0px solid red;
  font-size: var(--tiny);
  width: 5.4rem; 
  width: calc(var(--tiny) * 5.5); 
  margin-right: .54rem;
  text-align: right;
  white-space: nowrap;
}
.queryForm .form-row input {
  flex: 1;
  padding: .36rem; 
}
.queryForm .form-row input::placeholder {
  color: #999; 
  color: var(--main);
  opacity: .36;
}
.queryForm .required::before {
  content: "*";
  color: red;
  margin-right: 0.25em; 
}
.queryForm button {  
  margin: 1rem auto 0; 
  padding: .36rem 2.4rem; 
  border-radius: .36rem;  
}
.queryForm button:hover {
  background-color: #e67000;
}

/*----------------------------------*\
  # Best Value Property                     
\*----------------------------------*/
.bestvalue_wrap {
  margin-bottom: 4.5rem;
}
.bestvalue-grid {
  white-space: nowrap;
  width: 100%;
  padding: .63rem;
  background: var(--bestBg); 
  border-radius: 0 0 .36rem .36rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: .9rem;
}
.bestvalue-grid .heart-icon {
  position: absolute;
  bottom: .45rem;
  right: .63rem;
  width: 1.2rem;
  height: 1.2rem;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  transition: transform 0.2s;
  z-index: 2;
  background-image: url('../images/heart_not.svg'); 
}
.bestvalue-grid .heart-icon.active {
  background-image: url('../images/heart_ok.svg'); 
}
.bestvalue-grid .imgBox {
  position: relative;
  width:100%;  
  padding-bottom:72%;
}
.bestvalue-grid .imgBox img {  
  position:absolute;  
  top:0;  
  left:0;
  display:block;
  width:100%;
  height:100%; 
  object-fit:cover;
  object-position:50% 50%;  
}
a.property-card,
a.property-card:visited,
a.property-card:hover  {
  text-decoration:none; 
  color:inherit;  
}
.bestvalue-grid .property-card {
  background: #fff;
  border-radius: .54rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);box-shadow: var(--box-sd);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.bestvalue-grid .property-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
.bestvalue-grid .imgBox {
  overflow: hidden;
}
.bestvalue-grid .property-card img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease, filter 0.5s ease;
}
.bestvalue-grid .property-card:hover img {
  transform: scale(1.05);
  filter: brightness(1.1) drop-shadow(0 0 10px rgba(255, 200, 0, 0.6));
}
.bestvalue-grid .property-info {
  padding: .63rem;
}
.bestvalue-grid .property-info h2 {
  font-size: .86rem;font-size: .81rem;
  font-family: var(--base);
  line-height: 1.11;
  margin-bottom: .36rem;
  white-space: nowrap; 
  overflow: hidden; 
  text-overflow: ellipsis;   
  padding-bottom: .54rem;
  border-bottom: 1px dotted var(--mate);
}
.bestvalue-grid .info-row { 
  display: flex;
  align-items: flex-end;
  justify-content: space-between; 
  --fontSize:.711rem;
  color: var(--strong); 
  font-family: var(--base); 
} 
.bestvalue-grid .info-row p {
  margin-bottom: 0;   
  font-size: var(--fontSize);
  font-weight: 400;
}

.bestvalue-grid .info-row p:nth-child(2){ 
  width: calc(var(--fontSize) * 6.3); 
}
.bestvalue-grid .info-row span {
  padding-left: .18rem;
}

.bestvalue-grid .info-row span.num,
.bestvalue-grid .info-row span.price {
  font-family: var(--mix);
  font-size: 120%;
  font-weight: 500;
  padding-right: .09rem;
}
.bestvalue-grid .info-row span.price {
  color: var(--mate);
  font-weight: 700;
}
.bestvalue-grid .property-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease, filter 0.5s ease;
}

@media (max-width: 1199px) {  
  .bestvalue-grid {    
    grid-template-columns: repeat(2, 1fr);    
  }
}
@media (max-width: 767px) {
  .bestvalue-grid {    
    grid-template-columns: repeat(1, 1fr);    
  }
}