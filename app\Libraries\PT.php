<?php

namespace App\Libraries;

use DB;
use PF;

class PT {
    public static function nav($xmldoc, $controller, $subtitle = '') {
        if ('NULL' == gettype($xmldoc)) {
            $xmldoc = PF::xmlDoc('Setup.xml');
        }
        $xPath = "//參數設定檔/權限/選單/KIND/傳回值[.='" . $controller . "']/../..";
        $objxml1 = $xmldoc->xpath($xPath);
        if (!empty($objxml1)) {
            $title .= '<li class="breadcrumb-item">' . $objxml1[0]['主選單名稱'] . '</li>';
            $role = strval($objxml[0]['角色']);
        }

        $xPath = "//參數設定檔/權限/選單/KIND/傳回值[.='" . $controller . "']/parent::*";
        $objxml = $xmldoc->xpath($xPath);
        if (!empty($objxml)) {
            $title .= '<li class="breadcrumb-item active" aria-current="page">' . $objxml[0]->資料 . '</li>';

            if ('' == $role) {
                $role = strval($objxml[0]['角色']);
            }
        }

        $title = trim($title);
        if ($controller == $title || '' == $title || '>' == $title) {
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/傳回值[.='" . $controller . "']/../../..";
            $objxml1 = $xmldoc->xpath($xPath);
            if (!empty($objxml1)) {
                $title .= '<li class="breadcrumb-item">' . $objxml1[0]['主選單名稱'] . '</li>';
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/傳回值[.='" . $controller . "']/../..";
            $objxml1 = $xmldoc->xpath($xPath);
            if (!empty($objxml1)) {
                $title .= '<li class="breadcrumb-item" aria-current="page">' . $objxml1[0]->資料 . '</li>';
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/KIND/傳回值[.='" . $controller . "']/../..";
            $objxml1 = $xmldoc->xpath($xPath);
            if (!empty($objxml1)) {
                $title .= '<li class="breadcrumb-item" aria-current="page">' . $objxml1[0]->資料 . '</li>';
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/傳回值[.='" . $controller . "']/parent::*";
            $objxml = $xmldoc->xpath($xPath);
            if (!empty($objxml)) {
                $title .= '<li class="breadcrumb-item active" aria-current="page">' . $objxml[0]->資料 . '</li>';
                if ('' == $role) {
                    $role = strval($objxml[0]['角色']);
                }
            }
            $xPath = "//參數設定檔/權限/選單/KIND/KIND/KIND/傳回值[.='" . $controller . "']/parent::*";
            $objxml = $xmldoc->xpath($xPath);
            if (!empty($objxml)) {
                $title .= '<li class="breadcrumb-item active" aria-current="page">' . $objxml[0]->資料 . '</li>';
                if ('' == $role) {
                    $role = strval($objxml[0]['角色']);
                }
            }
        }
        // if (\Auth::guard('company')->check()) {
        //     \Auth::guard('company')->user()->authorize([
        //         //'xmldoc'=>$xmldoc,
        //         'role' => $role, //角色(如果有傳xmldoc則不用傳role)
        //         'controller' => $controller, //功能代碼
        //     ]);
        // }else{
        \Auth::guard('admin')->user()->authorize([
            //'xmldoc'=>$xmldoc,
            'role' => $role, //角色(如果有傳xmldoc則不用傳role)
            'controller' => $controller, //功能代碼
        ]);
        //}

        //權限管理

        $html = '<nav aria-label="breadcrumb">';
        $html .= '<ol class="breadcrumb">';
        $html .= '<li class="breadcrumb-item">' . __('位置') . '</li>';
        $html .= '<li class="breadcrumb-item">' . __('首頁') . '</li>';
        $html .= $title;
        if ('' != $subtitle) {
            $html .= '<li class="breadcrumb-item">' . $subtitle . '</li>';
        }

        $html .= '</ol>';
        $html .= '</nav>';

        return $html;
        //return '&nbsp; &nbsp; '.__('位置').' > '.__('首頁').' > '.$Title;
    }

    public static function randomkeys($len = 6, $chars = '1234567890') {
        // characters to build the password from
        mt_srand((float) microtime() * 1000000 * getmypid());
        // seed the random number generater (must be done)
        $password = '';
        while (strlen($password) < $len) {
            $password .= substr($chars, (mt_rand() % strlen($chars)), 1);
        }

        return $password;
    }

    public static function getEpostBody($epostid) {
        $epostbody = '';
        $rows = DB::table('epost');
        $rows->selectRaw('epostbody');
        //$rows->myWhere('alg|S', app()->getLocale(), 'lg', 'Y');
        $rows->myWhere('epostid|S', $epostid, 'epostid', 'Y');
        $rows->limit(1);
        //PF::dbSqlPrint($rows);
        if ($rows->count() > 0) {
            $rs = $rows->first();
            $epostbody = $rs->epostbody;
        }

        return $epostbody;
    }
    // <KIND 角色="limit">
    //     <資料>最新活動</資料>
    //     <傳回值>activity</傳回值>
    //     <網址>activity</網址>
    // </KIND>
    public static function checkRoleLimits($roles, $controller) {
        if ('999' == \Auth::guard('admin')->user()->role) {
            return true;
        }

        $roles = strval($roles);

        if ('ALL' == $roles) {
            return true;
        } elseif ('limit' == $roles) {
            if ('' == \Auth::guard('admin')->user()->limits) {
                return false;
            }
            if (in_array($controller, explode(',', \Auth::guard('admin')->user()->limits))) {
                return true;
            }
        } else {
            if (in_array(\Auth::guard('admin')->user()->role, explode(',', $roles))) {
                return true;
            }
        }

        return false;
    }
}
