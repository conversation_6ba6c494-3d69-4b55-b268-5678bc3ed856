---
description: make test
globs:
alwaysApply: false
---
# Laravel PHPUnit 測試規範指南

## 適用範圍
- 適用於所有 Laravel 專案的 PHPUnit 測試生成
- 當需求涉及 Laravel 元素（模型、控制器、遷移檔等）時，必須遵循此規範

## 主要目標
- 生成符合 Laravel PHPUnit 測試框架慣例的測試類別
- 依照專案結構建立測試檔案至正確路徑

## 測試框架與環境
- 使用 PHPUnit 測試框架，確保與最新版 Laravel 相容
- 遵循 Laravel 測試最佳實踐原則
- 使用 `phpunit.xml` 配置檔案執行測試

## 檔案結構與命名規範

### 檔案路徑對應關係
- 依據來源目錄產生對應的測試目錄：
  - `app/Http/Controllers/api` → `Tests/Feature/api`
  - `app/Http/Controllers/api/admin` → `Tests/Feature/api/admin`
  - `app/Services` → `Tests/Service`

### 類別命名與繼承
- 類別命名使用**小駝峰式**（如：`userTest`）
- 將控制器測試名稱從 `xxControllerTest` 簡化為 `xxTest`
- 必須繼承對應目錄的 `baseTest` 類別，而非直接繼承 `TestCase`
- 必須正確匯入對應的 `baseTest` 類別，例如：
  ```php
  namespace Tests\Feature\api\admin;

  class exampleTest extends \Tests\Feature\api\admin\baseTest
  ```

## 測試方法規範

### 方法命名與結構
- 測試方法命名格式：`test_功能中文描述_原函式名稱`
  - 例如：`test_取得使用者資料_index`、`test_刪除訂單_destroy`
- 僅針對控制器現有函式產生對應測試方法，不要新增其他測試方法

### HTTP 請求測試寫法
- 請求路徑必須包含函式名稱：
  - `$this->post('/路徑/{{函式名稱}}')`
  - `$this->get('/路徑/{{函式名稱}}')`
- API 路徑測試必須加入適當的認證標頭：
  - `api/admin` 目錄：`Bearer $this->adminuser->api_token`
  - `api/membercenter` 目錄：`Bearer $this->member->api_token`
  - `api/` 不用加 `Bearer`

### 必要的測試檢查
- 每個測試方法必須包含以下元素：
  1. 註解行：`//echo $response->getContent();`（用於調試）
  2. 回應格式檢查：
     - API 目錄：`$this->checkJson($response)`
     - 非 API 目錄：`$this->checkHtml($response)`
  3. HTTP 狀態碼檢查：`assertStatus(200)`
  4. 資料檢查：
     - JSON 回應：`assertJsonPath()` 檢查結構
     - 資料庫操作：
       - `store` 方法：使用 `assertDatabaseHas()` 檢查資料是否已新增
       - `destroy` 方法：使用 `assertDatabaseMissing()` 檢查資料是否已刪除

## 測試資料準備

### Factory 使用規範
- 測試資料必須使用 Factory 產生，禁止使用 DB::insert
- 所有測試資料準備必須在 `setUp()` 方法中完成
- 產生 Factory 前，先檢查同目錄的 `baseTest.php` 是否已包含相關 Factory
- 若資料表有外鍵關聯，必須一併產生相關上層資料表的 Factory

### 測試環境設定
- 測試開始必須包含：`public function setUp(): void {parent::setUp();}`
- 禁止使用 `DB::shouldReceive()` 和 Mock 寫法
- 僅撰寫成功路徑的測試案例

## 註解要求
- 使用**繁體中文**撰寫所有註解
- 註解風格必須簡潔清晰，明確描述測試目的與邏輯
- 每個測試方法至少包含一行註解，說明該測試的功能與預期結果

## 執行與驗證
- 測試前必須先讀取 `/dbspec.md` 檔案，了解資料表架構
- 測試完成後使用 Windows PowerShell 執行：
  ```
  php vendor/bin/phpunit -d APP_ENV=testing --configuration=phpunit.xml
  ```
- 必須檢視執行結果並修正所有錯誤，直到測試通過