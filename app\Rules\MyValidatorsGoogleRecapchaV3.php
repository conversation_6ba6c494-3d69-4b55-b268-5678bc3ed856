<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

/***
"功能名稱":"Validators-判斷 GoogleRecapchaV3",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 16:51:34",
***/
/*
<script
src="https://www.google.com/recaptcha/api.js?render={{config('recaptcha.id')}}"></script>
<script>
grecaptcha.ready(function() {
grecaptcha.execute('{{config('recaptcha.id')}}', {action: 'homepage'}).then(function(token) {
var recaptchaResponse = document.getElementById('recaptchaResponse');
recaptchaResponse.value = token;
});
});
</script> <input type="hidden" value="" name="google_recaptcha_token" id="recaptchaResponse">
*/
//$validators['google_recaptcha_token'] = ['required', 'string', new \App\Rules\MyValidatorsGoogleRecapchaV3()];
class MyValidatorsGoogleRecapchaV3 implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        //if (true == env('RECAPTCHA_ENABLE')) {
        return $this->verify($value);
        //}

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return ':attribute.failed';
    }

    /**
     * Verify token.
     */
    private function verify(string $token = null): bool
    {
        // $url = env('RECAPTCHA_URL', 'https://www.google.com/recaptcha/api/siteverify');

        // $response = (new \Client())->request('POST', $url, [
        //     'form_params' => [
        //         'secret' => \config('recaptcha.secret'),
        //         'response' => $token,
        //     ],
        // ]);
        if (\PF::isEmpty($token)) {
            throw new \CustomException('token is empty');
        }

        try {
            $headers = [
           ];
            $recaptcha_secret = \config('recaptcha.secret');

            $response = \Http::withHeaders($headers)->withOptions([
            'connect_timeout' => 15,
            'timeout' => 20,
             'verify' => false,
            ])->send('POST', 'https://www.google.com/recaptcha/api/siteverify', [
                    'form_params' => [//post
                    'secret' => $recaptcha_secret,
                    'response' => $token,
                ],
            ]);
            if (false == $response->ok()) {
                throw new \CustomException('Http Error:'.$response->body().'('.$response->status().')');
            }
        } catch (\Exception $e) {
            throw new \CustomException($e->getMessage());
        }

        $code = $response->getStatusCode();
        $content = json_decode($response->getBody()->getContents());

        return 200 == $code && true == $content->success;
    }
}
