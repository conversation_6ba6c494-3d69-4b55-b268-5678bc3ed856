<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/*上稿系統*/
class CreateepostTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        if (!Schema::hasTable('epost')) {
        Schema::create('epost', function (Blueprint $table) {
            
            $table->string('epostid', 50)->index()->comment('自動編號');
            $table->string('eposttitle', 100)->nullable()->comment('標題');
            $table->text('epostbody')->nullable()->comment('本文');
            $table->string('alg', 10)->nullable()->comment('語系');
            $table->integer('userid')->nullable()->comment('編輯人員');
            $table->string('useraccount', 50)->nullable()->comment('編輯人員');
            $table->timestamps();

            /*
            $table->timestamps();
            $table->string('kind')->index()->comment('種類');
            $table->dateTime('begindate')->nullable()->comment('開始時間');
            $table->dateTime('created_at')->nullable()->comment('建立時間');
            $table->dateTime('updated_at')->nullable()->comment('更新時間');
            $table->integer('hits')->default(0)->comment('點率次數');
            $table->float('boardsort', 5, 3)->nullable()->comment('排序號碼');
            $table->integer('userid')->nullable()->comment('編輯人員');
            $table->string('useraccount', 50)->nullable()->comment('編輯人員');
            $table->string('account',50)->unique();;
            $table->unique(array('kind', 'kindid'));
            $table->index(array('kind', 'kindid'));
            */
        });
    }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('epost');
    }
}
