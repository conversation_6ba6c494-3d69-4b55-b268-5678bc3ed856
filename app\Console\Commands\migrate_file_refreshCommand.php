<?php

namespace App\Console\Commands;

use DB;
use Illuminate\Console\Command;

//command php artisan features:create
class migrate_file_refreshCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate_file:refresh {table_name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '還原單一migrate';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        //$Database = \config('database.connections.mysql.database');
        //$tables = DB::connection()->getDoctrineSchemaManager()->listTableNames();
        //$tablename = $this->choice('資料庫?', $tables);
        $artisanService = new \App\Services\artisanService();
        $artisanService->migraterefresh($this->argument('table_name'));
        echo 'OK';
        // //try {
        // $sourceDir = base_path('database\migrations');

        // $mydir = dir($sourceDir);
        // while ($file = $mydir->read()) {
        //     if (('.' != $file) and ('..' != $file)) {
        //         if (mb_substr_count($file, '.php') > 0) {
        //             $file = str_replace('.php', '', $file);
        //             $files[] = $file;
        //         }
        //     }
        // }

        // $file = $this->choice('選擇您要還原的檔案?', $files);
        // //$kind = $this->ask($body);
        // sapi_windows_cp_set(65001);
        // ////php artisan migrate:refresh --path=/database/migrations/2020_04_02_112656_create_lg_table.php
        // $msg .= \Artisan::output();
        // echo "php artisan migrate:refresh --path=/database/migrations/".$file.".php";
        // $this->call('migrate:refresh', [
        //      '--path' => "/database/migrations/".$file.".php",
        // ]);

        //echo $msg;
    }
}
