function PF_Lg(str){
	switch (str){
		case "請上傳":	
			return 'Please upload';
			break;
		case "未填":
			return ' is required';
		 	break;
		case "請勾取其一":
		case "請選擇其一":
			return ' Please choose';
			break;
		case "格式錯誤":
			return ' not carrect';
			break;
		case "已經存在"	:
			return "Already exists";
			break;
			
		case "請輸入數字":
			return ' Please enter numbers';
			break;		
		case "長度必須為8~20位":
			return " Length must be 8~20 digits"
			break;
	       case "請用4~20碼之英文字母或數字，英文有大小寫之分，切勿用全形和其它特殊符號，如.,!@#$%^&*()等":
	       	return "Use 4 to 20 yards of letters or numbers, the English case-sensitive, do not use full-width, and other special symbols, such as .,!@#$%^&*()";			
			break;
		case "分機":
			return "ext";			
			break;
		case "網址前面請輸入http://或https://格式":
			return "Enter the URL http:// or https:// in front of formats";			
			break;
		default:
			return str
			break;
		
	}	


}