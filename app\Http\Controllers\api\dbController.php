<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use PF;
use DB;

//use Illuminate\Support\Facades\DB;

class dbController extends Controller
{
    private $data;

    /**
     *建構孝.
     */
    public function __construct()
    {
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function isunique(Request $request)
    {
        //var sendData = "DbTable=" + DbTable + "&Field=" + Field.name + "&Fieldvalue=" + Field.value;
        header('content-type: text/xml; charset=utf-8');
        $body = '';
        try {
            $validators = null;

            $validators['DbTable'] = 'required';
            $validators['Field'] = 'required';
            $validators['Fieldvalue'] = 'required|unique:'.$request['DbTable'].','.$request['Field'];
            $xml = new \SimpleXMLElement('<Data></Data>');

            $validator = \Validator::make($request->all(), $validators);
            // $validator->after(function ($validator) use ($request) {
            //     \PF::dbIsUnique($validator, $request['Fieldvalue'], $request['DbTable'], $request['Field'], '');
            // });
            //PF::printr( $validator->messages());
            if ($validator->fails()) {
                //   PF::printr(12);
                $xml->AddChild('item', 'Y');
            }
            $body = $xml->asXML();
        } catch (\Exception $e) {
            $body = $e->getMessage();
        }

        return response($body);
    }

    public function sethits(Request $request)
    {
        DB::update('update board set hits =hits+1 where id = ?', [$request->input('key')]);
    }

    public function city2(Request $request)
    {

        $html="";
        $this->city2Repo = app(\App\Repositories\city2Repository::class);
        $rows = $this->city2Repo->select('city2title', 'city2title');
        $rows->myWhere('city1title|S', $request->input('city1title'), 'key', 'Y');
        $rows->orderBy('city2title');

        $rows = $rows->get();
        $html.=<<<EOF
            <li class="mdc-list-item" 
            data-value=""
            onclick="document.forms['oForm'].elements['city2title'].value='';">不限</li>
            EOF;
        foreach ($rows as $rs) {
            
            $html.=<<<EOF
            <li class="mdc-list-item" 
            data-value="$rs->city2title"
            onclick="document.forms['oForm'].elements['city2title'].value=$(this).attr('data-value');">$rs->city2title</li>
            EOF;
        }
        return response($html);
    }
}
