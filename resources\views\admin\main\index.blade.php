@extends('admin.layouts.master')
@section('css')
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", () => {
        //console.log(jQuery("nav.mt-2 > ul > li.nav-item[level=1]").length);
        jQuery("nav.mt-2 > ul > li.nav-item[level=1]").each(function(i, item1) {
            // console.log($(this).find('li.nav-item[level=2]').html());
            // console.log($(this).find('li.nav-item').find('a').length);

            if ($(this).find('li.nav-item a').length == 0) {
                //console.log($(this).html());
                $(this).eq(0).hide();
            }
            $(item1).find('li.nav-item[level=2]').each(function(i, item2) {
                if ($(item2).find('li.nav-item a').length == 0) {
                    //console.log($(this).html());
                    $(item2).eq(0).hide();
                }

            });
        });
    });
</script>


@endsection
@section('content')



<div>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="#" class="nav-link">
                    HI.{{Auth::guard('admin')->user()->name}}

                    ({{PF::xmlSearch($data['xmldoc'],"//參數設定檔/角色/KIND/傳回值","資料",\Auth::guard('admin')->user()->role)}})
                </a>
            </li>
            {{-- <li class="nav-item d-none d-sm-inline-block">
                <a href="logout" class="nav-link">登出</a>
            </li> --}}
        </ul>

        <!-- Right navbar links -->
        <ul class="navbar-nav ml-auto">
            <!-- Navbar Search -->
            {{-- <li class="nav-item">
                <a class="nav-link" data-widget="navbar-search" href="#" role="button">
          <i class="fas fa-search"></i>
        </a>
                <div class="navbar-search-block">
                    <form class="form-inline">
                        <div class="input-group input-group-sm">
                            <input class="form-control form-control-navbar" type="search" placeholder="Search" aria-label="Search">
                            <div class="input-group-append">
                                <button class="btn btn-navbar" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button class="btn btn-navbar" type="button" data-widget="navbar-search">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </li> --}}

            <!-- Messages Dropdown Menu -->
            {{-- <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
            <i class="far fa-comments"></i>
            <span class="badge badge-danger navbar-badge">3</span>
            </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                    <a href="#" class="dropdown-item">
            <!-- Message Start -->
            <div class="media">
              <img src="dist/img/user1-128x128.jpg" alt="User Avatar" class="img-size-50 mr-3 img-circle">
              <div class="media-body">
                <h3 class="dropdown-item-title">
                  Brad Diesel
                  <span class="float-right text-sm text-danger"><i class="fas fa-star"></i></span>
                </h3>
                <p class="text-sm">Call me whenever you can...</p>
                <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 4 Hours Ago</p>
              </div>
            </div>
            <!-- Message End -->
          </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
            <!-- Message Start -->
            <div class="media">
              <img src="dist/img/user8-128x128.jpg" alt="User Avatar" class="img-size-50 img-circle mr-3">
              <div class="media-body">
                <h3 class="dropdown-item-title">
                  John Pierce
                  <span class="float-right text-sm text-muted"><i class="fas fa-star"></i></span>
                </h3>
                <p class="text-sm">I got your message bro</p>
                <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 4 Hours Ago</p>
              </div>
            </div>
            <!-- Message End -->
          </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
            <!-- Message Start -->
            <div class="media">
              <img src="dist/img/user3-128x128.jpg" alt="User Avatar" class="img-size-50 img-circle mr-3">
              <div class="media-body">
                <h3 class="dropdown-item-title">
                  Nora Silvester
                  <span class="float-right text-sm text-warning"><i class="fas fa-star"></i></span>
                </h3>
                <p class="text-sm">The subject goes here</p>
                <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 4 Hours Ago</p>
              </div>
            </div>
            <!-- Message End -->
          </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item dropdown-footer">See All Messages</a>
                </div>
            </li> --}}
            <!-- Notifications Dropdown Menu -->
            {{-- <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
          <i class="far fa-bell"></i>
          <span class="badge badge-warning navbar-badge">15</span>
        </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                    <span class="dropdown-item dropdown-header">15 Notifications</span>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
            <i class="fas fa-envelope mr-2"></i> 4 new messages
            <span class="float-right text-muted text-sm">3 mins</span>
          </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
            <i class="fas fa-users mr-2"></i> 8 friend requests
            <span class="float-right text-muted text-sm">12 hours</span>
          </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
            <i class="fas fa-file mr-2"></i> 3 new reports
            <span class="float-right text-muted text-sm">2 days</span>
          </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
                </div>
            </li> --}}
            <li class="nav-item">
                <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                    <i class="fas fa-expand-arrows-alt"></i>
                </a>
            </li>
            <li class="nav-item">

                <a class="nav-link" href="#" onclick="top.location.href='{{ url('/') }}/admin/main/logout'">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </li>
            {{-- <li class="nav-item">
                <a class="nav-link" data-widget="control-sidebar" data-slide="true" href="#" role="button">
          <i class="fas fa-th-large"></i>
        </a>
            </li> --}}
        </ul>
    </nav>
    <!-- /.navbar -->

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
        <!-- Brand Logo -->
        <a href="#" class="brand-link">

            <span class="brand-text font-weight-light">{{config('config.name')}}管理介面</span>
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar user panel (optional) -->
            {{-- <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                <div class="image">
                    <img src="dist/img/user2-160x160.jpg" class="img-circle elevation-2" alt="User Image">
                </div>
                <div class="info">
                    <a href="#" class="d-block">
                      {{Auth::guard('admin')->user()->name}}
            </a>
        </div>
</div> --}}

<!-- SidebarSearch Form -->
{{-- <div class="form-inline">
                <div class="input-group" data-widget="sidebar-search">
                    <input class="form-control form-control-sidebar" type="search" placeholder="Search" aria-label="Search">
                    <div class="input-group-append">
                        <button class="btn btn-sidebar">
                            <i class="fas fa-search fa-fw"></i>
                        </button>
                    </div>
                </div>
            </div> --}}

<!-- Sidebar Menu -->
<nav class="mt-2">
    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
        <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->

        {!!$data['menu']!!}


    </ul>
</nav>
<!-- /.sidebar-menu -->
</div>
<!-- /.sidebar -->
</aside>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper iframe-mode" data-widget="iframe" data-loading-screen="750">
    <div class="nav navbar navbar-expand navbar-white navbar-light border-bottom p-0">
        <div class="nav-item dropdown">
            <a class="nav-link bg-danger dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">Close</a>
            <div class="dropdown-menu mt-0">
                <a class="dropdown-item" href="#" data-widget="iframe-close" data-type="all">Close All</a>
                <a class="dropdown-item" href="#" data-widget="iframe-close" data-type="all-other">Close All Other</a>
            </div>
        </div>
        <a class="nav-link bg-light" href="#" data-widget="iframe-scrollleft"><i class="fas fa-angle-double-left"></i></a>
        <ul class="navbar-nav overflow-hidden" role="tablist"></ul>
        <a class="nav-link bg-light" href="#" data-widget="iframe-scrollright"><i class="fas fa-angle-double-right"></i></a>
        <a class="nav-link bg-light" href="#" data-widget="iframe-fullscreen"><i class="fas fa-expand"></i></a>
    </div>
    <div class="tab-content">
        <div class="tab-empty">
            <h2 class="display-4">



            </h2>
        </div>
        <div class="tab-loading">
            <div>
                <h2 class="display-4">loading <i class="fa fa-sync fa-spin"></i></h2>
            </div>
        </div>
        <div class="tab-pane fade active show" id="panel-pages-widgets-html" role="tabpanel" aria-labelledby="tab-pages-widgets-html"><iframe src="adminuserloginlog" style="height: 639px;"></iframe></div>
    </div>
</div>
<!-- /.content-wrapper -->
{{-- <footer class="main-footer">

    </footer> --}}

<!-- Control Sidebar -->
{{-- <aside class="control-sidebar control-sidebar-dark">
        <!-- Control sidebar content goes here -->
    </aside> --}}

<script language=JavaScript>
    document.addEventListener("DOMContentLoaded", () => {
        mytimer = setInterval(function() {
            var dict = {
                url: "{{ url('/') }}/admin/main/updateusetime",
                data: {
                    'id': "{{Session::get('adminuserlogin_id')}}"

                },
                noloading: false,
                //data:jQuery("#oForm").serialize(),
                //data:JSON.stringify(this.inputs),
                dataType: 'json',
                //debug:true,
            }
            PF_ajax(dict).done(function(obj) {
                console.log('logout');
            }).fail(function(resp) {
                //    _alert(resp.statusText);
            }).then(function() {

            });
        }, 60 * 1000);
        //$('.content-wrapper').IFrame('createTab', 'adminuserloginlog', 'adminuserloginlog', 'adminuserloginlog11', true);
        //$("[href='adminuserloginlog']").eq(0).trigger("click");
    });
</script>

<!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->
<script src="{{ url('/') }}/adminlte/js/adminlte.min.js"></script>

@endsection