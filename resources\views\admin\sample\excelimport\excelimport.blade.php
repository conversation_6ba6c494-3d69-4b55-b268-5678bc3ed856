@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
    function oForm_onsubmit(form) {
        if (PF_FormMultiAll(form) == false) {
            return false
        };
        PF_FieldDisabled(true)
        return true;
    }
</SCRIPT>
<div class="card">
    <div class="card-body">
        <!--// TODO : 前端資料填寫-->
        
        <form name="oForm" id="oForm" method="post" language="javascript"
            action="{{request()->url()}}/../excelimportstore" ENCTYPE="multipart/form-data"
            onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->


            <div class="form-group row">
                <label class="col-md-2">EXCEL檔案：<font class="text-danger">*</font></label>
                <div class="col-md-10">
                    <input type="file" accept=".xlsx" name="file1" class="form-control"  required>

                </div>
            </div>
            <div class="form-group row">

                <div class="col">
                    <a href="{{ url('images/excel/customer.xlsx') }}">範本</a>
                </div>
            </div>




            <div align="center">
                <button type="submit" class="btn btn-success">確定</button>

                {!!Session::get('success')!!}
            </div>

            @include('admin.layouts.hiddencommon', ['data'=>$data])



        </form>
    </div>
</div>

@stop