<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DB;

//聯絡我們
class feedback extends baseModel
{
    public $tabletitle = '聯絡我們';
    public $table = 'feedback';
    public $primaryKey = 'id';

    //欄位必填
    public $rules = [
        'id' => 'required',
'name' => 'required',
'email' => 'required',
'title' => 'required',
    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(11)'],//
'company'=>['title'=>'公司名稱','type'=>'varchar(50)'],//
'name'=>['title'=>'姓名','type'=>'varchar(100)'],//
'email'=>['title'=>'電子信箱','type'=>'varchar(500)'],//
'tel'=>['title'=>'電話','type'=>'varchar(50)'],//
'ext'=>['title'=>'分機','type'=>'varchar(50)'],//
'sex'=>['title'=>'性別','type'=>'varchar(5)'],// //先生,女士,
'mobile'=>['title'=>'行動電話','type'=>'varchar(50)'],//
'title'=>['title'=>'標題','type'=>'varchar(2000)'],//
'memo'=>['title'=>'留言內容','type'=>'mediumtext'],//
'created_at'=>['title'=>'建立時間','type'=>'datetime'],//
'updated_at'=>['title'=>'異動時間','type'=>'datetime'],//
'retitle'=>['title'=>'回覆標題','type'=>'varchar(4000)'],//
'rebody'=>['title'=>'回覆訊息','type'=>'mediumtext'],//
'redate'=>['title'=>'回覆日期','type'=>'datetime'],//
'memberid'=>['title'=>'會員編號','type'=>'int(11)'],//
'alg'=>['title'=>'語系','type'=>'varchar(10)'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';

    protected $fillable = ['company','name','email','tel','ext','sex','mobile','title','memo','created_at','updated_at','retitle','rebody','redate','memberid','alg']; //可充許傳入的欄位
    protected $guarded = [];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = ['created_at','updated_at','redate'];

    public function __construct($attr = array(), $exists = false)
    {
        $this->fillable = parent::getfillables(); //接受$request->all();
        parent::__construct($attr, $exists);

        parent::setFieldInfo($this->fieldInfo);
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {
        });
        static::deleting(function ($model) {
            //  DB::table('feedback')->select()
            // ->myWhere('id|N', $model->id, "id", 'Y')
            // ->delete();
        });
        static::deleted(function ($model) {
        });
    }
}
