<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CityController extends Controller {
    /**
     * 取得二級城市資料
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCity2(Request $request) {
        try {
            $city1title = $request->input('city1title');

            // 記錄請求日誌
            Log::info('City2 API 請求', ['city1title' => $city1title]);

            // 驗證參數
            if (empty($city1title)) {
                Log::warning('City2 API 請求缺少 city1title 參數');
                return response()->json([
                    'status' => 'error',
                    'message' => '缺少必要參數 city1title',
                    'data' => []
                ], 400);
            }

            // 從快取中取得城市資料
            $allCity2Rows = Cache::get('city2rows');

            if (!$allCity2Rows) {
                Log::warning('快取中沒有找到 city2rows 資料');
                return response()->json([
                    'status' => 'error',
                    'message' => '城市資料不可用',
                    'data' => []
                ], 500);
            }

            $city2rows = collect($allCity2Rows)
                ->where('city1title', $city1title)
                ->values();

            Log::info('City2 API 回應', [
                'city1title' => $city1title,
                'count' => $city2rows->count()
            ]);

            return response()->json([
                'status' => 'success',
                'message' => '成功取得城市資料',
                'data' => $city2rows,
                'count' => $city2rows->count()
            ]);
        } catch (\Exception $e) {
            Log::error('City2 API 錯誤', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '伺服器內部錯誤',
                'data' => []
            ], 500);
        }
    }
}
