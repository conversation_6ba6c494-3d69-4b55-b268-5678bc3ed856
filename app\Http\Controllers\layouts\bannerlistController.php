<?php

namespace App\Http\Controllers\layouts;

use App\Http\Controllers\Controller;
use DB;
use PF;
class bannerlistController extends Controller
{
    public $data;

    public function index($kind)
    {
        $this->data['kind']=$kind;
        //$rows = DB::table('board')->selectRaw('id,title,field1 as img,field2 as memo,memo as url,field9 as target');
        $rows = DB::table('board')->selectRaw('*');
        $rows->myWhere('kind|S', $kind, 'kind', 'Y');        
        //$rows->myWhere('alg|S', app()->getLocale(), 'lg', 'Y');        
        $rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');        
        $rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        $rows->orderByRaw('boardsort,id desc');
        //PF::dbSqlPrint($rows);
        $rows = $rows->get();
        

        $this->data['rows'] = $rows;
        //PF::printr($this->data['rows']);

        return view('layouts.bannerlist', [
            'data' => $this->data,
        ]);
    }
}