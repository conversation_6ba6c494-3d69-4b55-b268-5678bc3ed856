# EbayHouse 法拍屋系統

## 項目簡介

EbayHouse 是一個基於 Laravel 開發的法拍屋管理系統，提供法拍屋資訊管理、會員系統、圖片管理等功能。

## 技術架構

-   **後端框架**: Laravel 9.x
-   **前端樣式**: Tailwind CSS
-   **資料庫**: MySQL
-   **檔案管理**: Local Storage
-   **定時任務**: Laravel Task Scheduling
-   **測試框架**: PHPUnit

## 項目結構目錄

```
ebayhouse/
├── app/
│   ├── Console/Commands/         # Artisan 指令
│   ├── Http/Controllers/         # 控制器
│   │   ├── admin/               # 後台管理
│   │   ├── api/                 # API 接口
│   │   ├── member/              # 會員功能
│   │   └── membercenter/        # 會員中心
│   ├── Models/                  # 資料模型
│   ├── Services/                # 業務邏輯服務
│   ├── Repositories/            # 資料存取層
│   └── Libraries/               # 自定義函式庫
├── database/
│   ├── migrations/              # 資料庫遷移
│   └── seeders/                # 資料填充
├── public/
│   └── images/                 # 圖片檔案
└── resources/views/            # 視圖模板
```

## 功能介紹

### 商品管理系統

-   **過期商品自動清理**: ProductService 提供自動檢查並刪除過期法拍屋商品功能

    -   依據民國年格式的投標日期判斷是否過期
    -   自動解析並刪除商品相關圖片檔案 (逗號分隔格式)
    -   從 `public/images/product/` 目錄清理圖片資源
    -   完整的錯誤處理和日誌記錄

-   **圖片輪播系統**: 基於 Swiper.js 實現的房屋詳情頁面圖片輪播功能
    -   主圖片輪播與縮圖導航聯動
    -   支援鍵盤快捷鍵控制 (左右箭頭鍵)
    -   響應式設計，支援桌面和移動設備
    -   觸控滑動手勢支援
    -   圖片懶載入優化
    -   分頁指示器與圖片數量顯示
    -   預留全螢幕查看功能

### 會員系統

-   會員註冊、登入功能
-   我的收藏商品管理
-   會員中心個人資料管理

### 後台管理

-   管理員登入系統
-   商品資料管理
-   會員資料管理
-   系統設定管理

### API 服務

-   RESTful API 接口
-   商品查詢 API
-   會員相關 API
-   地區資訊 API

## 產生 Laravel PHP Unit Test 基礎規範

### 適用場景

-   適用於所有 Laravel 專案的 PHP Unit Test 生成。
-   若使用者明確要求 Laravel 測試，或上下文涉及 Laravel 元素（如模型、控制器、migration），則**必須**參考本塊。
-   若需求涉及非 Laravel 測試（如 Nuxt3 Vitest），則**忽略**本塊。

### 目標

-   生成符合 **Laravel PHPUnit 測試框架**慣例的測試類別。
-   測試檔案輸出至指定路徑，如果沒有請重新建立檔案。

### 測試框架

-   使用 **PHPUnit**，相容於最新版本的 Laravel。
-   遵循 Laravel 測試最佳實踐。

### 註解要求

-   **語言**：使用**繁體中文**撰寫註解。
-   **風格**：簡潔清晰，描述測試目的與邏輯。
-   **最低要求**：每個測試方法包含至少一行註解，說明功能。

### 嚴格規範

-   **檔案路徑**：輸出檔案位置請依來源目錄產生對應的目錄，service 放在 Tests\Service ，如: \api => Tests\Feature\api ， \api\admin => Tests\Feature\api\admin。
-   **類別繼承**：原繼承 `TestCase` 改成 `\Tests\Feature\baseTest`。
-   **類別命名**：使用**小駝峰**（如 `userTest`），將 `xxControllerTest` 改為 `xxTest`。
-   **方法要求**：依針對現有函式產生測試函式不要加其他函式
-   **方法命名**：格式為 `test_功能中文_函式名稱`（如 `test_取得使用者資料_index`）。
-   **必要檢查**：每個測試方法，有 API 目錄必須包含 $this->checkJson($response)，非 API 目錄請用$this->checkHtml($response)，使用 assertStatus 檢查狀態碼。 使用 assertJsonPath 檢查 JSON 結構。使用 assertDatabaseHas 檢查資料庫。

### 程式要求

-   補測試資料請用 factory 資料並改寫在 setUp()函式下，不要用 insert，post 傳送的值如果可以請使用 factory 產生的
-   一開始繼承`public function setUp(): void {parent::setUp();}`
-   不要使用 DB::shouldReceive 與 mock 寫法
-   只要寫成功的例子
-   寫完後執行 php vendor/bin/phpunit -d APP_ENV=testing --configuration=phpunit.xml 進行測試，並檢視執行結果，直到修正完畢
-   針對目錄 api 在 header 加入 Bearer $this->adminuser->api_token，membercenter 在 header 加入 Bearer $this->member->api_token
