<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use PF;
use DB;
use Config;
use Cache;

//use Illuminate\Support\Facades\DB;

class sampleController extends Controller
{
    private $data;
    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        //$this->data = PF::requestAll($this->data);
        //$this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

    }
    private $data;

    /**
     *建構子.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';

        $rows = DB::table('kindheadlinepush');
        $rows->selectRaw('*');
        $rows->myWhere('kindheadid|N', $this->data['kindheadid'], 'kindheadid', 'Y');
        $rows->orderByRaw('title');
        $rows = $rows->get();
        $jsondata['data'] = $rows;

        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
    public function show()
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';

        $rows = DB::table('kindheadlinepush');
        $rows->selectRaw('*');
        $rows->myWhere('id|N', $this->data['id'], 'id', 'Y');
        $rows=$rows->take(1);        
        $rows = $rows->get();
        if ($rows->count() > 0) {
            $rs=$rows->first();
            $jsondata['data'] = $rs;
        } else {
            throw new \CustomException("No data");
        }
        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
 public function raw()
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';
        try {
            //$header = $request->header('Authorization');
            $token = $request->bearerToken();

            if ($token != PF::getConfig('token')) {
                throw new \CustomException('code error');
            }
            $rawPostData = file_get_contents('php://input');
            //\Log::info("rawPostData:".$rawPostData);
            if (null != $rawPostData) {
                $json = json_decode($rawPostData, true);
                //PF::printr($json);

                foreach ($json as $key => $val) {
                    $this->data[$key] = $json[$key];
                }
                //\Log::info($json);
            }
        } catch (Exception $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        }

        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);

        //return response($json)->header('Content-Type', 'json');
    }
}
