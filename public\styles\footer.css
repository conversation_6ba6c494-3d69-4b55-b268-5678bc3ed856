/*----------------------------------*\ 
  # footer       
\*----------------------------------*/
.footer_wrap {  
  background: var(--footer, #ff8000);  
  width: 100%; 
  font-size: .81rem; 
  color: #fcfcfc;
}

.footer-content {  
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  max-width: 60rem;
  margin: 0 auto;
  padding: 1.5rem 1rem;
}

.footer-logo {
  flex: 1 1 200px;
}
.footer-logo img {
  width: 15rem;
  max-width: 81%;   
}

.footer-info {
  flex: 2 1 300px;
  line-height: 1.8;
}
.footer-info p {
  font-size: .9rem;
  margin-bottom: 0rem; 
  font-weight: 500;   
}
.footer-info a,
.footer-info a:visited,
.footer-info a:hover  {  
  color:#f9d3be;
  color: #fcfcfc;   
}

.footer-note {
  background-color: #181818;  
  padding: .75rem 1rem;  
  text-align: center;
}
.footer-note div {
  display: flex;
  justify-content: center;
  margin-bottom: .36rem;
}
.footer-note p {
  font-size: .81rem; 
  line-height: 1.6;
  font-weight: 400;
  color: #999;
  margin-bottom: 0rem;    
}
.footer-note a,
.footer-note a:visited,
.footer-note a:hover  {  
  color:#FFF;  
}
.footer-note h6 {
  font-size: .72rem; 
  color: #999;
  margin-bottom: 0rem;
  font-weight: 300;
  line-height: 1;  
}
@media screen and (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: .63rem;    
  }
  .footer_wrap .address {
    margin-top: 1.8rem;    
    padding-left: 0rem;
    text-align: left;    
  }
  .footer-logo,
  .footer-info {
    flex: 1 1 100%;
  }
  .footer-info {    
    text-align: left;
  }  
  .footer-logo img {    
    margin: 0 auto;
  }
  .footer-note div{
    display: inline-block;    
  }
  .footer-note a {
    display: inline-block;    
  }    
}
