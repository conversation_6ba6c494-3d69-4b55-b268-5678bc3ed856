<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
//php artisan migrate:refresh --path=/database/migrations/_create_myproduct_table.php
class CreateMyproductTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (!Schema::hasTable('myproduct')) {
            Schema::create('myproduct', function (Blueprint $table) {
                //$table->engine = 'MyISAM';
                $table->increments('id')->from(10000)->comment('自動編號');
                $table->integer('member_id')->nullable()->default(0)->comment('會員');
                $table->integer('product_id')->nullable()->default(0)->comment('物件');
                $table->unique(['member_id', 'product_id'], 'myproduct_unique');

                //$table->unique(['classt_id', 'member_id'], 'myproduct_unique');
                //$table->mediumText('jsonbody')->nullable()->comment('json');
                //$table->date('begin_date')->nullable()->comment('開始時間');
                //$table->date('close_date')->nullable()->comment('結束時間');
                $table->timestamp('created_at')->useCurrent()->comment('建立日期');
                $table->timestamp('updated_at')->useCurrent()->comment('編輯日期');
                //$table->timestamps();
            });
            \DB::statement("ALTER TABLE myproduct COMMENT '我的物件'");
        }
        /*
        $table->unsignedBigInteger('activitysession_id')->comment('場次');
        $table->foreign('activitysession_id')->references('id')->on('activitysession')->onDelete('cascade');

        $table->string('kind',50)->index()->comment('種類');
        $table->mediumText('body')->nullable()->comment('說明');
        $table->dateTime('begindate')->nullable()->comment('開始時間');
        $table->integer('hits')->default(0)->comment('點率次數');
        $table->float('sortnum', 5, 3)->nullable()->comment('排序號碼');
        $table->integer('adminsuer_id')->nullable()->comment('編輯人員');
        $table->string('adminuser_name', 50)->nullable()->comment('編輯人員');
        $table->string('edit_account',50)->comment('修改人');
        $table->string('account',50)->unique();;
        $table->timestamps('reviewed_at')->default('now');
        $table->unique(array('kind', 'kindid'));
        $table->index(array('kind', 'kindid'));
        $table->softDeletes();

        */
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('myproduct');
    }
}
