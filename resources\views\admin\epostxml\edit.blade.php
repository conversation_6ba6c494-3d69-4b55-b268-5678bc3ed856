@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
    function oForm_onsubmit(form)
    {
        if (PF_FormMultiAll(form)==false){return false};
   PF_FieldDisabled(form)//將全部button Disabled
   return true;
}
</SCRIPT>

<!--// TODO : 前端資料填寫-->
<form name="oForm"  id="oForm"  method="post" language="javascript" 
action="{{ URL::to('admin/epostxml/store') }}"  ENCTYPE="multipart/form-data" onsubmit="return oForm_onsubmit(this);"><!--novalidate-->


    <table border="0" width="100%"  bordercolorlight="#000000" bordercolordark="#FFFFFF" cellspacing="1" cellpadding="5" align="center" class="oFormTableMRwd">
    
{{$data['pmmodify']}}        

    </table>


    <div align="center">

        <input type="submit" value="確定">
        <input type="reset"  value="取消">
        <input type="hidden" name="edit"  value="{{$data['id']}}" />
        <input type="button"  value="回上一頁" onClick="javascript:window.history.go(-1)">
        {!!Session::get('success')!!}
    </div>
    @include('admin.layouts.hiddencommon')            
    {!! Form::hidden("edit",  $request->input("edit") ) !!}
    {!! Form::hidden("gobackurl",  $request->input("gobackurl") ) !!}
</form>


@endsection

