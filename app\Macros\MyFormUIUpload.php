<?php

namespace App\Macros;

use PF;
use Html;

// {{
//     Form::myUIUpload([
//     'name' => 'file',
//     'folder' => 'images/order',
//     'filename' => $data['title'],
//     'title' => $data['title'],
//     'width' => 800,
//     'height' => 800,
//     //'required' => 1
//     'accept' => '.png,.jpg,.gif',
//     ])
//     }}
/***
"功能名稱":"共用類別-Html Upload",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
 ***/
class MyFormUIUpload {
    protected $arr;

    public function __construct($form, $arr) {
        $this->arr = $arr;
    }

    public function createHtml() {
        try {
            $html = '';
            $storage = base_path('public/');
            //$storage = storage_path('//');
            if (PHP_OS == 'Linux') {
                $filename = $storage . $this->arr['folder'] . '/' . $this->arr['filename'];
            } else {
                $filename = $storage . $this->arr['folder'] . '/' . iconv('utf-8', 'big5', $this->arr['filename']);
            }
            if ('' == $this->arr['formname']) {
                $this->arr['formname'] = 'oForm';
            }
            $html .= '<div class="input-group mb-3">' . chr(13) . chr(10);
            if (false == file_exists($filename) && '' != $this->arr['filename']) {
                $html .= '<div class="input-group-prepend">
                <span class="input-group-text" id="basic-addon1">*</span>
              </div>';
            }
            if ('' == $this->arr['accept']) {
                $items = explode(',', \config('config.uploadfilelimit'));
                foreach ($items as $k => $v) {
                    $this->arr['accept'] .= '.' . $v . ',';
                }
                $this->arr['accept'] = ltrim(rtrim($this->arr['accept'], ','), ',');
            }
            $html .= '<input type="file"  multiple="multiple" class="form-control" ';
            foreach ($this->arr as $_key => $_value) {
                //if (false == PF::splitCompare('formname,folder,filename,width,height', $_key)) {
                if (false == in_array($_key, ['required', 'formname', 'folder', 'filename', 'width', 'height', 'limitfile', '\@change'])) {
                    $html .= ' ' . $_key . '="' . $_value . '"';
                }
            }
            if ($this->arr['required']) {
                $html .= ' required';
            }

            // if (null != $this->arr['limitfile']) {
            //     $this->arr['limitfile'] = str_replace(';', ',', $this->arr['limitfile']);
            //     //$html .= ' requiredclass="required[0,'.str_replace(",",";", $this->arr['limitfile']).']" ';
            //     $this->arr['limitfile'] = '.'.str_replace(',', ', .', $this->arr['limitfile']);
            //     $html .= ' accept="'.$this->arr['limitfile'].'" ';
            //     $this->data['accept'] = $this->arr['limitfile'];
            // }
            $html .= '>' . chr(13) . chr(10);
            $html .= '</div>';
            $html .= '<div>';
            $html .= '<input type="hidden" name="hiddenfile' . $this->arr['name'] . '" value="' . $this->arr['filename'] . "\">\n";

            $filename = $storage . $this->arr['folder'] . '/' . iconv('utf-8', 'big5', $this->arr['filename']);
            if (file_exists($filename) && '' != $this->arr['filename']) {
                $html .= '<div class="input-group mb-3">' . chr(13) . chr(10);

                if ('N' != $this->arr['isdel']) {
                    $html .= '<input  type="button" class="btn btn-danger btn-sm" value="' . __('移除') . '"  onclick="document.' . $this->arr['formname'] . ".elements['hiddenfile" . $this->arr['name'] . "'].value='';document.getElementById('showfile_" . $this->arr['name'] . "').style.display='none';\" />\n";
                }
                $html .= '<div id="showfile_' . $this->arr['name'] . "\">\n";
                $html .= '<a href="' . asset($this->arr['folder']) . '/' . urlencode(mb_convert_encoding($this->arr['filename'], 'big5', 'utf-8')) . "\" target=\"_blank\">\n";
                $html .= Html::myUIImage([
                    'folder' => $this->arr['folder'],
                    'filename' => $this->arr['filename'],
                    'width' => 100,
                    'height' => 100,
                    'isdisplayname' => $this->arr['isdisplayname'],
                    'noimg' => 'no-picture.gif',
                ]);
                $html .= "\n</a>\n";
                if ('Y' == $this->arr['isshowname']) {
                    $html .= $this->arr['filename'] . "\n";
                }
                $html .= "</div>\n";
                $html .= "</div>\n";
            }

            if ('' != $this->arr['width'] || '' != $this->arr['height']) {
                $html .= '『' . __('建議比例') . '：';
                if ('' != $this->arr['width']) {
                    $html .= $this->arr['width'] . 'px ';
                }
                if ('' != $this->arr['height']) {
                    $html .= $this->arr['height'] . 'px';
                }
                $html .= "』\n";
            }
            if ('' != $this->arr['memo']) {
                $html .= '<br>';
            }

            $html .= __('檔案格式限');
            $html .= ':' . $this->arr['accept'] . "\n";

            if ('' != $this->arr['width'] && 0 == substr_count($this->arr['name'], '[')) {
                $html .= '<input type="hidden" name="' . $this->arr['name'] . '_width" value="' . $this->arr['width'] . "\">\n";
            }
            if ('' != $this->arr['height'] && 0 == substr_count($this->arr['name'], '[')) {
                $html .= '<input type="hidden" name="' . $this->arr['name'] . '_height" value="' . $this->arr['height'] . "\">\n";
            }
            $html .= '</div>';
        } catch (\Exception $e) {
            $html .= $e->getMessage();
        }

        return $html;
    }
}
