@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')
    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">地點列表</h2>
    <div class="min-h-screen bg-gray-50 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- 頁面標題 -->
            <div class="text-center mb-12">

                <p class="text-lg text-gray-600 max-w-2xl mx-auto">探索我們的服務據點，找到最接近您的位置</p>
            </div>

            <!-- 地點列表 -->
            <div class="space-y-8">
                @foreach ($data['rows'] as $rs)
                    <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                        <div class="lg:flex">
                            <!-- 圖片區域 -->
                            <div class="lg:w-1/3 xl:w-1/4">
                                <div class="h-64 lg:h-full relative overflow-hidden">
                                    {{ Html::myUIImage([
                                        'folder' => 'https://www.ebayhouse.com.tw/images/place',
                                        'filename' => $rs->field1,
                                        'alt' => $data['title'],
                                        'class' => 'w-full h-full object-cover hover:scale-105 transition-transform duration-300',
                                    ]) }}
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                                </div>
                            </div>

                            <!-- 地圖區域 -->
                            <div class="lg:w-1/3 xl:w-1/3">
                                <div class="h-64 lg:h-full relative">
                                    <iframe
                                        src="https://maps.google.com.tw/maps?f=q&source=s_q&hl=zh-TW&geocode=&q={{ urlencode($rs->memo) }}&ie=UTF8&z=16&output=embed"
                                        class="w-full h-full border-0" allowfullscreen="" aria-hidden="false" tabindex="0"
                                        loading="lazy">
                                    </iframe>
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                            <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                            地圖位置
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- 內容區域 -->
                            <div class="lg:w-1/3 xl:w-5/12 p-8 flex flex-col justify-center">
                                <div class="space-y-6">
                                    <!-- 標題 -->
                                    <div>
                                        <h2
                                            class="text-2xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors duration-200">
                                            <a href="#" class="block">{{ $rs->title }}</a>
                                        </h2>

                                        <!-- 地址 -->
                                        <div class="flex items-start space-x-3 text-gray-600">
                                            <svg class="w-5 h-5 mt-0.5 text-gray-400 flex-shrink-0" fill="currentColor"
                                                viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="leading-relaxed">{{ $rs->memo }}</span>
                                        </div>
                                    </div>

                                    <!-- 分隔線 -->
                                    <div class="border-t border-gray-200"></div>

                                    <!-- 聯絡資訊 -->
                                    <div class="space-y-3">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-4">聯絡資訊</h3>

                                        @if ($rs->field2)
                                            <div class="flex items-center space-x-3">
                                                <div
                                                    class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                    <svg class="w-5 h-5 text-green-600" fill="currentColor"
                                                        viewBox="0 0 20 20">
                                                        <path
                                                            d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <p class="text-sm text-gray-500">電話</p>
                                                    <a href="tel:{{ $rs->field2 }}"
                                                        class="text-gray-900 font-medium hover:text-blue-600 transition-colors duration-200">
                                                        {{ $rs->field2 }}
                                                    </a>
                                                </div>
                                            </div>
                                        @endif

                                        @if ($rs->field3)
                                            <div class="flex items-center space-x-3">
                                                <div
                                                    class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor"
                                                        viewBox="0 0 20 20">
                                                        <path
                                                            d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z">
                                                        </path>
                                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z">
                                                        </path>
                                                    </svg>
                                                </div>

                                                <div>
                                                    <p class="text-sm text-gray-500">傳真</p>
                                                    <span class="text-gray-900 font-medium">{{ $rs->field3 }}</span>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- 操作按鈕 -->
                                    <div class="pt-4">
                                        <div class="flex space-x-3">

                                            <button
                                                onclick="window.open('https://maps.google.com.tw/maps?f=q&source=s_q&hl=zh-TW&geocode=&q={{ urlencode($rs->memo) }}&ie=UTF8&z=16', '_blank')"
                                                class="px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                        clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- 無資料提示 -->
            @if (count($data['rows']) == 0)
                <div class="text-center py-16">
                    <div class="max-w-md mx-auto">
                        <svg class="w-24 h-24 mx-auto text-gray-300 mb-6" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <h3 class="text-xl font-medium text-gray-900 mb-2">目前沒有地點資料</h3>
                        <p class="text-gray-500">請稍後再來查看，或聯絡我們了解更多資訊。</p>
                    </div>
                </div>
            @endif

            <!-- 分頁導航 -->
            @if ($data['rows'] && $data['rows']->hasPages())
                <div class="mt-12">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        {{ $data['rows']->links('layouts.paginate') }}
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
