<?php

namespace App\Mails;

use PF;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

/***
"功能名稱":"Mail寄發-純標題與內容",
"備註":" ",
"建立時間":"2022-01-18 13:29:27",
 ***/
class sendMail extends baseMail {
    public $tries = 3;
    use Queueable;
    use SerializesModels;
    public $config;

    // 讓外部可以將參數指定進來
    public function __construct($config) {
        $this->config = $config;
        //parent::setConfig();
    }

    public function build() {
        if (PF::isEmpty($this->config['subject'])) {
            return $this->view('email.empty');
        }
        // if (PF::isEmpty($this->config['view'])) {
        //     return $this->view('email.empty');
        // }
        // if (PF::isEmpty($this->config['with'])) {
        //     return $this->view('email.empty');
        // }
        // if (PF::isEmpty($this->config['to'])) {
        //     return $this->view('email.empty');
        // }
        $this->from(config('mail.from.address'), PF::getConfig('name'));
        $this->subject($this->config['subject']);
        $this->view('email.raw');
        $this->with($this->config);

        //$this->to($this->config['to']);

        return $this;
    }
}
