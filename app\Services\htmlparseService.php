<?php

namespace App\Services;

use DB;
use PF;
use PHPHtmlParser\Dom;
//use Illuminate\Support\Facades\DB;

use App\Libraries\UploadFile;

/**
 * Class JsonTranslator.
 */
class htmlparseService {
    public $debug;
    public $data;
    public $db;
    public $productid;

    public function __construct($data) {
        $this->data = $data;
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->db = new \App\Models\product();
        $this->fieldnicknames = $this->db::getFieldTitleArray();
    }

    public function noHtm2($str) {
        $str = trim($str);
        $str = preg_replace('@<(.*?)>@is', '', $str);

        return $str;
    }

    public function noHtml1($str) {
        $str = trim($str);
        $str = preg_replace('@<script(.*?)</script>@is', '', $str);
        $str = preg_replace('@<iframe(.*?)</iframe>@is', '', $str);
        $str = preg_replace('@<style(.*?)</style>@is', '', $str);
        $str = preg_replace('@<input(.*?)>@is', '', $str);
        $str = preg_replace('@<div(.*?)>@is', '', $str);
        //$str = preg_replace( "@<(.*?)>@is", "", $str );
        $str = preg_replace('/\s(?=\s)/', '', $str);
        $str = preg_replace('/[\n\r\t]/', ' ', $str);
        $str = str_replace("'", '', $str);

        return $str;
    }

    public function run($request) {
        //throw new \CustomException("資料參數有誤請洽管理人員");
        $validators = null;

        //$validators['number'] = 'required';
        $validators['body'] = 'required';
        $validator = \Validator::make($this->data, $validators);

        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $body = $this->data['body'];

        // $dom = new Dom();
        // $dom->load($body);
        // $address = $dom->find("span[id='ProAddress']")->innertext;
        // PF::printr($address);
        // exit();
        //echo $response->getContent();
        //echo url('/');
        //$dom->load($body);
        $GLOBALS['str'] = $body;
        $GLOBALS['str'] = $this->noHtml1($GLOBALS['str']);
        //echo $GLOBALS['str'];
        $court = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="ProOwner2">', '</span>', 0));                   //地址
        //echo $body;
        $court = str_replace('【', '', $court);
        $court = str_replace('】', '', $court);
        //  echo PF::getStr($body, '<span id="ProAddress">', '</span>', 0);

        $address = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="ProAddress">', '</span>', 0));                   //地址
        $number = str_replace('【', '', str_replace('】', '', $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="ProID">', '</span>', 1))));                     //number
        $thebidprice = 0;
        if (\Str::contains($GLOBALS['str'], ['已得標'])) {
            $thebidprice = PF::getStr($GLOBALS['str'], '已得標', '<', 0);
            $thebidprice = str_replace('　', '', $thebidprice);
        }
        $auctions = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="ProState">', '</span>', 1));

        //echo $auctions;
        if (substr_count($auctions, '　') > 0) {
            $array = explode('　', $auctions);
            $auctions = $array[0];
        }

        //已得標　3128<font size=1>

        $xx = $this->noHtm2(PF::getStr($GLOBALS['str'], ' <span id="ProOther">', '</span>', 1));               //型態
        //echo $xx;
        //{ 透天 / 公設 0 % / 加強磚造 / 樓高 2 層 }　
        $xx = str_replace('{', '', str_replace('}', '', $xx));

        $array = explode('/', $xx);

        $pattern = '';
        if (count($array) > 1) {
            for ($i = 0; $i < count($array); ++$i) {
                $array[$i] = str_replace(array(' ', '　'), array('', ''), $array[$i]);

                if ('' != $array[$i]) {
                    if (substr_count($array[$i], '年') > 0) {
                        $houseage = str_replace(array('屋齡', '年'), array('', ''), $array[$i]);       //屋齡
                    } elseif (substr_count($array[$i], '公設') > 0) {
                        $postulatemorethan = str_replace(array('公設', '%'), array('', ''), $array[$i]);                //公設比
                    } elseif (substr_count($array[$i], '層') > 0) {
                        $storey = str_replace(array('樓高', '層'), array('', ''), $array[$i]);                   //樓高
                    } elseif (substr_count('R.C��SRC、木造、加強磚造、鐵皮', $array[$i]) > 0) {
                        $architecture = $array[$i];                   //樓高
                    } elseif (PF::splitCompare('其他,其它,公寓,大樓,店面,透天,平房,廠房,華廈,農舍,別墅,店住', $array[$i])) {
                        $pattern = $array[$i];                   //型態
                    } else {
                        $buildname = $array[$i];                   //大樓名稱
                    }
                }
            }
        } else {
            $pattern = $xx;                    //型態
        }
        $pattern = str_replace('物件規格 : ', '', str_replace('}', '', $pattern));                     //number
        $pattern = str_replace(' ', '', $pattern);                     //型態
        if ('其它拍賣' == $court) {
            $producttitle = $number;
        } else {
            $producttitle = PF::xmlSearch($this->data['xmldoc'], '//參數設定檔/物件標題對照/KIND/資料', '傳回值', $pattern);
        }
        if (substr_count($address, '地號') > 0) {
            $pattern = '土地';                 //型態
        }

        /*
        <span id="ProState"><font color=#800080>
        <img src=../Images/STRT109.GIF> 已得標　193.9<font size=1> 萬</font>
        (加價 42.90<font size=1> 萬</font>)　楊先生 / 11<font size=1>  人</font>　
        8.68<font size=1> 萬/坪</font></font>
        </span>
        $DB->FieldAdd("sealedhuman",$sealedhuman,"S");                    //查封人
        		 $DB->FieldAdd("thenumberofbidders",$thenumberofbidders,"S");                    //投標人數
        		 $DB->FieldAdd("bidwere",$bidwere,"S");                    //得標人
        		 $DB->FieldAdd("thebidprice",$thebidprice,"S");                    //得標價格
        		 $DB->FieldAdd("increasetheamountof",$increasetheamountof,"S");                    //加價金額
        */

        $auctions = str_replace('（', '', $auctions);
        $auctions = str_replace('）', '', $auctions);
        //echo $GLOBALS['str'];
        $tenderdate = $this->noHtm2(PF::getStr($GLOBALS['str'], '投標日：', '</span>', 1));                //投標日

        $proofreadingday = $this->noHtm2(PF::getStr($GLOBALS['str'], '>校對日：', '</span>', 1));                    //校對日
        $debtor = $this->noHtm2(PF::getStr($GLOBALS['str'], '債務人：', '</span>', 1));                   //債務人

        // $productid=$rs->productid;
        // $producttitle=$rs->producttitle;
        // $online=$rs->online;
        // $flag='update';

        // }
        //if ($debugid!=''){$flag='update';}

        //             if ($flag!=''){

        $beattime = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="AuctionState"', '</span>', 1));                   //拍次
        $beattime = $this->noHtm2('<' . $beattime);

        if ('其它拍賣' == $court) {
            $nocross_point = '點交';
        } else {
            $nocross_point = $this->noHtm2(PF::getStr($GLOBALS['str'], 'AuctionState2">', '</span>', 1));                   //點交否
        }
        $mainlawnestablishment = $this->noHtm2(PF::getStr($GLOBALS['str'], 'DetailInner2"><span id="MSSize">', '</span>', 0));                                 //主建坪
        $mainlawnestablishment = str_replace('坪', '', $mainlawnestablishment);
        $attachedtolawnestablishment = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="MSSize2">', '</span>', 0));                   //附建坪

        $additionalping = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="APSize">', '</span>', 0));                //增建坪
        $additionalping = str_replace('坪', '', $additionalping);

        $postulateping = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="APSize2">', '</span>', 0));           //公設坪
        $postulateping = str_replace('坪', '', $postulateping);

        $noticelawnestablishment = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="BCLPSize">', '</span>', 0));           //公告建坪
        $noticelawnestablishment = str_replace('坪', '', $noticelawnestablishment);

        $stakeholdersfloor = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="BCLPSize2">', '</span>', 0));                   //持分地坪
        $stakeholdersfloor = str_replace('坪', '', $stakeholdersfloor);

        $pingtotalnumberof = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="BTUP">', '</span>', 0));                    //總坪數

        $pingtotalnumberof = trim(str_replace('坪', '', $pingtotalnumberof));

        $floorprice = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="BTUP2">', '</span>', 0));                    //坪單價
        $floorprice = str_replace('萬/坪', '', $floorprice);

        $aftermakingvalue_added = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="VAGAV">', '</span>', 0));                    //拍後增值
        $aftermakingvalue_added = str_replace('萬', '', $aftermakingvalue_added);

        $currentvalues = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="VAGAV2">', '</span>', 0));                    //公告現值

        $totalupset = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="PriceGuarantee">', '</span>', 0));                    //總底價
        $totalupset = trim(str_replace('萬', '', $totalupset));

        $margin = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="PriceGuarantee2">', '</span>', 0));                    //保證金

        $margin = str_replace('萬', '', $margin);

        $landaddress = PF::left($this->noHtm2(PF::getStr($GLOBALS['str'], '<table id="LandTable" border="0">', '</table>', 1)), 500);                    //土地
        $landprice = $this->noHtm2(PF::getStr($landaddress, '公現：', '元', 1));                  //土地
        $buildings = PF::left($this->noHtm2(PF::getStr($body, '<table id="BuildingTable" border="0">', '</table>', 1)), 500); //建物

        $bidsrecords = PF::left($this->noHtm2(PF::getStr($GLOBALS['str'], '<table id="FailTable" border="0">', '</table>', 1)), 500);
        $hisrightis = PF::left($this->noHtm2(PF::getStr($GLOBALS['str'], '<table id="LoanTable" border="0">', '</table>', 1)), 500);                    //他項權利

        $transcriptinformation = $this->noHtm2(PF::getStr($GLOBALS['str'], '<span id="ProCopy">', '</span>', 1));                   //謄本資料
        $courttranscript = PF::left($this->noHtm2(PF::getStr($GLOBALS['str'], '<table id="DepositionTable" border="0">', '</table>', 1)), 500);
        $landvalue = $this->noHtm2(PF::getStr($GLOBALS['str'], 'LandAddTable', '</table>', 1));                   //謄本資料

        if (mb_substr_count($landvalue, '1.') > 0) {
            $landvalue1 = PF::left($this->noHtm2(PF::getStr($landvalue, '【', '萬', 1)), 500);
            $landvalue2 = PF::left($this->noHtm2(PF::getStr($landvalue, '【', '萬', 1)), 500);
        }
        //PF::printr($landvalue1);
        // 		 if ($debugid!='' || $debugflag==1){

        // 		 }
        $auctionssortnum = 9;
        if ('應買' == $beattime) {
            $auctionssortnum = 1.1;
        } else {
            foreach ($this->data['xmldoc']->xpath('//參數設定檔/拍賣情況/KIND') as $v) {
                if (substr_count($auctions, $v->資料) > 0) {
                    $auctionssortnum = strval($v->傳回值);
                    break;
                }
            }
        }

        $online = 1;
        if (is_numeric($totalupset)) {
            if ($totalupset < 100) {
                $online = 0;
            }
        }
        if (substr_count($auctions, '得標') > 0 || substr_count($auctions, '撤回') > 0 || substr_count($auctions, '承受') > 0) {
            $online = 0;
        }

        $inputs['number'] = $number; /*編號-*/
        $inputs['court'] = $court; /*法院-*/
        $inputs['city1title'] = mb_substr($address, 0, 3, 'UTF-8'); /*縣市-*/
        $inputs['city2title'] = mb_substr($address, 3, 3, 'UTF-8'); /*鄉鎮-*/
        $inputs['auctions'] = $auctions; /*拍賣情況-*/
        $inputs['debtor'] = $debtor; /*債務人-*/
        $inputs['proofreadingday'] = $proofreadingday; /*校對日-*/
        $inputs['tenderdate'] = $tenderdate; /*投標日-*/
        $inputs['beattime'] = $beattime; /*拍次-*/
        $inputs['nocross_point'] = $nocross_point; /*點交否-*/
        $inputs['mainlawnestablishment'] = $mainlawnestablishment; /*主建坪-*/
        $inputs['attachedtolawnestablishment'] = $attachedtolawnestablishment; /*附建坪-*/
        $inputs['additionalping'] = $additionalping; /*增建坪-*/
        $inputs['postulateping'] = $postulateping; /*公設坪-*/
        $inputs['noticelawnestablishment'] = $noticelawnestablishment; /*公告建坪-*/
        $inputs['stakeholdersfloor'] = $stakeholdersfloor; /*持分地坪-*/
        if ('' != $pingtotalnumberof) {
            $pingtotalnumberof = str_replace(',', '', $pingtotalnumberof);
            if (is_numeric($pingtotalnumberof)) {
                $inputs['pingtotalnumberof'] = $pingtotalnumberof; /*總坪數-*/
            }
        }
        $inputs['floorprice'] = $floorprice; /*坪單價-*/
        $inputs['aftermakingvalue_added'] = $aftermakingvalue_added; /*拍後增值-*/
        $inputs['currentvalues'] = $currentvalues; /*公告現值-*/
        if (is_numeric($totalupset)) {
            $inputs['totalupset'] = $totalupset; /*總底價-*/
        }
        $inputs['margin'] = $margin; /*保證金-*/
        $inputs['address'] = str_replace('　', '', mb_substr($address, 6, 500, 'UTF-8')); /*地址-*/
        if ('' == $storey) {
            $storey = PF::getStr($inputs['address'], '號', '樓', 0);
        }
        $inputs['buildname'] = $buildname; /*大樓名稱-*/
        $inputs['pattern'] = $pattern; /*法拍屋種類-*/
        $inputs['houseage'] = $houseage; /*屋齡-*/
        $inputs['postulatemorethan'] = $postulatemorethan; /*公設比-*/
        $inputs['architecture'] = $architecture; /*架構-*/
        $inputs['storey'] = $storey; /*樓高-*/
        $inputs['landaddress'] = $landaddress; /*土地地址-*/
        $inputs['landprice'] = $landprice; /*土地公現-*/
        $inputs['buildings'] = $buildings; /*建物-*/
        $inputs['bidsrecords'] = $bidsrecords; /*流標記錄-*/
        $inputs['hisrightis'] = $hisrightis; /*他項權利-*/
        $inputs['transcriptinformation'] = $transcriptinformation; /*謄本資料-*/
        $inputs['courttranscript'] = $courttranscript; /*法院筆錄-*/
        $inputs['landvalue1'] = $landvalue1; /*土地增值金額1-*/
        $inputs['landvalue2'] = $landvalue2; /*土地增值金額2-*/
        $inputs['management'] = $management; /*管理方式-*/
        $inputs['fees'] = $fees; /*費用-*/
        $inputs['parkingmode'] = $parkingmode; /*停車方式-*/
        $inputs['transportfunction'] = $transportfunction; /*交通機能-*/
        $inputs['schooldistrict'] = $schooldistrict; /*學區-*/
        $inputs['location'] = $location; /*放置位置-  //[新品上市:new], [首頁下方大圖:home1], [首頁下方小圖:home2], */
        $inputs['online'] = $online; /*上下架-  //[上架:1], [下架:0], */
        $inputs['sealedhuman'] = $sealedhuman; /*查封人-*/
        $inputs['thenumberofbidders'] = $thenumberofbidders; /*投標人數-*/
        $inputs['bidwere'] = $bidwere; /*得標人-*/
        $inputs['thebidprice'] = $thebidprice; /*得標價格-*/
        $inputs['increasetheamountof'] = $increasetheamountof; /*加價金額-*/
        $inputs['facingthelaneis'] = $facingthelaneis; /*面臨路寬-*/
        //$inputs['memo'] = $memo; /*備註-*/
        //$inputs['photofile'] = $photofile; /*檔案-*/
        $inputs['mrtland'] = $mrtland; /*捷運路線-*/
        $inputs['mrtstation'] = $mrtstation; /*捷運站名-*/
        $inputs['point'] = $point; /*點數-*/
        $inputs['auctionssortnum'] = $auctionssortnum; /*排序號碼-*/
        $inputs['lat'] = $lat; /*經度-*/
        $inputs['lng'] = $lng; /*緯度-*/
        $inputs['url'] = $this->data['url'];
        if (substr_count($request->server('CONTENT_TYPE'), 'multipart/form-data') > 0) {
            /*檔案上傳*/
            $upload = new UploadFile();
            $upload->request = $request;
            $upload->inputs = $inputs;
            $upload->folder = 'images/product/';
            $upload->width = '800';
            $upload->height = '600';
            //$upload->limitext = config('app.FileLimit');
            $inputs = $upload->execute();
            //$jsondata['img'] = $inputs['img'];
        }

        //FIXME 那些欄位為必填判斷
        $validators = null;

        $validators['number'] = ['required'];
        $validators['city1title'] = ['required'];
        $validators['city2title'] = ['required'];
        $validators['address'] = ['required'];

        $validator = \Validator::make($inputs, $validators);

        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $rows = DB::table('product');
        $rows->selectRaw('productid');

        $rows->myWhere('number|S', $number, 'number', 'Y');

        $rows->limit(1);
        //$rows = $rows->get();
        //dd($row);
        //PF::dbSqlPrint($rows);
        if ($this->debug) {
            foreach ($inputs as $k => $v) {
                echo $this->fieldnicknames[$k] . '(' . $k . ')' . '=>' . $v;
                echo '<hr>';
            }
            //exit();
        }
        if (0 == $rows->count()) {
            try {
                //throw new \Exception('no data');
                $inputs['producttitle'] = $producttitle; /*編號-*/
                $inputs['createdate'] = date('Y-m-d H:i:s'); /*建立時間-*/

                $inputs['productkind'] = '法拍';
                $inputs['hits'] = 0; /*點率閱-*/
                //$inputs['created_at'] = date('Y-m-d H:i:s'); /*建立時間-*/

                $this->db->create($inputs);

                $productid = \DB::getPdo()->lastInsertId();
                $resultmessage = 'Add OK';
            } catch (\Exception $e) {
                if ($this->debug) {
                    echo $e->getMessage();
                }
                $inputs = null;
                $inputs['city1title'] = mb_substr($address, 0, 3, 'UTF-8'); /*縣市-*/
                $inputs['city2title'] = mb_substr($address, 3, 3, 'UTF-8'); /*鄉鎮-*/
                $inputs['address'] = str_replace('　', '', mb_substr($address, 6, 500, 'UTF-8')); /*地址-*/
                $inputs['number'] = $number;
                $inputs['online'] = 0;
                $this->db->create($inputs);
                $productid = \DB::getPdo()->lastInsertId();
                $resultmessage = 'Add Fail';
                //throw new \CustomException('轉檔失敗');
            }
        } else {
            $rs = $rows->first();
            if ('0' == $rs->online) {
                throw new \CustomException('Online:0 No Update');
            }

            $productid = $rs->productid;
            try {
                $inputs['updated_at'] = date('Y-m-d H:i:s');
                $this->db->find($rs->productid)->update($inputs);
                $resultmessage = 'Update Ok';
                //throw new \Exception('no data');
            } catch (\Exception $e) {
                if ($this->debug) {
                    echo $e->getMessage();
                }
                $resultmessage = 'Update Fail';
                \Log::error($productid . '=>' . $e->getMessage());
            }
        }
        $this->productid = $productid;
        $this->resultmessage = $resultmessage;
    }
}
