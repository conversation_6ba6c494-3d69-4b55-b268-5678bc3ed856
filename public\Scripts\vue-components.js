function myComponentsPlugin(app, options) {
    //<vue-color v-model="inputs.color"></vue-color>
    app.component('vue-color', {
        template: `<div class="input-group">
        <input type="color" class="form-control" v-model="newValue"  />
        <div class="input-group-append">
            <button type="button" @click="newValue='transparent';" class="btn btn-light">清空</button>
        </div>
        </div>`,
        props: ["value"],
        emits: ['update:modelValue'],
        data: function () {
            return {
                newValue: this.value
            };
        },
        methods: {
        },
        watch: {
            newValue: {
                handler(val) {
                    this.$emit('update:modelValue', val)
                },
                deep: true
            }
        },
    });
    //<vue-upload folder="{{ url('/') }}/images/ratingproject/" width=800 height=800 v-model="inputs.bg_img" :value="inputs.bg_img" accept="image/*"></vue-upload>
    app.component('vue-upload', {
        template: `<div>

        <img :src="newValue" border="0" align="absmiddle" style="width: 100px" />
        <button
            type="button"
            class="btn btn-tool"
            data-card-widget="remove"
            title="Remove"
            @click="newValue=''"
            v-show="newValue!=null"
        >
            <i class="fa fa-trash-o" aria-hidden="true"></i>
        </button>
        <input
            type="file"
            class="form-control"
            @change="onFileChange($event)"
            placeholder=""
            :accept="accept"
        />
       </div>`,
        props: ["accept", "width", "height", "value", "folder"],
        emits: ['update:modelValue'],
        data: function () {
            return {
                newValue: this.value
            };
        },
        methods: {
            onFileChange(e) {
                let file = e.target.files[0];
                const reader = new FileReader();
                reader.onload = (e) => {
                    resizeImage(e.target.result, this.width, this.height).then((res) => {
                        this.newValue = res;
                        this.$emit('update:modelValue', this.newValue);
                    });

                };
                reader.readAsDataURL(file);
            },
        },
        async mounted() {
            document.addEventListener("DOMContentLoaded", () => {

                if (this.value != null && this.value != "") {
                    var array = [".png", ".gif", ".jpg", ".jpeg"];
                    var isFound = false;
                    for (var i = 0; i < array.length; i++) {
                        if (this.value.indexOf('.' + array[i])) {
                            isFound = true;
                            break;
                        }
                    }
                    if (isFound) {
                        this.newValue = this.folder + this.value;
                    }
                }
            });
        },


    });
    //<vue-uploadmultiple width=800 height=800 v-model="inputs.file3" accept=".png,.jpg,*.gif"></vue-uploadmultiple>
    app.component('vue-uploadmultiple', {
        mixins: [myMixin],
        template: `
        <template v-for="(rs, index) in newValues">
        <img :src="rs" alt="Uploaded File" style="width:100px" />
        <button type="button" class="btn btn-tool" data-card-widget="remove" title="Remove" @click="onDel(newValues,index)">
            <i class="fas fa-times"></i>
        </button>
        <a href="javascript:;" @click="onMoveRow(newValues,'up',index);">▲ </a>
        <a href="javascript:;" @click="onMoveRow(newValues,'down',index);">▼</a>

        <br>
        </template>
        <input type="file" class="form-control" .name="name" multiple @change="onFilesChange($event)" placeholder="" :accept="accept" />
        寬:{{width}}px ; 高:{{height}}px

        `,
        props: ["accept", "width", "height", "value", "name"],
        emits: ['update:modelValue'],
        data: function () {
            return {
                newValues: []
            };
        },
        methods: {
            onFilesChange(e) {
                let files = e.target.files;
                for (let i = 0; i < files.length; i++) {
                    let fileReader = new FileReader();
                    fileReader.onload = (e) => {
                        //this.inputs.body4.imgs.push(e.target.result);
                        resizeImage(e.target.result, this.width, this.height).then((res) => {
                            this.newValues.push(res);
                            this.$emit('update:modelValue', this.newValues);
                        })
                    };
                    //console.log(["this.newValues.length", this.newValues.length]);
                    fileReader.readAsDataURL(files[i]);
                }

            },
        },
        created: function () {
            if (this.value != null) {
                this.newValues = this.value;
            }
        },
    });
    // <link href="{{ url('/') }}/select2/css/select2.css" rel="stylesheet" />
    // <script src="{{ url('/') }}/select2/js/select2.min.js"></script>
    // <vue-select2 v-model="rs.productitem_id" :options="productitem_ids">
    // </vue-select2>
    app.component('vue-select2', {
        template: `<select v-bind:name="name" class="form-control" :required="required"></select>`,
        props: ["name", "options", "value", "modelValue"],

        // props: {
        //     name: '',
        //     options: {
        //         Object
        //     },
        //     value: null,
        //     multiple: {
        //         Boolean,
        //         default: false

        //     }
        // },
        emits: ['update:modelValue'],
        data() {
            return {
                select2data: [],
            }
        },

        methods: {
            updateValue(newValue) {
                this.$emit('input', newValue);
            },
            formatOptions() {
                let vm = this;
                this.select2data.push({ id: '', text: 'Select' })
                this.options.forEach(function (rs, index) {
                    vm.select2data.push({ id: rs['id'], text: rs['text'] })
                })
            }
        },
        destroyed: function () {
            $(this.$el).off().select2('destroy')
        },

        mounted() {

            this.formatOptions()
            let vm = this
            let select = $(this.$el)
            select.select2({
                placeholder: 'Select',
                width: '100%',
                allowClear: true,
                data: this.select2data
            }).on('change', function () {
                vm.$emit('input', select.val())
                vm.$emit('update:modelValue', select.val());
            });

            if (typeof (this.modelValue) != "undefined") {
                select.val(this.modelValue).trigger('change')
            }
        },
        watch: {
            newValue: {
                handler(val) {
                    console.log(["val", val]);
                    this.$emit('update:modelValue', val)
                },
                deep: true
            }
        },
    })




}