<?php

namespace {{ namespace }};

use {{ rootNamespace }}Http\Controllers\Controller;
use Illuminate\Http\Request;

use DB;
use PF;
use Illuminate\Http\Request;

/***
"功能名稱":"活動",
"資料表":"{{ model }}",
"建立時間":"2022-01-18 11:38:08 ",
***/
class {{ class }} extends Controller

    private $data;
    private $xmlDoc;

    /**
     *TODO 建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
     
        // FIXME 共用的hidden變數
        $this->data['hiddens'] = [];
        $this->data['displaynames'] = $this->{{ model }}Repo->getFieldTitleArray();
        $this->data['nav'] = PT::nav($this->data['xmldoc'], '{{ model }}');
    }

   /**
     * @OA\Post(
     *     path="/api/admin/{{ model }}",security={{"bearerAuth":{}}},operationId="",tags={"後台/會員基金"},summary="列表",description="",     
  *     @OA\RequestBody(required=true,

     *      @OA\JsonContent(
     *      allOf={
     
     *         @OA\Schema(@OA\Property(property="page",description="頁數",type="integer",example="1",)),
     *         @OA\Schema(@OA\Property(property="pagesize",description="筆數/頁",type="integer",example="10",)),
     *         @OA\Schema(@OA\Property(property="search",description="搜尋",type="string",example="",)),
     *     })

     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",     
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),     
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="current_page", type="integer",description="目前頁數", ),
     *          @OA\Property(property="total", type="integer",description="總頁數", ),     
     * 
     *      @OA\Property(property="data",  type="array",
     *      @OA\Items(allOf={
     *         @OA\Schema(ref="#/components/schemas/member"),
     *         @OA\Schema(@OA\Property(property="kindtitle", type="string",description="系列", example="") ),
     *         @OA\Schema(@OA\Property(property="title", type="string",description="基金", example="") ), 
     *     }))
     
     *      ),)
     * ),)
     */  

    /**
     * TODO 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $rows = $this->getRows($request);
        //$rows = $rows->take(10);
        //PF::dbSqlPrint($rows);
        //$rows = $rows->get();
        $pagesize = (is_numeric($request->input('pagesize')) ? $request->input('pagesize') : 10);
        $rows = $rows->paginate($pagesize);
        foreach ($rows as $rs) {
            $rs->share_checkin_url = \config('line.life_line_url').'?live=1&eid='.$rs->session_id;
            $rs->share_checkin_qrcode_url = url('/').'/images/{{ model }}session/'.$rs->session_id.'.svg';
        }
        // 顯示sqlcmd
        $this->jsondata['data'] = $rows;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * 資料Rows.
     *
     * @return \Illuminate\Http\Response
     */
    public function getRows($request)
    {
        $rows = $this->{{ model }}Repo->selectRaw('*');
        
        $rows->myWhere('{{ model }}.id|N', $request->input('id'), $this->data['displaynames'], 'N');
        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(created_at,DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert(created_at,DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');
        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), 'desc' == $request->input('sorttype') ? $request->input('sorttype') : 'asc');
        } else {
            $rows->orderByRaw('{{ model }}.id desc');
        }
        //PF::dbSqlPrint($rows);

        return $rows;
    }

    /**
     * @OA\Post(
     *     path="/api/admin/{{ model }}/show",security={{"bearerAuth":{}}},operationId="",tags={"後台/活動"},summary="單筆顯示",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="id",description="編號",type="integer",example="1",),
     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/{{ model }}"),

     *         @OA\Schema(@OA\Property(property="share_url", type="string",description="報名網址", example="") ),
     *     }),
     *      @OA\Property(property="sessions", type="array",@OA\Items(
     *      allOf={
     *         @OA\Schema(ref="#/components/schemas/{{ model }}session"),

     *         @OA\Schema(@OA\Property(property="share_checkin_url", type="string",description="報到網址", example="") ),
     *     })


     *     )),
     *     ),)
     *),)
     */

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show($request)
    {
        $rows = $this->{{ model }}Repo->select('*');
        $rows->myWhere('id|N', $request->input('id'), 'id', 'Y');
        $rows = $rows->take(1);
        //  PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            $rs = $rows->first();
            $rs->share_url = \config('line.life_line_url').'?live=2&aid='.$rs->id;
            $rows = $this->{{ model }}sessionRepo->selectRaw('*');
            $rows->myWhere('{{ model }}_id|N', $request->input('id'), 'key', 'Y');
            //$rows->orderByRaw('id desc');
            $rows = $rows->get();

            $rs->sessions = $rows;
            //$rs->qrcodeurl= url('/').'/images/{{ model }}/'.$rs->id.".svg";
            $this->jsondata['data'] = $rs;
        } else {
            throw new \CustomException('no data');
        }

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

   /**
     * @OA\Post(
     *     path="/api/admin/{{ model }}/store",security={{"bearerAuth":{}}},operationId="",tags={"後台/會員"},summary="新增/編輯",description="編號有值代表編輯,沒有代表新增",
  *     @OA\RequestBody(required=true,
  *      @OA\JsonContent(
   *      allOf={
   *         @OA\Schema(ref="#/components/schemas/{{ model }}"),
   *         @OA\Schema(@OA\Property(property="", type="string",description="系列", example="") ),
   *     })
     *   ,),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *      @OA\Property(property="data", type="object",
     *          @OA\Property(property="id", type="integer",description="編號", ),
     *      )
     * 
     *     ),)
     *),)
     */

    /**
     * TODO 資料新增編輯儲存.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $edit = $request->input('id');
        //FIXME 那些欄位為必填判斷
        $validators = null;
        $validators['email'] = ['nullable', 'email']; //email
        $validators['title'] = ['required', new MyValidatorsUnique(
            '{{ model }}', ['id' => $edit]
            )];
        $validator = \Validator::make($this->data, $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $inputs = $request->all();

       

        if ('' == $edit) {
            $inputs['created_at'] = date('Y-m-d H:i:s'); //建立時間-
            //PF::printr($inputs);exit();
            $edit = $this->{{ model }}Repo->create($inputs)->id;
            $this->jsondata['resultmessage'] = '新增成功';
        } else {
            //PF::printr($inputs); exit();
            $this->{{ model }}Repo->update($inputs, $edit);
            $this->jsondata['resultmessage'] = '更新成功';
        }

       
        $this->jsondata['data']['id'] = $edit;

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * @OA\Post(
     *     path="/api/admin/{{ model }}/destroy",security={{"bearerAuth":{}}},operationId="",tags={"後台/活動"},summary="刪除",description="",
     *   @OA\RequestBody(required=true,@OA\MediaType(mediaType="application/json",@OA\Schema(
     *         @OA\Property(property="del",description="要刪除的編號",type="integer",example="1",description="多筆中間用逗號",),

     *       ),
     *   ),),
     *   @OA\Response(response=200,description="回覆",
     *     @OA\JsonContent(type="object",
     *      @OA\Property(property="resultcode",type="integer",description="訊息代碼",),
     *      @OA\Property(property="resultmessage",type="string",description="訊息內容"),
     *     ),)
     *),)
     */

    /**
     * TODO 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $this->{{ model }}Repo->deleteIds($this->data['del']);
        $this->jsondata['resultmessage'] = '刪除成功';

        return response()->json($this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
