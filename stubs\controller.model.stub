<?php

namespace {{ namespace }};

use {{ rootNamespace }}Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Validator;
use Exception,DB;
use PF;
use App\Repositories\{{ model }}Repository;
class {{ class }} extends Controller
{
    private $data;
    public function __construct({{ model }}Repository ${{ model }}Repo)
    {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->{{ model }}Repo=${{ model }}Repo;
        $this->data['displaynames'] = $this->{{ model }}Repo->getFieldTitleArray();
        
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        $rows = $this->{{ model }}Repo->selectRaw('*');
        $rows->myWhere('{{ model }}.id|N', $this->data['{{ model }}'], '{{ model }}id', 'Y');
        $rows->orderByRaw('{{ model }}.id desc');
        $rows = $rows->paginate(10);
        $this->data['rows'] = $rows;
        return view('{{ model }}.index', [
            'data' => $this->data
            ]
       );
    }
    public function show(Request $request,${{ model }}id)
    {
        $rows = $this->{{ model }}Repo->selectRaw('{{ model }}.*');
        $rows->myWhere('id|N', $this->data['{{ model }}'], '{{ model }}id', 'Y');
        $rows->orderByRaw('id desc');

        $rows = $rows->take(1)->get();
        //$rows = $rows->get();
        //dd($row);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
            
        } else {
            throw new \CustomException('No data');
        }
        
        // Config::set('config.keyword', '');
        // Config::set('config.description', '');

        return view('{{ model }}.show', [
            'data' => $this->data,
            ]
       );
    }

}
