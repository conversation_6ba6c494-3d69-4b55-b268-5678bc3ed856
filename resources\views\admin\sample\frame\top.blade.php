@extends('admin.layouts.master')
@section('css')
<style>
body {
    overflow: hidden;
    /*刪除水平與垂直捲軸*/
    overflow-x: hidden;
    /*刪除水平捲軸*/
    overflow-y: show;
}
</style>

@endsection

@section('js')
<script type="text/javascript">

jQuery(document).ready(function() {
   

    var location = "{{$data['location']}}";
    if (location == "") {
        location = 0;
    }
    location=parseInt(location);
    
    $("a").click(function(){
        
  	    v1=$(this);
        jQuery("a").each(function(i) {
            //v1=$(this)
            $(this).removeClass();
            console.log($(this).attr('href'));
            console.log($(v1).attr('href'));
            if ($(this).attr('href') == $(v1).attr('href')) {
                $(this).addClass("btn btn-primary");
                window.open($(this).attr('href'), 'main');
            } else {
                $(this).addClass("btn btn-light");
            }
          });
    });

    $("a").eq(location).trigger('click');





});
</script>




@endsection



@section('content')


<!-- Navigation-->

<div class="table-responsive-md">
    <table>

        <tr>
         
            <td>

                <a href="{{ asset('admin/member/edit') }}?edit={{$data['memberid']}}" target="main"
                    id="first" class="btn btn-primary" >編輯
                </a>

            </td>
            <td>
                <a href="{{ asset('customer/customer/edit') }}?edit={{$data['customerid']}}" target="main"
                    class="btn btn-light" >客戶編輯
                </a>
            </td>

            <td>

                <button type="button" class="btn btn-secondary" onclick="parent.windowclose();">關閉視窗</button>

            </td>


        </tr>
    </table>
</div>



@endsection