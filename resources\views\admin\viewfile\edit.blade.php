﻿@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
function oForm_onsubmit(form) {
    if (PF_FormMultiAll(form) == false) {
        return false
    };
    PF_FieldDisabled(form) //將全部button Disabled
    return true;
}
</SCRIPT>
<div class="card">
    <div class="card-body">
        <!--// TODO : 前端資料填寫-->

        <!--// TODO : 前端資料填寫-->
        <form name="oForm" id="oForm" method="post" language="javascript" action="{{ URL::to('admin/viewfile/store') }}"
            onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->


            <table border="0" width="100%" bordercolorlight="#000000" bordercolordark="#FFFFFF" cellspacing="1"
                cellpadding="5" align="center" class="oFormTableMRwd">
                @if (PF::xmlSearch($data['xmldoc'],"//參數設定檔/語系/KIND/傳回值","資料","zh")!="zh")
                <tr>

                    <td valign="top" width="20%">
                        <font class="ftitle">語系：</font>
                        <font class="DMIn">*</font>
                    </td>
                    <td valign="top">
                        {{Form::myUIXml([
                                           'xmldoc' => $data['xmldoc'],
                                           'type' =>'select',
                                           'title' =>'語系',
                                           'node' => '//參數設定檔/語系/KIND',
                                           'name' => 'alg',
                                           'value' => $data['alg'],
                                           'linecount' => 4,
                                           'requiredclass' => 'required[1,TEXT]',
                                           'onChange'=>"location.href='?edit=".$data['edit']."&alg='+ this.options[this.selectedIndex].value;" 
                                       ])
               }}
                    </td>
                </tr>
                @endif

                <tr>
                    <td valign="top" colspan="2">

                        <textarea name="body" cols="40" rows="10" title="內容"
                            style="width:100%;height:500px">{!!$data['body']!!}</textarea>

                        @if ($data['type']=="html")
                        <script src="{{ URL::to('ckeditor/ckeditor.js') }}"></script>
                        <script>
                CKEDITOR.replace('body');
                </script>
                        @endif

                    </td>
                </tr>





            </table>
            <div align="center">

                <button type="submit" class="btn btn-success">確定</button>
                <button type="reset" class="btn btn-secondary">取消</button>


                <input type="hidden" name="gobackurl" value="{{request()->getRequestUri()}}">

                {{Session::get('success')}}
            </div>




            {{ Form::hidden("edit", $data["edit"] ) }}

        </form>

    </div>
</div>


@stop