<?php

namespace App\Exceptions;

use Exception;


class CustomAlertException  extends Exception {
    public $message;
    public function __construct($message = "", $code = 0, Throwable $previous = null) {
        $this->message = $message;
    }
    public function report() {

        // \Log::debug('User not found');
    }
    public function render($request) {

        return view('layouts.backalert', [
            'alert' => $this->message,
        ]);
    }
}
