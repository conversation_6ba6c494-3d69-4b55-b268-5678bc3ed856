@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
    function oForm_onsubmit(form) {
        if (PF_FormMultiAll(form) == false) {
            return false
        };
        PF_FieldDisabled(form) //將全部button Disabled
        return true;
    }
</SCRIPT>
<div class="card">
    <div class="card-body">


        <!--// TODO : 前端資料填寫-->
        <form name="oForm" id="oForm" method="post" language="javascript" action="{{ URL::to('admin/epost/store') }}" onsubmit="return oForm_onsubmit(this);">
            <!--novalidate-->
            @if (PF::xmlSearch($xmlDoc,"//參數設定檔/語系/KIND/傳回值","資料","zh")!="zh")
            <div class="form-group row">
                語系：<font class="text-danger">*</font>
            </div>


            <div class="form-group row">
                <div class="col-sm-12 col-md-6  col-lg-8">
                    {{Form::myUIXml([
                        'xmldoc' => $data['xmldoc'],
                        'type' =>'select',
                        'title' =>'語系',
                        'node' => '//參數設定檔/語系/KIND',
                        'name' => 'alg',
                        'value' => $data['alg'],
                        'linecount' => 4,
                        'requiredclass' => 'required[1,TEXT]',
                        'onChange'=>"location.href='?edit=".$data['edit']."&alg='+ this.options[this.selectedIndex].value;" 
                        
                    ])
        }}
                </div>
                <div class="col-sm-12 col-md-6  col-lg-4">
                    請先選擇你要異動的語系內容
                </div>
            </div>




            @endif


            <div class="form-group row">
                {{__('內容')}}：
            </div>
            <div class="form-group row">

                <textarea name="epostbody" class="form-control" cols="40" rows="20" title="內容" style="width:100%">{!!$data['epostbody']!!}</textarea>
                @if ($data['type']=="html")
                <script src="{{ URL::to('ckeditor/ckeditor.js') }}"></script>
                <script>
                    CKEDITOR.replace('epostbody');
                </script>
                @endif

            </div>

            <div align="center" style=''>

                <button type="submit" class="btn btn-success">確定</button>
                <button type="reset" class="btn btn-secondary">取消</button>


                <input type="hidden" name="gobackurl" value="{{request()->getRequestUri()}}">
                {{Session::get('success')}}
            </div>

            {{ Form::hidden("edit", $data["edit"] ) }}
        </form>
    </div>
</div>
@stop