﻿//onclick="PF_Hits('board','id','{{$rs->id}}');"
function PF_Hits(dbtable, field, key) {

	jQuery.ajax({
		type: "post",
		data: 'dbtable=' + dbtable + '&field=' + field + '&key=' + key,
		url: FC_WebFolder + 'api/db/sethits',
		dataType: "html"
	})
}

function PF_escape(S) {
	return encodeURIComponent(S)
}
//PF_getdata('xx.php','yy','kind=1');
//<div id="yy" style="height:550px;overflow:hidden"></div>
function PF_getdata(requestURL, objname, pars) {
	if (typeof (objname) == "object") {
		$(objname).html('Loading content, please wait..');
	} else {
		$("#" + objname).html('Loading content, please wait..');
	}
	if (typeof (pars) == "object") {
		pars = $(pars).serialize();
	}
	jQuery.ajax({
		type: "post",
		data: pars,
		url: requestURL,
		dataType: "html",
		error: function (resp) {
			//	alert('Error: url : ' + requestURL+'?'+pars+'\n'+resp.responseText);
		},
		beforeSend: function () {
			//jQuery("#"+objname).html('Loading content, please wait..');
		},
		success: function (resp) {
			if (typeof (objname) == "object") {
				$(objname).html(resp);
			} else {
				$("#" + objname).html(resp);
			}
		}
	});
}
function PF_AjaxblockUI(requestURL, pars, title) {


	if (requestURL == '') { return; }
	jQuery.ajax({
		type: "post",
		url: requestURL,
		dataType: "json",
		data: pars,
		error: function (resp) {
			alert('Error: url : ' + requestURL + '?' + pars + '\n' + resp);
		},
		beforeSend: function () {
			//jQuery("#loading").toggle();
		},
		error: function (XMLHttpRequest, textStatus) {

		},
		success: function (resp) {
			try {
				if (resp.resultcode == 0) {
					Swal.fire({
						position: 'top-end',
						icon: 'success',
						title: resp.resultmessage,
						//text: title,
						showConfirmButton: false,
						timer: 1500
					})
					//$.growlUI(title,resp.resultmessage);
				} else {
					_alert(resp.resultmessage);
				}
			} catch (error) {
				console.log(error);
				_alert(error);
			}

		}
	})
}
//PF_sendtoline(document.title,location.href);
function PF_sendtoline(title, url) {


	window.open('https://social-plugins.line.me/lineit/share?url=' + encodeURIComponent(url) + '&text=' + encodeURIComponent(title) + '&from=line_scheme')
	//location.href = link;
	return false;
}
//PF_sendtofacebook(location.href);
function PF_sendtofacebook(url) {
	var link = 'http://www.facebook.com/share.php?u=' + encodeURIComponent(url);
	window.open(link)
	//location.href = link;
	return false;
}

function myproduct(s, product_id) {
	online = 0;
	if ($(s).attr('class') == 'notHeart') {
		online = 1;
	}

	var dict = {
		url: FC_WebFolder + "api/membercenter/myproduct/store/",
		data: {
			'product_id': product_id,
			'online': online
		},
		//data:jQuery("#oForm").serialize(),
		//data:JSON.stringify(this.inputs),
		dataType: 'json',
		noloading: true,

		//debug:true,
	}
	PF_ajax(dict).done(function (obj) {

		if (obj.resultcode == 0) {

			if (online == 1) {
				$(s).removeClass('notHeart');
				$(s).addClass('okHeart');
			} else {
				$(s).removeClass('okHeart');
				$(s).addClass('notHeart');
			}
			_toast(obj.resultmessage, 800)
		} else {
			_alert(obj.resultmessage);
		}
	}).fail(function (resp) {
		_alert(resp.statusText);
	}).then(function () {

	});


}
var productids = [];
var myTimerCount = 0;
jQuery(document).ready(function () {

	jQuery("[productid]").each(function (i, item) {
		$(item).click(function () {
			productid = $(item).attr('productid');
			if (typeof (productids[productid]) == "undefined") {
				productids[productid] = 1;

			}

			if (productids[productid] >= 3) {
				var m = bootstrapModal('物件編輯', 'admin?productid=' + productid, $(window).width() - 50, $(window).height() - 150);
				m1 = $(m);
				m1.modal({
					title: '',
					show: true,
					keyboard: true,
					url: '',
				});
				m1.on("hidden.bs.modal", function (e) {
					window.location.reload();

				});
			}
			productids[productid]++;
			console.log(productids[productid]);
		});
		// myTimer = setInterval(function () {
		// 	myTimerCount++;
		// }, 1000);

	});
	// $(item).mouseup(function () {
	// 	clearInterval(myTimer);
	// 	console.log("myTimerCount:" + myTimerCount);
	// 	if (myTimerCount >= 1) {
	// 		$.colorbox({
	// 			href: 'admin?productid=' + $(item).attr('productid'),
	// 			width: 500,
	// 			height: 800,
	// 			iframe: true,
	// 			overlayClose: false,
	// 			slideshow: false,
	// 			current: '',
	// 			onComplete: function () {
	// 				closeEvent = function () {
	// 					//PF_print('handled');
	// 				};
	// 			},
	// 			onClosed: function () {
	// 				window.location.reload();
	// 			}
	// 		});
	// 	}
	// 	myTimerCount = 0;

	// 	console.log(2);
	// });


});
/*
function PF_DivTop(){
		 if (jQuery('#divtop').length>0){

			$('#divtop').css('top', $(document).scrollTop() + $(window).height() - 200);
			$('#divtop').css('left', $(document).scrollLeft() + $(window).width() - 150);
			setTimeout("PF_DivTop()", 300)// 1秒後執行一次xxx()
		}
}
*/