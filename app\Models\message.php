<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DB;
use PF;
//留言版
class message extends baseModel
{
    

	public $tabletitle = '留言版';
    public $table = 'message';
    public $primaryKey = 'id';
    
    //欄位必填
    public $rules = [
		'id' => 'required',
'kind' => 'required',
'title' => 'required',

    ];
    public $fieldInfo = [
'id'=>['title'=>'自動編號','type'=>'int(11)'],//
'kind'=>['title'=>'','type'=>'varchar(255)'],//
'name'=>['title'=>'','type'=>'varchar(255)'],//
'email'=>['title'=>'','type'=>'varchar(255)'],//
'sex'=>['title'=>'','type'=>'varchar(255)'],//
'title'=>['title'=>'','type'=>'varchar(255)'],//
'body'=>['title'=>'','type'=>'varchar(2000)'],//
'created_at'=>['title'=>'建立時間','type'=>'datetime'],//
'updated_at'=>['title'=>'編輯時間','type'=>'datetime'],//
'rebody'=>['title'=>'','type'=>'varchar(2000)'],//
];
    //日期欄位的儲存格式。'Y-m-d' or 'U' or ...
    //protected $dateFormat = 'Y-m-d';
    
    protected $fillable = ['kind','name','email','sex','title','body','created_at','updated_at','rebody']; //可充許傳入的欄位
    protected $guarded =[];   //拒絶修改的欄位(fillable,guarded都設已fillable為準)
    protected $dates = ['created_at','updated_at'];    
  
  public function __construct($attr = array(), $exists = false) {
        $this->fillable = parent::getfillables();//接受$request->all();
        parent::__construct($attr, $exists);
        
        parent::setFieldInfo($this->fieldInfo);
  }         
  public static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            //$model->uuid = (string) Uuid::generate();
        });
        self::created(function ($model) {          
                //$model->uuid = (string) Uuid::generate();
        });
        static::updating(function ($model) {
            // do some logging
        });
        self::updated(function ($model) {          

        });
         static::deleting(function ($model) {
            //  DB::table('message')->select()
            // ->myWhere('id|N', $model->id, "id", 'Y')
            // ->delete();



          
        });
        static::deleted(function ($model) {
            
        });

    }	


}
