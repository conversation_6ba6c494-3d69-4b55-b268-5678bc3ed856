@extends('layouts.master')
@section('css')
@endsection

@section('js')
    <script language=JavaScript>
        document.addEventListener("DOMContentLoaded", () => {
            $('#paginatortotal1').html($('#paginatortotal').html());

        });
    </script>
@endsection
@section('banner')
@endsection
@section('content')
    <div class="row justify-content-center ml-0">


        @inject('search', '\App\Http\Controllers\layouts\searchController')
        {!! $search->index($data) !!}


    </div>
    <div class="px-3">
        <div class="theme-container">


            @include('layouts.house')



            {{ $data['rows'] != null ? $data['rows']->links('layouts.paginatecus') : '' }}
        </div>
    </div>
@endsection
