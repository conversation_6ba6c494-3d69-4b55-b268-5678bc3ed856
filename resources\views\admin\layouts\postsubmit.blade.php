@extends('admin.layouts.master')


@section('content')

<form name="oFormpossubmit" method="post" language="javascript"
    action="{{ isset($action) ? $action : request()->get('gobackurl') }}">
    @foreach (config('app.globalhidden') as $key)
    <input type="hidden" name="{{$key}}" value="{{request()->get($key)}}">
@endforeach


@if ($data['hiddens']!="")
    @foreach ($data['hiddens'] as $key)
    <input type="hidden" name="{{$key}}" value="{{$data[$key]}}">
    @endforeach
@endif
    <input type="hidden" name="page" value="{{$data['page']}}">
</form>

<script language=JavaScript>
$(function() {

    try {    
        @if ($data['alert']!="") 
            Swal.fire({
            //title: "Are you sure?",
            text: '{{__($data['alert'])}}',
            icon: "success",
            //buttons: true,
            dangerMode: true,
            })
            .then((willDelete) => {

                @if ($data['gobackurl']!="")            
                    document.forms['oFormpossubmit'].submit();
                @endif
            });
            
        @else
        document.forms['oFormpossubmit'].submit();
        @endif
        
    } catch (error) {
            alert(error)        
    }
});
</script>
@endsection