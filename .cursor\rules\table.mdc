---
description:
globs:
alwaysApply: false
---

# 任務

- 產生全部資表關聯vscode 的snippets 到`.vscode\table.code-snippets`

# 來源

- 請依照 `app/dbspec.md` 資料表定義檔案

# 程式要求

- 先把之前的資料清空
- 修改只限table.code-snippets檔案，不要新增其他檔案
- 資料表名稱用小寫， 如果有多層table請一併補上


如:
```
   "{table1}_{table2}": {
        "prefix": "with {table1} {table2} {table1 title} {table2 title}",
        "scope": "php",
        "body": [
                " \\$rows = \App\Models\{table1}::selectRaw('*')",
               "  ->with(['{table2}' => function (\\$query) {",
                   "  \\$query->selectRaw('{table1_id},id') ",
                       "  ->with(['{table2}' => function ($query) {",
                          "   $query->selectRaw('{table2_id},id'); ",
                      "   }]);",
              "   }])",
               " ->get();"
        ]
    },







```