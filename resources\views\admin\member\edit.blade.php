@extends('admin.layouts.master')

@section('css')
@endsection

@section('js')
@endsection

@section('nav')
{!!$data['nav']!!}
@endsection


@section('content')


<SCRIPT language=JavaScript>
    function oForm_onsubmit(form) {
        if (!form.checkValidity()) {
            checkValidity(form);
            return false;
        } else {
            PF_FieldDisabled(form); //將全部button Disabled
            return true;

        }
    }
</SCRIPT>

<div class="card">
    <div class="card-body">

        <!--// TODO : 前端資料填寫-->
        <form name="oForm" class="container-fluid p-1 needs-validation" id="oForm" method="post" language="javascript" novalidate action="{{ request()->url() }}/../store">


            <div class="form-group row">
                <label class="col-md-2">行動電話：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">

                    <input type="tel" class="form-control" name="mobile" value="{{$data['mobile']}}" required placeholder="ex 09123456789" pattern="09[1-8][0-9]([\-|\s]?)[0-9]{3}\1[0-9]{3}" />
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">姓名：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">

                    <input type="text" required class="form-control" name="name" value="{{$data['name']}}">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-md-2">LINE ID：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    <input type="text" class="form-control" name="lineid" value="{{$data['lineid']}}">
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">密碼：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">

                    <input type="password" class="form-control" name="password" autocomplete="new-password" pattern="[A-Za-z0-9]{8,20}" value="" size="40" min="8" max="20" data-toggle="tooltip" data-placement="top" title="密碼長度必須為8~20位, 其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於" />
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">性別：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    {{
    Form::myUIXml([
        'xmldoc'=>$data['xmldoc'],
        'type'=>"radio",
        'node'=>"//參數設定檔/性別/KIND",
        'name'=>"sex",
        'value'=>$data['sex'],
        'linecount'=>5,
        'required'=>false,
        //'v-model'=>"inputs.sex",

    ])
}}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">市話：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    <input type="tel" class="form-control" name="tel" value="{{$data['tel']}}" placeholder="ex xxx-xxxxxxxx#ext" />
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">電子信箱：<font class="text-danger p-1">*</font></label>
                <div class="col-md-10">

                    <input type="email" class="form-control" name="email" value="{{$data['email']}}" required placeholder="ex <EMAIL>" />
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">房屋類別：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    {{
    Form::myUIXml([
        'xmldoc'=>$data['xmldoc'],
        'type'=>"checkbox",
        'node'=>"//參數設定檔/房屋類別/KIND",
        'name'=>"patterns",
        'value'=>$data['patterns'],
        'linecount'=>5,
        'required'=>false,
        //'v-model'=>"inputs.patterns",

    ])
}}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">總底價：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    {{
    Form::myUIXml([
        'xmldoc'=>$data['xmldoc'],
        'type'=>"radio",
        'node'=>"//參數設定檔/購屋預算/KIND",
        'name'=>"totalupsets",
        'value'=>$data['totalupsets'],
        'linecount'=>5,
        'required'=>false,
        //'v-model'=>"inputs.totalupsets",

    ])
}}
                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">區域：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    {{
    Form::myUIDb([
        'type'=>"select2multiple",
        'sql'=>"select postal,concat(city2.city1title,'>',city2.city2title) as city2title from city2 inner join city1 on(city2.city1id=city1.city1id) order by sortnum desc,city2title",
        'name'=>"postals",
        'value'=>$data['postals'],
        'linecount'=>5,
        'required'=>false,
        //'v-model'=>'inputs.postals',


    ])
}}
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">細項需求：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    <textarea name="item_request" cols="50" rows="5" class="form-control">{{$data['item_request']}}</textarea>
                </div>
            </div>



            <div class="form-group row">
                <label class="col-md-2">是否訂閱電子報：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    {{Form::hidden('isepaper', '' ) }}
                    {{Form::checkbox('isepaper', 1,$data['isepaper'],['class'=>"form-check"])}}

                </div>
            </div>

            <div class="form-group row">
                <label class="col-md-2">備註：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    <textarea name="memo" cols="50" rows="5" class="form-control">{{$data['memo']}}</textarea>
                </div>
            </div>


            <div class="form-group row">
                <label class="col-md-2">會員狀態：<font class="text-danger p-1"></font></label>
                <div class="col-md-10">

                    {{
    Form::myUIXml([
        'xmldoc'=>$data['xmldoc'],
        'type'=>"radio",
        'node'=>"//參數設定檔/會員狀態/KIND",
        'name'=>"online",
        'value'=>$data['online'],
        'linecount'=>5,
        'required'=>false,
        //'v-model'=>"inputs.online",

    ])
}}
                </div>
            </div>



            <div align="center">
                <button type="submit" class="btn btn-success">確定</button> 
                <button type="reset" class="btn btn-secondary">取消</button> 
                <!--<button type="submit" class="btn btn-primary" onclick="oForm.edit.value='';">複製一筆</button>-->
                <button type="reset" class="btn btn-secondary" onClick="javascript:window.history.go(-1)">回上一頁</button>

            </div>
            @include('admin.layouts.hidden', ['method'=>'EditoForm'])
            {{ Form::hidden("edit",  $data['edit']) }}
            {{ Form::hidden("gobackurl", $data['gobackurl'] ) }}
        </form>
    </div>
</div>




@endsection