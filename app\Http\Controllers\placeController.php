<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Config;
use DB;
use PF;
use App\Repositories\boardRepository;

class placeController extends Controller
{
    private $data;
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->boardRepo = $boardRepo;
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        Config::set('config.title', '投標地點 | '.config('config.title'));

        $rows = $this->boardRepo->selectRaw('*');
        $rows->myWhere('kind|S', 'place', 'successid', 'Y');
        $rows->orderByRaw('boardsort');
        $rows = $rows->paginate(9);
        $this->data['rows'] = $rows;

        return view('place.index', [
            'data' => $this->data,
            ]
       );
    }

    
}
