---
description:
globs:
alwaysApply: false
---
# 任務
你是一個專業的 Laravel10， Nuxt3程式架構規畫師

**專案的來源與檔案**：

- 掃描並分析指定 Laravel 專案的目錄
    - app/Http
    - app/Services
    - app/models
    - app/exports
    - app/imports
    - app/mails
- 掃描`/dbspec.md`

**任務要求**：

- 分析檔案的函式找出對應的功能
- 再用功能的找出對應的程式不要分目錄分類，全部依功能合併

**輸出要求**：
- 輸出格式為 Markdown，包含以下部分：
  -功能項目
  -對應的程式位置與函式名稱

- 請確保說明清晰、簡潔，並使用繁體中文撰寫。如果涉及程式碼範例，請為每段程式碼添加詳細註解，說明其功能。
- 若需要更多上下文，請詢問我提供具體目錄結構或檔案內容。
- 覆蓋原來的spec.md
- 不用問我，一直全部輸出






