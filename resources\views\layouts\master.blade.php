<!doctype html>
<html lang="{{ app()->getLocale() }}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta http-equiv="content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="content-Language" content="zh-tw" />

    <title>@yield('title', PF::getConfig('title'))</title>
    <meta name="keywords" content="@yield('keyword', PF::getConfig('keyword'))" />
    <meta name="description" content="@yield('description', PF::getConfig('description'))" />
    <meta name="copyright" content="{{ PF::getConfig('name') }}" />
    <meta name="distribution" content="Taiwan" />
    <meta name="revisit-after" content="1 days" />
    <meta name="robots" content="index,follow" />
    <!--FaceBook-->
    <meta property="og:title" content="{{ config('config.title') }}" />
    <meta property="og:url" content="{{ Request::url() }}" />
    <meta property="og:site_name" content="{{ PF::getConfig('title') }}" />
    <meta property="og:description" content="{{ PF::getConfig('description') }}" />
    <meta property="og:type" content="website" />
    <meta property="og:image"
        content="{{ $data['ob:image'] != '' ? $data['ob:image'] : url('/') . '/images/fblogo.png' }}" />
    <link rel='index' title=" {{ PF::getConfig('title') }}" href="{{ str_replace('/public', '/', url('/')) }}" />
    <link rel="canonical" href="{{ str_replace('/public/', '/', Request::url()) }}" />

    <meta name="api_token"
        content="{{ Auth::guard('member')->check() ? Auth::guard('member')->user()->api_token : '' }}">

    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="private" />
    <meta http-equiv="Expires" content="0" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta content="no" http-equiv="imagetoolbar" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    {{-- <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" /> --}}
    <meta name="HandheldFriendly" content="True" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Favicons -->
    <link rel="shortcut icon" href="{{ asset('images/favicon.ico') }}" type="image/x-icon">
    <link href="{{ asset('css/bootstrap.min.css') }}?d=1" rel="stylesheet" type="text/css" />

    <link href="{{ asset('css/font-awesome.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ asset('css/css.css') }}?d=7" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="{{ asset('Scripts/jquery.js') }}"></script>
    <script type="text/javascript" src="{{ asset('Scripts/jquery-migrate.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('Scripts/bootstrap.bundle.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('Scripts/sweetalert.min.js') }}"></script>
    <script type="text/javascript"
        src="{{ asset('Scripts/PJSFunc.js') }}?d={{ config('app.env') != 'production' ? date('His') : '3' }}"></script>
    <script type="text/javascript"
        src="{{ asset('Scripts/JSFunc.js') }}?d={{ config('app.env') != 'production' ? date('His') : '3' }}"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: "#dc2626", // red-600
                            foreground: "#ffffff",
                        },
                        secondary: {
                            DEFAULT: "#2563eb", // blue-600
                            foreground: "#ffffff",
                        },
                    }
                }
            }
        }
    </script>

    @include('layouts.header')
    @yield('css')
    @yield('js')
    @include('layouts.masterjs')
    <!-- Global site tag (gtag.js) - Google Analytics -->

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-77T2BBV1M1"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-77T2BBV1M1');
    </script>


</head>

<base target="_self">

<body class="mdc-theme--background">
    <header class="header_wrap">
        <a href="{{ url('/') }}" title="回到首頁">
            <img src="{{ asset('images/brand.png') }}" class="brand" alt="brand" title="brand">
        </a>
        <div class="nav-button">
            <div class="icon-left"></div>
            <div class="icon-right"></div>
        </div>
        <nav class="nav">
            <ul>
                <li><a href="{{ url('/') }}" title="回到首頁">首頁</a></li>
                <li><a href="{{ url('/user') }}" title="了解公司簡介">公司簡介</a></li>
                <li><a href="{{ url('/place') }}" title="查看投標地點">投標地點</a></li>
                <li class="nav-submenu">
                    <a href="#0" title="買賣流程相關資訊">買賣流程</a>
                    <ul>
                        <li><a href="{{ url('/about/buy') }}" title="查看流程說明">流程說明</a></li>
                        <li><a href="{{ url('/faq') }}" title="查看常見問題">常見問題</a></li>
                    </ul>
                </li>
                <li><a href="{{ url('/success') }}" title="查看成交案例">成交案例</a></li>
                <li><a href="{{ url('/awards') }}" title="查看得獎最多案例">得獎最多</a></li>
                <li><a href="{{ url('/media') }}" title="查看媒體專訪">媒體專訪</a></li>
                <li><a href="{{ url('/contact') }}" title="聯絡我們">聯絡我們</a></li>
                @auth('member')
                    <li class="nav-submenu">
                        <a href="#0" title="會員專區">會員專區</a>
                        <ul>
                            <li><a href="{{ url('/') }}/membercenter/edit">

                                    資料編輯
                                </a>
                            <li> <a href="{{ url('/') }}/membercenter/myproduct" class="mdc-button"
                                    title="收藏清單">收藏清單</a></li>
                        </ul>
                    </li>
                @endauth
            </ul>
        </nav>
        @auth('member')
            <a href="{{ url('/membercenter/logout') }}" class="log" title="會員登出">
                <img src="{{ asset('images/logOut_mo.svg') }}" alt="登出" title="登出">
                <span class="logout">登出</span>
            </a>
        @else
            <a href="{{ url('/member/login') }}" class="log" title="會員登入/註冊">
                <img src="{{ asset('images/logIn.svg') }}" alt="登入/註冊" title="登入/註冊">
                <span class="login">登入</span>
            </a>
        @endauth

    </header>

    <!-- 手機版導航選單遮罩 -->
    <div id="overlay"></div>

    <!--桌機選單 end-->
    <main class="page__wrap" id="app">

        @yield('content')
    </main>
    <footer class="footer_wrap">
        <div class="footer-content">
            <div class="footer-logo">

                <img src="{{ url('/') }}/images/brand_footer.webp">
            </div>
            <div class="footer-info">
                <p>台北：02-2577-3333</p>
                <p>桃園：03-375-3545</p>
                <p>基隆：02-2462-3744</p>
                <p>高雄：07-550-8150</p>
                <p>台北市信義路二段213號11樓</p>
                <p>服務時間：(一)～(六) 09:30 - 21:00</p>
                <p>Email：<a href="mailto:<EMAIL>" title="發送郵件給我們"><EMAIL></a></p>
            </div>
        </div>

        <div class="footer-note">
            <div>
                <p>法拍屋資訊記載未詳盡者，以法院公告為準，產權登記以登記謄本為主。</p>
                <a href="{{ url('/about/disclaimer') }}" title="查看免責聲明">免責聲明</a>
            </div>
            <h6 class="copyright">Copyright ©<span class="getyear"></span> All Rights Reserved</h6>
        </div>
    </footer>

    <script src="{{ url('/') }}/Scripts/main.js"></script>


</body>

</html>
