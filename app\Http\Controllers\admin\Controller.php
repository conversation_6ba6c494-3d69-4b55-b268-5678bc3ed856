<?php

namespace App\Http\Controllers\admin;

use Illuminate\Routing\Controller as BaseController;
use PF;

class Controller extends BaseController
{
    

    public function __construct()
    {
        
        // if (substr_count(\Request::url(), '/api') > 0) {
        //     header('Access-Control-Allow-Origin: *');
        //     header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        //     header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Accept');
        // }
        //PF::printr(4);
        //PF::printr($this->limit);
    }
}
