<?php

namespace App\Http\Controllers\layouts;
use App\Http\Controllers\Controller;
use PF;
use DB;
use Session;

class topController extends Controller
{

    public $data;

    public function index()
    {
        
        $rows = DB::table('kind')->selectRaw('*');
        $rows->myWhere('kind|S', 'newskind', 'kind', 'Y');        
        $rows->orderByRaw('kindsortnum');
        //PF::dbSqlPrint($rows);
        $rows = $rows->get();
        

        $this->data['rows'] = $rows;

        return view('layouts.top', [
            'data' => $this->data,
        ]);
    }
}
