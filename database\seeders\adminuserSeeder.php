<?php
namespace Database\Seeders;
use Illuminate\Database\Seeder;
use DB;
class adminuserSeeder extends Seeder
{
    public $tablename = 'adminuser';
    /**
     * Run the database seeds.
     */
    public function run()
    {
        

        DB::table('adminuser')->truncate();

        $faker = \Faker\Factory::create('zh_TW');

        $data[] = [
                'account' => 'admin',
                'name'=>'管理者',
                'email'=>'<EMAIL>',
                'password' =>  \Hash::make('admin'),
                'api_token'=>'admin',
                'status' => '999',
                'online' => 1,
            ];
        $data[] = [
                'account' => 'allen',
                'name'=>'工程師',
                'email'=>'<EMAIL>',
                'password' => \Hash::make('aaa1234'),
                'api_token'=>'allen',
                'status' => '999',
                'online' => 1,
            ];

        DB::table($this->tablename)->insert($data);

        // $this->call(UsersTableSeeder::class);
    }
   
}
