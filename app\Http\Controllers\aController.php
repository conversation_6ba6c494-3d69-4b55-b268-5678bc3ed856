<?php

namespace App\Http\Controllers;

use DB;
use PF;
use Illuminate\Http\Request;

//use Illuminate\Support\Facades\DB;

class aController extends Controller {
    private $data;

    /**
     *建構子.
     */
    public function __construct() {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $host = request()->getHost();
        if ('allennb.com.tw' != request()->getHost()) {
            throw new \CustomException('no limit');
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {

        \Config::set('config.title', '控制台');

        $this->data['database'] = \config('database.connections.mysql.database');


        $this->data['tables'] = \DB::getSchemaBuilder()->getAllTables();

        $this->data['tabletitles'] = [];

        foreach ($this->data['tables'] as $k => $v) {
            $name = head($v);
            $comments = DB::select("SHOW TABLE STATUS LIKE '" . $name . "'");

            $tableComment = "";
            if (isset($comments[0]->Comment)) {
                $tableComment = $comments[0]->Comment;
            }


            $this->data['tabletitles'][$name] = $tableComment;
        }


        $sourceDir = base_path('database\seeders');

        $mydir = dir($sourceDir);
        while ($file = $mydir->read()) {
            if (('.' != $file) and ('..' != $file)) {
                if (mb_substr_count($file, '.php') > 0) {
                    $file = str_replace('Seeder.php', '', $file);
                    $files[] = $file;
                }
            }
        }
        $this->data['seeders'] = $files;


        // $tableInfos = [];
        // $tables = DB::connection()->getDoctrineSchemaManager()->listTableNames();

        // foreach ($tables as $k => $v) {
        //     if ((in_array($v, ['adminuser', 'adminuserloginlog', 'board', 'kind', 'city1', 'city2', 'kind', 'failed_jobs', 'epost', 'formquerylog', 'jobs', 'migrations', 'viewcount'])) == false) {


        //         $item = [];
        //         $item['name'] = $v;
        //         $comments = \Schema::getConnection()
        //             ->getDoctrineSchemaManager()
        //             ->listTableDetails($v)
        //             ->getOptions()['comment'];

        //         $item['comments'] = $comments;
        //         $fields = DB::select('SHOW FULL COLUMNS FROM ' . $v);
        //         $item['fields'] = $fields;
        //         $tableInfos[] = $item;
        //     }
        // }

        // $this->data['tableInfos'] = $tableInfos;
        //\PF::printr(["this->data[tableInfos]", $this->data['tableInfos']]);

        return view('a.index', [
            'data' => $this->data,
        ]);
    }

    public function clear(Request $request) {
        $artisanService = new \App\Services\artisanService();

        return $artisanService->clear();
    }

    public function migrate(Request $request) {
        echo '<pre>';
        echo date('Y-m-d H:i:s') . PHP_EOL;

        $artisanService = new \App\Services\artisanService();
        $artisanService->migrate();
        echo '</pre>';

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    public function migrate_refresh_excute(Request $request) {
        echo '<pre>';
        echo date('Y-m-d H:i:s') . PHP_EOL;
        $artisanService = new \App\Services\artisanService();
        $artisanService->migraterefresh($request->input('table_name'));
        echo '</pre>';

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    public function seeder(Request $request) {
        $artisanService = new \App\Services\artisanService();

        $artisanService->seed($request->input('seeder'));

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    public function createfile(Request $request) {
        //$isadmin = $this->ask('is admin(Y,N):');

        //$ismodel = $this->ask('is create model(Y,N):');
        $folder = 'C:/AppServ/laravel/1/sample/controller/';
        $tofolder = base_path('app/Http/Controllers/');

        $filename = $this->data['filename'];
        $table_name = $this->data['table_name'];
        $table_title = "";
        if ($table_name != "") {
            $comments = DB::select("SHOW TABLE STATUS LIKE '" . $table_name . "'");

            if (isset($comments[0]->Comment)) {
                $table_title = $comments[0]->Comment;
            }
        }

        $foldername = $this->data['foldername'];
        if (\Str::contains($foldername, ['admin'])) {
            $tofolder = $tofolder . 'admin/';
        } else {
            $tofolder .= $foldername;
        }

        $foldernamecontroller = '';
        if ('' != $foldername) {
            $foldernamecontroller = '\\' . $foldername;
            $foldernameview = $foldername . '.';
        } else {
            $foldernameview = $foldername;
        }


        if ('' == $filename) {
            throw new \Exception('name is null');
        }

        // PF::printr($tablename);
        // PF::printr("filename:".$filename);
        // PF::printr($foldername);
        // PF::printr($folder);
        // PF::printr("foldernameview:".$foldernamecontroller);
        // echo  'tofolder:'.$foldername;
        // echo  "<br>";

        $tofile = $tofolder . '/' . $filename . 'Controller.php';

        if (false == \File::exists($tofile)) {
            echo  'create controller<br>';
            if (\Str::contains($foldername, ['admin'])) {
                if ('' != $table_name) {
                    $ebody = \File::get($folder . '/admin/Controller.php');
                } else {
                    $ebody = \File::get($folder . '/admin/norepoController.php');
                }
            } else {
                if ('' != $table_name) {
                    $ebody = \File::get($folder . '/Controller.php');
                } else {
                    $ebody = \File::get($folder . '/norepoController.php');
                }
            }

            $ebody = str_replace('[+name+]', $filename, $ebody);
            $ebody = str_replace('[+table_title+]', $table_title, $ebody);

            $ebody = str_replace('[+repositoryname+]', $table_name, $ebody);
            $ebody = str_replace('[+foldername+]', $foldername, $ebody);
            $ebody = str_replace('[+foldernameview+]', $foldernameview, $ebody);
            $ebody = str_replace('[+foldernamecontroller+]', $foldernamecontroller, $ebody);
            // if ('Y' == $isadmin) {
            //     $ebody = str_replace('[+admin+]', 'admin', $ebody);
            // } else {
            //     $ebody = str_replace('[+admin+]', '', $ebody);
            // }

            \File::put($tofile, $ebody);
        }

        echo  'create index edit view<br>';
        $array = explode(',', 'index');
        $folder = resource_path('views/' . $foldername . '/' . $filename);
        if (false == \File::isDirectory($folder)) {
            \File::makeDirectory($folder, 0777, true, true);
        } else {
            echo $folder . ' foler exist<br>';
        }
        for ($i = 0; $i < count($array); ++$i) {
            $folder = 'C:/AppServ/laravel/1/sample/views/' . $array[$i];
            if ('Y' == $isadmin) {
                $folder = 'C:/AppServ/laravel/1/sample/views/admin/' . $array[$i];
            }
            $tofile = resource_path('views/' . $foldername . '/' . $filename . '/' . $array[$i] . '.blade.php');
            echo $tofile . '<br>';
            if (false == \File::exists($tofile)) {
                $success = \File::copy($folder . '.blade.php', $tofile);
                echo $array[$i] . ('1' == $success ? 'OK' : $success);
            } else {
                echo $tofile . ' > ' . $array[$i] . ' exist ';
            }
            echo  '<br>';
        }

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    public function createtablename(Request $request) {
        $name = $request->input('newtablename');
        if (PF::isEmpty($name)) {
            throw new \CustomException('no table name');
        }
        try {
            \Artisan::call('make:migration', [
                'name' => 'create_' . $name . '_table ',
            ]);
            echo 'done make:migration' . PHP_EOL;
        } catch (\Exception $e) {
            return response($e->getMessage());
        }

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    public function test(Request $request) {
        \PF::setConfig('title', "測試框架");
        $this->data['rows'] = [];
        $SCRIPT_FILENAME = $_SERVER['SCRIPT_FILENAME'];
        $folder = str_replace('public/index.php', '', $SCRIPT_FILENAME);

        //throw new \Exception('no data');

        $directory = base_path('tests');
        $files = \File::allFiles($directory, $hidden = false);
        $rs = [];
        foreach ($files as $k => $v) {
            try {
                if (false == \Str::contains($v->getRealpath(), ['baseTest.php']) && \Str::contains($v->getRealpath(), ['.php'])) {
                    //if (\Str::contains($v->getRealpath(), ['Feature\orderTest'])) {
                    //PF::printr($v->getRealpath());

                    $type = $v->getRealpath();
                    $types = explode('tests\\', $type);
                    $name = str_replace('.php', '', $types[1]);
                    $rs['name'] = $name;
                    $name = '\\Tests\\' . $name;
                    //PF::printr($class_name);
                    //$model=get_class($class_name); // Output: Checkbox
                    $reflector = new \ReflectionClass($name);
                    //$model = $reflector->newInstance();
                    //$methods = array();
                    $rs1 = [];
                    foreach ($reflector->getMethods() as $m) {
                        //PF::printr($m->class);
                        if (\Str::contains($m->name, ['test_'])) {
                            $method = strval($m->name);
                            $rs2 = [];
                            $rs2['method'] = $method;

                            //echo $SCRIPT_FILENAME;

                            //echo $folder;
                            $action = "vendor\bin\phpunit --filter '" . $method . "' " . $folder . $name . '.php';

                            //C:/AppServ/laravel/pay-classtwdash/vendor\bin\phpunit C:/AppServ/laravel/pay-classtwdash/tests\Feature\orderTest.php --filter test_訂單加名片次數

                            $rs2['file'] = $action;
                            $rs1[] = $rs2;
                        }
                    }
                    $rs['rows'] = $rs1;

                    $this->data['rows'][] = $rs;
                }
            } catch (\Exception $e) {
                echo $e->getMessage(); //throw e;
            } finally {
            }
        }

        return view(
            'a.test',
            [
                'data' => $this->data,
            ]
        );
    }

    public function sqlfile(Request $request) {
        $database = \config('database.connections.mysql.database');

        $directory = "C:\Users\<USER>\Downloads";
        $files = \File::files($directory, $hidden = false);

        $filename = '';
        $lasttime = '';
        foreach ($files as $k => $v) {
            if (\Str::contains($v->getRealpath(), ['.sql'])) {
                if ('' == $lasttime || \File::lastModified($v->getRealpath()) > $lasttime) {
                    $filename = $v->getRealpath();
                    $lasttime = \File::lastModified($v->getRealpath());
                }
            }

            //date('Y-m-d H:i:s', \File::lastmodified(item));//時間
            //PF::formatNumber(v->getSize()/1024/1024,2).M;//大小
        }
        echo '<pre>';
        echo date('Y-m-d H:i:s') . PHP_EOL;
        try {

            //\PF::printr(['v->getRealpath()', $v->getRealpath(), \File::lastModified($v->getRealpath())]);
            if ($filename) {
                echo "C:\\xampp\\mysql\\bin\\mysql $database -u root -p123456  < $filename --default-character-set=utf8mb4<BR>";
                echo $filename . ' ' . date('Y-m-d H:i:s', \File::lastModified($filename));
                $sqlcommand = file_get_contents($filename);
                echo PHP_EOL;
                preg_match_all('/CREATE TABLE `(.*?)` \(/i', $sqlcommand, $arr);

                for ($i = 0; $i < count($arr[1]); ++$i) {
                    $name = $arr[1][$i];
                    if (\Schema::hasTable($name)) {
                        echo  '刪除資料表 : ' . $name . ' ';
                        \Schema::dropIfExists($name);
                        //echo \DB::table($name)->truncate();
                        echo  PHP_EOL;
                    }
                }

                echo  '執行結果 : ' . PHP_EOL;
                //echo $sqlcommand;
                DB::unprepared($sqlcommand);
            }
            //throw new \Exception('no data');
        } catch (\CustomException $e) {
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
            //throw $e;
        } finally {
        }

        echo '</pre>';

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    public function mysqlfile(Request $request) {
        $database = \config('database.connections.mysql.database');
        $directory = "C:\Users\<USER>\Downloads";
        if ($request->input('directory') != "") {

            $directory = base_path($request->input('directory'));
        }

        $files = \File::files($directory, $hidden = false);

        $filename = '';
        $lasttime = '';
        foreach ($files as $k => $v) {
            if (\Str::contains($v->getRealpath(), ['.sql'])) {
                if ('' == $lasttime || \File::lastModified($v->getRealpath()) > $lasttime) {
                    $filename = $v->getRealpath();
                    $lasttime = \File::lastModified($v->getRealpath());
                }
            }

            //date('Y-m-d H:i:s', \File::lastmodified(item));//時間
            //PF::formatNumber(v->getSize()/1024/1024,2).M;//大小
        }

        if ($filename) {
            \DB::statement('DROP DATABASE IF EXISTS `' . $database . '`');
            \DB::statement('CREATE DATABASE `' . $database . '` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');

            $cmd = 'C:\xampp\mysql\bin\mysql ' . $database . ' -u root -p123456 < ' . $filename . ' --default-character-set=utf8mb4';
            echo $cmd;
            // ob_implicit_flush(true);
            // ob_flush();
            echo exec($cmd);
        }

        // $artisanService = new \App\Services\artisanService();

        // $artisanService->seed("devinit");

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    public function createdata(Request $request) {
        echo '<pre>';
        echo date('Y-m-d H:i:s') . PHP_EOL;

        $validators = null;
        $validators['table_name'] = 'required';
        $validators['id'] = 'required';
        $validators['count'] = 'required';
        $this->data['displaynames']['id'] = 'id';
        $validator = \Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $tableName = $request->input('table_name');
        $count = $request->input('count');
        $id = $request->input('id');
        $fields = DB::select('SHOW FULL COLUMNS FROM ' . $tableName);
        //\PF::printr(["fields ", $fields]);
        // $fields = \Schema::getColumnListing($tableName);
        // \PF::printr(["fields", $fields]);
        $insertField1s = '';
        $keyField = "id";
        foreach ($fields  as $key => $rs) {
            if ('PRI' == $rs->Key) {
                $keyField = $rs->Field;
            } else {
                $insertField1s .= $rs->Field . ',';
            }
        }

        $insertField1s = ltrim(rtrim($insertField1s, ','), ',');

        for ($i = 0; $i < $count; ++$i) {
            $insertField2s = '';
            foreach ($fields  as $key => $rs) {
                if ($rs->Key == "UNI") {
                    $insertField2s .= "concat(" . $rs->Field . ",'-" . rand(1, 100000) . "'),";
                } else {
                    switch ($rs->Field) {
                        case 'title':

                            $insertField2s .= "concat(" . $rs->Field . ",'-" . rand(1, 100000) . "'),";
                            break;
                        case $keyField:

                            break;
                        default:
                            $insertField2s .= $rs->Field . ',';
                            break;
                    }
                }
            }
            $insertField2s = ltrim(rtrim($insertField2s, ','), ',');

            \DB::statement('insert into ' . $tableName . ' (' . $insertField1s . ') select ' . $insertField2s . ' from ' . $tableName . ' where ' . $keyField . '=?', [$id]);
            //echo ($i + 1) . " ok";
        }
        echo '</pre>';

        return response('ok')->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
    }

    //     public function sql(Request $request)
    //     {
    //         $log = \File::get(storage_path('logs/'.'laravel-'.date('Y-m-d').'.log'));
    //         //\PF::printr(['log', $log]);

    //         //$items=explode("chr(13).chr(10)",$s);

    // //        $pattern = '/select\s+(.*?)\s+\[time:(\d+\.\d+)\]/';

    //         //$pattern = '/(select|insert|update|delete)\s+.*?(?=\s+\[time:)/is';
    //         //$pattern = '/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]\s+([\s\S]*?(?:select|insert|update|delete)\s+[\s\S]+?)\n.*?\[time:([\d.]+)\]/';
    //         $pattern = '/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]\s+[^:]+:\s+([\s\S]*?(select|insert|update|delete)\s+[\s\S]+?)\n.*?\[time:([\d.]+)\]/';

    //         preg_match_all($pattern, $log, $matches, PREG_SET_ORDER);

    //         $matches = array_reverse($matches);
    //         //\PF::printr(['matches ', $matches]);

    //         //$lastTenQueries = array_slice($matches, 50);
    //         //\PF::printr(['matches', $lastTenQueries]);

    //         $sqls = [];
    //         $i = 0;
    //         foreach ($matches as $match) {
    //             $query = $match[2];
    //             if (false == \Str::contains($query, ['update `adminuserloginlog`', 'select * from `adminuser` ', 'select * from `adminuserloginlog` ', 'CONTENT_TYPE'])) {
    //                 ++$i;
    //                 if ($i >= 50) {
    //                     break;
    //                 }
    //                 $item = [];
    //                 $item['sql'] = $query;
    //                 $item['time'] = $match[1];
    //                 $sqls[] = $item;
    //             }
    //         }
    //         $this->data['sqls'] = $sqls;

    //         //return response('OK');
    //         return view('a.sql', [
    //             'data' => $this->data,
    //             ]);
    //     }


}
