{
    // ========== 布局类 ==========
    "Flex Container": {
        "prefix": "tw flex container flex容器",
        "scope": "html,blade,vue-html",
        "body": [
            "flex"
        ]
    },
    "Flex Row Reverse": {
        "prefix": "tw flex row-reverse 反向水平",
        "scope": "html,blade,vue-html",
        "body": [
            "flex-row-reverse"
        ]
    },
    "Justify Center": {
        "prefix": "tw flex justify-center 水平居中",
        "scope": "html,blade,vue-html",
        "body": [
            "justify-center"
        ]
    },
    "Items Center": {
        "prefix": "tw flex items-center 垂直居中",
        "scope": "html,blade,vue-html",
        "body": [
            "items-center"
        ]
    },
    "Grid 2 Columns": {
        "prefix": "tw grid cols-2 網格兩欄",
        "scope": "html,blade,vue-html",
        "body": [
            "grid-cols-2"
        ]
    },
    // ========== 間距類 ==========
    "Padding All 4": {
        "prefix": "tw padding p4 全邊距4",
        "scope": "html,blade,vue-html",
        "body": [
            "p-4"
        ]
    },
    "Padding X 4": {
        "prefix": "tw padding px4 水平邊距4",
        "scope": "html,blade,vue-html",
        "body": [
            "px-4"
        ]
    },
    "Margin Top 4": {
        "prefix": "tw margin mt4 上外邊距4",
        "scope": "html,blade,vue-html",
        "body": [
            "mt-4"
        ]
    },
    "Space X 4": {
        "prefix": "tw space x4 子元素間距",
        "scope": "html,blade,vue-html",
        "body": [
            "space-x-4"
        ]
    },
    // ========== 文字類 ==========
    "Text Size XL": {
        "prefix": "tw text xl 大文字",
        "scope": "html,blade,vue-html",
        "body": [
            "text-xl"
        ]
    },
    "Font Bold": {
        "prefix": "tw text bold 粗體",
        "scope": "html,blade,vue-html",
        "body": [
            "font-bold"
        ]
    },
    "Text Center": {
        "prefix": "tw text center 文字居中",
        "scope": "html,blade,vue-html",
        "body": [
            "text-center"
        ]
    },
    "Text Truncate": {
        "prefix": "tw text truncate 省略號",
        "scope": "html,blade,vue-html",
        "body": [
            "truncate"
        ]
    },
    // ========== 顏色類 ==========
    "Text Blue 500": {
        "prefix": "tw text blue500 藍字",
        "scope": "html,blade,vue-html",
        "body": [
            "text-blue-500"
        ]
    },
    "BG Red 100": {
        "prefix": "tw bg red100 淺紅背景",
        "scope": "html,blade,vue-html",
        "body": [
            "bg-red-100"
        ]
    },
    "Border Gray 300": {
        "prefix": "tw border gray300 灰邊框",
        "scope": "html,blade,vue-html",
        "body": [
            "border border-gray-300"
        ]
    },
    // ========== 效果類 ==========
    "Shadow MD": {
        "prefix": "tw shadow md 中型陰影",
        "scope": "html,blade,vue-html",
        "body": [
            "shadow-md"
        ]
    },
    "Rounded LG": {
        "prefix": "tw rounded lg 大圓角",
        "scope": "html,blade,vue-html",
        "body": [
            "rounded-lg"
        ]
    },
    "Opacity 50": {
        "prefix": "tw opacity 50 半透明",
        "scope": "html,blade,vue-html",
        "body": [
            "opacity-50"
        ]
    },
    // ========== 交互類 ==========
    "Hover Scale": {
        "prefix": "tw hover scale 懸浮放大",
        "scope": "html,blade,vue-html",
        "body": [
            "hover:scale-105 transition-transform"
        ]
    },
    "Focus Ring": {
        "prefix": "tw focus ring 聚焦環",
        "scope": "html,blade,vue-html",
        "body": [
            "focus:ring-2 focus:ring-blue-500"
        ]
    },
    "Active Opacity": {
        "prefix": "tw active opacity 點擊效果",
        "scope": "html,blade,vue-html",
        "body": [
            "active:opacity-75"
        ]
    },
    // ========== 常用組合 ==========
    "Card Style": {
        "prefix": "tw card style 卡片樣式",
        "scope": "html,blade,vue-html",
        "body": [
            "p-6 bg-white rounded-lg shadow-md"
        ]
    },
    "Input Style": {
        "prefix": "tw input style 輸入框",
        "scope": "html,blade,vue-html",
        "body": [
            "w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500"
        ]
    },
    "Nav Link": {
        "prefix": "tw nav link 導航連結",
        "scope": "html,blade,vue-html",
        "body": [
            "text-gray-600 hover:text-blue-500 transition-colors"
        ]
    },
    // ========== 響應式設計 ==========
    "MD Breakpoint": {
        "prefix": "tw md 中螢幕斷點",
        "scope": "html,blade,vue-html",
        "body": [
            "md:$1"
        ]
    },
    "LG Hidden": {
        "prefix": "tw lg hidden 大屏隱藏",
        "scope": "html,blade,vue-html",
        "body": [
            "lg:hidden"
        ]
    },
    // ========== 動畫類 ==========
    "Animate Spin": {
        "prefix": "tw animate spin 旋轉動畫",
        "scope": "html,blade,vue-html",
        "body": [
            "animate-spin"
        ]
    },
    "Transition All": {
        "prefix": "tw transition all 過渡效果",
        "scope": "html,blade,vue-html",
        "body": [
            "transition-all duration-300"
        ]
    },
    {
        // ========== 按鈕樣式 ==========
        "Button Primary": {
            "prefix": "tw button primary 主按鈕",
            "scope": "html,blade,vue-html",
            "body": [
                "bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            ]
        },
        "Button Secondary": {
            "prefix": "tw button secondary 次按鈕",
            "scope": "html,blade,vue-html",
            "body": [
                "bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
            ]
        },
        "Button Outline": {
            "prefix": "tw button outline 線框按鈕",
            "scope": "html,blade,vue-html",
            "body": [
                "border-2 border-blue-500 text-blue-500 px-4 py-2 rounded-md hover:bg-blue-50"
            ]
        },
        "Button Disabled": {
            "prefix": "tw button disabled 禁用按鈕",
            "scope": "html,blade,vue-html",
            "body": [
                "bg-gray-300 text-gray-500 cursor-not-allowed px-4 py-2 rounded-md"
            ]
        },
        "Button Small": {
            "prefix": "tw button small 小按鈕",
            "scope": "html,blade,vue-html",
            "body": [
                "text-sm px-3 py-1.5 rounded"
            ]
        },
        "Button Loading": {
            "prefix": "tw button loading 讀取狀態",
            "scope": "html,blade,vue-html",
            "body": [
                "opacity-75 cursor-wait"
            ]
        },
        // ========== 表格樣式 ==========
        "Table Basic": {
            "prefix": "tw table basic 基本表格",
            "scope": "html,blade,vue-html",
            "body": [
                "min-w-full divide-y divide-gray-200"
            ]
        },
        "Table Bordered": {
            "prefix": "tw table bordered 帶邊框表格",
            "scope": "html,blade,vue-html",
            "body": [
                "border-collapse border border-gray-200"
            ]
        },
        "Table Header": {
            "prefix": "tw table header 表頭樣式",
            "scope": "html,blade,vue-html",
            "body": [
                "bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            ]
        },
        "Table Cell": {
            "prefix": "tw table cell 表格單元格",
            "scope": "html,blade,vue-html",
            "body": [
                "px-6 py-4 whitespace-nowrap text-sm text-gray-900"
            ]
        },
        "Table Striped": {
            "prefix": "tw table striped 斑馬紋表格",
            "scope": "html,blade,vue-html",
            "body": [
                "even:bg-gray-50 odd:bg-white"
            ]
        },
        "Table Hover": {
            "prefix": "tw table hover 懸浮效果",
            "scope": "html,blade,vue-html",
            "body": [
                "hover:bg-gray-100 transition-colors"
            ]
        },
        "Table Responsive": {
            "prefix": "tw table responsive 響應式表格",
            "scope": "html,blade,vue-html",
            "body": [
                "overflow-x-auto shadow rounded-lg"
            ]
        },
        // ========== 進階按鈕組合 ==========
        "Button Group": {
            "prefix": "tw button group 按鈕組",
            "scope": "html,blade,vue-html",
            "body": [
                "inline-flex rounded-md shadow-sm [&>*]:rounded-none [&>*:first-child]:rounded-l-md [&>*:last-child]:rounded-r-md"
            ]
        },
        "Icon Button": {
            "prefix": "tw button icon 圖標按鈕",
            "scope": "html,blade,vue-html",
            "body": [
                "p-2 rounded-full hover:bg-gray-100 transition-colors"
            ]
        }
    }
}