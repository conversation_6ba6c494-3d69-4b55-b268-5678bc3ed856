<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

//command php artisan features:create
class seedersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seeders:run';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'COPY PROGRAM';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        //try {
        $sourceDir = base_path('database\seeders');
        // $files = \File::allFiles( $sourceDir );
        // // $replaceDocPath = str_replace( public_path(),'',$sourceDir );
        // foreach( $files as $file ) {

        //     $files[] = $file->getRelativePathname();

        // }
        $mydir = dir($sourceDir);
        while ($file = $mydir->read()) {
            if (('.' != $file) and ('..' != $file)) {
                if (mb_substr_count($file, '.php') > 0) {
                    $file = str_replace('.php', '', $file);
                    $files[] = $file;
                }
            }
        }

        $file = $this->choice('選擇您要的功能?', $files);
        //$kind = $this->ask($body);
        sapi_windows_cp_set(65001);
        $msg .= \Artisan::output();
        
        echo "php artisan db:seed --class=".$file .PHP_EOL;
        $this->call('db:seed', [
             '--class' => $file,
        ]);

        echo $msg;
    }
}
