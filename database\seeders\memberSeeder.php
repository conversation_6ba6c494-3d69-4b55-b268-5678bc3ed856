<?php

namespace Database\Seeders;

use App\Repositories\memberRepository;
use Illuminate\Database\Seeder;

class memberSeeder extends Seeder
{
    private $kindRepo;

    public function __construct(memberRepository $memberRepo)
    {
        $this->memberRepo = $memberRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->memberRepo->delete();

        $this->faker = \Faker\Factory::create('zh_TW');
        for ($i = 0; $i < 5; ++$i) {
            try {
                $data = [
                'lineid' => $this->faker->randomDigit.$i, //生成0-9之間的隨機數
        'name' => $this->faker->firstNameMale.$this->faker->lastName,   //姓名 ,
        'company' => $this->faker->company, //公司名稱
        'tel' => $this->faker->phoneNumber,   //電話
        'email' => $this->faker->freeEmail, //返回一個隨機郵箱
        'tag_id' => 10004,
        'role_id' => $this->faker->randomElement([1, 2, 3]), //隨機返回數組中的一個元素 ,
        'online' => 1,
        'group_id' => 10010,
    ];
                //print_r($data);
                $this->memberRepo->create($data);
            } catch (\Exception $e) {
                echo $e->getMessage();
            }
        }

        // $this->call(UsersTableSeeder::class);
    }
}
