<?php

namespace App\Http\Controllers\api\membercenter;

use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController {

    public $jsondata;
    public function __construct() {
        $this->jsondata['resultcode'] = 0;
        $this->jsondata['resultmessage'] = '';
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Accept');
    }
}
