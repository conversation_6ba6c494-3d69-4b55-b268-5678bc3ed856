@extends('admin.layouts.master')


@section('css')
@endsection

@section('js')
@endsection
@section('nav')
    {!! $data['nav'] !!}
@endsection


@section('content')
    {!! Session::get('msg') !!}


    <div class="container-fluid p-1">
        <form name="SearchoForm" id="SearchoForm" class="form-inline" method="post" language="javascript"
            action="{{ request()->getRequestUri() }}" onsubmit="return SearchoForm_onsubmit(this);">
            @include('admin.layouts.search', [])
            <div class="form-inline">

                <div class="input-group">


                    {{ Form::myUISelectMulti([
                        [
                            'formname' => 'SearchoForm',
                            'title' => '縣市',
                            'sql' => 'select city1title, city1title from city1 order by sortnum desc',
                            'name' => 'acity1title',
                            'value' => $data['acity1title'],
                            'required' => false,
                        ],
                        [
                            'formname' => 'SearchoForm',
                            'title' => '鄉鎮',
                            'url' => url('/api/dependentdropdown/city2'),
                            'name' => 'acity2title',
                            'value' => $data['acity2title'],
                            'required' => false,
                        ],
                    ]) }}


                    &nbsp;

                    <input type="text" class="form-control" name="aaddress" value="{{ $data['aaddress'] }}"
                        title="路名" requiredclass="required[0,TEXT]" placeholder="路名" />
                    &nbsp;
                    {{ Form::myUIXml([
                        'xmldoc' => $data['xmldoc'],
                        'type' => 'select',
                        'title' => '房屋類別',
                        'node' => '//參數設定檔/房屋類別/KIND',
                        'name' => 'apattern',
                        'value' => $data['apattern'],
                        'linecount' => 4,
                        'firsttxt' => '房屋類別',
                        'requiredclass' => 'required[0,TEXT]',
                    ]) }}
                    &nbsp;
                    {{ Form::myUIXml([
                        'xmldoc' => $data['xmldoc'],
                        'type' => 'select',
                        'title' => '坪數',
                        'node' => '//參數設定檔/坪數/KIND',
                        'name' => 'apingtotalnumberof',
                        'value' => $data['apingtotalnumberof'],
                        'linecount' => 4,
                        'firsttxt' => '坪數',
                        'requiredclass' => 'required[0,TEXT]',
                    ]) }}
                    &nbsp;
                    {{ Form::myUIXml([
                        'xmldoc' => $data['xmldoc'],
                        'type' => 'select',
                        'title' => '購屋預算',
                        'node' => '//參數設定檔/購屋預算/KIND',
                        'name' => 'atotalupset',
                        'value' => $data['atotalupset'],
                        'linecount' => 4,
                        'firsttxt' => '購屋預算',
                        'requiredclass' => 'required[0,TEXT]',
                    ]) }}&nbsp;
                    {{ Form::myUIXml([
                        'xmldoc' => $data['xmldoc'],
                        'type' => 'select',
                        'title' => '屋齡',
                        'firsttxt' => '屋齡',
                        'node' => '//參數設定檔/屋齡/KIND',
                        'name' => 'ahouseping',
                        'value' => $data['ahouseping'],
                        'linecount' => 4,
                        'requiredclass' => 'required[0,TEXT]',
                    ]) }}

                </div>

        </form>
    </div>
    @if (Gate::check('isAdminRole', ['999']))
        <div class="float-left noprint">
            @include('admin.layouts.delall')
        </div>
    @endif
    <div align="right">
        <form name="AddoForm" method="post" language="javascript" action="{{ url('admin/product') }}/create">
            <button type="submit" class="btn btn-info">新增</button>
            @include('admin.layouts.hidden', ['method' => 'AddoForm', 'data' => $data])
        </form>
    </div>

    <!--排序的參數-->
    <form name="SortoForm" method="post">
        @include('admin.layouts.hidden', ['method' => 'SortoForm', 'data' => $data])
    </form>


    <!--傳給下一頁的參數-->
    <form method="post" language="javascript" name="oForm" action="{{ url('admin/product') }}">

        @include('admin.layouts.hidden', ['method' => 'oForm', 'data' => $data])
        <div class="table-responsive">
            <table class="table table-striped table-hover  table-bordered table-fixed">

                <thead>
                    <tr valign="top" align="left">

                        <th align="center" width="120">
                            <div class="form-inline row">
                                功能
                            </div>
                        </th>
                        <th width="" id="number">案號</th>

                        <th width="" id="court">法院</th>
                        <th width="" id="city1title">建物/土地/座落大樓名稱/地目</th>
                        <th width="" id="tenderdate">投標日</th>

                        <th width="" id="totalupset">總底價<BR>保證金</th>
                        <th width="" id="pingtotalnumberof">總坪數<BR>持分地坪</th>
                        <th width="" id="floorprice">坪單價<BR>現況</th>
                        <th width="" id="beattime">拍次<BR>點交</th>
                        <th width="" id="online">上下架</th>


                        <th width="" id="location">放置位置</th>


                        <th width="60px" id="hits">點率閱</th>
                        <th width="60px" id="created_at">建立日期
                            <BR>
                            更新日期
                        </th>
                    </tr>
                </thead>


                <tbody>
                    @foreach ($data['rows'] as $rs)
                        <tr>

                            <td valign="top" align="center" width="120">
                                <div class="form-inline row">

                                    <button type="submit" class="btn btn-info"
                                        onclick="javascript:form.action='{{ url('admin/product') }}/edit?edit={{ $rs->productid }}';">
                                        編輯
                                    </button>

                                    <div class="col-md-5">

                                        @if (Auth::guard('admin')->user()->role == '999')
                                            <input type="checkbox" name="del[]" class="all"
                                                value="{{ $rs->productid }}">
                                        @endif

                                    </div>

                                </div>

                            </td>


                            <td>

                                <a href="{{ url('admin/product/show/') }}?productid={{ $rs->productid }}"
                                    target="{{ $rs->productid }}">
                                    {{ $rs->number }}
                                </a>


                            </td>


                            <td>
                                <a href="{{ url('house/show/') }}/{{ $rs->productid }}" target="{{ $rs->productid }}">
                                    {{ $rs->court }}
                                </a>
                            </td>
                            <td>

                                {{ $rs->city1title }}
                                <Br>


                                {{ $rs->city2title }}


                                {{ $rs->address }}
                                &nbsp; (
                                {{ $rs->pattern }}
                                /
                                {{ $rs->buildname }}
                                樓高
                                {{ $rs->storey }}
                                層)

                            </td>
                            <td>
                                {{ $rs->tenderdate }}

                            </td>
                            <td>
                                <font class="danger">
                                    {{ $rs->totalupset }}

                                </font>萬<Br>

                                {{ $rs->margin }}

                                萬
                            </td>

                            <td>

                                <font class="danger">
                                    {{ $rs->pingtotalnumberof }}

                                </font>坪
                                <BR>
                                {{ $rs->stakeholdersfloor }}
                                坪

                            </td>


                            <td>

                                <font class="danger">{{ $rs->floorprice }}</font>萬/坪
                                <BR>

                                {{ $rs->auctions }}

                            </td>





                            <td>
                                {{ $rs->beattime }}
                                <BR>
                                {{ $rs->nocross_point }}
                            </td>
                            <td>
                                {{ PF::xmlSearch($data['xmldoc'], '//參數設定檔/上下架/KIND/傳回值', '資料', $rs->online) }}

                            </td>



                            <td>
                                {{ PF::xmlSearch($data['xmldoc'], '//參數設定檔/放置位置/KIND/傳回值', '資料', $rs->location) }}
                                {{ PF::xmlSearch($data['xmldoc'], '//參數設定檔/管理者放置位置/KIND/傳回值', '資料', $rs->locationadmin) }}

                            </td>

                            <td>
                                {{ PF::formatNumber($rs->hits, -1) }}
                            </td>
                            <td>
                                {{ PF::formatDate($rs->created_at) }}
                                {{ PF::formatDate($rs->updated_at) }}

                            </td>
                        </tr>
                    @endforeach

                </tbody>



            </table>

            @if (count($data['rows']) == 0)
                No Data
            @endif
    </form>
    {{ $data['rows'] != null ? $data['rows']->links('layouts.paginate') : '' }}

    <div class="form-inline">

        <form name="oFromExcel" method="post" action="{{ request()->url() }}/excelexport" target="excel">
            <button class="btn btn-info">
                EXCEL匯出
            </button>
            @include('admin.layouts.hiddenall', [])
        </form>

    </div>

    </div>
@endsection
