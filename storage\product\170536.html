<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">


<html>
<head id="Head1"><meta http-equiv="Content-Type" content="text/html; charset=big5" /><meta name="title" content="案件內容" /><meta name="description" content="網頁說明文案" /><title>
	案件內容
</title><link rel="stylesheet" type="text/css" href="../Style.css" />

<style type="text/css">
#gotop {
    display: none;
    position: fixed;
    right: 20px;
    bottom: 20px;    
    padding: 6px 6px;    
    font-size: 20px;
    background: #777;
    color: white;
    cursor: pointer;
}

.r1, .r2,.r3,.r4,.r5 {
    display: block;
    overflow: hidden; 
    background:#FF9D37;
}
.r1 { 
    margin: 0 5px;
    height: 1px;
}
.r2 { 
    margin: 0 2px;
    height: 1px;
}
.r3 { 
    margin: 0 1px;
    height: 1px;
}        
.r4 {
    margin: 0 1px;
    height: 1px;
}
.r5 {
    margin: 0 1px;
    height: 1px;
}
div.maru    {
	background:#FF9D37;
	height:12px;
	padding:5px;
	color:#003;
	text-align:center;
}

    #abgne_float_ad
    {
        display: none;
        position: absolute;
    }
    #abgne_float_ad .abgne_close_ad
    {
        display: block;
        text-align: right;
        cursor: pointer;
        font-size: 12px;
    }
    #abgne_float_ad a img
    {
        border: none;
    }
    div.bigDiv
    {
        height: 3000px;
    }
</style>

<link href="../App_Themes/Default/StyleSheet.css" type="text/css" rel="stylesheet" /></head>
<Script type="text/javascript" src="../javaScripts/CommonFunctions.js" ></Script>
<script type="text/javascript" src="../javaScripts/jquery-latest.min.js"></script>
<Script Language="JavaScript">

    // 當網頁載入完
    $(window).load(function() {
        var $win = $(window),
			$ad = $('#abgne_float_ad').css('opacity', 0).show(), // 讓廣告區塊變透明且顯示出來
			_width = $ad.width(),
			_height = $ad.height(),
			_diffY = 140, _diffX = 20, // 距離右及上方邊距
			_moveSpeed = 800; // 移動的速度

        // 先把 #abgne_float_ad 移動到定點
        $ad.css({
            top: _diffY, // 往上
            left: $win.width() - _width - _diffX,
            opacity: 1
        });

        // 幫網頁加上 scroll 及 resize 事件
        $win.bind('scroll resize', function() {
            var $this = $(this);

            // 控制 #abgne_float_ad 的移動
            $ad.stop().animate({
                top: $this.scrollTop() + _diffY, // 往上
                left: $this.scrollLeft() + $this.width() - _width - _diffX
            }, _moveSpeed);
        }).scroll(); // 觸發一次 scroll()

        // 關閉廣告
        $('#abgne_float_ad .abgne_close_ad').click(function() {
            $ad.hide();
        });
    });

    //顯示大照片
    function PicLink(PicSerial) {
        //LinkString="http://59.124.58.1662/Member/PicShow.aspx?PicSerial=" + PicSerial + "&PicWidth=500";
        //LinkString = "http://www.lhouse.com.tw/auction2005/Member/PicShow.aspx?PicSerial=" + PicSerial + "&PicWidth=500";
        LinkString = "PicShow.aspx?PicSerial=" + PicSerial + "&PicWidth=500";
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,left=50,top=50");
        NewWin2.focus();
    }

    //隱藏或顯示資料
    function ShowHideTable(TableName) {
        if (TableName.style.display == "") {
            TableName.style.display = "none";
        }
        else {
            TableName.style.display = "";
        }
    }

    //隱藏或顯示AD
    function ShowHideAD(ADName) {
        if (ADName.style.display == "") {
            ADName.style.display = "none";
            GoogleAD.style.display = "none";
        }
        else {
            ADName.style.display = "";
            GoogleAD.style.display = "";
        }
    }

    //檢查有那些Table該隱藏
    function ChkTable() {
        if (LandTable.rows.length < 1)
        { LandRow.style.display = "none"; } //LandRow2.style.display="none";
        if (BuildingTable.rows.length < 1)
        { BuildingRow.style.display = "none"; } //BuildingRow2.style.display="none";
        if (ExtraTable.rows.length < 1)
        { ExtraRow.style.display = "none"; } //ExtraRow2.style.display="none";
        if (FailTable.rows.length < 1)
        { FailRow.style.display = "none"; FailRow2.style.display = "none"; }
        if (LandAddTable.rows.length < 1)
        { LandAddRow.style.display = "none"; }
        //if (BuildingTable.rows.length<1)
        //{ImageInfo.style.display="none";DepositionRow.style.display="none"}
    }

    //電子地圖
    //function ChkMap(City, District, Road, Sec, Lane, Alley, NO) {
	//if (City == "桃園縣") {
	//	City = "桃園市";
	//	District = District.substr(0,2) + "區";
	//}
        //LinkString = "http://lhouse.map.com.tw/showadd.asp?add=";
        //LinkString = LinkString + City;
        //LinkString = LinkString + District;
        //LinkString = LinkString + Road;
        //if (Sec != "") { LinkString = LinkString + Sec + "段"; }
        //if (Lane != "") { LinkString = LinkString + Lane + "巷"; }
        //if (Alley != "") { LinkString = LinkString + Alley + "弄"; }
        //if (NO != "") { LinkString = LinkString + NO + "號"; }
        //NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,left=50,top=50");
        //NewWin2.focus();
    //}

    //謄本
    //function ChkApograph(ProID,CourtNickName,ProBatch,ProDep)
    //{		
    //	LinkString="Apograph.aspx?ProID=" + ProID + "&ProNickName=" + CourtNickName + "&ProBatch=" + ProBatch + "&ProDep=" + ProDep;
    //	NewWin2 = window.open(LinkString,"NewWin2","width=750,height=650,resizable=yes,scrollbars=yes,status=yes");
    //	NewWin2.focus();
    //}

    function ShowApograph(ProSerial, ProCourt, ProID, ProDep, CourtNick) {
        //LinkString="http://file.lhouse.com.tw/Auction_PicLink/ShowApograph.asp?ProID="+ProID+"&ProCourt="+ProCourt+"&ProSerial="+ProSerial+"&ProDep="+ProDep+"&CourtNick="+CourtNick
        //LinkString="http://219.87.73.103/Auction_PicLink/ShowApograph.asp?ProID="+ProID+"&ProCourt="+ProCourt+"&ProSerial="+ProSerial+"&ProDep="+ProDep+"&CourtNick="+CourtNick

        //2012/10/16 因應個資法,暫停使用 
        //LinkString = "http://ts.lhouse.com.tw/Auction_PicLink/ShowApograph.asp?ProID=" + ProID + "&ProCourt=" + ProCourt + "&ProSerial=" + ProSerial + "&ProDep=" + ProDep + "&CourtNick=" + CourtNick
        //NewWin2=window.open(LinkString,"NewWin2","menubar=1,toolbar=0,width=750,height=650,resizable=yes,scrollbars=yes,left=50,top=50");
        //NewWin2.focus();
        alert('本項服務因應個資法要求，暫時關閉服務，不便之處敬請見諒！');
    }

    //相關照片
    function ChkPic(LinkString) {
        NewWin2 = window.open(encodeURI(encodeURI(LinkString)), "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,left=50,top=50");
        NewWin2.focus();
    }

    //街景影像
    /*
    function Video(City,District,Road)
    {
    NewWin2 = window.open("http://media1.lhouse.com.tw/street.asp?County="+City+"&City="+District+"&Road="+Road,"NewWin2","width=750,height=650,resizable=yes,scrollbars=yes,left=50,top=50");
    NewWin2.focus();
    }
    */

    //Google Map
    function Gmap(ProSerial) {
        NewWin2 = window.open("http://www.80168.com.tw/Auction2010/LHouseService/GMap.asp?Addre=", "GMap", "width=750,height=650,resizable=yes,scrollbars=yes,left=50,top=50");
        NewWin2.focus();
    }

    //Court State
    function cstate() {
        NewWin2 = window.open("http://www.lhouse.com.tw/Common/MOJ.mht", "CourtState", "width=750,height=650,resizable=yes,scrollbars=yes,left=50,top=50");
        NewWin2.focus();
    }

    //原始公告
    function GoBulletin(ProYear, ProCourt, ProID, CourtNickName) {
        //LinkString="http://59.124.58.166/Bulletin.aspx?ProYear=" + ProYear + "&ProCourt=" + ProCourt + "&ProID=" + ProID + "&CourtNickName=" + CourtNickName;
        LinkString = "http://yellow.lhouse.com.tw/Bulletin.aspx?ProYear=" + ProYear + "&ProCourt=" + ProCourt + "&ProID=" + ProID + "&CourtNickName=" + CourtNickName;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //同路段行情
    function SameRoadQuery(ProBCity, ProBDistrict, ProBVillage, ProBRoad, ProBSec, ProBLane) {
        //window.location.href = "AuctionQueryResult.aspx?QueryType=201&ProBCity=" + ProBCity + "&ProBDistrict=" + ProBDistrict + "&ProBVillage=" + ProBVillage + "&ProBRoad=" + ProBRoad + "&ProBSec=" + ProBSec + "&ProBLane=" + ProBLane;
        LinkString = "AuctionQueryResult.aspx?QueryType=201&ProBCity=" + ProBCity + "&ProBDistrict=" + ProBDistrict + "&ProBVillage=" + ProBVillage + "&ProBRoad=" + ProBRoad + "&ProBSec=" + ProBSec + "&ProBLane=" + ProBLane;
        NewWin2 = window.open(LinkString, "NewWin2", "width=800,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //同地段行情
    function SameLandQuery(ProLCity, ProLDistrict, ProLSec, ProLSubSec) {
        //window.location.href = "AuctionQueryResult.aspx?QueryType=202&ProLCity=" + ProLCity + "&ProLDistrict=" + ProLDistrict + "&ProLSec=" + ProLSec + "&ProLSubSec=" + ProLSubSec;
        LinkString = "AuctionQueryResult.aspx?QueryType=202&ProLCity=" + ProLCity + "&ProLDistrict=" + ProLDistrict + "&ProLSec=" + ProLSec + "&ProLSubSec=" + ProLSubSec;
        NewWin2 = window.open(LinkString, "NewWin2", "width=800,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //中古屋行情
    function SecondHandNew(City, District) {
        LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/front/member/rentlist/rentlistnew.asp?address=" + City + District;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //中古屋歷史行情
    function SecondHandQuery(City, District) {
        LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/front/member/rentlist/rentlistold.asp?address=" + City + District;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //租屋行情
    function RentQuery(City, District) {
        //LinkString="http://www.lhouse.com.tw/Rent/QueryRent.asp?City=" + City + "&District=" + District ;
        //LinkString = "Front/Rent/QueryRent.asp?City=" + City + "&District=" + District;
        LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/front/member/rentlist/rentlist.asp?address=" + City + District;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //國有財產局
    function NationQuery() {
        //LinkString="http://www.lhouse.com.tw/member/NationalProperty/NationalPropertyQuery.asp" ;
        LinkString = "Front/Member/NationalProperty/NationalPropertyQuery.asp";
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //兇宅相關資訊
    function UnLuckyQuery() {
        alert("提醒您：現在您要連線到寬頻房訊以外的網站瀏覽參考，\n\n　　　　該網站提供之資訊敬請各會員自行查明。");
        LinkString = "http://www.unluckyhouse.com/index.php";
        NewWin2 = window.open(LinkString, "UnLucky", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //本區都更範圍圖
    function ChangeCity(District) {
        LinkString = "changecityjpg/ccshow.asp?cc=" + District;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }


    //本區基地台
    function EAreaQuery() {
        //LinkString="http://www.lhouse.com.tw/Member/ProNoteEArea.asp?City=" + form1.ProBCity.value + "&Road=" + form1.ProBRoad.value ;
        LinkString = "Front/Member/ProNoteEArea.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //本區廣播電台
    function ERadioQuery() {
        //LinkString="http://www.lhouse.com.tw/Member/ProNoteERadio.asp?City=" + form1.ProBCity.value + "&Road=" + form1.ProBRoad.value ;
        LinkString = "Front/Member/ProNoteERadio.asp?City=" + form1.ProBCity.value + "&Road=" + form1.ProBRoad.value;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //本區空氣品質
    function EAirQuery() {
        //LinkString="http://www.lhouse.com.tw/Member/ProNoteEAir.asp?City=" + form1.ProBCity.value + "&Road=" + form1.ProBRoad.value ;
        LinkString = "Front/Member/ProNoteEAir.asp?City=" + form1.ProBCity.value + "&Road=" + form1.ProBRoad.value;
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //近期行情查詢
    function NewsCutedQuery() {
        //LinkString = "PriceQueryList.asp";
        LinkString = "http://www.lhouse.com.tw/Member/pricequery.asp";
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //法拍道路地
    function AuctionRoadQuery() {
        //LinkString="http://www.lhouse.com.tw/Member/ProNoteEAir.asp?City=" + form1.ProBCity.value + "&Road=" + form1.ProBRoad.value ;
        LinkString = "/Auction2010/LHouseService/Front/LHouseService/Object/AuctionRoad.aspx";
        NewWin2 = window.open(LinkString, "NewWin2", "width=650,height=450,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //得標道路地
    function ReceiveRoadQuery() {
        //LinkString="http://www.lhouse.com.tw/Member/ProNoteEAir.asp?City=" + form1.ProBCity.value + "&Road=" + form1.ProBRoad.value ;
        LinkString = "/Auction2010/LHouseService/Front/LHouseService/Object/ReceiveRoad.aspx";
        NewWin2 = window.open(LinkString, "NewWin2", "width=650,height=450,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }

    //相似物件
    function SimilarQuery() {
        LinkString = "AuctionQueryResult.aspx?QueryType=46&City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&LCity=" + form1.ProLCity.value + "&LDistrict=" + form1.ProLDistrict.value + "&ProCategory=2";
        NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
        NewWin2.focus();
    }
    
    //其它資訊
    function ExtraInfo(QueryType) {
        switch (QueryType) {
            case 1:
                LinkString = "LifeGroup.aspx?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 2:
                LinkString = "Mrt.aspx?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 3:
                //LinkString = "http://www.lhouse.com.tw/Member/NowPrice.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value;
                LinkString = "Front/Member/NowPrice.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 4:
                //LinkString="http://www.lhouse.com.tw/Member/CityPlan/CP_CityPlan.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value ;
                LinkString = "Front/Member/CityPlan/CP_CityPlan.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 5:
                //LinkString="http://www.lhouse.com.tw/Member/ProNote.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value + "&Kind=1" ;
                LinkString = "Front/Member/ProNote.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value + "&Kind=1";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 6:
                //LinkString="http://www.lhouse.com.tw/Member/MetroVicinity/MV_Mrt.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value ;
                LinkString = "Front/Member/MetroVicinity/MV_Mrt.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 10:
                //LinkString="http://www.lhouse.com.tw/Member/ProNote.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value + "&Kind=2" ;
                LinkString = "Front/Member/ProNote.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value + "&Kind=2";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 11:
                //LinkString="http://www.lhouse.com.tw/Member/ProNote.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value + "&Kind=3" ;
                LinkString = "Front/Member/ProNote.asp?City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&Road=" + form1.ProBRoad.value + "&Kind=3";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 12:
                //LinkString="http://www.lhouse.com.tw/auction2005/Member/NewsQuery.aspx" ;
                LinkString = "NewsQuery.aspx";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 13:
                LinkString = "http://qservice.dba.tcg.gov.tw/squatter/squ_dlg.asp";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 14:
                LinkString = "http://www.lhouse.com.tw/Member/ProNoteFD.asp?type=1&District=" + form1.ProBDistrict.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 15:
                LinkString = "http://www.lhouse.com.tw/Member/ProNotePark.asp?type=1&District=" + form1.ProBDistrict.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 16:
                LinkString = "http://www.lhouse.com.tw/Member/ProNoteBic.asp?type=1";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 17:
                LinkString = "http://www.lhouse.com.tw/Member/ProNoteWiFi.asp?type=1";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;

            case 18:
                //LinkString = "http://www.lhouse.com.tw/Member/ProNoteFDA.asp?type=1";
		LinkString = "http://www.lhouse.com.tw/Member/ProNoteFDA2.asp?type=1&District=" + form1.ProBDistrict.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 19:
                LinkString = "http://www.zonemap.taipei.gov.tw/showmapMain.aspx";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 20:
                LinkString = "http://plan.kcg.gov.tw/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 21:
                LinkString = "http://www.dgbas.gov.tw/lp.asp?ctNode=2849&CtUnit=333&BaseDSD=7";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 22:
                LinkString = "http://www.lhouse.com.tw/Member/ProRentPrice.asp?type=1&Dis=" + form1.ProBDistrict.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 23:
                LinkString = "http://service.moj.gov.tw/criminal/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 24:
                LinkString = "http://www.esunbank.com.tw/b2c/financetrial.aspx?kind=3";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 25:
                LinkString = "http://www.e-bus.taipei.gov.tw/index_6_1.html";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 26:
                LinkString = "http://citybus.taichung.gov.tw/tcbus2/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 27:
                LinkString = "http://ebus.klcba.gov.tw/KLBusWeb/buswindow_large.jsp?rgid=101";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 28:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/Front/Member/ProTLand.asp";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 29:
                LinkString = "http://www.tp.edu.tw/neighbor/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 30:
                LinkString = "http://www.ntpc.edu.tw/web/Home?command=display&page=flash";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 31:
                LinkString = "http://168.thb.gov.tw/navigate.do#";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 32:
                LinkString = "http://taiwanbus.tw/ByBus.aspx?Lang=";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 33:
                LinkString = "http://tw.house.yahoo.com/experts/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 34:
                LinkString = "http://tw.house.yahoo.com/celeb/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 35:
                LinkString = "http://tw.house.yahoo.com/design/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 36:
                LinkString = "http://yahoo.twproperty.com.tw/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 37:
                LinkString = "http://media.doli.taipei.gov.tw/map/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 38:
                LinkString = "http://streethouse.ncree.narl.org.tw/form04.aspx";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 39:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/landsearch.asp";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 40:
                LinkString = "http://www.tldep.taipei.gov.tw/GAMMA/";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 41:
                LinkString = "../images/realprice6.jpg";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 42:
                LinkString = "cityplanlink.asp?city=" + form1.ProBCity.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 43:
                LinkString = "http://www.lhouse.com.tw/Member/UdnTV.asp";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 46:
		LinkString = "AuctionQueryResult.aspx?QueryType=46&City=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value + "&LCity=" + form1.ProLCity.value + "&LDistrict=" + form1.ProLDistrict.value + "&ProCategory=2";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 47:
                LinkString = "http://www.54168.com.tw/Auction2010/LHouseService/Weather/GetWeatherA.aspx?City=" + escape(form1.ProBCity.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 48:
                LinkString = "Front/Member/ProBackBuilding.asp?District=" + form1.ProBDistrict.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 49:
                LinkString = "http://qservice.dba.tcg.gov.tw/squatter/squ_dlg.asp";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 50:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/realpricesearch/realpricelist.asp?kind=2&city=" + form1.ProBCity.value;
		LinkString =  LinkString + "&District=" + form1.ProBDistrict.value + "&road=" + form1.ProBRoad.value + "&Sec=" + form1.ProBSec.value;
		LinkString =  LinkString + "&LCity=" + form1.ProLCity.value + "&LDistrict=" + form1.ProLDistrict.value + "&LSec=" + form1.ProLSec.value;
		//LinkString =  LinkString + "&LSubSec=" + from1.ProLSubSec.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 51:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/TC.pdf";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 52:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/TG.pdf";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 53:
                LinkString = "Front/Member/ProNoteSteal.asp?District=" + form1.ProBDistrict.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 54:
                LinkString = "http://wq.epa.gov.tw/Code/Default.aspx?Water=River";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
	    case 55:
                LinkString = "http://ebus.gov.taipei/Query/BusDynamicMainList";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 81:
                LinkString = "https://www.youtube.com/watch?v=D2N59GB9NjQ";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 82:
                LinkString = "https://www.youtube.com/watch?v=zw2AmkW0r_I";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 83:
                LinkString = "https://www.youtube.com/watch?v=GPU_WTJEQJY";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 84:
                LinkString = "https://www.youtube.com/watch?v=szw9B0AavPc";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 85:
                LinkString = "https://www.youtube.com/watch?v=nPU19rvcsSI";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 86:
                LinkString = "https://www.youtube.com/watch?v=hDOK0vtMcws";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 87:
                LinkString = "https://www.youtube.com/watch?v=7-7W0en25tk";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 88:
                LinkString = "https://www.youtube.com/watch?v=RGgfhSD7ArM";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 89:
                LinkString = "https://www.youtube.com/watch?v=8j9_JsG3flg";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 90:
                LinkString = "https://www.youtube.com/watch?v=npGJJubOAIA";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 91:
                LinkString = "https://www.youtube.com/watch?v=NQ8m0pwqUP0";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 92:
                LinkString = "https://www.youtube.com/watch?v=-iFYa9oRcl4";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 93:
                LinkString = "https://www.youtube.com/watch?v=CKiQg0UEcI8";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 94:
                LinkString = "https://www.youtube.com/watch?v=UgvSk8PK1Kw";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 95:
                LinkString = "http://twd.water.gov.taipei/billing/ps_16/AA/AA19/AA1901.aspx";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
        }
    }

    function ExtraInfoMap(QueryType) {
        switch (QueryType) {
            case 5:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s4&City=" + escape(form1.ProBCity.value) + "&District=" + escape(form1.ProBDistrict.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 10:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s5&City=" + escape(form1.ProBCity.value) + "&District=" + escape(form1.ProBDistrict.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 11:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s6&City=" + escape(form1.ProBCity.value) + "&District=" + escape(form1.ProBDistrict.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 17:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s2&City=" + escape(form1.ProBCity.value) + "&District=" + escape(form1.ProBDistrict.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 18:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s3&City=" + escape(form1.ProBCity.value) + "&District=" + escape(form1.ProBDistrict.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 19:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s1&City=" + escape(form1.ProBCity.value) + "&District=";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 20:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s8&City=" + escape(form1.ProBCity.value) + "&District=" + escape(form1.ProBDistrict.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 53:
                LinkString = "/googlemapv3/common/t_mapsearch_info.aspx?QueryType=s9&City=" + escape(form1.ProBCity.value) + "&District=" + escape(form1.ProBDistrict.value);
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
        }
    }

    //實價登錄
    function RealPriceA(QueryType) {
        switch (QueryType) {
            case 1:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/realpricesearch/realpricelist1.asp?kind=2&city=" + form1.ProBCity.value;
		LinkString =  LinkString + "&District=" + form1.ProBDistrict.value + "&road=" + form1.ProBRoad.value + "&Sec=" + form1.ProBSec.value;
		LinkString =  LinkString + "&LCity=" + form1.ProLCity.value + "&LDistrict=" + form1.ProLDistrict.value + "&LSec=" + form1.ProLSec.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 2:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/realpricesearch/realpricelist2.asp?city=" + form1.ProBCity.value + "&District=" + form1.ProBDistrict.value;
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
            case 3:
                LinkString = "https://www.54168.com.tw/Auction2010/LHouseService/realpricesearch/realpricesearch.asp";
                NewWin2 = window.open(LinkString, "NewWin2", "width=750,height=650,resizable=yes,scrollbars=yes,status=yes,left=50,top=50");
                NewWin2.focus();
                break;
        }
    }

    //要求業務與其聯絡
    function ContactRequire(ProCourt, ProID) {
        NewWin2 = window.open("ContactRequire.aspx?ProCourt=" + ProCourt + "&ProID=" + ProID, "NewWin2", "width=750,height=300,resizable=yes,scrollbars=yes,left=50,top=50");
        NewWin2.focus();
    }

    function newinfo() {
        NewWin2 = window.open("http://IM.LHOUSE.COM.TW/SearchReqResult.aspx?QType=2", "NewWin2", "width=750,height=600,resizable=yes,scrollbars=yes,left=50,top=50");
        NewWin2.focus();
    }

    //列管追蹤備註
    function TraceNote(ProCourt, ProID) {
        LinkString = "TraceNote.aspx?ProCourt=" + ProCourt + "&ProID=" + ProID;
        NewWin2 = window.open(LinkString, "NewWin2", "width=700,height=300,resizable=yes,scrollbars=yes,status=yes");
        NewWin2.focus();
    }

    //20170526更改原紅色字體為黑色
    function ChangeFontColor() {
	document.getElementById("fontcolor1").style.color = "Black";
	document.getElementById("fontcolor2").style.color = "Black";
	document.getElementById("fontcolor3").style.color = "Black";
	document.getElementById("fontcolor4").style.color = "Black";
    }
	
</Script>

<script type="text/javascript">
$(function(){
    $("#gotop").click(function(){
        jQuery("html,body").animate({
            scrollTop:0
        },1000);
    });
    $(window).scroll(function() {
        if ( $(this).scrollTop() > 300){
            $('#gotop').fadeIn("fast");
        } else {
            $('#gotop').stop().fadeOut("fast");
        }
    });
});
</script>

<body onLoad="javascript:ChkTable();">
<div id="gotop">^ TOP ^</div>
<script language="JavaScript">

function kDown()
{
	alert("感謝您對寬頻房訊的支持!");
	return true;
}
function right(mousebutton)
{
	//return true;
	if (event.button == 2 || event.button==3)
	{
		alert("本系統已申請專利，專利字號：發明第一六七七七二號！！");
		return false;
	}

}
function mov()
{
	return false;
}
document.onkeydown=kDown;
document.onmousedown=right;
document.oncontextmenu=mov;
document.onselectstart=mov;
document.ondragstart=mov;
document.onmousemove=mov;

</script>

<center>
<form name="form1" method="post" action="AuctionDetail.aspx?ProSerial=2263257&amp;ProCourt=201&amp;ProID=109%u52a9%u5b50131&amp;CheckSum=2021-4-14-2-24-59" id="form1">
<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="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" />


<script src="/Auction2010/ScriptResource.axd?d=mLq0KKSTXZ7t3pMs_05F88f-u9hkpxFuN8vIxhXy-uS0zW-9wqtjAyB2xdiLu-yflZIVWMnI5tdXk7CG1u62ETuf_TsLjlM7ezM-TGhRV4TkJOmjHSPT9gmlKieMfjI7Q4AU961a1TvsARdVn0jnrXXbrnqQCxon5gPR6nLsu4SYXFV00&amp;t=3f4a792d" type="text/javascript"></script>
<script type="text/javascript">
//<![CDATA[
if (typeof(Sys) === 'undefined') throw new Error('ASP.NET Ajax 用戶端架構無法載入。');
//]]>
</script>

<script src="/Auction2010/ScriptResource.axd?d=MCP8sAbs0QvMDIm-_xxwda77QNzdrx6JuMl7oN-TQg6adcwbjrZ1i4bYSGloqa_y1Q54I1c9HR2YDsfZzurSvKcTySkrx1znu4gnVjuDuu0GK04fb0zgOt9pIFjBRSxJ7tdBmHEs5osaCTFvugH4RJAhlLOpQpr0HeTBJfiT1D4A4RmE0&amp;t=5f915bbd" type="text/javascript"></script>
<script src="/Auction2010/ScriptResource.axd?d=FGj09mQxNiVI_WQ2UXylj4ADdNY5kxBpbug4TlaoJnK_mzSgnq6E6Jv1nvBECaCbSZ_bYZtSMBtTg-4BG68xQPF8aoFiUrNS-TYEoaPamXPLGCTVXQ_X15qaZyJ-oOPCAFiF0u-pZSbH_n2uJX32K-_0c5tNqKAJPebH5jCrvdMk696Q0&amp;t=5f915bbd" type="text/javascript"></script>
<input type="hidden" name="__VIEWSTATEGENERATOR" id="__VIEWSTATEGENERATOR" value="A71C31B9" />
<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="/wEWGAL+k5qTDQLnyrXjDwLAsIeADQK9/qHOCgKhz5mmBQKaj4mGDQLP4azuDwKRuYSgDgK9is/VCwLriN+WAQLAsL+6AwK9/rm6CgLP4aTvDALV8fSMDgKQiZviDwK5l93oDgKM54rGBgK7q7GGCALr8uDuCgLyxI7QDgLyxKKQDgK0oeesDAKTsazlDwLbgqb+CV17+0pNNcO9r5UIQ2974Vq3HWFs" />

<input name="hidProID" type="hidden" id="hidProID" value="109助子131" />
<input name="ProBCity" type="hidden" id="ProBCity" value="台北市" />
<input name="ProBDistrict" type="hidden" id="ProBDistrict" value="北投區" />
<input name="ProBVillage" type="hidden" id="ProBVillage" />
<input name="ProBRoad" type="hidden" id="ProBRoad" />
<input name="ProBSec" type="hidden" id="ProBSec" />
<input name="ProBLane" type="hidden" id="ProBLane" />
<input name="ProBAlley" type="hidden" id="ProBAlley" />
<input name="ProBNO" type="hidden" id="ProBNO" />
<input name="ProLCity" type="hidden" id="ProLCity" value="台北市" />
<input name="ProLDistrict" type="hidden" id="ProLDistrict" value="北投區" />
<input name="ProLSec" type="hidden" id="ProLSec" value="文林" />
<input name="ProLSubSec" type="hidden" id="ProLSubSec" value="四" />
<input name="ProDep" type="hidden" id="ProDep" value="子" />
<input name="hidCourtNickName" type="hidden" id="hidCourtNickName" value="士林行執處" />
<div id="UpdatePanel2">
	
<div id="TopBannerPanel">
		

	</div>

</div>
<div class="AuctionDetialTitleList" >
<!-- <input name="Submit" type="button" onClick="JavaScript:window.opener.focus();" value="回列表"> -->
<input name="Submit2" type="button" onClick="javascript:print()" value="列印">
<input type="button" value="改顏色" onClick="JavaScript:ChangeFontColor();">
<input type="submit" name="Button1" value="加入列管" id="Button1" title="直接點選即可加入列管" />
<input type="submit" name="Button2" value="刪除列管" id="Button2" />
<input type="button" name="Submit4" value="查看列管" onClick="JavaScript:window.location.href='AuctionQueryResult.aspx?QueryType=7'" title="全部列管都在裡面">
<input type="button" value="錯誤回報" onClick="JavaScript:window.location.href='Front/Member/PropertyErrorReturn/PER_PropertyErrorReturn.asp?ProSerial=2263257&ProID=109助子131&ProCourt=201&Back=1'" title="資料有出入請點錯誤回報">
<input type="button" value="是否拍賣過" title="查看本物件是否曾經拍賣過" onClick="JavaScript:window.location.href='Front/Member/IsAuction/IA_IsAuction.asp?ProCourt=201&ProID=109助子131'">
<span id="Bulletin"><input type=button name=Submit3  class='style12' value=法院公告 onClick=JavaScript:GoBulletin('109','201','109助子131','士林行執處');></span>
<input type="button" name="Submit3" value="回首頁" onClick="JavaScript:window.opener.focus();">
</div>
<div class="AuctionDetialTitleList" >
    <table width="100%" border=0 cellpadding=0 cellspacing=0>
        <tr>        
            <td align=right>
                <span class="inZoomTitle fontSize13" >字級設定:</span>
                <a onclick="javascript:inZoom('fontSize21','DetailContext')" href="javascript:void(0);" ><span class="inZoomBlock fontSize21" >巨</span></a>
                <a onclick="javascript:inZoom('fontSize17','DetailContext')" href="javascript:void(0);" ><span class="inZoomBlock fontSize17" >大</span></a>
                <a onclick="javascript:inZoom('fontSize15','DetailContext')" href="javascript:void(0);" ><span class="inZoomBlock fontSize15" >中</span></a>
                <a onclick="javascript:inZoom('fontSize13','DetailContext')" href="javascript:void(0);" ><span class="inZoomBlock fontSize13" >小</span></a>
                <a onclick="javascript:inZoom('fontSize11','DetailContext')" href="javascript:void(0);" ><span class="inZoomBlock fontSize11" >細</span></a>                
                &nbsp;            
                <!-- FB -->
                分享至：<iframe name="FB" src="fb3.asp?sn=2263257&des=案號：109助子131，坪單價：100.41萬，底價：607.5萬，地址：" frameborder="0" width="24" scrolling="no" height="24"></iframe>
                <!-- <a href="javascript: void(window.open('http://www.facebook.com/share.php?u='.concat(encodeURIComponent(location.href))+'&t='.concat(encodeURIComponent(document.title))) );" title="分享至 Facebook"><img src="/rent/images/NewInterface/icon/fb.jpg" border="0" /></a> -->
                <a id="TwitterLink" title="分享至 twitter" href="javascript: void(window.open('http://twitter.com/home/<USER>'.concat(encodeURIComponent(document.title)) .concat(' ') .concat(encodeURIComponent(location.href))));"><img title="分享至 twitter" src="/rent/images/NewInterface/icon/twitter.jpg" border="0" /></a>
                <a href="javascript: void(window.open('http://www.plurk.com/?qualifier=shares&status=' .concat(encodeURIComponent(location.href)) .concat(' ') .concat('&#40;') .concat(encodeURIComponent(document.title)) .concat('&#41;')));" title="分享至 plurk"><img src="/rent/images/NewInterface/icon/plurk.jpg" border="0" /></a>                        
			    <!-- 請聯絡業務 -->
			    <!--<a id="ContactRequire"><span class="style4"><b><font color="red">請專人與我聯絡</font></b></span></a>-->
			    【<span id="salesname"><font color="#990000"><img src='../images/R7_b020.gif' /> <a href='/Portal/Secretary' target='_blank'>購屋小秘書</a></font></span>】
                 <!--<input name="ShowHideLawDeclare" type="button" onclick='javascript:ShowHideAD(MoveAD);' value="顯示 / 隱藏 廣告">-->
                 <input type="submit" name="TopBannerButton" value="顯示 / 隱藏 廣告" id="TopBannerButton" />           
            </td>            

        </tr>
    </table>
</div>

<table width="800px" cellpadding="0" cellspacing="0" class="DetailTable">
<tr>
	<td>
		<!-- 標題說明 -->
		<table width="100%">
			<tr >
				<td></td>
				<td>
				    <!-- 案號 -->
				    <span id="ProID"><font size='5'>【109助子131】</font></span>
				    <!-- 住址 --> 
				    <span id="ProAddress"><font Size=5><b>台北市北投區　文林段四小段73-35地號</b></font></span>
				    <!-- 拍賣情況 -->
				    <span id="ProState">（<font color='#FF0000'><b>流標</b></font>）</span>
				    <!-- 20210415 新增熱門指數而排版 -->
				    <table border=0 width=100% cellpadding=0 cellspacing=0><tr><td>
                    <!-- 案件規格 -->
				    <span id="ProOther"></span>
				    <!-- 通訊投標 -->
				    <span id="DeliverMsg"><font color="#FF6600"></font></span>
					</td><td align=right>
				     <!-- 熱門指數 -->
				    <span id="ProClick"><img src='../images/R7_b020.gif' /> <font size=3 color=red><b>熱門指數：0021</b></font></span></td></tr></table>

				</td>
				<td></td>
			</tr>
		</table>
	</td>
</tr>
<tr>
	<td>
		<!-- 標題資料 -->
		<table width="100%" class="DetailTable">
		    <tr>
		        <td class="DetailContentTitle" >
                    <!-- 投標日 --> 
				    <span id="SaleDateTime"><b><img src='../images/R7_b020.gif' /> 投標日：<font color='#CC0033' class='NumberStyle'>110/1/24 - 110/4/22</font></b></span>
		        </td>
		        <td class="DetailContentTitle" >
                    <!-- 法院 -->
                    <span id="ProOwner2"><img src='../images/R7_b020.gif' />【士林行執處】</span>
                    <!-- 校對日 -->
                    <span id="ProOwner3">校對日：2021/4/14</span>                    
		        </td>
		        <td class="DetailContentTitle" >
				    <!-- 債務人 -->
                    <span id="ProOwner"><img src='../images/R7_b020.gif' /> 債務人：<span class='fontSize15 Red001'>承＊＊設</span></span>
		        </td>	        		        
		    </tr>
		</table>
	</td>
</tr>	
<tr>
	<td>
		<!-- 標題資料 -->
		<table width="100%" class="DetailTable">
			<tr >
				<td class="DetailContentTitle" style="width:10%">
				    <table width="100%">
				        <tr>
				            <td style="text-align:center;">
				                <span id="AuctionState"><b><i><font color="#000066" size="5">應買</font></i></b></span>
				            </td>
				        </tr>
				        <tr>
				            <td style="text-align:center">
				                <span id="AuctionState2"><font color='#CC0033'><b>不點交</b></font></span>
				            </td>
				        </tr>				        
				    </table>
				</td>
				<td class="DetailContentTitleSep"></td>
				<td class="DetailContentTitle" style="width:22%" >
				    <table width="100%" >
				        <tr>
				            <td class="DetailInner1"><b>主建坪</b></td>
				            <td class="DetailInner2"><span id="MSSize"></span></td>
				        </tr>
				        <tr>
				            <td><b>附建坪</b></td>
				            <td><span id="MSSize2"></span></td>
				        </tr>
				        <tr>
				            <td><b>公告建坪</b></td>
				            <td><span id="BCLPSize"></span></td>
				        </tr>				        				        
				    </table>
                </td>
				<td class="DetailContentTitleSep"></td>
				<td class="DetailContentTitle" style="width:22%">
				    <table width="100%" >
				        <tr>
				            <td class="DetailInner1"><b>增建坪</b></td>
				            <td class="DetailInner2"><span id="APSize"></span></td>
				        </tr>
				        <tr>
				            <td><b>公設坪</b></td>
				            <td><span id="APSize2"></span></td>
				        </tr>
				        <tr>
				            <td><b>持分地坪</b></td>
				            <td><span id="BCLPSize2"><font class='NumberStyle'>6.05</font> <font size='1'>坪</font></span></td>
				        </tr>				        				        
				    </table>				
				</td>
				<td class="DetailContentTitleSep"></td>
				<td class="DetailContentTitle" style="width:22%">
				    <table width="100%" >
				        <tr>
				            <td class="DetailInner1"><b>總坪數</b></td>
				            <td class="DetailInner2"><span id="BTUP"></span></td>
				        </tr>
				        <tr>
				            <td><b>坪單價</b></td>
				            <td><span id="BTUP2"><font id='fontcolor1' color='#CC0033' class='NumberStyleRed'>100.41 </font><font size='1'>萬/坪</font></span></td>
				        </tr>
				        <tr>
				            <td><b>拍後增值</b></td>
				            <td><span id="VAGAV"><span class='NumberStyle'>-171.77</span> <font size='1'>萬</font></span></td>
				        </tr>				        				        
				    </table>				
				</td>
				<td class="DetailContentTitleSep"></td>
				<td class="DetailContentTitle" style="width:22%">
				    <table width="100%" >
				        <tr>
				            <td class="DetailInner1"><b>總底價</b></td>
				            <td class="DetailInner2"><span id="PriceGuarantee"><font id='fontcolor3' color='#CC0033' class='NumberStyleRed'>607.5 </font><font size='1'>萬</font></span></td>
				        </tr>
				        <tr>
				            <td><b>保證金</b></td>
				            <td><span id="PriceGuarantee2"><font id='fontcolor4' color='#CC0033' class='NumberStyleRed'>121.5 </font><font size='1'>萬</font></span></td>
				        </tr>
				        <tr>
				            <td><b>公告現值</b></td>
				            <td><span id="VAGAV2"><span class='NumberStyle'>218000</span> <font size='1'>元</font></span></td>
				        </tr>				        				        
				    </table>							     
				</td>
			</tr>
		</table>
	</td>
</tr>
<tr>
	<td>
		<!-- 內文資料 -->
		<table width="100%" id="DetailContext" name="DetailContext" border="1" bordercolor="#E3E3E3" cellpadding="0" cellspacing="0" style="text-align:left">
            
						
			<tr ID="LandRow">
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>土地</b></td></tr>
					</table>
				</td>
				<td><table id="LandTable" border="0">
	<tr>
		<td valign="top">1.</td><td>台北市北投區文林段四小段73-35地號，25 ㎡，持分 7995 / 10000【607.5 萬】 (公現：218000 元，110年公現)</td>
	</tr>
</table> </td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr ID="BuildingRow">
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>建物</b></td></tr>
					</table>
				</td>
				<td><table id="BuildingTable" border="0">

</table></td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr ID="ExtraRow">
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>其它</b></td></tr>
					</table>
				</td>
				<td><table id="ExtraTable" border="0">

</table></td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr ID="FailRow">
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>流標記錄</b></td></tr>
					</table>
				</td>
				<td><table id="FailTable" border="0">
	<tr>
		<td valign="top" nowrap="nowrap">1拍.</td><td>109/11/24【750 萬】，保証金 150 萬，坪價 123.97 萬，流標</td>
	</tr><tr>
		<td valign="top" nowrap="nowrap">2拍.</td><td>109/12/15【675 萬】，保証金 135 萬，坪價 111.57 萬，流標</td>
	</tr><tr>
		<td valign="top" nowrap="nowrap">3拍.</td><td>110/1/12【607.5 萬】，保証金 121.5 萬，坪價 100.41 萬，流標</td>
	</tr>
</table></td>
			</tr>
			<tr ID="FailRow2" class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr >
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>他項權利</b></td></tr>
					</table>
				</td>
				<td><span id="ProCancel" class="Green001"></span><table id="LoanTable" border="0">

</table></td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr ID="DepositionRow">
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>謄本資料</b></td></tr>
					</table>
				</td>
				<td><span id="ProCopy"></span></td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr>
				<td width="100" class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<!-- 法院筆錄 -->
						<tr class="SETitle2_1"><td><b>
						<span id="CourtNoteTitle">法院筆錄</span>
						</b></td></tr>
					</table>
				</td>
				<td><span id="ProDeposition" class="Red001 FontSize15"></span><div id="KeyWords_Deposition"><table id="DepositionTable" border="0">
	<tr>
		<td valign="top">1.</td><td>本件係拍賣土地應有部分，查無義務人現實占有部分，拍定後不點交。</td>
	</tr><tr>
		<td valign="top">2.</td><td>依卷附鑑價報告、查封筆錄&#26305;現場照片所示,本件土地像位於門牌號碼：臺北市北投區石牌路1段166巷31弄8之1號建物前之狹長型道路用地，現況為既成巷道，其上無建物占用。</td>
	</tr><tr>
		<td valign="top">3.</td><td>本件土地無抵押權設定。</td>
	</tr><tr>
		<td valign="top">4.</td><td>依臺北市政府都市發展局函復，本件土地使用分區為「第三種住宅區」，使用時應依「臺北市士地使用分區管制自治條例」及該區都市計畫說明書圖規定辦理。</td>
	</tr><tr>
		<td valign="top">5.</td><td>本件係拍費土地應有部分，倘非共有人拍定，共有人有優先承賈權。</td>
	</tr><tr>
		<td valign="top">6.</td><td>#本件投標日為繕打日期起算，請注意#</td>
	</tr><tr>
		<td valign="top">7.</td><td>前次案號：109助子131一</td>
	</tr>
</table></div></td>
			</tr>
                           
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr ID="LandAddRow">
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>土地增值</b></td></tr>
					</table>
				</td>
				<td><table id="LandAddTable" border="0">
	<tr>
		<td valign="top">1.</td><td>台北市北投區文林段四小段73-35地號：25 ㎡ * ( 7995 / 10000 ) * 218000 元 - 607.5 萬 =【-171.77 萬】</td>
	</tr>
</table></td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr align="center" style="color:#993333;font-size:13px;font-family:微軟正黑體, 新細明體, 細明體;font-weight:bold;">
				<td width="100"></td>
				<td >【寬頻房訊】本資料經校對法院公告，並申調謄本整理而成，若有遺誤，概以法院公告為主【版權所有】</td>
			</tr>
<!-- 20150904 新增突顯實價登錄比較功能 -->
			<tr ID="RealPriceShow">
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td>實價登錄</td></tr>
					</table>
				</td>
				<td>
					<table id="RealPriceInfo" width="100%"  border="0" cellspacing="0" cellpadding="0">
			                      <tr>
				                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='JavaScript:RealPriceA(1);'>同路段資料參考</a></td>
				                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='JavaScript:RealPriceA(2);'>同區域資料參考</a></td>
				                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='JavaScript:RealPriceA(3);'>實價登錄查詢</a></td>
			                      </tr>
					</table>
				</td>
			</tr>


			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>相關資訊</b><br /><span class="fontSize13">【<a href="JavaScript:ShowHideTable(OtherInfo);">顯示/隱藏</a>】</span></td></tr>
					</table>
				</td>
				<td>
                    <table id="OtherInfo" width="100%"  border="0" cellspacing="0" cellpadding="0">
<!--                      <tr>
                        <td width="33%" height="20"><img  src="../Images/R7_b02.gif" width="8" height="7"> <a id="SameRoad">法拍建物拍定行情</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="SameLand" href="JavaScript:SameLandQuery(form1.ProLCity.value,form1.ProLDistrict.value,form1.ProLSec.value,form1.ProLSubSec.value);">法拍土地拍定行情</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="SecondHandNew">本區中古屋資料</a></td>
                        </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="Rent">本區租屋資料</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="Nation">國有財產局拍賣資訊</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7" /> <a href='https://www.54168.com.tw/Auction2010/LHouseService/landsearch.asp' target="_blank">其它土地定位</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="SecondHand">本區中古屋資料(歷史資料)</a></td>
			<td width="33%">　</td>
                      </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="EArea" href="JavaScript:EAreaQuery();">本區基地台</a>(<a href='JavaScript:ExtraInfoMap(18);'>地圖版</a>)</td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="ERadio" href="JavaScript:ERadioQuery();">本區廣播電塔(電磁波)</a>(<a href='JavaScript:ExtraInfoMap(19);'>地圖版</a>)</td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="EAir" href="JavaScript:EAirQuery();">本縣市空氣品質</a></td>
                      </tr>

                      <tr>
                        <td height="20" colspan="3">
                          
                        </td>
                        </tr> -->
                      <tr>
                        <td height="20" colspan="3"><img src="../images/R7_b020.gif" /><font color=#993333>【友善設施】</font>
                          <table id="DLF" cellspacing="0" border="0" width="100%">
	<tr>
		<td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/ProNotePark.asp?type=1&City=台北市&District=北投區&Road=' target=_blank>本區公園錄地資訊</a>
						      </td><td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/ProNoteBic.asp?type=1' target=_blank>北市自行車道資訊</a>
						      </td><td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/ProNoteWiFi.asp?type=1' target=_blank>無線上網熱點資訊</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://taiwanbus.tw/ByBus.aspx?Lang=' target=_blank>全省公路客運</a>
						      </td><td width="33%">
						  	    　<a href='http://taipei.youbike.com.tw/cht/f11.php' target=_blank>微笑單車 Youbike</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProNotePolice.asp' target=_blank>北市警察局分佈點</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProNoteFireStation.asp' target=_blank>北市消防局分佈點</a>
						      </td><td width="33%">
						  	    　<a href='http://www.ece.moe.edu.tw/?page_id=2085' target=_blank>全國公私立幼兒園</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProITaiwan.asp?City=台北市&District=北投區&Road=' target=_blank>iTaiwan 無線熱點</a>
						      </td>
	</tr>
</table>
                        </td>
                        </tr>

                      <tr>
                        <td height="20" colspan="3"><img src="../images/R7_b020.gif" /><font color=#993333>【生活商圈】</font>
                          <table id="DLL" cellspacing="0" border="0" width="100%">
	<tr>
		<td width="33%">
						  	    　<a href='LifeGroup.aspx?City=台北市&District=北投區&Road=' target=_blank>商圈環境 </a>
						      </td><td width="33%">
						  	    　<a href='https://m.metro.taipei/roadmap.asp?City=台北市&District=北投區&Road=' target=_blank>捷運路網圖</a>
						      </td><td width="33%">
						  	    　<a href='https://www.dgbas.gov.tw/lp.asp?CtNode=3091&CtUnit=333&BaseDSD=7&mp=1' target=_blank>行政院主計處物價月報</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://www.e-bus.taipei.gov.tw/index_6_1.html' target=_blank>大台北公車動態查詢</a>
						      </td><td width="33%">
						  	    　<a href='http://ebus.gov.taipei/Query/BusDynamicMainList' target=_blank>大台北公車動態查詢(新)</a>
						      </td><td width="33%">
						  	    　<a href='http://www.dep-in.gov.taipei/epb/iframe/trash.aspx' target=_blank>台北市垃圾清運路線查詢</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProNoteMT.asp' target=_blank>大台北規劃中捷運線</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/images/台北捷運各站戶籍所得地圖.png' target=_blank>台北市捷運各站所得地圖(PNG)</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/images/台鐵西部幹線各站戶籍所得地圖.png' target=_blank>台鐵西部幹線各站所得(PNG)</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/台北捷運南北環路線規劃.JPG' target=_blank>捷運南北環路線規劃</a>
						      </td><td width="33%">
						  	    　<a href='https://www.tp.edu.tw/neighbor/html/' target=_blank>鄰里學區查詢系統</a>
						      </td><td width="33%">
						  	    　<a href='http://www.tcooc.gov.taipei/News_Content.aspx?n=66C72C728AC83337&sms=CA5BD9955AAB3039&s=876DE0ECD56CE0D4' target=_blank>台北市商圈列表</a>
						      </td>
	</tr>
</table>
                        </td>
                        </tr>

                      <tr>
                        <td height="20" colspan="3"><img src="../images/R7_b020.gif" /><font color=#993333>【參考行情】</font>
                          <table id="DLP" cellspacing="0" border="0" width="100%">
	<tr>
		<td width="33%">
						  	    　<a href='Front/Member/NowPrice.asp?City=台北市&District=北投區&Road=' target=_blank>預售及中古屋行情分析</a>
						      </td><td width="33%">
						  	    　<a href='AuctionQueryResult.aspx?QueryType=201&ProBCity=台北市&ProBDistrict=北投區&ProBRoad=' target=_blank>法拍建物拍定行情</a>
						      </td><td width="33%">
						  	    　<a href='AuctionQueryResult.aspx?QueryType=202&ProLCity=台北市&ProLDistrict=北投區&ProLSec=文林' target=_blank>法拍土地拍定行情</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/images/TPECity.jpg' target=_blank>北市十大豪宅社區成交行情</a>
						      </td><td></td><td></td>
	</tr>
</table>
                        </td>
                        </tr>

                      <tr>
                        <td height="20" colspan="3"><img src="../images/R7_b020.gif" /><font color=#993333>【險惡設施】</font>
                          <table id="DLE" cellspacing="0" border="0" width="100%">
	<tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProNote.asp?Kind=1&City=台北市&District=北投區&Road=' target=_blank>本區輻射屋</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProNote.asp?Kind=2&City=台北市&District=北投區&Road=' target=_blank>本區海砂屋</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProNote.asp?Kind=3&City=台北市&District=北投區&Road=' target=_blank>本區危樓</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/ProNoteFD.asp?type=1&City=台北市&District=北投區&Road=' target=_blank>本區易積水區域</a>
						      </td><td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/ProNoteFDA2.asp?type=1&City=台北市&District=北投區&Road=' target=_blank>本區易淹水區域</a>
						      </td><td width="33%">
						  	    　<a href='http://media.lio.gov.taipei/map/' target=_blank>預售工地職災(兇宅)地圖</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://twd.water.gov.taipei/billing/ps_16/AA/AA19/AA1901.aspx' target=_blank>北水處自來水鉛管查詢</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProNoteEArea.asp?City=台北市&District=北投區&Road=' target=_blank>本區基地台</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProNoteERadio.asp?City=台北市&District=北投區&Road=' target=_blank>本區廣播電塔(電磁波)</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProTPLane.asp' target=_blank>台北市救災不易窄巷</a>
						      </td><td></td><td></td>
	</tr>
</table>
                        </td>
                        </tr>

                      <tr>
                        <td height="20" colspan="3"><img src="../images/R7_b020.gif" /><font color=#993333>【環境防災】</font>
                          <table id="DLN" cellspacing="0" border="0" width="100%">
	<tr>
		<td width="33%">
						  	    　<a href='https://www.54168.com.tw/Auction2010/LHouseService/Front/Member/ProTLand.asp' target=_blank>土壤液化區域一覽表</a>
						      </td><td width="33%">
						  	    　<a href='http://streethouse.ncree.narl.org.tw/form04.aspx' target=_blank>街屋耐震查詢</a>
						      </td><td width="33%">
						  	    　<a href='http://www.tldep.taipei.gov.tw/GAMMA/' target=_blank>幅射即時監測</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://wq.epa.gov.tw/Code/Default.aspx?Water=River' target=_blank>環境水質監測查詢</a>
						      </td><td width="33%">
						  	    　<a href='http://aqicn.org/city/taiwan/shihlin/' target=_blank>空氣品質即時監測</a>
						      </td><td width="33%">
						  	    　<a href='http://www.cwb.gov.tw/V7/earthquake/quake_index.htm' target=_blank>地震消息</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://fault.moeacgs.gov.tw/MgFault/Home/pageMap?LFun=1' target=_blank>台灣活動斷層查詢</a>
						      </td><td width="33%">
						  	    　<a href='http://gis.moeacgs.gov.tw/gwh/gsb97-1/sys_2014b/' target=_blank>地質敏感區查詢</a>
						      </td><td width="33%">
						  	    　<a href='http://www.google.org/crisismap/taiwan' target=_blank>台灣防災地圖</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://satis.ncdr.nat.gov.tw/Dmap/102Catalog-CountyTown.aspx' target=_blank>災害潛勢查詢</a>
						      </td><td width="33%">
						  	    　<a href='../images/20151226.jpg' target=_blank>北市山坡地違規潛勢圖</a>
						      </td><td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/Solifluction.asp?type=1' target=_blank>北市土石流防災避難地圖</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProNoteHo.asp' target=_blank>北市急救責任醫院</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/datataipei/pronotewaterm.asp' target=_blank>北市水質監測</a>
						      </td><td width="33%">
						  	    　<a href='https://www.liquid.net.tw/CGS/Web/Map.aspx' target=_blank>全省土壤液化區查詢</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProPArea.asp?City=台北市&District=北投區&Road=' target=_blank>北市各區里疏散避難圖</a>
						      </td><td width="33%">
						  	    　<a href='https://nidss.cdc.gov.tw/ch/NIDSS_DiseaseMap.aspx?dc=1&dt' target=_blank>鄰近傳染病查詢</a>
						      </td><td width="33%">
						  	    　<a href='https://www.liquid.net.tw/Taipei/Main/pages/index.html' target=_blank>北市土壤液化潛勢查詢系統</a>
						      </td>
	</tr>
</table>
                        </td>
                        </tr>

                      <tr>
                        <td height="20" colspan="3"><img src="../images/R7_b020.gif" /><font color=#993333>【法規影音】</font>
                          <table id="DLV" cellspacing="0" border="0" width="100%">
	<tr>
		<td width="33%">
						  	    　<a href='Front/Member/CityPlan/CP_CityPlan.asp?City=台北市&District=北投區&Road=' target=_blank>都市計劃</a>
						      </td><td width="33%">
						  	    　<a href='cityplanlink.asp?City=台北市&District=北投區&Road=' target=_blank>本區都市計劃</a>
						      </td><td width="33%">
						  	    　<a href='https://www.youtube.com/watch?v=_s4xhGMAeYc' target=_blank>台北捷運簡介</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='https://www.youtube.com/watch?v=hDOK0vtMcws' target=_blank>高鐵簡介</a>
						      </td><td width="33%">
						  	    　<a href='https://www.youtube.com/watch?v=npGJJubOAIA' target=_blank>台北形象廣告</a>
						      </td><td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/UdnTV.asp' target=_blank>輕鬆入門法拍屋</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://www.54168.com.tw/Auction2010/LhouseService/Front/RedevelopmentPDF_2010.asp' target=_blank>北市劃定更新範圍圖(PDF)</a>
						      </td><td width="33%">
						  	    　<a href='http://www.54168.com.tw/Auction2010/LhouseService/Front/Redevelopment2018.asp' target=_blank>2018北市公辦都更區</a>
						      </td><td></td>
	</tr>
</table>
                        </td>
                        </tr>

                      <tr>
                        <td height="20" colspan="3"><img src="../images/R7_b020.gif" /><font color=#993333>【其它】</font>
                          <table id="DLO" cellspacing="0" border="0" width="100%">
	<tr>
		<td width="33%">
						  	    　<a href='Front/Member/MetroVicinity/MV_Mrt.asp?City=台北市&District=北投區&Road=' target=_blank>捷運商圈法拍屋</a>
						      </td><td width="33%">
						  	    　<a href='NewsQuery.aspx' target=_blank>相關新聞</a>
						      </td><td width="33%">
						  	    　<a href='http://qservice.dba.tcg.gov.tw/squatter/squ_dlg.asp' target=_blank>違建案件查詢</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://www.zone.taipei.gov.tw/new_showmapMain.aspx' target=_blank>本縣市都計套繪查詢</a>
						      </td><td width="33%">
						  	    　<a href='http://www.lhouse.com.tw/Member/ProRentPrice.asp?type=1&City=台北市&District=北投區&Road=' target=_blank>本區房屋租金上限</a>
						      </td><td width="33%">
						  	    　<a href='http://service.moj.gov.tw/criminal/' target=_blank>重大刑案通緝犯資料查詢</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='https://www.esunbank.com.tw/bank/personal/loan/tools/info/loan-calculation' target=_blank>貸款試算</a>
						      </td><td width="33%">
						  	    　<a href='http://1968.freeway.gov.tw/' target=_blank>即時路況資訊(含影像)</a>
						      </td><td width="33%">
						  	    　<a href='https://house.yahoo.com.tw/' target=_blank>地產專欄</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='https://house.yahoo.com.tw/list/%E5%B0%88%E5%AE%B6%E5%B0%88%E8%A8%AA' target=_blank>名人談房產</a>
						      </td><td width="33%">
						  	    　<a href='https://yahoo.house108.com.tw/' target=_blank>居家裝潢</a>
						      </td><td width="33%">
						  	    　<a href='http://yahoo.twproperty.com.tw/' target=_blank>找新屋</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='http://easymap.land.moi.gov.tw/P02/' target=_blank>其它土地定位</a>
						      </td><td width="33%">
						  	    　<a href='../images/realprice6.jpg' target=_blank>全台實價登錄豪宅</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProBackBuilding.asp?City=台北市&District=北投區&Road=' target=_blank>台北市違建名單</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProNoteSteal.asp?City=台北市&District=北投區&Road=' target=_blank>本區竊案熱點</a>
						      </td><td width="33%">
						  	    　<a href='http://www.myhousing.com.tw/index.php' target=_blank>預售及新屋查詢</a>
						      </td><td width="33%">
						  	    　<a href='http://lvr.land.moi.gov.tw/homePage.action' target=_blank>內政部實價登錄查詢</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='../images/20151225.jpg' target=_blank>刑案及破案數統計表</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProNoteTF.asp' target=_blank>北市工廠登記分佈</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProNoteGas.asp' target=_blank>北市天然氣承裝點</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/ProNoteGasL.asp' target=_blank>北市桶裝瓦斯行</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/datataipei/pronotegass.asp' target=_blank>加油站及加氣站分佈點</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/datataipei/pronoteill.asp?City=%e5%8f%b0%e5%8c%97%e5%b8%82&District=%e5%8c%97%e6%8a%95%e5%8d%80&Road=' target=_blank>北市頂樓違建</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/PDF/20170106_自力更新與公辦都更的推動構想.pdf' target=_blank>自力更新與公辦都更推動(PDF)</a>
						      </td><td width="33%">
						  	    　<a href='https://www.land.moi.gov.tw/chhtml/content.asp?cid=1505' target=_blank>新舊地號查詢</a>
						      </td><td width="33%">
						  	    　<a href='0' target=_blank>台北市危老重建問答集(PDF)</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='Front/Member/PDF/20190308_台北市危老重建問答集(108年2月版).PDF' target=_blank>北市危老重建問答集(PDF)</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProTPOldList.asp' target=_blank>北市公辦都更案簡介(PDF)</a>
						      </td><td width="33%">
						  	    　<a href='Front/Member/ProTPOldST.asp' target=_blank>北市危老重建工作站</a>
						      </td>
	</tr><tr>
		<td width="33%">
						  	    　<a href='https://develop.land.moi.gov.tw/Normal?ssl=true' target=_blank>重劃土地查詢</a>
						      </td><td></td><td></td>
	</tr>
</table>
                        </td></tr>
<!--                        
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='http://www.lhouse.com.tw/Member/2010iprice.asp' target=_blank >國際不動產價格指數</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='http://www.lhouse.com.tw/Member/2010istock.asp' target=_blank >國際不動產證券化指數</a></td>                      
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="NewsCuted" href="JavaScript:NewsCutedQuery();">近期行情查詢</a></td>
                      </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="UnLucky" href="JavaScript:UnLuckyQuery();">兇宅相關資訊</a></td>                      
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="AuctionRoadLink" href="JavaScript:AuctionRoadQuery();">法拍道路地</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="ChangeCity" href="JavaScript:ChangeCity('8');">本區都更範圍圖</a></td>                        
                      </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='http://www.youtube.com/watch?v=RkaNE7c9Hjw' target="_blank">寬頻榮耀</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='http://satis.ncdr.nat.gov.tw/Dmap/102Catalog-CountyTown.aspx' target="_blank">災害潛勢地圖</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='http://lvr.land.moi.gov.tw/' target="_blank">內政部實價登錄查詢</a></td>
                      </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="link_transaction" href="http://price.591.com.tw" target="_blank">民間版實價登錄查詢</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='http://www.google.org/crisismap/taiwan' target="_blank">台灣防災地圖</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a id="UseAreaLink" href="http://www.54168.com.tw/portal/wms/Default.aspx?ProSerial=2263257&amp;ProCourt=201&amp;Address=%e5%8f%b0%e5%8c%97%e5%b8%82%e5%8c%97%e6%8a%95%e5%8d%80%e6%96%87%e6%9e%97%e6%ae%b5%e5%9b%9b%e5%b0%8f%e6%ae%b573-35%e5%9c%b0%e8%99%9f" target="_blank">使用分區套繪</a></td>
                      </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7" /> <a href='http://www.youbike.com.tw/info.php' target="_blank">微笑單車Youbike</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7" /> <a id="link_similar" href="JavaScript:SimilarQuery();">相似物件</a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='JavaScript:ExtraInfo(47);'>出門看屋天氣</a></td>
                      </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7" /> <a href='http://www.lhouse.com.tw/Member/TSCC.asp' target="_blank"><font color="red">台中容移辦法</font></a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='JavaScript:ExtraInfo(48);'><font color="red">台北市違建名單</font></a>(<a href='JavaScript:ExtraInfoMap(20);'>地圖版</a>)</td>
			<td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7"> <a href='http://gis.moeacgs.gov.tw/gwh/gsb97-1/sys_2014b/'><font color="red">地質敏感區查詢</font></a></td>
                      </tr>
                      <tr>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7" /> <a href='1040605台中市區域計畫簡報.pdf' target="_blank"><font color="red">台中市區域計劃簡報</font></a></td>
                        <td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7" /> <a href='http://www.myhousing.com.tw/index.php' target="_blank"><font color="red">預售及新屋查詢</font></a></td>
			<td width="33%" height="20"><img src="../Images/R7_b02.gif" width="8" height="7" /> <a href='http://fault.moeacgs.gov.tw/MgFault/Home/pageMap?LFun=1' target="_blank"><font color="red">台灣活動斷層查詢</font></a></td>
                      </tr>
-->
                    </table>
				</td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>相關影像</b><br /><span class="fontSize13">【<a href="JavaScript:ShowHideTable(ImageInfo);">顯示/隱藏</a>】</span></td></tr>
					</table>
				</td>
				<td>
                    <table id="ImageInfo" width="100%"  border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td width="100%">
                                <table width="100%"  border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <!-- <td width="20%" height="20"><span class="style4"><img src="../Images/R7_b02.gif" width="8" height="7"><a id="Map"><b><font color="#990000">電子地圖</font></b></a></span></td> -->
                                        <td width="20%" height="20"><span class="style4"><img src="../Images/R7_b02.gif" width="8" height="7">
						<span id="tst"><iframe name="TST" id="TST" src="https://pic.54168.com.tw/ts/tst.asp?ProID=109助子131&ProSerial=2263257&ProCourt=201&ProDep=子&CourtNick=士林行執處" frameborder="0" width="100" scrolling="no" height="21"></iframe></span></span></td>
                                        <!--<td width="20%" height="20"><span class="style4"><img src="../Images/R7_b02.gif" width="8" height="7"><a id="Video">週邊路況影像</a></span></td>-->
                                        <td width="20%" height="20"><span class="style4"><img src="../Images/R7_b02.gif" width="8" height="7"><a id="PicRelate"><b><font color="#990000">相關照片</font></b></a></span></td>
                                        <!-- 20171103 <td width="20%" height="20"><span class="style4"><img src="../Images/R7_b02.gif" width="8" height="7"><a id="GMap"><b><font color="#990000">空照圖</font></b></a></span></td> -->
                                        <td width="20%" height="20"><span class="style4"><span id="NPic"></span></span></td>
                                        <td width="20%" height="20"><span class="style4"><span id="LPic"><iframe name="LPic" id="LPic" src="https://pic.54168.com.tw/LPic/jointlistl.asp?lcity=台北市&ldis=北投區&ladd=台北市北投區文林段四小段73-35地號" frameborder="0" marginheight="0" marginwidth="0" width="150" scrolling="no" height="30"></iframe></span></span></td>
					<td width="20%" height="20"><span class="style4"><span id="SPic"><iframe name="SPic" id="SPic" src="spic.asp?add=台北市北投區文林段四小段73-35地號" frameborder="0" marginheight="5" marginwidth="5" width="150" scrolling="no" height="30"></iframe></span></span></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <table width="100%"  border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td width="33%">
                                            <div align="center">
                                                                    
                                                                      
                                            </div>
                                        </td>
                                        <td width="33%">
                                            <div align="center">
                                                 
                                                <span id="PicShow"></span> 
                                                <input name="Pic1Serial" type="hidden" id="Pic1Serial" />                                        
                                            </div>
                                        </td>
                                        <td width="33%">
                                            <div align="center">
                                                  
                                                <input name="Pic2Serial" type="hidden" id="Pic2Serial" />                                      
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan=3>
                                             
                                             
                                            
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
				</td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>Google地圖</b><br /><span class="fontSize13">【<a href="JavaScript:ShowHideTable(googlemap);">顯示/隱藏</a>】</span></td></tr>
					</table>
				</td>
				<td>
				<table id="googlemap" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                <td>   
				<span id="GGMap"><a href="https://www.google.com/maps/place/25.11403,121.5146" target=_blank><img src="../images/g-icon.jpg" border=0></a><br>　<img src="https://pic.54168.com.tw/ShowG.asp?ProLCity=台北市&ProLDistrict=北投區&Address=文林段四小段73-35地號&kml=n" width="300" height="220" /><br /><iframe src="https://pic.54168.com.tw/ShowH.asp?ProLCity=台北市&ProLDistrict=北投區&Address=文林段四小段73-35地號&kml=n" frameborder="0" width="200" scrolling="no" height="40"></iframe></span>
				</td></tr></table>
				</td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>列管備註</b><br /><span class="fontSize13">【<a href="JavaScript:ShowHideTable(TraceInfo);">顯示/隱藏</a>】</span></td></tr>
					</table>
				</td>
				<td>
                    <table id="TraceInfo" width="100%"  border="0" cellspacing="0" cellpadding="0">
                      <tr>
                        <td width="12%">
                            <input type="button" value="新增案件追蹤備註" onClick=javascript:TraceNote('201','109助子131');>
                        </td>
                        <td width="88%">
				          <table width="100%"  border="0" cellspacing="0" cellpadding="0">
                            <tr>
                              <td height="20"><span class="style4"><span id="TraceNote"></span></span></td>                      
                            </tr>                    
                        </table></td>
                      </tr>
                        
                    </table>
				</td>
			</tr>
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>案件評價</b><br /><span class="fontSize13">【<a href="JavaScript:ShowHideTable(RatingInfo);">顯示/隱藏</a>】</span></td></tr>
					</table>
				</td>
				<td valign="middle">
    <table id="RatingInfo" width="100%" border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td width="95">
                <div id="Rating1" class="ratingStar">
	<input type="hidden" name="Rating1_RatingExtender_ClientState" id="Rating1_RatingExtender_ClientState" />
                <a href="javascript:void(0)" id="Rating1_A" title="0" style="text-decoration:none"><span id="Rating1_Star_1" class="ratingItem Empty">&nbsp;</span><span id="Rating1_Star_2" class="ratingItem Empty">&nbsp;</span><span id="Rating1_Star_3" class="ratingItem Empty">&nbsp;</span><span id="Rating1_Star_4" class="ratingItem Empty">&nbsp;</span><span id="Rating1_Star_5" class="ratingItem Empty">&nbsp;</span></a>
</div>
            </td>
            <td width="75">
                <span id="labelValue1" class="NumberStyle"><font color="#CC0033">[無人評價]</font></span>
            </td>
            <td height="45" valign="middle">
            <iframe name='RatingFrame' src='Rating.aspx?ProSerial=2263257&ProID=109助子131&ProCourt=201' frameborder='0' width='430' scrolling='no' height='100%'></iframe>
            </td>
            <td>
                <!--<input type="submit" name="Ratingbtn" value="我要評價" id="Ratingbtn" />-->
                <a id="RatingLink" title="本月排名" href="javascript: void(window.open('/Auction2010/LHouseService/ratingreport.aspx','ratingwin','width=480,height=360'));">本月排名</a>                
            </td>
        </tr>
    </table>
    
				</td>
			</tr>
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>訪談記錄</b><br /><span class="fontSize13">【<a href="JavaScript:ShowHideTable(MSNInfo);">顯示/隱藏</a>】</span></td></tr>
					</table>
				</td>
				<td>
                    <table id="MSNInfo" width="100%" border="0">
                        <tr><td>
<!-- 20170110 訪談記錄 -->

			</td>
<!-- 案件提報 20130520 -->
<td width=20% align=center><iframe name="SPro" src="spro\spro.asp?pros=2263257" frameborder="0" width="80" scrolling="no" height="30"></iframe>
                        </td></tr>
                    </table>   
				</td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>						
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><b>分店資訊</b><br /></td></tr>
					</table>
				</td>
				<td><span id="pcompany"><a href="http://goo.gl/2t5qmA" target=_blank>寬頻房訊留言板</a>:有任何問題皆可留言告知，謝謝!!<br>住址:台北市士林區天母北路33號<br>電話:(02)2871-1001　傳真:(02)2871-1025　手機:</span></td>
			</tr>
			<tr class="DetailLineSpace"><td width="100"></td><td ></td></tr>
            <div id="UpdatePanel1">
	 
			<tr>
				<td width="100"  class="SETitle2_1">
					<table class="SEInnerTbl">
						<tr class="SETitle1" ><td></td></tr>
						<tr class="SETitle2_1"><td><span class="fontSize12">【<a id="MovieButton" href="javascript:__doPostBack('MovieButton','')">顯示/隱藏</a>】</span></td></tr>
					</table>
				</td>
				<td>                    							           				                       
				    <div id="MoviePanel">
		             
                    <div id="MoveMediaInfo" name="MoveMediaInfo">
                                    <table width="100%" cellpadding="2" cellspacing="0">
                                        <tr>
<!-- 20200108 VILLA婚禮影片&公司簡介 -->
					    <td>
                                                <!-- <iframe width="160" height="150" src="//www.youtube.com/embed/WSRkXvAbltc?feature=player_detailpage" frameborder="0" allowfullscreen></iframe> -->
						<iframe src="https://player.vimeo.com/video/376289703" width="220" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>
						<br>
						<iframe width="220" height="140" src="https://www.youtube.com/embed/RkaNE7c9Hjw" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                            </td>

<!-- 20130730 暫時下架                                            <td>
                                                <iframe width="170" height="160" src="https://www.youtube.com/embed/RkaNE7c9Hjw?rel=0"
                                                    frameborder="0"></iframe>
                                            </td> -->
                                            <td>
<!-- 20200108 2020年尾牙影片 -->
                                                <!-- <iframe width="160" height="150" src="//www.youtube.com/embed/GM0EjiENX0s?feature=player_detailpage"
                                                    frameborder="0"></iframe> -->
                                            </td>
                                            <td width=140>
                                                <div id="Panel1">
			
                                                    <span id="SalesAD"></span>
                                                
		</div>
                                            </td>
                                            <td><br><br>
                                                <iframe id="Iframe1" frameborder="0" width="100%" height="100%" marginheight="0" marginwidth="0" scrolling="no" src="/Auction2010/UC/DetailAD.aspx?City=%a5x%a5_%a5%ab&amp;District=%a5_%a7%eb%b0%cf"></iframe>
                                            </td>
                                            <td><br><br>
                                            	 
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                    
	</div>                                        
				</td>
			</tr>                     
            <tr>
                            <td width="100" class="SETitle2_1">
                                <table class="SEInnerTbl">
                                    <tr class="SETitle1">
                                        <td>
                                        </td>
                                    </tr>
                                    <tr class="SETitle2_1">
                                        <td>
                                            <b>買賣即時通</b><br />
                                            <span class="fontSize12">【<a href="JavaScript:ShowHideTable(NeedMsgInfo);">顯示/隱藏</a>】</span>
                                            <br />
                                            <br />
                                            <a href="http://www.54168.com.tw/lhouseutil/mobile/login.aspx" target="_blank">買賣單手機版</a><br />
                                            <a href="http://www.54168.com.tw/lhouseutil/cp/login.aspx" target="_blank">買賣單電腦版</a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <div id="NeedMsgPanel">
		
                                <div id="NeedMsgInfo" name="NeedMsgInfo">
                                    <iframe id="Iframe3" frameborder="0" width="100%" height="200" marginheight="0" marginwidth="0" scrolling="no" src="/lhouseutil/cp/Marquee.aspx"></iframe>
                                </div>
                                
	</div>   
                            </td>
                        </tr>
                        
</div>   

		</table>
	</td>
</tr>
<tr>
	<td>
	    <input name="ShowHideLawDeclare" type="button" onclick='javascript:ShowHideTable(LawDeclare);' value="顯示 / 隱藏 法律宣告">
		<!-- 上方文字說明 -->
		<table width="100%" id="LawDeclare">
			<tr >
				<td rowspan="2" width="70%" align="left" >
				    <img src="../images/R7_b020.gif" />為配合新版個資法規定，自即日起隱藏部份個人資料，不便之處敬請見諒！依個人資料保護法第2條及第20條規定辦理
				    <br />
                    <span style="color:#993333;font-size:13px;font-family:微軟正黑體, 新細明體, 細明體;font-weight:bold;">
                        <!-- <img src="../images/R7_b020.gif" />【寬頻房訊】本資料經校對法院公告，並申調謄本整理而成，若有遺誤，概以法院公告為主【版權所有】 -->
                        <img src="../images/R7_b020.gif" />【寬頻房訊】本資料業經校對法院、行執處及其他標售公告，並整理而成，投標前請向該股或標售單位確認，若有遺誤，概以法院、行執處及其他標售公告為主。【版權所有】
                        &nbsp; &nbsp; &nbsp; &nbsp; <a title="訂閱電子報" target=_blank href="https://docs.google.com/spreadsheet/viewform?formkey=cjQ1RTVvMVZVQnh2OExwcDlGWXZob2c6MA..#gid=0">
                                訂閱電子報
                                </a>
                    </span>
                    <span id="CourtState"><br><font color=green size=2><img src='../images/R7_b020.gif' />本案件為法務部行政執行署案件，請投標人勿到法院投標，詳細地址請按此<a href=JavaScript:cstate();>法務部行政執行署管轄轄區</a></font></span>
				</td>
			</tr>
		</table>
	</td>
</tr>
</table>
<!-- 聯播廣告 -->
<div id="BottomBannerPanel">
	
	<!--20210415拿掉BYEBYE字樣 應該是無效JS檔了 <script type="text/javascript" src="//adsense.scupio.com/adpinline/ADmediaJS/lhouse1_2304_5990_6656_1.js"></script> -->

</div>
<!--FlowAD-->
<div id="FlowBannerPanel">
	
<div id="abgne_float_ad"> 
         <!-- <span class="abgne_close_ad">關閉 [X]</span>
 <a href="http://www.youtube.com/embed/xgiNn9kjYh0" target="_blank"><img src="/UploadService/Banner/20140627171038149.gif" /></a><br /><br />
    <a href="https://www.youtube.com/embed/iXUay7sudQQ" target="_blank"><img src="/UploadService/Banner/100.jpg" /></a>-->
</div>

</div>
<!-- ---------------------------------------------------------------------------------------------------------------------------------- -->
<img id="GetPropertyRecord" src="" height="0" width="0" border="0" />
<!-- ---------------------------------------------------------------------------------------------------------------------------------- -->
<!-- 聯播廣告 -->
<div id='processBar' name='processBar' style='display:none;' ></div>


<script type="text/javascript">
//<![CDATA[
Sys.Application.initialize();
Sys.Application.add_init(function() {
    $create(AjaxControlToolkit.RatingBehavior, {"AutoPostBack":false,"CallbackID":"Rating1","ClientStateFieldID":"Rating1_RatingExtender_ClientState","EmptyStarCssClass":"Empty","FilledStarCssClass":"Filled","ReadOnly":true,"StarCssClass":"ratingItem","WaitingStarCssClass":"Saved","id":"Rating1_RatingExtender"}, null, null, $get("Rating1"));
});
//]]>
</script>
</form>
</center>		
<iframe name='IframeTransSession' id='IframeTransSession' src='/Auction2010/LHouseService/TransferASPSession/Transfer_Session.aspx?EventType=1&TargetURL=/Auction2010/LHouseService/TransferASPSession/Transfer_Session.asp' style='width:0px;height:0px;display:none;'></iframe>		
</body>

</html>



<script type="text/javascript">
    //alert(opener.document.URL);
    //alert(opener.top.location);
</script>
<script type="text/javascript">

    var _gaq = _gaq || [];
    _gaq.push(['_setAccount', 'UA-********-4']);
    _gaq.push(['_trackPageview']);

    (function() {
        var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
    })();

</script>

<script src="../javaScripts/AjaxModule/MyAjaxFramework.js"></script>
<script type="text/javascript">
    CallDetectKeyWords("KeyWords_Deposition", null, null);

    function CallDetectKeyWords(tarName, barName, btn) {
        var serviceUrl = "AjaxService/getKeyWords.aspx?category=deposition";
        //Start to Call Ajax
        var callback = function(xmlhttp, tarName, btn) {
            var updateUi = function(xmlHttp, tarName, btn) {    //定義要呼叫的介面更新方法
                //更換此方法即可
                updateKeywords(xmlHttp, tarName, btn);               
            };
            updatePage(xmlhttp, tarName, btn, updateUi);     //在回呼函數中呼叫Tab面板畫面資訊更新函數
        };
        var progressbar = function(n, barName) {
            updataProgressBar(n, barName);                 //在回呼函數中呼叫畫面資訊進度更新函數
        };
        //requestAjax(serviceUrl, callback, null, null, null, null, null);              //呼叫已封裝的非同步請求函數(沒有更新區域)(沒有進度)(沒有觸發元件,可能是timer)
        //requestAjax(serviceUrl, callback, tarName, null, null, null, null);           //呼叫已封裝的非同步請求函數(沒有進度)(沒有觸發元件,可能是timer)
        //requestAjax(serviceUrl, callback, tarName, null, null, null, null);           //呼叫已封裝的非同步請求函數(沒有進度)(沒有觸發元件,可能是timer)
        //requestAjax(serviceUrl, callback, tarName, progressbar, barName, null, null); //呼叫已封裝的非同步請求函數(有進度)(沒有觸發元件,可能是timer)
        //requestAjax(serviceUrl, callback, tarName, progressbar, barName, null, btn);    //呼叫已封裝的非同步請求函數(更新區域名稱,有進度及進度名稱)(有觸發元件)
        requestAjax(serviceUrl, callback, tarName, progressbar, 'processBar', null, null); //呼叫已封裝的非同步請求函數(有進度)(沒有觸發元件,可能是timer)        
    }
    
    //  功能 : for CHP 更新畫面的數質
    //  參數 : tarName代表要更新的區域[可能為陣列] ,data要更新的資料
    //傳回值 : UI更新
    function updateKeywords(xmlHttp, tarName, btn) {        
        var out = setTimeout(function() {
            var data = xmlHttp.responseXML;      //獲得伺服器端回應的資料(Xml,Text,Header等等)
            //有更新區域才需要判斷data存不存在
            if (data) {
                //alert(data);
                //根據參數傳送的Name或ID,決定要插入資訊的容器
                if (tarName) {            
                    var display = document.getElementById(tarName);

                    var aKeywordInfoS = data.documentElement.getElementsByTagName("KeyWordInfoS");
                    
                    
                    //replace all keywords
                    var newText = display.innerHTML;

                    var keyword, description, url;
                    
                    //for loop
                    for (var i = 0; i < aKeywordInfoS.length; i++) {
                        var aKeyword = aKeywordInfoS[i].getElementsByTagName("KeyWord");
                        var aDescription = aKeywordInfoS[i].getElementsByTagName("Description");
                        var aUrl = aKeywordInfoS[i].getElementsByTagName("URL");

                        
                        if (aKeyword[0].childNodes.length > 0)
                            keyword = aKeyword[0].firstChild.nodeValue;
                        else
                            return;
                        
                        if (aDescription[0].childNodes.length > 0)
                            description = aDescription[0].firstChild.nodeValue;
                        else
                            description = "";
                            
                        if (aUrl[0].childNodes.length > 0)
                            url = aUrl[0].firstChild.nodeValue;
                        else
                            url = "";                    

                        //alert("KeyWord:" + keyword + "\r\nDescription:" + description + "\r\nUrl:" + url);                    

                        raRegExp = new RegExp(keyword, "g");                        
                        if (url != "")
                            newText = newText.replace(raRegExp, "<div id='" + i + "_SLICE' style='position:absolute; margin-top: 30px; margin-right: 30px; z-index: 1; display: none; border: 2px solid #CCCCCC; width: 250px; background: #FFE082 url(../images/bg_noticebox_yellow.png) repeat-x top;'>" + description + "</div><a href='" + url + "' class='css_keyWords' target='_blank' onmouseover=\"document.getElementById('" + i + "_SLICE').style.display='block'\" onmouseout=\"document.getElementById('" + i + "_SLICE').style.display='none'\">" + keyword + "</a><span style='font-size:9pt;color:black;vertical-align:sub;font-weight:Bold;'>?</span>");                        
                        else
                            newText = newText.replace(raRegExp, "<div id='" + i + "_SLICE' style='position:absolute; margin-top: 30px; margin-right: 30px; z-index: 1; display: none; border: 2px solid #CCCCCC; width: 250px; background: #FFE082 url(../images/bg_noticebox_yellow.png) repeat-x top;'>" + description + "</div><a class='css_keyWords' onmouseover=\"document.getElementById('" + i + "_SLICE').style.display='block'\" onmouseout=\"document.getElementById('" + i + "_SLICE').style.display='none'\">" + keyword + "</a><span style='font-size:9pt;color:black;vertical-align:sub;font-weight:Bold;'>?</span>");                       
                        //alert(newText);
                    }
                    
                    for (var i = 0; i < aKeywordInfoS.length; i++) {
                        var aKeyword = aKeywordInfoS[i].getElementsByTagName("KeyWord");
                        var aDescription = aKeywordInfoS[i].getElementsByTagName("Description");
                        var aUrl = aKeywordInfoS[i].getElementsByTagName("URL");


                        if (aKeyword[0].childNodes.length > 0)
                            keyword = ">" + aKeyword[0].firstChild.nodeValue;
                        else
                            return;

                        if (aDescription[0].childNodes.length > 0)
                            description = aDescription[0].firstChild.nodeValue;
                        else
                            description = "";

                        if (aUrl[0].childNodes.length > 0)
                            url = aUrl[0].firstChild.nodeValue;
                        else
                            url = "";                      
                        //alert(keyword + description + url);

                        raRegExp = new RegExp(keyword, "g");
                        //newText = newText.replace(raRegExp, " title='" + description + "'>" + keyword.substr(1, keyword.length));
                        //alert(newText);
                    }                
                    //show out
                    display.innerHTML = newText;
                }
            }
            else {
                //tarName為null,自行定義更新名稱 或者 不更新畫面
                //alert("Server has finished the request...");
                //alert("伺服器已處理完成,沒有更新畫面的需求");
            }
                
                
            clearTimeout(out);
        }, 2000);
    }

    //  功能 : for CHP 描繪進度表及顯示目前進度狀態
    //  參數 : n表示xmlHttp.readyState目前的值, barName代表要更新的區域
    //傳回值 : 進度或字串文字
    function updataProgressBar(n, barName) {
        //根據參數傳送的序號,決定要插入資訊的容器
        var info = document.getElementById(barName);
        //設定ProgressBar的Stlye為顯示
        switch (n) {
            case 0:
                //還沒開始      0%
                info.innerHTML = "請稍後...0%";
                info.style.display = "";
                break;
            case 1:
                //讀取中        25%
                info.innerHTML = "請稍後...25%";
                info.style.display = "";
                break;
            case 2:
                //已讀取        50%
                info.innerHTML = "請稍後...50%";
                info.style.display = "";
                break;
            case 3:
                //資訊交換中    75%
                info.innerHTML = "請稍後...75%";
                info.style.display = "";
                break;
            case 4:
                //一切完成      100%
                info.innerHTML = "已完成...100%";
                //設定ProgressBar的Stlye為隱藏 [after 0.5 second]
                var out = setTimeout(function() {
                    info.style.display = "none";
                    clearTimeout(out);
                }, 1000);
                break;
        }
    }

    function createShowBox(name) {
    
        var ShowBoxFactories = [
            function() { return new document.getElementById(name) },
            function() { return new document.getElementByName(name) },
        ];
        
        var my_showboxtag = false;
        for (var i = 0; i < ShowBoxFactories.length; i++) {
            //嘗試呼叫工廠中的每一種定義函數,如果成功則傳回,不然就繼續下一種呼叫方法
            try {
                my_showboxtag = ShowBoxFactories[i]();
            }
            catch (e) {
                continue;       //發生意外,該函數不存在,繼續下一個函式呼叫
            }
            break;              //如果成功了,則中止for迴圈
        }

        if (!my_showboxtag) {
            my_showboxtag = document.createElement(name);
        }
        
        
    }
        
</script>
<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-24022475-4', 'auto');
  ga('send', 'pageview');

</script>