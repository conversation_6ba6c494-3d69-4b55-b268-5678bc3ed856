@extends('layouts.master')
@section('css')


@stop

@section('js')


@stop

@section('content')

    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">成功案例</h2>

    <main class="flex-grow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">


            <!-- 案例詳情 -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <!-- 案例標題 -->
                <div class="bg-red-600 text-white p-4">
                    <h2 class="text-2xl font-bold">{{ $data['title'] }}</h2>
                    <p class="text-sm mt-1">成交日期：{{ \Carbon\Carbon::parse($data['created_at'])->format('Y年m月d日') }}</p>
                </div>

                <!-- 案例內容 -->
                <div class="p-6">


                    <div class="gallery-thumbs">
                        @if ($data['field1'] != '')
                            <div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
                                <ol class="carousel-indicators">
                                    {{-- 反轉圖片順序 --}}
                                    @foreach (explode(',', $data['field1']) as $key => $item)
                                        <li data-target="#carouselExampleIndicators" data-slide-to="{{ $key }}"
                                            class="{{ $key == 0 ? 'active' : '' }}"></li>
                                    @endforeach
                                </ol>
                                <div class="carousel-inner">
                                    {{-- 反轉圖片順序 --}}
                                    @foreach (explode(',', $data['field1']) as $key => $item)
                                        <div class="carousel-item {{ $key == 0 ? 'active' : '' }}">

                                            <img src="{{ url('/') }}/images/success/{{ $item }}"
                                                alt="{{ $rs->title }}" class="d-block w-100">

                                        </div>
                                    @endforeach

                                </div>
                                <button class="carousel-control-prev" type="button"
                                    data-target="#carouselExampleIndicators" data-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="sr-only">Previous</span>
                                </button>
                                <button class="carousel-control-next" type="button"
                                    data-target="#carouselExampleIndicators" data-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="sr-only">Next</span>
                                </button>
                            </div>
                        @endif


                    </div>

                    <!-- 案例資訊 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8" style="font-size:20px">
                        <!-- 左側：基本資訊 -->
                        <div class="md:col-span-2 space-y-6">
                            <div>
                                <h3 class="text-xl font-bold mb-4">案例介紹</h3>
                                <p class="text-gray-700">
                                    {{ $data['field5'] }}
                                </p>
                            </div>

                            <div>
                                <h3 class="text-xl font-bold mb-4">房屋特色</h3>
                                <div class="flex flex-wrap">
                                    {{ $data['field5'] }}
                                </div>
                            </div>


                        </div>

                        <!-- 右側：詳細資訊與聯絡方式 -->

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-bold mb-4">成交資訊</h3>
                            <ul class="space-y-2">
                                <li class="flex justify-between">
                                    <span class="text-gray-600">市場行情：</span>
                                    <span class="font-medium">約
                                        {{ $data['field2'] }}
                                        萬元</span>
                                </li>
                                <li class="flex justify-between">
                                    <span class="text-gray-600">成交價格：</span>
                                    <span class="font-medium text-red-600">
                                        {{ $data['field3'] }}
                                        萬元</span>
                                </li>
                                <li class="flex justify-between">
                                    <span class="text-gray-600">節省金額：</span>
                                    <span class="font-medium text-red-600">約
                                        {{ $data['field4'] }}
                                        萬元</span>
                                </li>

                            </ul>
                        </div>

                        {{-- <div class="bg-blue-50 p-4 rounded-lg">
                            <h3 class="text-lg font-bold mb-4">專業經理人</h3>
                            <div class="flex items-center">
                                <div class="w-16 h-16 rounded-full overflow-hidden mr-4">
                                    <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/S__15507515.jpg-mYZ5ZZ60VSRyM6sPFBU7xVoJqT9uLj.jpeg"
                                        alt="王經理照片" class="w-full h-full object-cover">
                                </div>
                                <div>
                                    <p class="font-bold">王經理</p>
                                    <p class="text-sm text-gray-600">法拍屋省錢專家</p>
                                    <p class="text-sm mt-1">
                                        <i class="fas fa-phone-alt text-blue-600 mr-1"></i>
                                        <a href="tel:0912-345-678" class="text-blue-600 hover:underline">0912-345-678</a>
                                    </p>
                                </div>
                            </div>
                        </div> --}}
                    </div>
                </div>

                <!-- 客戶見證 -->
                <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                    <h3 class="text-xl font-bold mb-4">客戶見證</h3>
                    <div class="flex items-start">
                        {{-- <div class="w-16 h-16 rounded-full overflow-hidden mr-4 flex-shrink-0">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="林先生照片"
                                class="w-full h-full object-cover">
                        </div> --}}
                        <div>
                            <p class="italic text-gray-700">
                                {!! $data['body'] !!}
                            </p>
                            {{-- <p class="text-right mt-2 text-gray-600 font-medium">- 林先生・台北市</p> --}}
                        </div>
                    </div>
                </div>
            </div>
            <!-- 相關案例 - 輪播展示 -->
            <div class="mt-12">
                <h3 class="text-2xl font-bold text-red-600 mb-6">相關案例</h3>

                <div class="related-carousel relative">


                    <!-- 輪播內容 -->
                    <div class="related-carousel-inner" id="related-carousel-container">
                        <!-- 第一頁 - 顯示4個案例 -->
                        <div class="related-carousel-item w-full">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <!-- 相關案例 1 -->
                                @foreach (DB::table('board')->selectRaw('id,title,field3,field4,memo,field1')->where('kind', 'success')->orderby('boardsort')->limit(4)->get() as $key => $rs)
                                    <div class="related-case-card" style="font-size: 20px">

                                        <a href="{{ url('/') }}/success/show/{{ $rs->id }}" class="block">


                                            @if ($rs->field1 != '')
                                                <a href="{{ url('/') }}/success/show/{{ $rs->id }}"
                                                    title="{{ $rs->title }}">
                                                    {{ Html::myUIImage([
                                                        'folder' => 'https://www.ebayhouse.com.tw/images/success',
                                                        'filename' => $rs->field1,
                                                        'alt' => $rs->title,
                                                        'width' => 300,
                                                        'height' => 300,
                                                        'noimg' => 'no-picture.gif',
                                                        'class' => 'related-case-image',
                                                    ]) }}
                                                </a>
                                            @endif

                                            <div class="p-4">
                                                <h4 class="font-bold" style="font-size: 20px">{{ $rs->title }}</h4>
                                                <p class="text-sm text-gray-600 mt-1" style="font-size: 20px">
                                                    {{ $rs->memo }}</p>
                                                <p class="text-sm text-gray-700 mt-2" style="font-size: 20px">
                                                    成交價格：{{ $rs->field3 }}
                                                    萬元<BR>
                                                    節省金額：約{{ $rs->field4 }}
                                                    萬元</p>
                                            </div>
                                        </a>
                                    </div>
                                @endforeach

                            </div>
                        </div>

                    </div>

                    <!-- 輪播指示器 -->
                    <div class="related-carousel-indicators mt-4" id="related-carousel-indicators">
                        <div class="related-carousel-indicator active" data-index="0"></div>
                        <div class="related-carousel-indicator" data-index="1"></div>
                    </div>
                </div>
            </div>
        </div>




    </main>


@stop
