---
description: swagger
globs:
alwaysApply: false
---
# Swagger 註解生成指南

本指南用於生成符合 OpenAPI 3.0 規範的 Swagger 註解，專為 PHP Laravel 控制器中的 API 端點設計。生成的註解將遵循特定風格，確保結構清晰、語法正確，並包含必要的描述和範例。

## 通用規範

### 基本結構
- 使用 `@OA\` 標記生成 OpenAPI 註解，嵌入 PHP 文件註解格式 (`/** ... */`)
- 每個 API 端點必須包含以下屬性：
  - `path`: API 路徑，格式為 `/api/{目錄}/{功能}`
  - `security`: admin 目錄使用 Bearer 認證，設置為 `security={{"bearerAuth":{}}}`
  - `tags`: 依模組名稱決定顯示名稱，格式為 `{模組}/{功能}`：
    - 模組為 `admin` → 顯示為「後台」
    - 模組為 `membercenter` → 顯示為「會員中心」
    - 其他模組 → 顯示為「前台」
  - `summary`: 簡短描述 API 功能（如「列表」、「新增/編輯」、「刪除」）
  - `description`: 提供額外功能說明（如「編號有值代表編輯,沒有代表新增」）

### 回應結構
- 必須包含 `@OA\Response`，狀態碼為 200，描述為「回覆」
- 回應內容為 JSON 格式，包含以下屬性：
  - `resultcode`: 整數，表示訊息代碼（如 0 表示成功）
  - `resultmessage`: 字串，表示訊息內容（如「新增成功」）
  - `data`: 物件或陣列，根據功能返回具體資料結構

### 其他要求
- 使用中文描述欄位（如 `description="頁數"`、`"訊息代碼"`）
- 註解必須與程式碼中的請求參數、回應結構和資料模型一致

## 功能類型詳細規範

### 1. 列表功能 (index 方法)
- **HTTP 方法**: POST
- **路徑**: `/api/{模組}/{功能}`（如 `/api/admin/adminuser`）
- **請求體**:
  - 使用 `@OA\RequestBody` 定義，標記為 `required=true`
  - 格式為 JSON，使用 `@OA\JsonContent`
  - 必須包含以下欄位（使用 `@OA\Schema` 定義）：
    - `page`: 整數，描述為「頁數」，範例為 1
    - `pagesize`: 整數，描述為「筆數/頁」，範例為 10
    - `search`: 字串，描述為「搜尋」，範例為 ""
    - `sortname`: 字串，描述為「排序欄位」，範例為 ""
    - `sorttype`: 字串，描述為「排序方式」，範例為 "desc"（值為 asc 或 desc）
    - `searchstartdate`: 字串，描述為「開始時間」，格式為 YYYY-MM-DD，範例為 "2021-01-01"
    - `searchenddate`: 字串，描述為「結束時間」，格式為 YYYY-MM-DD，範例為 "2099-12-31"
- **回應**:
  - 使用 `@OA\Response` 定義，包含 `@OA\JsonContent`
  - `data` 為物件，包含：
    - `current_page`: 整數，描述為「目前頁數」
    - `total`: 整數，描述為「總頁數」
    - `data`: 陣列，使用 `@OA\Items` 定義，每筆資料引用模型 Schema（如 `ref="#/components/schemas/{模型}"`）
  - 可選：陣列中的 `@OA\Items` 可包含額外屬性，格式為 `property="名稱",type="類型",description="描述",example="範例"`

### 2. 新增/編輯功能 (store 方法)
- **HTTP 方法**: POST
- **路徑**: `/api/{模組}/{功能}/store`（如 `/api/admin/adminuser/store`）
- **請求體**:
  - 使用 `@OA\RequestBody` 定義，標記為 `required=true`
  - 格式為 JSON，使用 `@OA\JsonContent`
  - 當程式有使用 `$inputs = $request->all();` 時，引用模型 Schema（如 `ref="#/components/schemas/{模型}"`）
  - 包含以下欄位（根據程式碼中的驗證規則）：
    - 必填欄位：從 `\Validator::make` 的規則提取（如 account、name）
    - 可選欄位：標記為 nullable（如 email）
    - 特殊欄位：如密碼（password），在編輯時為可選（nullable），範例為 "min:8"
- **回應**:
  - 使用 `@OA\Response` 定義，包含 `@OA\JsonContent`
  - `data` 為物件，包含：
    - `id`: 整數，描述為「編號」，範例為 10101

### 3. 刪除功能 (destroy 方法)
- **HTTP 方法**: POST
- **路徑**: `/api/{模組}/{功能}/destroy`（如 `/api/admin/adminuser/destroy`）
- **請求體**:
  - 使用 `@OA\RequestBody` 定義，標記為 `required=true`
  - 格式為 JSON，使用 `@OA\JsonContent`
  - 包含以下欄位：
    - `del`: 整數或字串，描述為「要刪除的編號」，範例為 "1"，額外描述為「多筆中間用逗號」
- **回應**:
  - 使用 `@OA\Response` 定義，包含 `@OA\JsonContent`
  - 僅包含 `resultcode` 和 `resultmessage`，無 `data` 屬性
  - `resultmessage` 範例為「刪除成功」

## 程式碼風格與對應關係

- Swagger 註解必須與 PHP 程式碼緊密對應：
  - 請求參數：從 `$request->input()` 或 `\Validator::make` 的規則提取
  - 回應結構：從 `$this->jsondata` 或返回資料結構生成
  - 資料模型：假設存在對應 Schema（如 `ref="#/components/schemas/{模型}"`）
- 註解格式必須整齊，每行對齊，使用適當縮進
- 分頁功能必須包含 `current_page`、`total` 和 `data` 陣列
- 模型關聯（如 `with(['relation'])`）必須在 `@OA\Items` 中引用相關 Schema
- 特殊邏輯（如密碼加密、事務處理）必須反映在請求和回應結構中

## 實際應用

1. 請直接在原函式上方補上相關 Swagger 註解
2. 完成後使用 PowerShell 命令測試 API：
   ```powershell
   Invoke-RestMethod -Uri "https://allennb.com.tw:442/{專案目錄}/public/api/{路徑}"
   ```
3. 檢查回應中的 `resultcode`，確保為 0（表示成功）
4. 如有錯誤，修正註解並重新測試，直到 `resultcode` 返回 0

## 範例

為以下控制器方法生成 Swagger 註解：
- 模組: product
- 功能: product
- 方法:
  - index: 列表功能
  - store: 新增/編輯功能
  - destroy: 刪除功能

確保路徑為 `/api/product/product`、`/api/product/product/store`、`/api/product/product/destroy`，標籤為 `tags={"後台/產品"}`，並且請求和回應結構與程式碼邏輯一致。