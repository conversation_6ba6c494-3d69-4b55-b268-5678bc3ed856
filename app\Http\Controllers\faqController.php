<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Config;
use PF;
use App\Repositories\boardRepository;

class faqController extends Controller
{
    private $data;

    public function __construct(boardRepository $boardRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->boardRepo = $boardRepo;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        Config::set('config.title', '常見問題 | '.config('config.title'));
        Config::set('config.keyword', '');
        Config::set('config.description', '');
        $rows = $this->boardRepo->selectRaw('*');
        $rows->myWhere('kind|S', 'faq', 'boardid', 'Y');
        $rows->orderByRaw('boardsort');

        $rows = $rows->get();

        //$rows = $rows->paginate(9);
        //$rows = $rows->paginate(10);
        $this->data['rows'] = $rows;

        return view('faq.index', [
            'data' => $this->data,
            ]
       );
    }

    public function show(Request $request, $boardid)
    {
        $rows = $this->boardRepo->selectRaw('board.*');
        $rows->myWhere('boardid|N', $this->data['board'], 'boardid', 'Y');
        $rows->orderByRaw('boardid desc');

        $rows = $rows->take(1)->get();
        //$rows = $rows->get();
        //dd($row);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('No data');
        }
        Config::set('config.title', $this->data['title'].' | '.config('config.title'));
        // Config::set('config.keyword', '');
        // Config::set('config.description', '');

        return view('board.show', [
            'data' => $this->data,
            ]
       );
    }
}
