<?php

namespace App\Http\Middleware;

use Closure;
use PF;
use Session;

class AuthAdmin
{
    protected $except = [
        'admin',
        'admin/adminlogin/',
        'admin/adminlogin/googlelogin',
        'admin/adminlogin/googlecallback',
        'admin/adminlogin/selectaccount',
        'admin/adminlogin/selectaccountstore',
        'admin/adminlogin/login',
    ];
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        
        foreach ($this->except as $excluded_route) {
        
            if ($request->path() === $excluded_route) {
                //Skipping $excluded_route from auth check...
                return  $next($request);
            }
        }
        

        if ($request->is('admin/*') || $request->is('admincontrol/*') ) {
            
            if (false == \Auth::guard('admin')->check()) {
                return redirect('/admin/');
            }
            if (substr_count($request->getRequestUri(), '/destroy') > 0) {                
                if ('999' != \Auth::guard('admin')->user()->status) {                
                    throw new \CustomException("權限不足");
                }
            }

        }

        return $next($request);
    }
}
