<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use PF;
use DB;

//use Illuminate\Support\Facades\DB;

class dataController extends Controller
{
    private $data;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        //$this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    public function index(Request $request)
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';

        try {
            switch ($this->data['str']) {
                case 'city1':
                    $rows = DB::table('city1')->select('city1title', 'city1title');
                    //$rows->myWhere('partid|N', $key, 'key', 'N');
                    $rows->orderBy('sortnum', 'desc');

                    break;
                case 'city2':
                    $rows = DB::table('city2')->select('city2title', 'city2title');
                    $rows->myWhere('city1title|S', $this->data['city1title'], 'key', 'N');
                    $rows->myWhere('city1title|S', $this->data['acity1title'], 'key', 'N');
                    $rows->orderBy('city2title');

                    break;
                case 'kindhead':
                    $rows = DB::table('kindhead')->select('kindheadid', 'kindheadtitle');
                    $rows->orderBy('kindheadsort');
                    break;
                case 'kindmain':
                    $rows = DB::table('kindmain')->select('kindmainid', 'kindmaintitle');
                    $rows->myWhere('kindheadid|N', $this->data['kindheadid'], 'kindheadid', 'Y');
                    $rows->orderBy('kindmainsort');
                    break;
                case 'kinditem':
                        $rows = DB::table('kinditem')->select('kinditemid', 'kinditemtitle');
                        $rows->myWhere('kindmainid|N', $this->data['kindmainid'], 'kindmainid', 'Y');
                        $rows->orderBy('kinditemtitle');
                    break;          
            
                default:
                    throw new \CustomException('no str');
                    break;
                }
            $rows = $rows->get();
            $data = [];
            foreach ($rows as $rs) {
                $rs = get_object_vars($rs);
                $arrkey = array_keys($rs);
                $vtitle = 1;
                if (1 == count($arrkey)) {
                    $vtitle = 0;
                }
                $data[] = [
                    'value' => $rs[$arrkey[0]],
                    'title' => $rs[$arrkey[$vtitle]],
                ];
            }

            $jsondata['data'] = $data;
            //throw new \Exception('no data');
        } catch (\CustomException $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        }
        //json = json_encode(body,JSON_UNESCAPED_UNICODE);
        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    public function ip()
    {
        return response(\Request::ip());
    }
}
