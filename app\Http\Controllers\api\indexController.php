<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use PF;
use DB;
use Config;
use Cache;

//use Illuminate\Support\Facades\DB;

class indexController extends Controller
{
    private $data;
    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';
        try{
        
        } catch (\Exception $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        }

        return response()->json($jsondata);
    
    }

}
