@extends('admin.layouts.master')
@section('css')
@endsection

@section('js')
@endsection
@section('nav')
{!!$data['nav']!!}
@endsection


@section('content')

{!!Session::get('msg')!!}


<form name="SearchoForm" method="post" language="javascript" action="{{request()->getRequestUri()}}"
    onsubmit="return SearchoForm_onsubmit(this);">

    <table width="100%" border="0" align="center">
        <tr>
            <td>
                <div align="center">

                    <span>


                        <input type="month" id="start" name="yyyymm" value="{{$data['yyyymm']}}">





                        <input type="submit" value="查詢">



                </div>

            </td>
        </tr>

    </table>



</form>



<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript">
google.charts.load('current', {
    'packages': ['corechart']
});
google.charts.setOnLoadCallback(drawChart);

function drawChart() {
    var data = google.visualization.arrayToDataTable([
        ['Year', '今年人次', '去年人次'],
        @foreach ($data['dates'] as $rs) 
            ['{{$rs[0]}}', {{$rs[1]}},{{$rs[2]}}],
        @endforeach
    ]);
    var options = {
        title: '{{$data['yyyymm']}}月總人數:{{$data['total']}}',
        curveType: 'function',
        legend: {
            position: 'bottom'
        },
        vAxis: {
            title: "瀏覽人數",
            viewWindowMode: 'explicit',
            viewWindow: {
                min: 0
            }
        }

    };

    var chart = new google.visualization.LineChart(document.getElementById('curve_chart'));

    chart.draw(data, options);
}
</script>

<div id="curve_chart" style="width: 100%; height: 500px"></div>



@endsection