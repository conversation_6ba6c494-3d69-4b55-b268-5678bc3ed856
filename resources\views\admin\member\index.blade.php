@extends('admin.layouts.master')

@section('css')
@endsection

@section('js')
@endsection

@section('nav')
    {!! $data['nav'] !!}
@endsection


@section('content')
    <div class="container-fluid p-1 noprint">
        <form name="SearchoForm" class="form-inline" method="post" language="javascript" action="{{ request()->url() }}"
            onsubmit="return SearchoForm_onsubmit(this);">
            @include('admin.layouts.search', [])
        </form>
    </div>


    <!--排序的參數-->
    <form name="SortoForm" method="post">
        @include('admin.layouts.hidden', ['method' => 'SortoForm', 'data' => $data])
    </form>


    <!--傳給下一頁的參數-->
    <form method="post" language="javascript" name="oForm" action="{{ request()->url() }}">

        <div class="table-responsive">
            <table class="table table-striped table-hover  table-bordered table-fixed">

                <thead>
                    <tr valign="top" align="left">

                        <th align="center" width="95">
                            @if (Gate::check('isAdminRole', ['999']))
                                <div class="form-row align-items-center">
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-danger btn-sm"
                                            onclick="(!_confirm('確定要刪除？',function (confirmed) {if (confirmed) {document.forms['oForm'].action='{{ request()->url() }}/destroy';oForm.submit();}}));"><i
                                                class="fa fa-trash" aria-hidden="true"></i></button>
                                    </div>
                                    <div class="col-auto">
                                        <label class="h6 small"
                                            onClick="if (this.innerHTML=='全選'){try{checkAll(jQuery($('input[name=\'del[]\']')))}catch(e){alert('目前無可刪除資料');return false;};this.innerHTML='全不選';}else{try{uncheckAll($('input[name=\'del[]\']'))}catch(e){};this.innerHTML='全選';}">全選</label>
                                    </div>
                                </div>
                            @endif
                        </th>

                        <th width="" id="mobile">行動電話</th>
                        <th width="" id="name">姓名</th>
                        <th width="" id="sex">性別</th>
                        <th width="" id="email">電子信箱</th>
                        <th width="" id="logincount">登入次數</th>
                        <th width="" id="online">會員狀態</th>
                        <th width="" id="created_at">建立時間</th>
                    </tr>
                </thead>


                @include('admin.layouts.hidden', ['method' => 'oForm', 'data' => $data])
                <tbody>
                    @foreach ($data['rows'] as $rs)
                        <tr>

                            <td valign="top" align="center">
                                <div class="form-row align-items-center">


                                    <div class="col-auto">
                                        <button type="submit" class="btn btn-info btn-sm"
                                            onclick="javascript:form.action='{{ request()->url() }}/edit?edit={{ $rs->id }}';">
                                            <i class="fa fa-edit" aria-hidden="true"></i>
                                        </button>
                                    </div>

                                    <div class="col-auto">
                                        @if (Gate::check('isAdminRole', ['999']))
                                            <input type="checkbox" name="del[]" class="all"
                                                value="{{ $rs->id }}">
                                        @endif
                                    </div>

                                </div>
                            </td>
                            <td>
                                {{ $rs->mobile }}
                            </td>
                            <td>
                                {{ $rs->name }}
                            </td>
                            <td>
                                <a href="javascript:;" onclick="PF_formSearch('member.sex|S','{{ $rs->sex }}')">
                                    {{ PF::xmlSearch($data['xmlDoc'], '//參數設定檔/性別/KIND/傳回值', '資料', $rs->sex) }}
                                </a>
                            </td>
                            <td>
                                <a href="mailto:{{ $rs->email }}">{{ $rs->email }}</a>
                            </td>
                            <td>
                                {{ PF::formatNumber($rs->logincount, 0) }}
                            </td>
                            <td>
                                <a href="javascript:;" onclick="PF_formSearch('member.online|S','{{ $rs->online }}')">
                                    {{ PF::xmlSearch($data['xmlDoc'], '//參數設定檔/會員狀態/KIND/傳回值', '資料', $rs->online) }}
                                </a>
                            </td>
                            <td>
                                {{ PF::formatDate($rs->created_at) }}
                            </td>
                        </tr>
                    @endforeach

                </tbody>



            </table>


        </div>
    </form>
    @if (count($data['rows']) == 0)
        No Data
    @endif
    {{ method_exists($data['rows'], 'links') ? $data['rows']->links('layouts.paginate') : '' }}
    <div class="form-inline noprint">
        <button type="button" reload class="btn btn-primary btn-sm"
            data-url="{{ request()->url() }}/excelimport?@foreach ($data['hiddens'] as $key => $item){{ $item }}={{ $data[$item] }}& @endforeach"
            data-toggle="modal" data-title="EXCEL匯入" data-width="550" data-height="500">
            EXCEL匯入
        </button>
    @endsection
