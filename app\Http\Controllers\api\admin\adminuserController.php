<?php

namespace App\Http\Controllers\api\admin;

use Illuminate\Http\Request;
use PF;

//use Illuminate\Support\Facades\DB;

class adminuserController extends Controller
{
    private $data;
    private $db;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();

        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->db = new \App\Models\adminuser();
        $this->fieldnicknames =$this->db::getFieldTitleArray();
    }

    public function logout(Request $request)
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';
        
        
            $inputs =null;              
            $inputs['api_token'] = null;
            \App\Models\adminuser::myWhere('userid', \Auth::guard('apiadmin')->user()->userid,'title','Y')->update($inputs);
            
            
       
        //json = json_encode(body,JSON_UNESCAPED_UNICODE);
        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
