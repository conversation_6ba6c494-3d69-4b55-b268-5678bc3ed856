// hero-slider
var swiper = new Swiper(".hero.swiper-container", {
  slidesPerView: 1,
  effect: 'fade',
  pagination: {
    el: '.swiper-pagination',
    clickable: true
  },
  loop: true,
  autoplay: {
    delay: 2000,
  },
  speed: 3000
});



// housing (slider__thumbs) - 修改為支援自動輪播的縮圖輪播器
window.sliderThumbs = new Swiper(".slider__thumbs .thumb-slider", {
  direction: "vertical",
  slidesPerView: 4, // 同時顯示4張縮圖
  spaceBetween: 8,
  navigation: {
    nextEl: ".slider__next",
    prevEl: ".slider__prev"
  },
  freeMode: true,
  watchSlidesProgress: true,
  // 添加自動輪播功能
  autoplay: {
    delay: 3000, // 3秒自動切換
    disableOnInteraction: false, // 用戶操作後繼續自動播放
    pauseOnMouseEnter: true // 滑鼠懸停時暫停
  },
  loop: true, // 循環播放
  breakpoints: {
    0: {
      direction: "horizontal",
      slidesPerView: 3 // 手機版顯示3張
    },
    768: {
      direction: "vertical",
      slidesPerView: 4 // 桌面版顯示4張
    }
  }
});

// housing (main slider) - 主圖輪播器
window.sliderImages = new Swiper(".sliders .main-slider", {
  direction: "vertical",
  slidesPerView: 1,
  spaceBetween: 32,
  mousewheel: true,
  navigation: {
    nextEl: ".slider__next",
    prevEl: ".slider__prev"
  },
  pagination: {
    el: ".swiper-pagination",
    clickable: true,
    type: "bullets"
  },
  grabCursor: true,
  thumbs: {
    swiper: window.sliderThumbs
  },
  // 添加自動輪播功能
  autoplay: {
    delay: 4000, // 4秒自動切換
    disableOnInteraction: false, // 用戶操作後繼續自動播放
    pauseOnMouseEnter: true // 滑鼠懸停時暫停
  },
  loop: true, // 循環播放
  effect: "slide", // 滑動效果
  speed: 800, // 切換速度
  breakpoints: {
    0: {
      direction: "horizontal"
    },
    768: {
      direction: "vertical"
    }
  }
});