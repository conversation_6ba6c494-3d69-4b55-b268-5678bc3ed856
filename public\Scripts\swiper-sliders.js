// hero-slider
var swiper = new Swiper(".hero.swiper-container", {
  slidesPerView: 1,
  effect: 'fade',
  pagination: {
    el: '.swiper-pagination',
    clickable: true
  },
  loop: true,
  autoplay: {
    delay: 2000,
  },
  speed: 3000
});



// housing (slider__thumbs)
const sliderThumbs = new Swiper(".slider__thumbs .swiper-container", {
  direction: "vertical",
  slidesPerView: 5,
  spaceBetween: 6.3,
  navigation: {
    nextEl: ".slider__next",
    prevEl: ".slider__prev"
  },
  freeMode: true,
  breakpoints: {
    0: {
      direction: "horizontal"
    },
    768: {
      direction: "vertical"
    }
  }
});
const sliderImages = new Swiper(".sliders .swiper-container", {
  direction: "vertical",
  slidesPerView: 1,
  spaceBetween: 32,
  mousewheel: true,
  navigation: {
    nextEl: ".slider__next",
    prevEl: ".slider__prev"
  },
  grabCursor: true,
  thumbs: {
    swiper: sliderThumbs
  },
  breakpoints: {
    0: {
      direction: "horizontal"
    },
    768: {
      direction: "vertical"
    }
  }
});