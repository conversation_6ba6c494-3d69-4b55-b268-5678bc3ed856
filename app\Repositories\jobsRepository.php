<?php

namespace App\Repositories;

use App\Models\jobs;
use DB;
use PF;

class jobsRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(jobs $model)
    {
        $this->model = $model;
    }
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    public function view($rows)
    {
        $rows->join('diversionurl', 'diversionurl.id', '=', 'diversionlog.diversionurl_id');
        return $rows;
    }


    public function create($inputs)
    {

        return parent::create($inputs);
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        return  parent::update($inputs,$id,'id') ;      
    }

    public function deleteIds($ids)
    {
    //     $this->jobsxxRepo = app(\App\Repositories\jobsxxRepository::class);
    //     $rows = $this->jobsxxRepo->select(null);
    //     $rows->myWhere('jobs_id|ININT', $ids, 'jobs_id', 'Y');
    //     $rows->delete();

        
    //    \DB::delete('delete jobssign from  jobssign INNER JOIN jobsxx ON (jobsxx.id=jobssign.jobsxx_id) where jobsxx.jobs_id in (?)',[$ids]);
        
        parent::deleteIds($ids);
      

        // $rows=$this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // $rows->get()->each(function ($rs) {
        //     $path=storage_path($this->model->table.'/'.$rs->img.".json");            
        //     //$path=public_path('images/'.$this->model->table.'/'.$rs->img);
        //     if (\File::exists($path)) {
        //        \File::delete($path);
        //     }            
        //     $rs->delete();
        // });
        
    }
}
