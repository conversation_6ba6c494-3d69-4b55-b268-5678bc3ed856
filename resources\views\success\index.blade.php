@extends('layouts.master')
@section('css')
@endsection

@section('js')
@endsection

@section('content')
    <section class="title">
        <h1 class="animation">
            <span>房地產專業服務</span>
        </h1>
    </section>

    <h2 class="team-title">成功案例</h2>


    <main class="flex-grow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="space-y-8">
                <div class="text-center">

                    <p class="text-gray-600 mt-2">我們幫助許多客戶成功取得理想的房產，以下是部分成功案例</p>
                </div>



                <!-- 案例卡片網格 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 案例 1 -->
                    @foreach ($data['rows'] as $rs)
                        <div class="case-card">
                            <a href="case-detail-1.html" class="block">
                                <div class="case-image-container">
                                    <a href="{{ url('/') }}/success/show/{{ $rs->id }}" class="photo">
                                        {{ Html::myUIImage([
                                            'folder' => 'https://www.ebayhouse.com.tw/images/success',
                                            'filename' => $rs->field1,
                                            'alt' => $rs->title,
                                            'width' => 313,
                                            'height' => 200,
                                            'noimg' => 'no-picture.gif',
                                            'class' => 'd-block w-100',
                                        ]) }}

                                    </a>


                                    <div class="case-title" style="font-size: 20px">{{ $rs->title }}</div>
                                </div>
                            </a>
                            <div class="case-info">
                                <div class="flex flex-wrap mt-2">
                                    <div class="location-tag">
                                        <i class="fas fa-map-marker-alt mr-1 text-red-600"></i>
                                        <span style="font-size: 20px">{{ $rs->memo }}</span>
                                    </div>
                                    {{-- <div class="location-tag">
                                <i class="fas fa-home mr-1 text-red-600"></i>
                                <span>豪宅</span>
                            </div> --}}
                                </div>
                                <p class="mt-2 text-gray-700 text-sm" style="font-size: 20px">

                                    成交價格：{{ $rs->field3 }}
                                    萬元<BR>
                                    節省金額：約{{ $rs->field4 }}
                                    萬元
                                </p>
                                <div class="mt-3 text-right">
                                    <a href="{{ url('/') }}/success/show/{{ $rs->id }}"
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        查看詳情 <i class="fas fa-arrow-right ml-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                    @if (count($data['rows']) == 0)
                        No Data
                    @endif




                </div>
            </div>
            {{ method_exists($data['rows'], 'links') ? $data['rows']->links('layouts.paginate') : '' }}
        </div>
    </main>
@endsection
