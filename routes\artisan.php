<?php

use Illuminate\Support\Facades\Route;

Route::get('/clear', function () {
    $artisanService = new \App\Services\artisanService();
    echo $artisanService->clear();
});
Route::get('/seed/{file}', function ($file) {
    $artisanService = new \App\Services\artisanService();
    $artisanService->seed($file);
});
// php artisan migrate --force
Route::get('/migrate', function () {
    $artisanService = new \App\Services\artisanService();
    $artisanService->migrate();
});
Route::get('/migrate/{tablename}', function ($tablename) {
    $artisanService = new \App\Services\artisanService();
    $artisanService->migraterefresh($tablename);
});
Route::get('/migraterefresh/{file}', function ($file) {
    $artisanService = new \App\Services\artisanService();
    $artisanService->migraterefresh($file);
});
Route::get('/vue', function () {
    $artisanService = new \App\Services\artisanService();
    echo $artisanService->vue();
});
Route::get('/nuxt', function () {
    $artisanService = new \App\Services\artisanService();
    echo $artisanService->nuxt();
});

Route::get('/keygenerate', function () {
    \Artisan::call('migrate:generate');

    return 'key:generate!';
});

Route::get('/command', function () {
    \Artisan::call('makejs:update');

    return 'command!';
});
Route::get('/testmail', function () {
    return new App\Mails\feedbackreMail(1);
});
