<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PF;
use Config;
use App\Repositories\boardRepository;

//use Illuminate\Support\Facades\DB;

class newsController extends Controller
{
    private $data;
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->boardRepo = $boardRepo;
        Config::set('config.title', '房訊新聞 | '.config('config.title'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $kindid = null)
    {
        //PF::printr(0);
        $rows = $this->boardRepo->selectRaw('*');
        $rows->myWhere('kind|S', 'news', 'kind', 'N');

        //$rows->myWhere('kindid|INT', $kindid, 'kindid', 'N');
        $rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
        $rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        $rows->orderByRaw('boardsort desc,created_at desc');
        
        
        if ('' != $kindid) {
            $this->data['search'] = $kindtitle;
        }
        //PF::dbSqlPrint($rows);
        $rows = $rows->paginate(12);
        // 顯示sqlcmd

        $this->data['rows'] = $rows;

        return view('news.index', [
            'data' => $this->data,
            ]
       );
    }

    public function search()
    {
        Config::set('config.title', $this->data['search'].' - '.config('config.title'));
        $rows = $this->boardRepo->selectRaw('*');
        $rows->myWhere('kind|S', 'news', 'kind', 'N');
        $rows->myWhere('title^memo^body^field9', $this->data['search'], 'kindid', 'N');
        //$rows->myWhere('kindid|INT', $this->data['kindid'], 'kindid', 'N');
        $rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');
        $rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');
        $rows->orderByRaw('boardsort desc,id desc');
        //PF::dbSqlPrint($rows);
        $rows = $rows->paginate(9);
        // 顯示sqlcmd

        $this->data['rows'] = $rows;

        return view('news.index', [
            'data' => $this->data,
            ]
       );
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $rows = $this->boardRepo->selectRaw('board.*');

        $rows->myWhere('id|N', $id, 'id', 'Y');
        //PF::dbSqlPrint($rows);
        $rows->limit(1);
        if ($rows->count() > 0) {
            $rs = $rows->first();
            Config::set('config.title', $rs->title.' | '.config('config.title'));
            //Config::set('config.keyword', $rs->field9);
            
            Config::set('config.description', PF::nohtml($rs->body));
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
                //PF::printr($this->data[$key]);
            }
            //$this->data['body']
            // $this->data['body'] = preg_replace('/width: [0-9]+[a-z]{2};?/i', '', $this->data['body']);
            // $this->data['body'] = preg_replace('/height: [0-9]+[a-z]{2};?/i', '', $this->data['body']);
            \DB::update('update board set hits=hits+1 where id = ?', [$id]);
           // Post::find($post_id)->increment('view_count');
        //echo 4;
        } else {
            die('No Data');
        }

        return view('news.show', [
            'data' => $this->data,
            ]
       );
    }
}
