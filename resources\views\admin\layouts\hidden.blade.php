@if ($method == 'SortoForm')
    @foreach (Request::all() as $key => $item)
        @if ($data['hiddens'] == null || in_array(trim($key), $data['hiddens']) == false)
            @if (is_array($item))
                <input type="hidden" name="{{ $key }}" value="{{ implode(',', $item) }}" />
            @elseif ($key != 'page' && $item != '')
                <input type="hidden" name="{{ $key }}" value="{{ strval($item) }}" />
            @endif
        @endif
    @endforeach

    @if (request()->get('sortname') == '')
        <input type="hidden" name="sortname" value="{{ request()->get('sortname') }}">
    @endif
    @if (request()->get('sorttype') == '')
        <input type="hidden" name="sorttype" value="{{ request()->get('sorttype') }}">
    @endif
@else
    @foreach (Request::all() as $key => $item)
        @if (PF::left($key, 2) == '__' && (isset($except) == false || (isset($except) && in_array($key, $except) == false)))
            @if (is_array($item))
                <input type="hidden" name="{{ $key }}" value="{{ implode(',', $item) }}" />
            @elseif ($key != 'page' && $item != '')
                <input type="hidden" name="{{ $key }}" value="{{ strval($item) }}" />
            @endif
        @endif
    @endforeach

    @if ($method != 'SearchoForm' && $method != 'AddoForm')
        @foreach (config('app.globalhidden') as $key)
            <input type="hidden" name="{{ $key }}" value="{{ request()->get($key) }}">
        @endforeach
    @endif

    @if ($method == 'oForm' || $method == 'EditoForm')
        <input type="hidden" name="page" value="{{ request()->get('page') }}">
    @endif
@endif


@if ($method == 'AddoForm' || $method == 'oForm')
    <input type="hidden" name="gobackurl" value="{{ request()->url() }}">
@endif

@if ($data['hiddens'] != '')

    @if ($method == 'SearchoForm')
        @foreach ($data['hiddens'] as $key)
            @if (array_search($key, config('app.globalhidden')) == false &&
                    (isset($except) == false || (isset($except) && in_array($key, $except) == false)))
                <input type="hidden" name="{{ $key }}" value="{{ request()->get($key) }}">
            @endif
        @endforeach
    @else
        @foreach ($data['hiddens'] as $key)
            @if (request()->get($key) != '')
                <input type="hidden" name="{{ $key }}" value="{{ request()->get($key) }}">
            @endif
        @endforeach
    @endif



@endif
