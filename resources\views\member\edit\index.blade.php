@extends('layouts.master')
@section('css')

@stop

@section('js')
    <SCRIPT language=JavaScript>
        function oForm_onsubmit(form) {
            if (PF_FormMultiAll(form) == false) {
                return false
            };

            if (typeof(form.elements['password']) != "undefined" && typeof(form.elements['password1']) !=
                "undefined") {
                if (form.elements['password'].value != form.elements['password1'].value) {
                    alert('密碼與確認密碼不符');
                    form.elements['password1'].focus();
                    return false;
                }
            }

            PF_FieldDisabled(form) //將全部button Disabled
            return true;
        }
    </SCRIPT>

@stop


@section('content')

    <!-- 會員資料編輯頁面 - 使用 Tailwind CSS -->
    <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md mx-auto">
            <!-- 頁面標題區域 -->
            <div class="text-center mb-8">
                <div class="mx-auto h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-2">資料編輯</h2>
                <p class="text-gray-600">更新您的個人資料</p>
            </div>

            <!-- 表單容器 -->
            <div class="bg-white shadow-xl rounded-lg overflow-hidden">
                <div class="px-6 py-8">
                    <form accept-charset="UTF-8" action="{{ url('/') }}/membercenter/edit/store" method="post"
                        onsubmit="return oForm_onsubmit(this)" class="space-y-6">
                        @csrf

                        <!-- 電子信箱顯示區域 -->
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-700">
                                電子信箱
                                <span class="text-red-500 ml-1">*</span>
                            </label>
                            <div class="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md text-gray-800 font-medium">
                                {{ $data['email'] }}
                            </div>
                            <p class="text-xs text-gray-500">電子信箱無法修改</p>
                        </div>

                        <!-- 姓名輸入欄位 -->
                        <div class="space-y-1">
                            <label for="name" class="block text-sm font-medium text-gray-700">
                                姓名
                                <span class="text-red-500 ml-1">*</span>
                            </label>
                            <input id="name" name="name" type="text" required value="{{ $data['name'] }}"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                          placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500
                                          focus:border-blue-500 transition duration-150 ease-in-out
                                          hover:border-gray-400" />
                        </div>

                        <!-- 行動電話輸入欄位 -->
                        <div class="space-y-1">
                            <label for="mobile" class="block text-sm font-medium text-gray-700">
                                行動電話
                                <span class="text-red-500 ml-1">*</span>
                            </label>
                            <input id="mobile" name="mobile" type="tel" required requiredclass="required[1,MOBILE]"
                                value="{{ $data['mobile'] }}" placeholder="請輸入您的行動電話"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                          placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500
                                          focus:border-blue-500 transition duration-150 ease-in-out
                                          hover:border-gray-400" />
                        </div>

                        <!-- 密碼輸入欄位 -->
                        <div class="space-y-1">
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                新密碼
                                <span class="text-gray-500 text-xs">(不修改請留空)</span>
                            </label>
                            <input id="password" name="password" type="password" autocomplete="new-password"
                                requiredclass="required[0,PASSWORD]" maxlength="20" placeholder="請輸入新密碼"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                          placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500
                                          focus:border-blue-500 transition duration-150 ease-in-out
                                          hover:border-gray-400" />
                            <p class="text-xs text-gray-500 mt-1">
                                密碼長度必須為8~20位，包含至少一位數字、一位英文，特殊符號僅限於 ! @ # $ % & *
                            </p>
                        </div>

                        <!-- 確認密碼輸入欄位 -->
                        <div class="space-y-1">
                            <label for="password1" class="block text-sm font-medium text-gray-700">
                                確認新密碼
                                <span class="text-gray-500 text-xs">(不修改請留空)</span>
                            </label>
                            <input id="password1" name="password1" type="password" autocomplete="new-password"
                                requiredclass="required[0,PASSWORD]" maxlength="20" placeholder="請再次輸入新密碼"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
                                          placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500
                                          focus:border-blue-500 transition duration-150 ease-in-out
                                          hover:border-gray-400" />
                        </div>

                        <!-- 提交按鈕 -->
                        <div class="pt-4">
                            <button type="submit"
                                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md
                                           shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700
                                           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                                           transition duration-150 ease-in-out transform hover:scale-105
                                           disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                更新資料
                            </button>
                        </div>

                        <!-- 安全提示 -->
                        <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-amber-700">
                                        <strong>安全提醒：</strong>請妥善保管您的帳戶資訊，避免在公共場所修改密碼。
                                    </p>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>

            <!-- 返回連結 -->
            <div class="mt-6 text-center">
                <a href="{{ url('/membercenter') }}"
                    class="inline-flex items-center text-sm text-blue-600 hover:text-blue-500 transition duration-150 ease-in-out">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    返回會員中心
                </a>
            </div>
        </div>
    </div>

@stop
