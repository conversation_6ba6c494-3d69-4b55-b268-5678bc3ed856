<?php

namespace App\Macros;

use DB;
use PF;

/***
"功能名稱":"共用類別-Html select多層式",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
***/
// {{Form::myUISelectThird([
//     'formname' =>'oForm',
//     'title1' =>'縣市',
//     'title2' =>'鄉鎮',
//     'sql1' => 'select city1title,city1title from city1 order by sortnum desc',
//     'sql2' => 'city2SQLCmd',
//     'name1' => 'city1',
//     'name2' => 'city2',
//     'value1' => $data['city1'],
//     'value2' => $data['city2'],
//     'requiredclass1' => 'required[1,TEXT]',
//     'requiredclass2' => 'required[1,TEXT]',
// ])
// }}
class MyFormUISelectThird
{
    public $i = 0;
    protected $arr;

    public function __construct($form, $arr)
    {
        $this->arr = $arr;
    }

    public function createHtml()
    {
        try {
            $html = '';
            if ('' == $this->arr['sql1']) {
                $html .= 'sql1 command is null';

                return;
            }
            if ('' == $this->arr['sql2']) {
                $html .= 'sql2 command is null';

                return;
            }

            $selectfirsttxt = __('請選擇').'..';
            $i = 0;

            if ('' == $this->arr['firsttxt1']) {
                $this->arr['firsttxt1'] = __('請選擇').'..';
            }
            if ('' == $this->arr['firsttxt2']) {
                $this->arr['firsttxt2'] = __('請選擇').'..';
            }
            if ('' == $this->arr['firsttxt3']) {
                $this->arr['firsttxt3'] = __('請選擇').'..';
            }
            $html .= '<div class="form-inline">';
            $html .= '<select name="'.$this->arr['name1'].'"';
            $html .= ' id="'.$this->arr['name1'].'"';
            $html .= ' title="'.$this->arr['title1'].'"';
            $html .= ' class="form-control"';
            $html .= " v-model='inputs.".$this->arr['name1']."' ";
            if (null != $this->arr) {
                foreach ($this->arr as $_key => $_value) {
                    if ('1' == PF::right($_key, 1)) {
                        if (false == PF::splitCompare('sql1,sql2,sql3,value1,value2,value3,firsttxt,name1,name2,name3', $_key)) {
                            $html .= ' '.PF::left($_key, mb_strlen($_key) - 1).'="'.$_value.'"';
                        }
                    }
                }
            }

            if ('' != $this->arr['requiredclass1']) {
                if ('required[1,' == substr($this->arr['requiredclass1'], 0, 11)) {
                    $html .= ' required ';
                }
            }

            $html .= " onChange=\"PJ_SelectThird('".asset('/api/db/selectthrid')."',this.options[this.options.selectedIndex].value,'".$this->arr['sql2']."',$(this).next('select').eq(0),";
            if ('' != $this->arr['name3']) {
                $html .= "$(this).parent().find('select').eq(2)";
            } else {
                $html .= "'',''";
            }
            $html .= ')"';
            $html .= ">\n";

            if ('' != $this->arr['firsttxt1']) {
                $html .= '<option value="">'.__($this->arr['firsttxt1']).'</option>'.chr(13).chr(10);
            }

            $pdo = DB::getPdo();
            $rows = $pdo->prepare($this->arr['sql1']);
            $rows->setFetchMode(\PDO::FETCH_NUM);
            $rows->execute();
            $rows = $rows->fetchAll();

            foreach ($rows as $rs) {
                $ischeckstatus = 0;
                $keystr = $rs[0];
                if ('' == $keystr) {
                    $keystr = $rs[1];
                }
                $text = $rs[1];

                if (trim($this->arr['value1']) == trim($keystr)) {
                    $ischeckstatus = 1;
                }

                $html .= "<option value=\"$keystr\"";
                if (1 == $ischeckstatus) {
                    $html .= 'selected';
                }
                $html .= '>';
                $html .= __($text).'</option>'.chr(13).chr(10);
            }

            $html .= "\n";

            $html .= '</select>&nbsp;'.chr(13).chr(10);

            //==============================================================
            //name2
            $html .= '<select name="'.$this->arr['name2'].'"';
            $html .= ' id="'.$this->arr['name2'].'"';
            $html .= ' title="'.$this->arr['title2'].'"';
            $html .= ' class="form-control"';
            $html .= " v-model='inputs.".$this->arr['name2']."' ";

            if (null != $this->arr) {
                foreach ($this->arr as $_key => $_value) {
                    if ('2' == PF::right($_key, 1)) {
                        if (false == in_array($_key, ['sql1', 'sql2', 'sql3', 'value1', 'value2', 'value3', 'sql', 'firsttxt', 'name1', 'name2', 'namee'])) {
                            $html .= ' '.PF::left($_key, mb_strlen($_key) - 1).'="'.$_value.'"';
                        }
                    }
                }
            }

            if ('' != $this->arr['requiredclass2']) {
                if ('required[1,' == substr($this->arr['requiredclass2'], 0, 11)) {
                    $html .= ' required ';
                }
            }

            if ('' != $this->arr['name3']) {
                $html .= " onChange=\"PJ_SelectThird('".asset('/api/db/selectthrid')."',this.options[this.options.selectedIndex].value,'".$this->arr['sql3']."',$(this).parent().find('select').eq(2),'','');\"";
            }
            $html .= ">\n";

            if ('' != $this->arr['firsttxt2']) {
                $html .= '<option value="">'.__($this->arr['firsttxt2']).'</option>'.chr(13).chr(10);
            }

            $html .= '</select>&nbsp;'.chr(13).chr(10);

            $html .= "<SCRIPT language='JavaScript' style='display:none'>".chr(13).chr(10);
            $html .= '$(function() {  '.chr(13).chr(10);
            if ('' != $this->arr['value2']) {
                $html .= "PJ_SelectThird('".asset('/api/db/selectthrid')."','".$this->arr['value1']."','".$this->arr['sql2']."',document.forms['".$this->arr['formname']."'].elements['".$this->arr['name2']."'],'','".$this->arr['value2']."')".chr(13).chr(10);
                $html .= 'try {';
                $html .= "      app.\$data.inputs['".$this->arr['name2']."'] = '".$this->arr['value2']."'".chr(13).chr(10);
                $html .= '} catch (error) {'.chr(13).chr(10);
                $html .= '}'.chr(13).chr(10);
            } else {
                $html .= "PJ_SelectThird('".asset('/api/db/selectthrid')."','".$this->arr['value1']."','".$this->arr['sql2']."',document.forms['".$this->arr['formname']."'].elements['".$this->arr['name2']."'],'','');".chr(13).chr(10);
            }
            $html .= '});'.chr(13).chr(10);
            $html .= '</SCRIPT>'.chr(13).chr(10);
            //==============================================================
            //name3
            if ('' != $this->arr['name3']) {
                $html .= '<select name="'.$this->arr['name3'].'"';
                $html .= ' id="'.$this->arr['name3'].'"';
                $html .= ' title="'.$this->arr['title3'].'"';
                $html .= ' class="form-control"';
                $html .= " v-model='inputs.".$this->arr['name3']."' ";

                if (null != $this->arr) {
                    foreach ($this->arr as $_key => $_value) {
                        if ('3' == PF::right($_key, 1)) {
                            if (false == in_array($_key, ['sql1', 'sql2', 'sql3', 'value1', 'value2', 'value3', 'sql', 'firsttxt', 'name1', 'name2', 'name3'])) {
                                $html .= ' '.PF::left($_key, mb_strlen($_key) - 1).'="'.$_value.'"';
                            }
                        }
                    }
                }

                if ('' != $this->arr['requiredclass3']) {
                    if ('required[1,' == substr($this->arr['requiredclass3'], 0, 11)) {
                        $html .= ' required ';
                    }
                }
                $html .= ">\n";

                if ('' != $this->arr['firsttxt3']) {
                    $html .= '<option value="">'.__($this->arr['firsttxt3']).'</option>'.chr(13).chr(10);
                }

                $html .= '</select>&nbsp;'.chr(13).chr(10);
                $html .= "<SCRIPT language='JavaScript' style='display:none'>".chr(13).chr(10);
                $html .= '$(function() {  '.chr(13).chr(10);
                if ('' != $this->arr['value3']) {
                    $html .= "PJ_SelectThird('".asset('/api/db/selectthrid')."','".$this->arr['value2']."','".$this->arr['sql3']."',document.forms['".$this->arr['formname']."'].elements['".$this->arr['name3']."'],'','".$this->arr['value3']."')".chr(13).chr(10);
                    $html .= 'try {';
                    $html .= "      app.\$data.inputs['".$this->arr['name3']."'] = '".$this->arr['value3']."'".chr(13).chr(10);
                    $html .= '} catch (error) {'.chr(13).chr(10);
                    $html .= '}'.chr(13).chr(10);
                } else {
                    $html .= "PJ_SelectThird('".asset('/api/db/selectthrid')."','".$this->arr['value2']."','".$this->arr['sql3']."',document.forms['".$this->arr['formname']."'].elements['".$this->arr['name3']."'],'','');".chr(13).chr(10);
                }
                $html .= '});'.chr(13).chr(10);
                $html .= '</SCRIPT>'.chr(13).chr(10);
            }
            $html .= '</div>';
        } catch (\Exception $e) {
            $html = $e->getMessage();
        }

        return $html;
    }
}
