@extends('layouts.master')
@section('css')

@stop

@section('js')
<SCRIPT language=JavaScript>
    function oForm_onsubmit(form) {
        if (PF_FormMultiAll(form) == false) {
            return false
        };

        if (typeof(form.elements['password']) != "undefined" && typeof(form.elements['password1']) !=
            "undefined") {
            if (form.elements['password'].value != form.elements['password1'].value) {
                alert('密碼與確認密碼不符');
                form.elements['password1'].focus();
                return false;
            }
        }

        PF_FieldDisabled(form) //將全部button Disabled
        return true;
    }
    </SCRIPT>

@stop


@section('content')


<div class="FormContainer">
  <div class="all-wrapper with-pattern">
    <div class="auth-box-w">
      <div class="logo-w">
      </div>
      <h4 class="auth-header">
        資料編輯
      </h4>
      
      <form accept-charset="UTF-8" action="{{ url('/') }}/membercenter/edit/store" class="simple_form new_user" id="new_user" method="post">
        
        <div class="form-inputs">
          <div class="form-group email required user_email">
            <label class="email required" for="students_user_email">
                電子信箱
                <abbr title="必填">
                    *
                </abbr>
            </label>
            {{$data['email']}}
          </div>
          <div class="form-group email required user_email">
            <label class="email required" for="students_user_email">
                姓名
                <abbr title="必填">
                    *
                </abbr>
            </label>
            
            <input aria-required="true"  class="form-control"
                 name="name" required="required" type="text"
                value="{{$data['name']}}" />
          </div>
          <div class="form-group email required user_email">
            <label class="email required" for="students_user_email">
              行動電話
              <abbr title="必填">
                  *
              </abbr>
          </label>
          
          <input aria-required="true"  class="form-control"
               name="mobile" required="required" type="tel"
requiredclass="required[1,MOBILE]" 
              value="{{$data['mobile']}}" />
          </div>
          <div class="form-group password required user_password">
            <label class="password required" for="students_user_password">
                密碼
                <abbr title="必填">
                    
                </abbr>
            </label>
            <input type="password" class="form-control" name="password" autocomplete="off"
            
            requiredclass="required[0,PASSWORD]" value="" size="40"
            maxlength="20" data-toggle="tooltip" min="8" max="20" data-placement="top" 
            title=" 密碼長度必須為8~20位,其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ #$ % & *"  />

          </div>
          
          <div class="form-group password required user_password_confirmation">
            <label class="password required" for="user_password_con　firmation">
              確認密碼
              <abbr title="必填">
                *
              </abbr>
            </label>
            <input type="password" class="form-control" name="password1" autocomplete="off"
            
            requiredclass="required[0,PASSWORD]" value="" size="40"
            maxlength="20" pattern="[A-Za-z0-9]{8,20}" data-toggle="tooltip" data-placement="top"
            title=" 密碼長度必須為8~20位,其中必須包含至少一位數字、一位英文，若需有特殊符號僅限於 ! @ #$ % & *" />

            
          </div>
          
          
      
       
        </div>
        <div class="form-actions">
          
            <input class="btn btn btn-primary btn-lg btn-block" data-disable-with="註冊" name="commit" type="submit"
            value="確定" />
          
        </div>
        
      </form>
    </div>
  </div>
</div>




@stop