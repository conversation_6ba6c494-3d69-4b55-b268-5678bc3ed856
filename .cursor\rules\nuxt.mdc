---
description:
globs:
alwaysApply: false
---
# Nuxt 前端頁面開發任務

## 目標
- 根據使用者提供的資料表名稱和功能類型，開發或修改 Nuxt 前端頁面程式碼
- 所有輸出程式碼必須包含繁體中文註解，清楚說明每個部分的用途

## 前置需求
1. 使用者需提供以下資訊：
   - 資料表名稱 (table_name)，例如：board
   - 功能類型 (function)，例如：list、edit、show

## 執行流程
1. **檢查類型定義檔案**
   - 檢查 `resources/js/types/{table_name}.d.ts` 是否存在
   - 若不存在，回應：「錯誤：`{table_name}.d.ts` 檔案不存在，請先建立該檔案於 resources/js/types/{table_name}.d.ts 目錄下。」並終止執行

2. **讀取必要檔案**
   - 讀取 `resources/js/types/{table_name}.d.ts` 以獲取資料表欄位及其描述
   - 若有範例程式，讀取 `resources/js/pages/sample/{function}.txt` 作為參考

3. **程式碼修改原則**
   - 僅修改當前檔案，不建立或修改其他檔案
   - 若程式碼缺少 `<script>` 區塊，則補充完整的 script 區塊
   - 若已有 `getData` 函式，則新增一個名為 `getData_{table_name}` 的函式，功能與原函式相同
   - 將所有 `<a href>` 標籤替換為 `<NuxtLink :to="{ path: '', query: {} }" title="">`

## 功能實現細節

### 若功能類型為「list」
- 參考範例程式碼結構
- 在指定區塊內加入 `v-for` 迴圈，顯示資料表欄位
- 範例格式：
  ```vue
  <div v-for="(rs, index) in data.data" :key="index">
    {{ rs.field_name }}
  </div>
  ```
- 限制：
  - 只修改當前節點內的 HTML 程式碼
  - 只顯示頁面上需要的欄位，不額外產生其他欄位

### 若功能類型為「edit」
- 使用 Element Plus 元件替換現有的 HTML 表單控制項
- 根據資料表欄位類型選擇適合的 Element Plus 元件
- 保留原有的資料綁定和驗證邏輯

### 若功能類型為「show」
- 遍歷資料並顯示資料表欄位值
- 範例格式：`{{ data.field_name }}`
- 只顯示頁面上需要的欄位，不額外產生其他欄位