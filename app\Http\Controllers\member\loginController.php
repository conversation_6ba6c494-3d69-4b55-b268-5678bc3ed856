<?php

namespace App\Http\Controllers\member;

use DB;
use PF;
use Config;
use Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\memberRepository;
use App\Repositories\myproductRepository;

class loginController extends Controller {
    private $memberRepo;
    private $myproductRepo;
    private $data;

    public function __construct(memberRepository $memberRepo, myproductRepository $myproductRepo) {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->memberRepo = $memberRepo;
        $this->myproductRepo = $myproductRepo;
        $this->fieldnicknames = $this->memberRepo->getFieldTitleArray();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        // if ('' != $this->data['redirect']) {
        //     $redirect = $this->data['redirect'];
        //     $items = explode('/', url('/'));
        //     foreach ($items as $k => $v) {
        //         $redirect = str_replace($v, '', $redirect);
        //     }
        //     $redirect = str_replace('//', '/', $redirect);

        //     \Session::put('loginurl', $redirect);
        //     \Session::save();
        // }
        \Config::set('config.title', __('會員登入') . ' | ' . config('config.title'));

        //PF::printr(session('link'));
        return view(
            'member.login.index',
            [
                'data' => $this->data,
            ]
        );
    }

    public function store(Request $request) {

        //throw new \Exception('no data');

        $validators['mobile'] = 'required';
        $validators['password'] = 'required';
        //$validators['google_recaptcha_token'] = ['required', 'string', new \App\Rules\MyValidatorsGoogleRecapchaV3()];
        $validator = \Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);

        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        if (\Auth::guard('member')->attempt(['mobile' => $request->input('mobile'), 'password' => $request->input('password'), 'online' => 1], $this->data['isremember'])) {
            $inputs['api_token'] = hash('sha256', \Str::random(80));
            $inputs['lastlogin_dt'] = date('Y-m-d H:i:s');
            $inputs['lastlogin_ip'] = \Request::ip();
            $inputs['logincount'] = DB::raw('logincount+1');
            $this->memberRepo->update($inputs, \Auth::guard('member')->id());

            $loginurl = \Session::get('loginurl');

            // \Session::forget('loginurl');
            // if (PF::isEmpty($loginurl)) {
            //     $loginurl = 'membercenter/ordergroup';
            // }
            $this->data['action'] = $loginurl;
            $this->data['alert'] = '登入成功';



            $rows = $this->myproductRepo->selectRaw('*');
            $rows->myWhere('member_id|N', \Auth::guard('member')->id(), "kind", 'Y');
            $rows = $rows->get();
            $myproducts = $rows->map(function ($rs) {
                return $rs->product_id;
            })->implode(',');
            \Session::put('myproducts', $myproducts);

            return redirect(url('/'))->with('js', "_alert('登入成功','success')");

            //return redirect(session('link'));
        } else {
            return back()->with('js', "_toast('登入失敗',1000,'warning')");
        }


        return response()->noContent();
    }
}
