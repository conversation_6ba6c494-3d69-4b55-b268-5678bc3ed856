<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use PF;
use DB;

//use Illuminate\Support\Facades\DB;

class testController extends Controller
{
    private $data;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        // $formquerylogService = new \App\Services\formquerylogService();
        // $formquerylogService->pagename = '成立訂單';
        // $formquerylogService->run();
        //將request全部導入到$this->data變數中
        //$this->data = PF::requestAll($this->data);
        //$this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';
        try {
            $rows = DB::table('company');
            $rows->selectRaw('lat,lng,companycity1,companycity2,companyaddress');
            $rows->myWhere('companyid|N', $this->data['companyid'], 'companyid', 'N');
            $rows->orderByRaw('companyid desc');
            $rows->limit(1);
            //$rows = $rows->get();
            //dd($row);
            //PF::dbSqlPrint($rows);

            if ($rows->count() > 0) {
                $rs = $rows->first();
                $jsondata['data'] = $rs;
            } else {
                throw new \CustomException('no data');
            }
        } catch (\Exception $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        }

        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    
}
