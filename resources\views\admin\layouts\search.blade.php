<div class="form-row align-items-center">
    @if ($data['fieldsearchname']!=null)

    <div class="col-auto" id="searchdiv">

        <input type="text" name="search" class="form-control" value="{{{$data['search']}}}" onclick="this.value='';" size="10" placeholder="Search…">


    </div>
    <div class="col-auto">
        {{Form::select('searchname',$data['fieldsearchname'],$data['searchname'],['class'=>'form-control'])}}
    </div>
    <div class="col-auto">
        <button type="submit" class="btn btn-primary btn-rounded btn-sm">
            <i class="fa fa-search"></i>
        </button>
    </div>
    @endif
    @if ($data['fieldsearchdatename']!=null)
    <!--依日期搜尋Start-->
    <div class="col-auto">
        {{Form::select('searchdatename',$data['fieldsearchdatename'],$data['searchdatename'],['class'=>'form-control'])}}
    </div>
    <div class="col-auto">
        {{
                        Form::myUISelectDate([
                            'name'=>'searchstartdate',
                            'type'=>'date',        
                            'title'=>'開始日期',        
                            'value'=>$data['searchstartdate'],        
                            ])
                        }}
    </div>
    <div class="col-auto">
        {{
                        Form::myUISelectDate([
                            'name'=>'searchenddate',
                            'type'=>'date',        
                            'title'=>'結束日期',        
                            'value'=>$data['searchenddate'],        
                            
                            ])
                        }}
    </div>



    <div class="col-auto">
        <button type="submit" class="btn btn-primary btn-rounded btn-sm">
            <i class="fa fa-search"></i>
        </button>

    </div>
    @endif
    @include('admin.layouts.hidden', ['method'=>'SearchoForm'])
</div>