<?php

namespace App\Http\Controllers\admin;

use DB;
use PF;
use PT;
use Config;
use Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\productRepository;
use Illuminate\Support\Facades\Validator;
use App\Models\product;

class productController extends adminController {
    private $fieldnicknames;
    private $data;
    private $xmlDoc;

    private $productRepo;

    /**
     *TODO 建構子.
     */
    public function __construct(productRepository $productRepo) {
        //$this->limit="xx";
        parent::__construct();
        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');

        // FIXME 導覽列

        $this->data['nav'] = PT::nav($this->data['xmldoc'], 'product', $this->data['nav']);

        // FIXME 共用的hidden變數
        $this->data['hiddens'] = ['acity1title', 'acity2title', 'apattern', 'apingtotalnumberof', 'atotalupset', 'ahouseping', 'aauctionssortnum'];
        $this->productRepo = $productRepo;
        $this->db = new \App\Models\product();
        $this->data['displaynames'] = $this->productRepo->getFieldTitleArray();

        //$this->data['displaynames'] = $this->db->fieldnicknames;
    }

    /**
     * TODO 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        //FIXME 定義那些欄位可以搜尋
        $fieldsearchname = [
            '' => '請選擇',
            'productkind' => $this->data['displaynames']['productkind'],
            'producttitle' => $this->data['displaynames']['producttitle'],
            'number' => $this->data['displaynames']['number'],
            'tenderdate' => $this->data['displaynames']['tenderdate'],
            'online|INT' => $this->data['displaynames']['online'],
            'court' => $this->data['displaynames']['court'],
            'location' => $this->data['displaynames']['location'],
            'proofreadingday' => $this->data['displaynames']['proofreadingday'],
            'auctions' => $this->data['displaynames']['auctions'],
            'debtor' => $this->data['displaynames']['debtor'],
            'beattime' => $this->data['displaynames']['beattime'],
            'totalupset|INT' => $this->data['displaynames']['totalupset'],
            'nocross_point' => $this->data['displaynames']['nocross_point'],
            'margin' => $this->data['displaynames']['margin'],
            'mainlawnestablishment' => $this->data['displaynames']['mainlawnestablishment'],
            'city1title' => $this->data['displaynames']['city1title'],
            'city2title' => $this->data['displaynames']['city2title'],
            'attachedtolawnestablishment' => $this->data['displaynames']['attachedtolawnestablishment'],
            'address' => $this->data['displaynames']['address'],
            'additionalping' => $this->data['displaynames']['additionalping'],
            'pattern' => $this->data['displaynames']['pattern'],
            'postulateping' => $this->data['displaynames']['postulateping'],
            'buildname' => $this->data['displaynames']['buildname'],
            'noticelawnestablishment' => $this->data['displaynames']['noticelawnestablishment'],
            'houseage|INT' => $this->data['displaynames']['houseage'],
            'stakeholdersfloor' => $this->data['displaynames']['stakeholdersfloor'],
            'postulatemorethan' => $this->data['displaynames']['postulatemorethan'],
            'pingtotalnumberof|INT' => $this->data['displaynames']['pingtotalnumberof'],
            'storey' => $this->data['displaynames']['storey'],
            'floorprice' => $this->data['displaynames']['floorprice'],
            'architecture' => $this->data['displaynames']['architecture'],
            'aftermakingvalue_added' => $this->data['displaynames']['aftermakingvalue_added'],
            'management' => $this->data['displaynames']['management'],
            'parkingmode' => $this->data['displaynames']['parkingmode'],
            'fees' => $this->data['displaynames']['fees'],
            'transportfunction' => $this->data['displaynames']['transportfunction'],
            'facingthelaneis' => $this->data['displaynames']['facingthelaneis'],
            'schooldistrict' => $this->data['displaynames']['schooldistrict'],
            'sealedhuman' => $this->data['displaynames']['sealedhuman'],
            'thenumberofbidders' => $this->data['displaynames']['thenumberofbidders'],
            'thebidprice' => $this->data['displaynames']['thebidprice'],
            'memo' => $this->data['displaynames']['memo'],
            'landprice' => $this->data['displaynames']['landprice'],
            'increasetheamountof' => $this->data['displaynames']['increasetheamountof'],
            'landaddress' => $this->data['displaynames']['landaddress'],
            'currentvalues' => $this->data['displaynames']['currentvalues'],
            'bidsrecords' => $this->data['displaynames']['bidsrecords'],
            'buildings' => $this->data['displaynames']['buildings'],
            //'courttranscript' => $this->data['displaynames']['courttranscript'],
            'hisrightis' => $this->data['displaynames']['hisrightis'],
            'landvalue1' => $this->data['displaynames']['landvalue1'],
            //'transcriptinformation' => $this->data['displaynames']['transcriptinformation'],
            'landvalue2' => $this->data['displaynames']['landvalue2'],
            'mrtland' => $this->data['displaynames']['mrtland'],
            'mrtstation' => $this->data['displaynames']['mrtstation'],
            'bidwere' => $this->data['displaynames']['bidwere'],
            'locationadmin' => $this->data['displaynames']['locationadmin'],
            // 'point' => $this->data['displaynames']['point'],
            // 'lat' => $this->data['displaynames']['lat'],
            // 'lng' => $this->data['displaynames']['lng'],
            //'url' => $this->data['displaynames']['url'],
        ];
        //FIXME 定義那些日期欄位可以搜尋
        $fieldsearchdatename = [
            'created_at' => $this->data['displaynames']['created_at'],

            //'createdate' => $this->data['displaynames']['createdate'],

            'updated_at' => $this->data['displaynames']['updated_at'],
        ];
        $this->data['fieldsearchname'] = $fieldsearchname;
        $this->data['fieldsearchdatename'] = $fieldsearchdatename;

        $rows = $this->getRows($request);

        //$rows->limit(1);
        //PF::dbSqlPrint($rows);
        $rows = $rows->paginate(100);
        // 顯示sqlcmd

        $this->data['rows'] = $rows;

        return view('admin.product.index', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料Rows.
     *
     * @return \Illuminate\Http\Response
     */
    public function getRows($request) {
        $rows = DB::table('product')->selectRaw('product.*');

        $rows->myWhere($request->input('searchname'), $request->input('search'), $this->data['displaynames'], 'N');

        //依條件時間搜尋資料的SQL語法
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|>=', $request->input('searchstartdate'), $this->data['displaynames'], 'N');
        $rows->myWhere('convert(' . $request->input('searchdatename') . ',DATE)|<=', $request->input('searchenddate'), $this->data['displaynames'], 'N');
        $rows->myWhere('city1title|S', $this->data['acity1title'], $this->data['displaynames'], 'N');
        $rows->myWhere('city2title|S', $this->data['acity2title'], $this->data['displaynames'], 'N');

        if ('土地' != $this->data['apattern']) {
            $rows->myWhere('pattern|S', $this->data['apattern'], $this->data['displaynames'], 'N');
        } elseif ('土地' == $pattern) {
            $rows->myWhere('pattern|INS', PF::getConfig('landkind'), $this->data['displaynames'], 'N');
        }

        $rows->myWhere('address', $this->data['aaddress'], $this->data['displaynames'], 'N');

        if ('' != $this->data['apingtotalnumberof']) {
            $pingtotalnumberofSplit = explode('~', $this->data['apingtotalnumberof']);
            $rows->myWhere('pingtotalnumberof|>', $pingtotalnumberofSplit[0], $this->data['displaynames'], 'N');
            $rows->myWhere('pingtotalnumberof|<', $pingtotalnumberofSplit[1], $this->data['displaynames'], 'N');
        }

        if (null != $this->data['atotalupset']) {
            $totalupsetSplit = explode('~', $this->data['atotalupset']);
            $rows->myWhere('totalupset|>', $totalupsetSplit[0], $this->data['displaynames'], 'N');
            $rows->myWhere('totalupset|<', $totalupsetSplit[1], $this->data['displaynames'], 'N');
        }
        if ('' != $this->data['apingtotalnumberof']) {
            $pingtotalnumberofSplit = explode('~', $this->data['apingtotalnumberof']);
            $rows->myWhere('pingtotalnumberof|>=', $pingtotalnumberofSplit[0], 'kindid', 'N');
            $rows->myWhere('pingtotalnumberof|<=', $pingtotalnumberofSplit[1], 'kindid', 'N');
        }

        if ('' != $this->data['ahouseping']) {
            $houseageSplit = explode('~', $this->data['ahouseping']);
            $rows->myWhere('houseage|>', $houseageSplit[0], $this->data['displaynames'], 'N');
            $rows->myWhere('houseage|<', $houseageSplit[1], $this->data['displaynames'], 'N');
        }
        $rows->myWhere('auctionssortnum|INT', $this->data['aauctionssortnum'], $this->data['displaynames'], 'N');

        if ($request->input('sortname')) {
            $rows->orderBy($request->input('sortname'), 'desc' == $request->input('sorttype') ? $request->input('sorttype') : 'asc');
        } else {
            $rows->orderBy('productid', 'desc');
        }

        return $rows;
    }

    /**
     * TODO 資料建立.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request) {
        $rows = DB::table('product')->select('auctionssortnum');
        $rows->orderBy('productid', 'desc');
        $rows->limit(1);
        //PF::dbSqlPrint($rows);
        if ($rows->count() > 0) {
            $rs = $rows->first();
            $this->data['auctionssortnum'] = $rs->auctionssortnum + 1;
        }

        return view('admin.product.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request) {
        try {
            $f = public_path('images/json/' . $this->data['productid'] . '.json');
            if (false == \File::exists($f)) {
                throw new \CustomException("No data");
            }
            $jsonbody = \File::get($f);
            if ($jsonbody != "") {
                $json = \PF::json_decode($jsonbody, true); //ture=>可以用$json['yyy'];false=>可以直接update
                $this->data['url'] = $json['url'];
            }

            $this->data['body'] = \File::get($f);
        } catch (\Exception $e) {
            die($e->getMessage());
        }

        return view('admin.product.show', [
            'data' => $this->data,
        ]);
    }

    public function showstore(Request $request) {
        //PF::printr($this->data['number']);
        $hs = new \App\Services\htmlparseService($this->data);
        $hs->debug = 1;
        $hs->run($request);

        return response($hs->resultmessage);
    }

    /**
     * TODO 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request) {
        $edit = $this->data['edit'];

        $validators = null;
        $validators['edit'] = 'required';
        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }

        $rows = DB::table('product');
        $rows->select('product.*');

        $rows->Where('productid', '=', $edit);
        $rows->limit(1);

        //dd($row);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            //將資料庫欄位與值全部導入到$this->data
            foreach ($rows->first() as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('no data');
        }

        return view('admin.product.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * TODO 資料新增編輯儲存.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request) {
        $edit = $request->input('edit');
        //FIXME 那些欄位為必填判斷
        $validators = null;

        $validators['number'] = ['required'];
        $validators['city1title'] = ['required'];
        $validators['city2title'] = ['required'];
        $validators['address'] = ['required'];

        $validator = \Validator::make($this->data, $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }

        $inputs = $request->all();

        // $inputs['number'] = $this->data['number']; /*案號-*/
        // $inputs['online'] = $this->data['online']; /*上下架-*/
        // $inputs['court'] = $this->data['court']; /*法院-*/
        // $inputs['location'] = $this->data['location']; /*放置位置-  //[新增物件:0001], [黃金店面:storefront], [整棟透天:allowed], [辦公廠房:factories], [低總價成家專案:married], [台北捷運:TaipeiMRT], [機場快線:AirportExpressLine], [台中捷運:TaichungMRT], [高雄捷運:KaohsiungMRT], [新品上市:new], [首頁下方大圖:home1], [首頁下方小圖:home2], */
        // $inputs['proofreadingday'] = $this->data['proofreadingday']; /*校對日-*/
        // $inputs['auctions'] = $this->data['auctions']; /*拍賣情況-  //[待標:1], [流標:2], [應買:3], [停拍:4], [撤回:5], [得標:6], [承受:7], */
        // $inputs['tenderdate'] = $this->data['tenderdate']; /*投標日-*/
        // $inputs['debtor'] = $this->data['debtor']; /*債務人-*/
        // $inputs['beattime'] = $this->data['beattime']; /*拍次-*/
        // $inputs['totalupset'] = $this->data['totalupset']; /*總底價-*/
        // $inputs['nocross_point'] = $this->data['nocross_point']; /*點交否-*/
        // $inputs['margin'] = $this->data['margin']; /*保證金-*/
        // $inputs['mainlawnestablishment'] = $this->data['mainlawnestablishment']; /*權狀坪數-*/
        // $inputs['city1title'] = $this->data['city1title']; /*縣市-*/
        // $inputs['city2title'] = $this->data['city2title']; /*鄉鎮-*/
        // $inputs['attachedtolawnestablishment'] = $this->data['attachedtolawnestablishment']; /*附建坪數-*/
        // $inputs['address'] = $this->data['address']; /*地址-*/
        // $inputs['additionalping'] = $this->data['additionalping']; /*增建坪-*/
        // $inputs['pattern'] = $this->data['pattern']; /*型態-*/
        // $inputs['postulateping'] = $this->data['postulateping']; /*公設坪-*/
        // $inputs['buildname'] = $this->data['buildname']; /*大樓名稱-*/
        // $inputs['noticelawnestablishment'] = $this->data['noticelawnestablishment']; /*公告建坪-*/
        // $inputs['houseage'] = $this->data['houseage']; /*屋齡-  //[10年以下:0~10], [10~20年:10~20], [20~30年:20~30], [30年以上:30~99], */
        // $inputs['stakeholdersfloor'] = $this->data['stakeholdersfloor']; /*持分地坪-*/
        // $inputs['postulatemorethan'] = $this->data['postulatemorethan']; /*公設比-*/
        // $inputs['pingtotalnumberof'] = $this->data['pingtotalnumberof']; /*總坪數-*/
        // $inputs['storey'] = $this->data['storey']; /*樓高-*/
        // $inputs['floorprice'] = $this->data['floorprice']; /*坪單價-*/
        // $inputs['architecture'] = $this->data['architecture']; /*架構-*/
        // $inputs['aftermakingvalue_added'] = $this->data['aftermakingvalue_added']; /*拍後增值-*/
        // $inputs['management'] = $this->data['management']; /*管理方式-*/
        // $inputs['parkingmode'] = $this->data['parkingmode']; /*停車方式-*/
        // $inputs['fees'] = $this->data['fees']; /*費用-*/
        // $inputs['transportfunction'] = $this->data['transportfunction']; /*交通機能-*/
        // $inputs['facingthelaneis'] = $this->data['facingthelaneis']; /*面臨路寬-*/
        // $inputs['schooldistrict'] = $this->data['schooldistrict']; /*學區-*/
        // $inputs['sealedhuman'] = $this->data['sealedhuman']; /*查封人-*/
        // $inputs['thenumberofbidders'] = $this->data['thenumberofbidders']; /*投標人數-*/
        // $inputs['thebidprice'] = $this->data['thebidprice']; /*得標價格-*/
        // $inputs['memo'] = $this->data['memo']; /*附註說明-*/
        // $inputs['landprice'] = $this->data['landprice']; /*土地公現-*/
        // $inputs['increasetheamountof'] = $this->data['increasetheamountof']; /*加價金額-*/
        // $inputs['landaddress'] = $this->data['landaddress']; /*土地地址-*/
        // $inputs['currentvalues'] = $this->data['currentvalues']; /*公告現值-*/
        // $inputs['bidsrecords'] = $this->data['bidsrecords']; /*流標記錄-*/
        // $inputs['courttranscript'] = $this->data['courttranscript']; /*法院筆錄-*/
        // $inputs['buildings'] = $this->data['buildings']; /*建物-*/
        // $inputs['hisrightis'] = $this->data['hisrightis']; /*他項權利-*/
        // $inputs['landvalue1'] = $this->data['landvalue1']; /*土地增值金額1-*/
        // $inputs['transcriptinformation'] = $this->data['transcriptinformation']; /*謄本資料-*/
        // $inputs['landvalue2'] = $this->data['landvalue2']; /*土地增值金額2-*/
        // $inputs['mrtland'] = $this->data['mrtland']; /*捷運路線-*/
        // $inputs['mrtstation'] = $this->data['mrtstation']; /*捷運站名-*/
        // $inputs['bidwere'] = $this->data['bidwere']; /*得標人-*/
        // $inputs['locationadmin'] = $this->data['locationadmin']; /*管理者放置位置-  //[熱門推薦:master], */
        // $inputs['point'] = $this->data['point']; /*google 經緯度-*/
        // $inputs['auctionssortnum'] = $auctionssortnum; /*排序號碼-*/


        // $inputs['lat'] = $this->data['lat']; /*經度-*/
        // $inputs['lng'] = $this->data['lng']; /*緯度-*/
        // $inputs['img']=$this->data['img'];/*連結照片-*/
        try {
            //FIXME 檔案上傳取得值
            $upload = new \App\Libraries\UploadFile();
            $upload->request = $request;
            $upload->inputs = $inputs;
            $upload->folder = 'images/product/';
            $upload->width = '800';
            $upload->height = '600';
            //$upload->limitext = config('app.FileLimit');
            $inputs = $upload->execute();
        } catch (\Exception $e) {
            die($e->getMessage());
        }

        if ('' == $edit) {
            $inputs['createdate'] = date('Y-m-d H:i:s'); /*建立時間-*/
            $inputs['hits'] = 0; /*點率閱-*/
            $inputs['created_at'] = date('Y-m-d H:i:s'); /*建立時間-*/
            $row = $this->productRepo->create($inputs);

            $edit = $row->productid;
            $this->data['alert'] = '新增成功';
        } else {
            //  \PF::printr($inputs);
            $this->productRepo->update($inputs, $edit);
            //   exit();
            if ('' == $this->data['gobackurl']) {
                return back()->with('js', "_toast('更新成功',500)");
            }
            $this->data['alert'] = '更新成功';
        }

        //Session::flash('msg', $msg);
        //return back()->with('success','update ok');
        //return back()->with('js','alert(\'更新成功\')');
        return view('admin.layouts.postsubmit', [
            'data' => $this->data,
        ]);
    }

    /**
     * TODO 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request) {
        $rows = DB::table('product')->selectRaw('img');
        $rows->myWhere('productid|ININT', $this->data['del'], 'del', 'Y');
        $rows = $rows->get();
        //PF::dbSqlPrint($rows);
        //PF::dbDeleteFile('images/product', $rows);

        $rows = $this->db->myWhere('productid|ININT', $this->data['del'], 'del', 'Y');
        //PF::dbSqlPrint($rows);
        $rows->get()->each(function ($row) {
            $row->delete();
        });

        //$this->db::destroy(explode(",", $this->data['del']));

        return view('admin/layouts/postsubmit', [
            'data' => $this->data,
        ]);
    }

    /**
     * TODO 資料Excel匯出.
     *
     * @return \Illuminate\Http\Response
     */
    public function excelexport(Request $request) {
        $rows = $this->getRows($request);

        return \Excel::download(new \App\Exports\productExport($this->data, $this->data['displaynames'], $rows), '法拍物件.xlsx');
    }
    public function parse(Request $request) {
        $this->data['body'] = $request->input('body');
        $this->data['url'] = $request->input('url');
        if ($request->input('body') != "") {
            $json = \PF::json_decode($request->input('body'), true); //ture=>可以用$json['yyy'];false=>可以直接update

            $request->merge($json);
            $woo104Service = new \App\Services\woo104Service();
            $inputs = $woo104Service->parse($request, 1);

            $input1s = [];
            // 建立 product 模型的實例

            $productInstance = new product();

            foreach ($productInstance->fieldInfo as $k => $v) {
                //\PF::printr(["k", $k]);
                $input1s[$k . "-" . $v['title']] = $inputs[$k];
            };
            // foreach ($inputs as $k => $v) {
            //     $input1s[$k] = $v;
            // };

            $this->data['body1'] = json_encode($input1s, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        }

        return view('admin.product.show', [
            'data' => $this->data,
        ]);
    }
}
