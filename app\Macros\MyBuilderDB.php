<?php

namespace App\Macros;

use DB;
use PF;
use CustomException;

/***
"功能名稱":"共用類別-資料庫語法",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
 ***/
class MyBuilderDB {
    protected $builder;
    protected $pdo;
    protected $searchname;
    protected $codition;
    protected $search;
    protected $title;
    protected $searchtempvalue;
    protected $andor;
    protected $isrequired;

    public function __construct($builder) {
        $this->builder = $builder;
    }

    public function sql($searchname, $search, $nicknamearray, $isrequired) {
        //$sSQLCmd .= PF_dbSqlNsearch('signupid|N', $signupid, 'signupid', 'Y');

        if ('' == $searchname) {
            return $this->builder;
        }

        if ('0' == $isrequired || 'N' == $isrequired || '' == $isrequired) {
            if (PF::isEmpty($search)) {
                return $this->builder;
            }
        }

        if (substr_count($searchname, '&') > 0 || substr_count($searchname, ':') > 0) {
            return $this->builder;
        }
        $this->isrequired = $isrequired;
        $this->searchname = $searchname;

        $this->search = $search;

        if ('1' == $this->isrequired || 'Y' == $this->isrequired) {
            if ('string' == gettype($nicknamearray)) {
                $this->title = $nicknamearray;
            } else {
                $this->title = $nicknamearray[$searchname];
            }
            if ('' == $this->title) {
                $this->title = $this->searchname;
            }
            if (PF::isEmpty($this->search)) {
                throw new CustomException(__($this->title) . ' is empty');
            }
        }

        //PF::printr(2);
        $this->sqlcommand();

        return  $this->builder;
    }

    /**
     * Prepare the values.
     */
    private function sqlcommand() {
        $x = 0;
        if (is_array($this->search)) {
            $cc = '';
            $tmp = '';
            foreach ($this->search as $value) {
                $tmp .= $cc . $value;
                $cc = ',';
            }
            $this->search = $tmp;
        }

        $search = trim($this->search);

        $searchnameSplit = explode('^', $this->searchname);

        $searchSplit = explode('^', $search);

        $andor = 'and';

        if (count($searchnameSplit) != count($searchSplit)) {
            $andor = 'or';
        }

        $this->builder->where(function ($query) use ($searchnameSplit, $searchSplit, $andor) {
            //    PF::printr(count($searchnameSplit));
            for ($i = 0; $i < count($searchnameSplit); ++$i) {
                $searchtempvalue = count($searchSplit) == count($searchnameSplit) ? $searchSplit[$i] : $searchSplit[0];
                if (substr_count($searchtempvalue, ' and ') > 0) {
                    $SearchTempSplit = explode(' and ', $searchtempvalue);
                } else {
                    $SearchTempSplit = explode(' or ', $searchtempvalue);
                }
                $dbrejectstring = config('app.dbrejectstring');
                for ($j = 0; $j < count($SearchTempSplit); ++$j) {
                    $array = explode('|', $searchnameSplit[$i]);
                    $searchtempvalue = $SearchTempSplit[$j];
                    $sn1 = $array[0];
                    $sn2 = '';
                    if (isset($array[1])) {
                        $sn2 = $array[1];
                    }
                    $sn2 = strtoupper($sn2);
                    if (\Str::contains($sn1, $dbrejectstring)) {
                        throw new \CustomException('input wrong format :' . htmlspecialchars($sn1));
                    }



                    // if (in_array($sn1, config('app.dbrejectstring'))) {
                    //     throw new \CustomException('input wrong format :'.htmlspecialchars($sn1));
                    // }

                    if (PF::isDate($searchtempvalue)) {
                        switch ($sn2) {
                            case '<':
                            case '<=':
                            case '>':
                            case '>=':
                            case '<>':
                                if (mb_substr_count($searchtempvalue, '-') > 0) {
                                    $sn1 = 'convert(' . $sn1 . ',DATE)';
                                    if (mb_substr_count($searchtempvalue, ':') > 0) {
                                        $sn1 = 'convert(' . $sn1 . ',DATETIME)';
                                    }
                                }
                                break;
                        }
                    }
                    $sn1 = str_replace(' ', '', $sn1);
                    $sn1 = str_replace('--', '', $sn1);
                    $sn1 = addslashes($sn1);

                    $sn1 = DB::raw($sn1);

                    switch ($sn2) {
                        case 'INT':
                        case 'N':

                            if (false == is_numeric($searchtempvalue)) {
                                if ('Y' == $this->isrequired) {
                                    throw new CustomException(__($this->title) . ' not number type');
                                }
                                break;
                            }
                            if ('and' == $andor) {
                                $query->where($sn1, '=', $searchtempvalue);
                            } else {
                                $query->orWhere($sn1, '=', $searchtempvalue);
                            }
                            break;
                        case 'D':
                            if (false == PF::isDate($searchtempvalue)) {
                                if ('Y' == $this->isrequired) {
                                    throw new CustomException(__($this->title) . ' is empty');
                                }
                                throw new Exception($searchtempvalue . 'not date type');
                            }
                            if ('and' == $andor) {
                                $query->where(DB::raw('convert(' . $sn1 . ',DATE)'), '=', DB::raw('convert(\'' . $searchtempvalue . '\',DATE)'));
                            } else {
                                $query->orWhere(DB::raw('convert(' . $sn1 . ',DATE)'), '=', DB::raw('convert(\'' . $searchtempvalue . '\',DATE)'));
                            }
                            break;
                        case 'S':
                        case '=':
                            if ('and' == $andor) {
                                $query->where($sn1, '=', $searchtempvalue);
                            } else {
                                $query->orWhere($sn1, '=', $searchtempvalue);
                            }
                            break;
                        case 'ININT':
                        case 'INS':
                            $arrs = explode(',', $searchtempvalue);
                            $searchtempvalue = '';
                            $cc = '';
                            if ('ININT' == $sn2) {
                                foreach ($arrs as $key => $item) {
                                    if ('' != $item && is_numeric($item)) {
                                        $searchtempvalue .= $cc . $item;
                                        $cc = ',';
                                    }
                                }
                            } else {
                                foreach ($arrs as $key => $item) {
                                    if ('' != $item) {
                                        $searchtempvalue .= $cc . $item;
                                        $cc = ',';
                                    }
                                }
                            }

                            if ('Y' == $this->isrequired) {
                                if ('' == $searchtempvalue) {
                                    throw new CustomException(__($this->title) . ' is empty');
                                }
                            }
                            if ('' != $searchtempvalue) {
                                if ('and' == $andor) {
                                    $query->whereIn($sn1, explode(',', $searchtempvalue));
                                } else {
                                    $query->orWhereIn($sn1, explode(',', $searchtempvalue));
                                }
                            }
                            break;
                        case 'NOTIN':
                        case 'NOTINT':
                            if ('and' == $andor) {
                                $query->whereNotIn($sn1, explode(',', $searchtempvalue));
                            } else {
                                $query->orWhereNotIn($sn1, explode(',', $searchtempvalue));
                            }
                            break;
                        case '<':
                        case '<=':
                        case '>':
                        case '>=':
                        case '<>':
                            if ('and' == $andor) {
                                $query->where($sn1, $sn2, $searchtempvalue);
                            } else {
                                $query->orWhere($sn1, $sn2, $searchtempvalue);
                            }
                            break;
                        case 'NULL':
                            if ('and' == $andor) {
                                $query->whereNull($sn1);
                            } else {
                                $query->orWhereNull($sn1);
                            }
                            break;
                        case 'NOTNULL':
                            if ('and' == $andor) {
                                $query->whereNotNull($sn1);
                            } else {
                                $query->orWhereNotNull($sn1);
                            }
                            break;
                        case 'OR':

                            $query->where(function ($query) use ($sn1, $searchtempvalue) {
                                $items = explode(',', $searchtempvalue);
                                foreach ($items as $k => $v) {
                                    $query->orWhere($sn1, 'like', '%' . $v . '%');
                                }

                                return $query;
                            });

                            break;

                        default:

                            switch ($searchtempvalue) {
                                case 'NULL':
                                    if ('and' == $andor) {
                                        $query->whereNull($sn1);
                                    } else {
                                        $query->orWhereNull($sn1);
                                    }
                                    break;
                                case 'NOTNULL':
                                    if ('and' == $andor) {
                                        $query->whereNotNull($sn1);
                                    } else {
                                        $query->orWhereNotNull($sn1);
                                    }
                                    break;
                                default:
                                    if ('and' == $andor) {
                                        $query->where($sn1, 'like', '%' . $searchtempvalue . '%');
                                        //$query->whereRaw("(? like '%?%')", [$sn1,$searchtempvalue]);
                                    } else {
                                        //$query->orWhereRaw("(? like '%?%')", [$sn1,$searchtempvalue]);
                                        $query->orWhere($sn1, 'like', '%' . $searchtempvalue . '%');
                                    }
                                    break;
                            }
                    }
                }
            }
        });
    }
}
