@extends('layouts.master')

@section('css')
    <style>
        /* 確保 header 登入圖示正確顯示在右邊 */
        .header_wrap>a[title*="登入"],
        .header_wrap>a[title*="會員"] {
            position: absolute !important;
            right: 9px !important;
            top: calc((100% - 51px) / 2) !important;
            z-index: 11 !important;
        }

        @media (min-width: 360px) {

            .header_wrap>a[title*="登入"],
            .header_wrap>a[title*="會員"] {
                right: 15px !important;
                top: calc((100% - 51px) / 2) !important;
            }
        }

        @media (min-width: 1200px) {

            .header_wrap>a[title*="登入"],
            .header_wrap>a[title*="會員"] {
                right: 1.35% !important;
            }
        }
    </style>
@endsection

@section('content')
    <!-- 標題區域 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                    加入會員<br>
                    <span class="text-yellow-300">掌握賺錢脈動</span>
                </h1>

            </div>
        </div>
    </div>

    <!-- 主要內容區域 -->
    <div class="container mx-auto px-4 py-12">
        <div class="max-w-7xl mx-auto">
            <div class="grid lg:grid-cols-2 gap-8">

                <!-- 註冊表單區域 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">註冊成為會員</h3>
                        <p class="text-gray-600">加入我們，開啟您的投資之路</p>
                    </div>

                    <form name="oForm1" class="space-y-6" id="oForm1" method="post"
                        action="{{ \request()->middlewareurl }}create" novalidate>
                        @csrf

                        <!-- 姓名輸入框 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-gray-700">姓名</label>
                            <input name="name" type="text" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"
                                placeholder="請輸入您的姓名">
                            <div class="invalid-feedback text-red-500 text-sm hidden">請輸入姓名</div>
                        </div>

                        <!-- 手機輸入框 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-gray-700">手機號碼</label>
                            <input type="tel" required name="mobile" placeholder="例如：09123456789"
                                pattern="09[1-8][0-9]([|]?)[0-9]{3}[0-9]{3}"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400">
                            <div class="invalid-feedback text-red-500 text-sm hidden">請輸入正確的手機號碼格式</div>
                        </div>

                        <!-- 電子信箱輸入框 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-gray-700">電子信箱</label>
                            <input name="email" type="email" required placeholder="例如：<EMAIL>"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400">
                            <div class="invalid-feedback text-red-500 text-sm hidden">請輸入正確的電子信箱格式</div>
                        </div>

                        <!-- 密碼輸入框 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-gray-700">密碼</label>
                            <input name="password" type="password" required minlength="8" placeholder="密碼最少為8個字元"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400">
                            <div class="invalid-feedback text-red-500 text-sm hidden">密碼最少需要8個字元</div>
                        </div>

                        <!-- 註冊按鈕 -->
                        <div class="pt-4">
                            <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                                立即註冊
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 登入表單區域 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">登入會員帳號</h3>
                        <p class="text-gray-600">歡迎回來，請登入您的帳號</p>
                    </div>

                    <form name="oForm2" class="space-y-6" id="oForm2" method="post"
                        action="{{ \request()->middlewareurl }}login/store" novalidate>
                        @csrf

                        <!-- 手機號碼輸入框 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-gray-700">手機號碼</label>
                            <input name="mobile" type="tel" placeholder="請輸入手機號碼" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 hover:border-gray-400">
                            <div class="invalid-feedback text-red-500 text-sm hidden">請輸入手機號碼</div>
                        </div>

                        <!-- 密碼輸入框 -->
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-gray-700">密碼</label>
                            <input name="password" type="password" placeholder="請輸入密碼" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 hover:border-gray-400">
                            <div class="invalid-feedback text-red-500 text-sm hidden">請輸入密碼</div>
                        </div>

                        <!-- 忘記密碼連結 -->
                        <div class="text-right">
                            <a href="{{ url('/') }}/member/forgetpwd"
                                class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200">
                                忘記密碼？
                            </a>
                        </div>

                        <!-- 登入按鈕 -->
                        <div class="pt-4">
                            <button type="submit"
                                class="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-green-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                                立即登入
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 額外資訊區域 -->
            <div class="mt-12 text-center">
                <div class="bg-gray-50 rounded-xl p-8">
                    <h4 class="text-xl font-bold text-gray-800 mb-4">為什麼選擇我們？</h4>
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                    </path>
                                </svg>
                            </div>
                            <h5 class="font-semibold text-gray-800 mb-2">安全保障</h5>
                            <p class="text-gray-600 text-sm">銀行級加密技術保護您的個人資料</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <h5 class="font-semibold text-gray-800 mb-2">專業分析</h5>
                            <p class="text-gray-600 text-sm">提供最新的市場動態與投資建議</p>
                        </div>
                        <div class="text-center">
                            <div
                                class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                    </path>
                                </svg>
                            </div>
                            <h5 class="font-semibold text-gray-800 mb-2">24小時服務</h5>
                            <p class="text-gray-600 text-sm">全天候客服支援，隨時為您服務</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表單驗證 JavaScript -->
    <script>
        // 表單驗證功能
        (function() {
            'use strict';

            // 取得所有需要驗證的表單
            const forms = document.querySelectorAll('.needs-validation, #oForm1, #oForm2');

            // 為每個表單添加驗證事件
            Array.from(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();

                        // 顯示錯誤訊息
                        const invalidInputs = form.querySelectorAll(':invalid');
                        invalidInputs.forEach(function(input) {
                            const feedback = input.parentNode.querySelector(
                                '.invalid-feedback');
                            if (feedback) {
                                feedback.classList.remove('hidden');
                            }
                            input.classList.add('border-red-500', 'focus:ring-red-500');
                            input.classList.remove('border-gray-300', 'focus:ring-blue-500',
                                'focus:ring-green-500');
                        });
                    } else {
                        // 隱藏錯誤訊息
                        const feedbacks = form.querySelectorAll('.invalid-feedback');
                        feedbacks.forEach(function(feedback) {
                            feedback.classList.add('hidden');
                        });

                        const inputs = form.querySelectorAll('input');
                        inputs.forEach(function(input) {
                            input.classList.remove('border-red-500', 'focus:ring-red-500');
                            input.classList.add('border-gray-300');
                        });
                    }

                    form.classList.add('was-validated');
                }, false);

                // 即時驗證
                const inputs = form.querySelectorAll('input');
                inputs.forEach(function(input) {
                    input.addEventListener('input', function() {
                        if (input.checkValidity()) {
                            const feedback = input.parentNode.querySelector(
                                '.invalid-feedback');
                            if (feedback) {
                                feedback.classList.add('hidden');
                            }
                            input.classList.remove('border-red-500', 'focus:ring-red-500');
                            input.classList.add('border-gray-300');
                        }
                    });
                });
            });
        })();
    </script>
@endsection
