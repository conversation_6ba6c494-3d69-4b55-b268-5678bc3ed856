@extends('admin.layouts.master')
@section('css')
@endsection

@section('js')
@endsection
@section('nav')
{!!$data['nav']!!}
@endsection


@section('content')



<div class="container-fluid p-1">
    <form name="SearchoForm" class="form-inline" method="post" language="javascript"
        action="{{request()->getRequestUri()}}" onsubmit="return SearchoForm_onsubmit(this);">
        @include('admin.layouts.search', [])
    </form>
</div>




<div align="right" style=''>
    <form name="AddoForm" method="post" language="javascript" action="{{url('admin/adminuser')}}/create">
        <button type="submit" class="btn btn-info">新增</button>
        @include('admin.layouts.hidden', ['method'=>'AddoForm','data'=>$data])
    </form>
</div>
<div class="table-responsive-md">
    <table class="table table-striped table-hover  table-bordered table-fixed">

        <form name="SortoForm" method="post">
            @include('admin.layouts.hidden', ['method'=>'SortoForm','data'=>$data])
            <thead>
                <tr valign="top" align="left">

                    <th align="center" width="120">
                        <div class="form-row align-items-center">


                            @if (Auth::guard('admin')->user()->role=="999")
                            <div class="col-auto">
                                <button type="button" class="btn btn-danger"
                                    onclick="if (confirm('確定要刪除？')==false){return false;};document.forms['oForm'].action='{{url('admin/adminuser/destroy')}}';document.forms['oForm'].submit();">刪除</button>
                            </div>
                            <div class="col-auto">
                                <label class="h6 small"
                                    onClick="if (this.innerHTML=='全選'){try{checkAll(jQuery($('input[name=\'del[]\']')))}catch(e){alert('目前無可刪除資料');return false;};this.innerHTML='全不選';}else{try{uncheckAll($('input[name=\'del[]\']'))}catch(e){};this.innerHTML='全選';}">全選</label>
                            </div>
                            @endif

                        </div>

                    </th>
                    <th width="" id="account">帳號</th>
                    <th width="" id="name">姓名</th>
                    <th width="" id="email">E-Mail</th>
                    <th width="" id="role">角色</th>
                    <th width="" id="online">是否核可</th>
                    <th width="" id="created_at">建立時間</th>

                </tr>
            </thead>

        </form>


        <!--傳給下一頁的參數-->
        <form method="post" language="javascript" name="oForm" action="{{url('admin/adminuser')}}">

            @include('admin.layouts.hidden', ['method'=>'oForm'])
            <tbody>
                @foreach ($data['rows'] as $rs)

                <tr>

                    <td valign="top" title="編輯" align="center">
                        <div class="form-row align-items-center">
                            <div class="col-auto">
                                <button type="submit" class="btn btn-info"
                                    onclick="javascript:form.action='{{url('admin/adminuser')}}/edit?edit={{ $rs->id }}';">
                                    編輯</button>
                            </div>
                            @if (Auth::guard('admin')->user()->role=="999")
                            <div class="col-auto">                                
                                <input type="checkbox" name="del[]"  value="{{$rs->id}}">
                            </div>
                            @endif
                        </div>
                    </td>

                    <td>

                        {{$rs->account}}
                         </td>

                    <td>

                        {{$rs->name}}
                         </td>

                    <td>

                        <a href="mailto:{{ $rs->email}}">{{ $rs->email}}</a> 
                         
                    </td>
                    <td>

                        {{PF::xmlSearch($data['xmldoc'],"//參數設定檔/角色/KIND/傳回值","資料",$rs->role)}}

                         </td>
                    <td>

                        @if ($rs->online=="1")
                        是
                        @endif

                         </td>

                    <td>

                        {{PF::formatDate($rs->created_at)}}
                         </td>

                </tr>
                @endforeach

            </tbody>
        </form>


    </table>
</div>
{{ $data['rows']->links('layouts.paginate') }}




@endsection