<IfModule mod_rewrite.c>
    Options FollowSymLinks
    RewriteEngine on
    # xxx.com.tw => www.xxx.com.tw
    #RewriteCond %{HTTP_HOST} !^www\. [NC]
    #RewriteRule ^(.*)$ http://www.%{HTTP_HOST}/$1 [R=301,L]

    # http://www.abc.com.tw => https://www.abc.com.tw
    #RewriteCond %{HTTPS} !=on
    #RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    #把public目錄拿掉
    #RewriteCond %{REQUEST_URI} !^public
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
<files *.html>
    SetOutputFilter DEFLATE
</files>