@extends('layouts.master')
@section('css')
@endsection

@section('js')
    <script type="application/ld+json">
    [{
        "@context": "http://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [{
                "@type": "ListItem",
                "position": 1,
                "item": {
                    "@id": "{{ asset('/') }}",
                    "name": "首頁"
                }
            },
            {
                "@type": "ListItem",
                "position": 2,
                "item": {
                    "@id": "{{ url('/') }}/house",
                    "name": "法拍物件"
                }
            },
            {
                "@type": "ListItem",
                "position": 43,
                "item": {
                    "@id": "{{ url('/') }}/house/show/{{$data['productid']}}",
                    "name": "法拍-{{$data['city1title']}}{{$data['city2title']}}{{$data['address']}}"
                }
            }
        ]
    }]
</script>
@endsection

@section('content')
    <style>
        .imgcenter {
            margin-left: auto;
            margin-right: auto;
            display: block;
            min-height: 500px !important;
        }



        /*mobile  */
        @media screen and (max-width: 724px) and (min-width: 0) {
            .imgcenter {
                min-height: 300px !important;
            }
        }

        /*pc*/
        @media screen and (min-width: 725px) {
            .imgcenter {
                min-height: 500px !important;
            }
        }
    </style>


    <div class="theme-container">
        <div class="p-4 border-b border-gray-200 flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
                <h2 class="text-xl md:text-2xl font-bold text-gray-800">{{ $data['producttitle'] }}</h2>
                <p class="text-gray-600 mt-1 md:text-3xl font-bold flex items-center">
                    <i class="fas fa-map-marker-alt  mr-2 text-red-600 "></i>
                    {{ $data['city1title'] }}
                    {{ $data['city2title'] }}
                    {{ $data['address'] }}
                </p>
            </div>
            <div class="flex items-center mt-4 md:mt-0">
                <p class="text-2xl md:text-3xl font-bold text-red-600 mr-4">
                    {{ PF::formatNumber($data['totalupset'], 0) }}萬</p>

                <button type="button"
                    onclick="window.open('{{ url('/') }}/house/print?productid={{ $data['productid'] }}')"
                    class="print-button px-4 py-2 flex items-center bg-red-500 text-white rounded" id="print-button">

                    <span class="block text-center font-bold mt-1">列 印</span>
                </button>
            </div>
        </div>
        <div class="row m-1">
            <div class=" column col-lg-9 col-sm-12">

                <div class="mdc-card p-3 mt-3 single-property">

                    @if ($data['img'] != '')
                        <div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
                            <ol class="carousel-indicators">
                                {{-- 反轉圖片順序 --}}
                                @foreach (array_reverse(explode(',', $data['img'])) as $key => $item)
                                    <li data-target="#carouselExampleIndicators" data-slide-to="{{ $key }}"
                                        class="{{ $key == 0 ? 'active' : '' }}"></li>
                                @endforeach
                            </ol>
                            <div class="carousel-inner">
                                {{-- 反轉圖片順序 --}}
                                @foreach (array_reverse(explode(',', $data['img'])) as $key => $item)
                                    <div class="carousel-item {{ $key == 0 ? 'active' : '' }}">

                                        <img src="{{ url('/') }}/images/product/{{ $item }}"
                                            alt="{{ $rs->title }}" class="imgcenter houseimghouse">

                                    </div>
                                @endforeach
                            </div>
                            <button class="carousel-control-prev" type="button" data-target="#carouselExampleIndicators"
                                data-slide="prev">
                                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                <span class="sr-only">Previous</span>
                            </button>
                            <button class="carousel-control-next" type="button" data-target="#carouselExampleIndicators"
                                data-slide="next">
                                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                <span class="sr-only">Next</span>
                            </button>
                        </div>
                    @endif


                </div>

                @if ($data['pdf'] != '')
                    <button type="button" style="width:150px;margin-top: 10px"
                        onclick="window.open('{{ url('/') }}/images/product/{{ $data['pdf'] }}')"
                        class="btn btn-danger">拍賣單位公告</button>
                @endif


            </div>
            <style>
                @media screen and (max-width: 724px) and (min-width: 0) {
                    .carousel-item img {

                        width: 100%;
                    }
                }

                @media screen and (min-width: 725px) {
                    .carousel-item img {

                        max-height: 500px;
                    }
                }
            </style>
            <div class="mdc-card property-item grid-item column col-lg-3 col-sm-12">
                <div class="widget">
                    <CENTER>
                        <div class="widget-title bg-primary">【物件資料】</div>
                    </CENTER>

                    <div class="property-content-wrapper">
                        <div class="property-content">
                            <div class="content pb-3">
                                <div class="features mt-3">
                                    <p><span>公告底價</span>

                                        <span class="fw-900 primary-color">{{ PF::formatNumber($data['totalupset'], 0) }}
                                            萬</span>
                                    </p>
                                    <p><span>權狀坪數</span>
                                        <span>{{ PF::formatNumber($data['pingtotalnumberof'], 2) }} 坪</span>
                                    </p>
                                    <p><span>每坪單價</span>
                                        <span class="fw-900 primary-color">{{ $data['floorprice'] }} 萬</span>
                                    </p>
                                    <p><span>保 證 金</span>
                                        <span>{{ PF::formatNumber($data['totalupset'] * 0.2, 0) }} 萬</span>
                                    </p>

                                    <p><span>投標日期</span>
                                        <span class="fw-900 primary-color">{{ $data['tenderdate'] }}</span>
                                    </p>
                                    <p><span>拍　　次</span>
                                        <span>{{ $data['beattime'] }} </span>
                                    </p>
                                    <p><span>主建坪數</span><span>{{ $data['mainlawnestablishment'] }} 坪</span></p>
                                    <p><span>附屬建物</span><span>{{ str_replace('坪', '', $data['attachedtolawnestablishment']) }}
                                            坪</span></p>
                                    <p><span>公共設施</span><span>{{ $data['postulateping'] }} 坪</span></p>
                                    <p><span>增建面積</span><span>{{ $data['additionalping'] }} 坪</span></p>
                                    <p><span>持分地坪</span><span>{{ $data['stakeholdersfloor'] }} 坪</span></p>




                                </div>



                            </div>
                        </div>
                    </div>
                </div>
            </div>




        </div>
        <div class="widget">
            <div class="mdc-card property-item grid-item column-4 full-width-page">


                <div class="property-content-wrapper">

                    <div class="widget">
                        @if ($data['memo'] != '')
                            <div class="widget">
                                <CENTER>
                                    <div class="widget-title bg-primary">【本案特色分析報告】</div>
                                </CENTER>

                                <p> {!! $data['memo'] !!} </p>
                            </div>
                        @endif

                        <div class="widget">
                            <CENTER>
                                <div class="widget-title bg-primary">【電子地圖】</div>
                            </CENTER>
                            <iframe
                                src="https://maps.google.com.tw/maps?f=q&source=s_q&hl=zh-TW&geocode=&q={{ urlencode($data['city1title'] . $data['city2title'] . $data['address']) }}&ie=UTF8&z=16&output=embed"
                                width="100%" height="333" frameborder="0" style="border:0;" allowfullscreen=""
                                aria-hidden="false" tabindex="0"></iframe>
                        </div>
                        <!-- <div class="mdc-card p-3 mt-3">
                                                                                                                                                                                                    <h2 class="uppercase text-center fw-500 mb-2">案件街景</h2>
                                                                                                                                                                                                    <div id="contact-map"></div>
                                                                                                                                                                                                </div> -->

                        @if ($data['videourl'] != '')
                            <div class="mdc-card p-3 mt-3">
                                <h2 class="uppercase text-center fw-500 mb-2">案件影片</h2>
                                <div class="videoWrapper">
                                    <iframe src="{{ $data['videourl'] }}"></iframe>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="widget">
                    <CENTER>
                        <div class="widget-title bg-primary">【免費諮詢】</div>
                    </CENTER>


                    <form name="oForm" class="needs-validation" id="oForm" method="post" language="javascript"
                        action="{{ url('/') }}/house/store" novalidate>
                        <div class="card card-success">

                            <div class="card-body">
                                <div class="form-group row">
                                    <label class="col-md-2">姓名：<span class="text-danger p-1">

                                            *
                                        </span></label>
                                    <div class="col-md-10">

                                        <input type="text" required class="form-control" name="name"
                                            value="{{ $data['name'] }}">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-2">室內電話：<span class="text-danger p-1">

                                        </span></label>
                                    <div class="col-md-10">

                                        <input type="tel" class="form-control" name="tel"
                                            value="{{ $data['tel'] }}" placeholder="ex xxx-xxxxxxxx#ext" />
                                    </div>
                                </div>



                                <div class="form-group row">
                                    <label class="col-md-2">行動電話：<span class="text-danger p-1">
                                            *
                                        </span></label>
                                    <div class="col-md-10">

                                        <input type="tel" class="form-control" name="mobile"
                                            value="{{ $data['mobile'] }}" placeholder="ex 09123456789" required
                                            pattern="09[1-8][0-9]([\-|\s]?)[0-9]{3}\1[0-9]{3}" />
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-md-2">案件地址：<span class="text-danger p-1">

                                        </span></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="memo" required
                                            value="{{ $data['city1title'] }}{{ $data['city2title'] }} {{ $data['address'] }}">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-md-2">電子信箱：<span class="text-danger p-1">

                                            *
                                        </span></label>
                                    <div class="col-md-10">

                                        <input type="email" class="form-control" name="email"
                                            value="{{ $data['email'] }}" required placeholder="ex <EMAIL>" />
                                    </div>
                                </div>




                                <div class="row around-xs middle-xs p-2 mb-3">
                                    <button class="mdc-button mdc-button--raised bg-accent" type="submit">
                                        <span class="mdc-button__ripple"></span>
                                        <span class="mdc-button__label">填好送出</span>
                                    </button>
                                </div>
                                <script src="https://www.google.com/recaptcha/api.js?render={{ config('recaptcha.id') }}"></script>
                                <script>
                                    $("button").each(function(index) {
                                        $(this).prop('disabled', true);
                                    });
                                    grecaptcha.ready(function() {
                                        grecaptcha.execute("{{ config('recaptcha.id') }}", {
                                            action: 'homepage'
                                        }).then(function(token) {
                                            var recaptchaResponse = document.getElementById('recaptchaResponse');
                                            recaptchaResponse.value = token;
                                            $("button").each(function(index) {
                                                $(this).prop('disabled', false);
                                            });
                                        });
                                    });
                                </script> <input type="hidden" value=""
                                    name="google_recaptcha_token" id="recaptchaResponse">
                                <style>
                                    .grecaptcha-badge {
                                        visibility: hidden;
                                    }
                                </style>
                            </div>
                        </div>

                    </form>
                </div>
            </div>

            <div class="widget">
                <CENTER>
                    <div class="widget-title bg-primary">【超值物件】</div>
                </CENTER>
                <div class="properties-carousel">
                    <div class="swiper-container carousel-outer">
                        <div class="properties-wrapper row justify-content-center">
                            @foreach ($data['rows'] as $rs)
                                @include('layouts.houseitem')
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="widget">
                    <CENTER>
                        <div class="widget-title bg-primary">
                            發佈：{{ PF::formatDate($data['created_at']) }}｜更新：{{ PF::formatDate($data['updated_at']) }}｜點閱：{{ $data['hits'] }}
                        </div>
                    </CENTER>
                </div>
            </div>


        </div>
    </div>
@endsection
