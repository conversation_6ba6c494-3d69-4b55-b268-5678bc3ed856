Vue.mixin({
	methods: {
		onSortDirt: function (data, name, dirt) {
			console.log('onSortDirt' + name + dirt);
			var data = data;
			if (dirt == 'asc') {
				data.sort((a, b) => (a[name] > b[name] ? 1 : a[name] === b[name] ? (a.size > b.size ? 1 : -1) : -1));
			} else {
				data.sort((a, b) => (a[name] < b[name] ? 1 : a[name] === b[name] ? (a.size > b.size ? 1 : -1) : -1));
			}

			return data;
		},
		formatNumber: function (value, arg1) {
			if (typeof value == 'undefined') {
				return value;
			}
			try {
				return value.toLocaleString();
			} catch (error) {
				console.log('formatNumber error:' + value);
			}
			return value;
		},
		onAddTop: function (data, item) {
			data.unshift(JSON.parse(JSON.stringify(item)));
		},
		onAdd: function (data, item) {
			data.push(JSON.parse(JSON.stringify(item)));
		},
		onDel: function (data, index) {
			data.splice(index, 1);
		},
		onMoveRow: function (data, action, key) {
			switch (action) {
				case 'up':
					if (key > 0) {
						var tmp = data[key - 1];
						data[key - 1] = data[key];
						data[key] = tmp;
						data.push();
					}
					break;
				case 'down':
					if (data.length - 1 != key) {
						console.log('1', 1);
						var tmp = data[key + 1];
						data[key + 1] = data[key];
						data[key] = tmp;
						data.push();
					}
					break;
				case 'top':
					var tmp = data[0];
					data[0] = data[key];
					data[key] = tmp;
					data.push();
					break;
				case 'bottom':
					var tmp = data[data.length - 1];
					data[data.length - 1] = data[key];
					data[key] = tmp;
					data.push();
					break;
				default:
					break;
			}
		},
		selectFile: function (event, inputs) {
			name = event.currentTarget.name;
			inputs[name + '_filename'] = event.target.files[0].name;
			//console.log(vm.inputs[name+'_filename']);
			var reader = new FileReader();
			reader.readAsDataURL(event.target.files[0]);
			reader.onload = () => {
				inputs[name] = reader.result;
				//console.log(vm.inputs[name]);
			};
		},
		range: function (start, end) {
			list = [];
			for (i = start; i <= end; i++) list.push(i);
			return list;
		}
	}
	// computed: {
	//     range: function (start, end){
	//         list = []
	//         for (i = start; i <= end ; i ++) list.push(i);
	//         return list;
	//     }
	// }
});
