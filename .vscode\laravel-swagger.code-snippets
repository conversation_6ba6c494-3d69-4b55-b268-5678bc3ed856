{
  "api get path": {
    "prefix": "api swagger get path ",
    "scope": "php",
    "body": [
      "/**",
      "     * @OA\\Get(",
      "     *     path=\"/api/product/show/{number}\",security={{\"bearerAuth\":{}}},operationId=\"\",tags={\"後台/會員頻道\"},summary=\"列表\",description=\"\",",
      "      *     @OA\\Parameter(",
      "      *      name=\"number\",in=\"path\",description=\"id編號\",required=true,example=\"1123456\",@OA\\Schema(type=\"number\")",
      "      *     ),",
      "      *     @OA\\Parameter(",
      "      *      name=\"kind\",in=\"path\",description=\"種類\",required=true,example=\"xxx\",@OA\\Schema(type=\"string\")",
      "      *     ),",
      "     *   @OA\\Response(response=200,description=\"回覆\",",
      "     *     @OA\\JsonContent(type=\"object\",     ",
      "     *      @OA\\Property(property=\"resultcode\",type=\"integer\",description=\"訊息代碼\",),",
      "     *      @OA\\Property(property=\"resultmessage\",type=\"string\",description=\"訊息內容\"),          ",
      "      ",
      "    *      @OA\\Property(property=\"data\", type=\"object\",",
      "*      allOf={",
      "    *         @OA\\Schema(ref=\"#/components/schemas/memberfund\"),",
      "    *         @OA\\Schema(@OA\\Property(property=\"kindtitle\", type=\"string\",description=\"系列\", example=\"\") ),",
      "    *         @OA\\Schema(@OA\\Property(property=\"title\", type=\"string\",description=\"基金\", example=\"\") ), ",
      "    *     })",
      "",
      "     *     ,)",
      "     *),)",
      "*/"
    ]
  },
  "api get": {
    "prefix": "api swagger get ? query",
    "scope": "php",
    "body": [
      "/**",
      "     * @OA\\Get(",
      "     *     path=\"/api/product/show?id={number}\",security={{\"bearerAuth\":{}}},operationId=\"\",tags={\"後台/會員頻道\"},summary=\"列表\",description=\"\",",
      "      *     @OA\\Parameter(",
      "      *      name=\"number\",in=\"query\",description=\"id編號\",required=true,example=\"1123456\",@OA\\Schema(type=\"number\")",
      "      *     ),",
      "     *   @OA\\Response(response=200,description=\"回覆\",",
      "     *     @OA\\JsonContent(type=\"object\",     ",
      "     *      @OA\\Property(property=\"resultcode\",type=\"integer\",description=\"訊息代碼\",),",
      "     *      @OA\\Property(property=\"resultmessage\",type=\"string\",description=\"訊息內容\"),          ",
      "      ",
      "    *      @OA\\Property(property=\"data\", type=\"object\",",
      "*      allOf={",
      "    *         @OA\\Schema(ref=\"#/components/schemas/memberfund\"),",
      "    *         @OA\\Schema(@OA\\Property(property=\"kindtitle\", type=\"string\",description=\"系列\", example=\"\") ),",
      "    *         @OA\\Schema(@OA\\Property(property=\"title\", type=\"string\",description=\"基金\", example=\"\") ), ",
      "    *     })",
      "",
      "     *     ,)",
      "     *),)",
      "*/"
    ]
  },
  "api list include": {
    "prefix": "api swagger response include list 不分頁",
    "scope": "php",
    "body": [
      "/**",
      "     * @OA\\Post(",
      "     *     path=\"/api/admin/${1:member}\",security={{\"bearerAuth\":{}}},operationId=\"\",tags={\"後台/會員基金\"},summary=\"列表\",description=\"\",     ",
      "  *     @OA\\RequestBody(required=true,",
      "",
      "     *      @OA\\JsonContent(",
      "     *      allOf={",
      "     ",

      "     *         @OA\\Schema(@OA\\Property(property=\"search\",description=\"搜尋\",type=\"string\",example=\"\",)),",

      "     *     })",
      "",
      "     *   ,),",
      "     *   @OA\\Response(response=200,description=\"回覆\",",
      "     *     @OA\\JsonContent(type=\"object\",     ",
      "     *      @OA\\Property(property=\"resultcode\",type=\"integer\",description=\"訊息代碼\",),",
      "     *      @OA\\Property(property=\"resultmessage\",type=\"string\",description=\"訊息內容\"),     ",

      "     *      @OA\\Property(property=\"data\",  type=\"array\",",
      "     *      @OA\\Items(allOf={",
      "     *         @OA\\Schema(ref=\"#/components/schemas/${1:member}\"),",
      "     *         @OA\\Schema(@OA\\Property(property=\"kindtitle\", type=\"string\",description=\"系列\", example=\"\") ),",
      "     *         @OA\\Schema(@OA\\Property(property=\"title\", type=\"string\",description=\"基金\", example=\"\") ), ",
      "     *     }))",
      "     ",
      "     *      ,)",
      "     * ),)",
      "     */  "
    ]
  },
  "api list include page": {
    "prefix": "api swagger response include list 分頁",
    "scope": "php",
    "body": [
      "/**",
      "     * @OA\\Post(",
      "     *     path=\"/api/admin/${1:member}\",security={{\"bearerAuth\":{}}},operationId=\"\",tags={\"後台/會員基金\"},summary=\"列表\",description=\"\",     ",
      "  *     @OA\\RequestBody(required=true,",
      "",
      "     *      @OA\\JsonContent(",
      "     *      allOf={",
      "     ",
      "     *         @OA\\Schema(@OA\\Property(property=\"page\",description=\"頁數\",type=\"integer\",example=\"1\",)),",
      "     *         @OA\\Schema(@OA\\Property(property=\"pagesize\",description=\"筆數/頁\",type=\"integer\",example=\"10\",)),",
      "     *         @OA\\Schema(@OA\\Property(property=\"search\",description=\"搜尋\",type=\"string\",example=\"\",)),",

      "     *     })",
      "",
      "     *   ,),",
      "     *   @OA\\Response(response=200,description=\"回覆\",",
      "     *     @OA\\JsonContent(type=\"object\",     ",
      "     *      @OA\\Property(property=\"resultcode\",type=\"integer\",description=\"訊息代碼\",),",
      "     *      @OA\\Property(property=\"resultmessage\",type=\"string\",description=\"訊息內容\"),     ",
      "     *      @OA\\Property(property=\"data\", type=\"object\",",
      "     *          @OA\\Property(property=\"current_page\", type=\"integer\",description=\"目前頁數\", ),",
      "     *          @OA\\Property(property=\"total\", type=\"integer\",description=\"總頁數\", ),     ",
      "     * ",
      "     *      @OA\\Property(property=\"data\",  type=\"array\",",
      "     *      @OA\\Items(allOf={",
      "     *         @OA\\Schema(ref=\"#/components/schemas/${1:member}\"),",
      "     *         @OA\\Schema(@OA\\Property(property=\"kindtitle\", type=\"string\",description=\"系列\", example=\"\") ),",
      "     *         @OA\\Schema(@OA\\Property(property=\"title\", type=\"string\",description=\"基金\", example=\"\") ), ",
      "     *     }))",
      "     ",
      "     *      ),)",
      "     * ),)",
      "     */  "
    ]
  },
  "api item list": {
    "prefix": "api swagger add 第二層 Schema array ",
    "scope": "php",
    "body": [
      "*         @OA\\Schema(@OA\\Property(property=\"datas\", type=\"array\",",
      "     *           @OA\\Items(allOf={",
      "     *              @OA\\Schema(type=\"object\",@OA\\Property(property=\"title\", type=\"string\",description=\"標題\", example=\"\") ),",
      "     *              @OA\\Schema(type=\"object\",@OA\\Property(property=\"body\", type=\"string\",description=\"本文\", example=\"\") ),",
      "     *         }))),"
    ]
  },
  "api item object": {
    "prefix": "api swagger add 第二層 Schema object ",
    "scope": "php",
    "body": ["*         @OA\\Schema(@OA\\Property(property=\"${1:product}\", type=\"object\",",
"     * *           allOf={",
"     *         @OA\\Schema(ref=\"#/components/schemas/${1:product}\"),",
"     *              @OA\\Schema(type=\"object\",@OA\\Property(property=\"title\", type=\"string\",description=\"標題\", example=\"\") ),",
"     *              @OA\\Schema(type=\"object\",@OA\\Property(property=\"body\", type=\"string\",description=\"本文\", example=\"\") ),",
"     *         })),",

    ]
    },

  "api show include": {
    "prefix": "api swagger show include",
    "scope": "php",
    "body": [
      "/**",
      "     * @OA\\Post(",
      "     *     path=\"/api/admin/${1:member}/show\",security={{\"bearerAuth\":{}}},operationId=\"\",tags={\"後台/會員基金\"},summary=\"單筆顯示\",description=\"\",     ",
      "     *     @OA\\RequestBody(required=true,",
      "",
      "     *      @OA\\JsonContent(",
      "     *      allOf={",
      "     *         @OA\\Schema(@OA\\Property(property=\"id\", type=\"string\",description=\"編號\", example=\"\") ), ",
      "     *     })",
      "",
      "     *   ,),",
      "     *   @OA\\Response(response=200,description=\"回覆\",",
      "     *     @OA\\JsonContent(type=\"object\",     ",
      "     *      @OA\\Property(property=\"resultcode\",type=\"integer\",description=\"訊息代碼\",),",
      "     *      @OA\\Property(property=\"resultmessage\",type=\"string\",description=\"訊息內容\"),          ",
      "      ",
      "    *      @OA\\Property(property=\"data\", type=\"object\",",
      "    *      allOf={",
      "    *         @OA\\Schema(ref=\"#/components/schemas/memberfund\"),",
      "    *         @OA\\Schema(@OA\\Property(property=\"kindtitle\", type=\"string\",description=\"系列\", example=\"\") ),",
      "    *         @OA\\Schema(@OA\\Property(property=\"title\", type=\"string\",description=\"基金\", example=\"\") ), ",
      "    *     })",
      "",
      "     *     ),)",
      "     *),)    ",
      "     */  "
    ]
  },
  "api store include": {
    "prefix": "api swagger store include",
    "scope": "php",
    "body": [
      " /**",
      "     * @OA\\Post(",
      "     *     path=\"/api/admin/${1:member}/store\",security={{\"bearerAuth\":{}}},operationId=\"\",tags={\"後台/會員\"},summary=\"新增/編輯\",description=\"編號有值代表編輯,沒有代表新增\",",
      "  *     @OA\\RequestBody(required=true,",
      "  *      @OA\\JsonContent(",
      "   *      allOf={",
      "   *         @OA\\Schema(ref=\"#/components/schemas/${1:member}\"),",
      "   *         @OA\\Schema(@OA\\Property(property=\"\", type=\"string\",description=\"系列\", example=\"\") ),",
      "   *     })",
      "     *   ,),",
      "     *   @OA\\Response(response=200,description=\"回覆\",",
      "     *     @OA\\JsonContent(type=\"object\",",
      "     *      @OA\\Property(property=\"resultcode\",type=\"integer\",description=\"訊息代碼\",),",
      "     *      @OA\\Property(property=\"resultmessage\",type=\"string\",description=\"訊息內容\"),",
      "     *      @OA\\Property(property=\"data\", type=\"object\","
      "     *      allOf={",
      "     *             @OA\\Schema(@OA\\Property(property=\"id\", type=\"integer\",description=\"編號\", example=\"10101\") ),",
      "     *          }",
      "     *      )",

      "     * ",
      "     *     ),)",
      "     *),)",
      "     */",
      ""
    ]
  },
  "api store array": {
    "prefix": "api swagger store request list",
    "scope": "php",
    "body": [
      "/**",
      "     * @OA\\Post(",
      "     *     path=\"/api/admin/orderdetaillog/store\",security={{\"bearerAuth\":{}}},operationId=\"\",tags={\"前台\"},summary=\"訂單明細出貨更新\",description=\"\",",
      "     *     @OA\\RequestBody(required=true,",
      "     *      @OA\\JsonContent(",
      "     *      @OA\\Property(property=\"data\",  type=\"array\",",
      "     *      @OA\\Items(allOf={",
      "",
      "     *         @OA\\Schema(@OA\\Property(property=\"sku\", type=\"string\",description=\"產品料號\", example=\"TWA3-0800275170\") ),",
      "     ",
      "     *     }))",
      "     *   ,)),",
      "",
      "     *   @OA\\Response(response=200,description=\"回覆\",",
      "     *     @OA\\JsonContent(type=\"object\",",
      "     *      @OA\\Property(property=\"resultcode\",type=\"integer\",description=\"訊息代碼\",),",
      "     *      @OA\\Property(property=\"resultmessage\",type=\"string\",description=\"訊息內容\"),",
      "",
      "     *      @OA\\Property(property=\"data\",  type=\"array\",",
      "     *      @OA\\Items(allOf={",
      "",
      "     *         @OA\\Schema(@OA\\Property(property=\"result\", type=\"string\",description=\"結果\", example=\"\") ),",
      "     *     })",
      "",
      "     *      ),)",
      "     * ),)",
      "     */"
    ]
  }
}
