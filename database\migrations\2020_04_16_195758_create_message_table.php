<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
/*留言版*/
class CreatemessageTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('message')) {
        Schema::create('message', function (Blueprint $table) {
            $table->increments('messageid');  
            $table->integer('id')->comment('自動編號');         
$table->string('kind',255)->comment('留言類別');         
$table->string('name',255)->nullable()->comment('姓名');         
$table->string('email',255)->nullable()->comment('電子信箱');         
$table->string('sex',255)->nullable()->comment('性別');         
$table->string('title',255)->comment('標題');         
$table->string('body',2000)->nullable()->comment('留言內容');         
$table->dateTime('created_at')->nullable()->comment('建立時間');         
$table->dateTime('updated_at')->nullable()->comment('異動時間');         
$table->string('rebody',2000)->nullable()->comment('回覆訊息');         

                        
            
            $table->integer('userid')->nullable()->comment('編輯人員');
            $table->string('useraccount', 50)->nullable()->comment('編輯人員');
            $table->timestamps();
            
            
            /*
            $table->timestamps();
            $table->string('kind')->index()->comment('種類'); 
            $table->dateTime('begindate')->nullable()->comment('開始時間');
            $table->dateTime('created_at')->nullable()->comment('建立時間');
            $table->dateTime('updated_at')->nullable()->comment('更新時間');
            $table->integer('hits')->default(0)->comment('點率次數');
            $table->float('boardsort', 5, 3)->nullable()->comment('排序號碼');
            $table->integer('userid')->nullable()->comment('編輯人員');
            $table->string('useraccount', 50)->nullable()->comment('編輯人員');
            $table->string('account',50)->unique();;
            $table->unique(array('kind', 'kindid'));
            $table->index(array('kind', 'kindid'));
            */
        });
    }
        
    }



    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('message');
    }
}

