<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class generateTableSnippets extends Command {
    /**
     * 命令名稱
     *
     * @var string
     */
    protected $signature = 'generate:table-snippets';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '產生 VS Code table.code-snippets 檔案';

    /**
     * 執行命令
     *
     * @return int
     */
    public function handle() {
        $this->info('開始產生 table.code-snippets...');

        try {
            // 取得所有資料表
            $tables = DB::getSchemaBuilder()->getAllTables();
            $snippets = [];

            foreach ($tables as $table) {
                // 取得資料表名稱（根據不同資料庫驅動程式調整）
                $tableName = $this->getTableName($table);

                // 跳過系統表格
                if ($this->shouldSkipTable($tableName)) {
                    continue;
                }

                $this->info("處理資料表: {$tableName}");

                // 取得資料表欄位資訊
                $columns = DB::getSchemaBuilder()->getColumnListing($tableName);

                // 取得外鍵關聯
                $foreignKeys = $this->getForeignKeys($tableName);

                // 產生模型名稱
                $modelName = $this->getModelName($tableName);

                // 取得資料表註解
                $tableComment = $this->getTableComment($tableName);

                // 產生基本 snippet
                $snippets[$tableName] = $this->generateBasicSnippet($tableName, $modelName, $tableComment);

                // 產生關聯 snippets
                foreach ($foreignKeys as $foreignKey) {
                    $relationSnippets = $this->generateRelationSnippets($tableName, $modelName, $foreignKey, $tableComment);
                    $snippets = array_merge($snippets, $relationSnippets);
                }
            }

            // 產生 JSON 檔案
            $this->generateSnippetsFile($snippets);

            $this->info('table.code-snippets 產生完成！');
            return 0;
        } catch (\Exception $e) {
            $this->error('產生失敗: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * 取得資料表名稱
     */
    private function getTableName($table) {
        // 根據不同資料庫驅動程式調整
        if (is_object($table)) {
            // MySQL - 動態取得資料庫名稱
            $databaseName = config('database.connections.mysql.database');
            $tableProperty = 'Tables_in_' . $databaseName;

            if (isset($table->{$tableProperty})) {
                return $table->{$tableProperty};
            }

            // PostgreSQL
            if (isset($table->table_name)) {
                return $table->table_name;
            }
            // SQLite
            if (isset($table->name)) {
                return $table->name;
            }
            // 其他可能的屬性
            if (isset($table->tablename)) {
                return $table->tablename;
            }
        }

        // 如果是陣列
        if (is_array($table)) {
            return $table[0] ?? null;
        }

        return $table;
    }

    /**
     * 判斷是否跳過此資料表
     */
    private function shouldSkipTable($tableName) {
        $skipTables = [
            'migrations',
            'password_resets',
            'failed_jobs',
            'personal_access_tokens',
            'jobs'
        ];

        return in_array($tableName, $skipTables);
    }

    /**
     * 取得外鍵關聯
     */
    private function getForeignKeys($tableName) {
        $foreignKeys = [];

        try {
            // 取得資料表欄位資訊
            $columns = DB::getSchemaBuilder()->getColumnListing($tableName);

            foreach ($columns as $column) {
                // 檢查是否為外鍵（以 _id 結尾）
                if (Str::endsWith($column, '_id') && $column !== 'id') {
                    $relatedTable = Str::replaceLast('_id', '', $column);

                    // 檢查關聯資料表是否存在
                    if (DB::getSchemaBuilder()->hasTable($relatedTable)) {
                        $foreignKeys[] = [
                            'column' => $column,
                            'related_table' => $relatedTable,
                            'related_model' => $this->getModelName($relatedTable)
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            $this->warn("無法取得 {$tableName} 的外鍵資訊: " . $e->getMessage());
        }

        return $foreignKeys;
    }

    /**
     * 取得模型名稱
     */
    private function getModelName($tableName) {
        // 將資料表名稱轉換為模型名稱（首字母大寫，單數形式）
        return Str::studly(Str::singular($tableName));
    }

    /**
     * 取得資料表註解
     */
    private function getTableComment($tableName) {
        try {
            $comment = DB::select("SELECT table_comment FROM information_schema.tables WHERE table_schema = ? AND table_name = ?", [
                config('database.connections.mysql.database'),
                $tableName
            ]);

            return $comment[0]->table_comment ?? $tableName;
        } catch (\Exception $e) {
            return $tableName;
        }
    }

    /**
     * 產生基本 snippet
     */
    private function generateBasicSnippet($tableName, $modelName, $tableComment) {
        return [
            'prefix' => "with {$tableName} {$tableComment}",
            'scope' => 'php',
            'body' => [
                "\\\$rows = \\App\\Models\\" . strtolower($modelName) . "::selectRaw('*')",
                "    ->get();"
            ]
        ];
    }

    /**
     * 產生關聯 snippets
     */
    private function generateRelationSnippets($tableName, $modelName, $foreignKey, $tableComment) {
        $snippets = [];
        $relatedTable = $foreignKey['related_table'];
        $relatedModel = $foreignKey['related_model'];
        $relationName = $relatedTable;

        // 取得關聯資料表的註解
        $relatedTableComment = $this->getTableComment($relatedTable);

        // 取得關聯資料表的主要顯示欄位
        $displayColumns = $this->getDisplayColumns($relatedTable);

        // 產生單一關聯 snippet
        $snippets["{$tableName}_{$relatedTable}"] = [
            'prefix' => "with {$tableName} {$relatedTable} {$tableComment} {$relatedTableComment}",
            'scope' => 'php',
            'body' => [
                "\\\$rows = \\App\\Models\\" . strtolower($modelName) . "::selectRaw('*')",
                "    ->with(['{$relationName}' => function (\\\$query) {",
                "        \\\$query->selectRaw('{$displayColumns}');",
                "    }])",
                "    ->get();"
            ]
        ];

        return $snippets;
    }

    /**
     * 取得顯示欄位
     */
    private function getDisplayColumns($tableName) {
        try {
            $columns = DB::getSchemaBuilder()->getColumnListing($tableName);

            // 預設包含 id
            $displayColumns = ['id'];

            // 尋找常見的顯示欄位
            $commonDisplayFields = ['name', 'title', 'mname', 'sname', 'kindtitle', 'account', 'email'];

            foreach ($commonDisplayFields as $field) {
                if (in_array($field, $columns)) {
                    $displayColumns[] = $field;
                    break; // 只取第一個找到的
                }
            }

            return implode(',', $displayColumns);
        } catch (\Exception $e) {
            return 'id';
        }
    }

    /**
     * 產生 snippets 檔案
     */
    private function generateSnippetsFile($snippets) {
        $snippetsPath = base_path('.vscode/table.code-snippets');

        // 確保目錄存在
        $directory = dirname($snippetsPath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        // 產生 JSON 內容
        $jsonContent = json_encode($snippets, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        // 寫入檔案
        File::put($snippetsPath, $jsonContent);

        $this->info("檔案已產生: {$snippetsPath}");
        $this->info("總共產生 " . count($snippets) . " 個 snippets");
    }
}
