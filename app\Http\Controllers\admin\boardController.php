<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Validator;
use PF;
use PT;
use App\Libraries\UploadFile;
use App\Libraries\PM;
use DB;

class boardController extends adminController
{
    private $fieldnicknames;
    private $data;

    private $hiddenarray;
    private $pm;

    /**
     * 建構值
     */
    public function __construct()
    {
        try {
            //$this->limit="xx";
            parent::__construct();

            $this->data = PF::requestAll($this->data);
            //PF::printr($this->data);
            $this->db = new \App\Models\board();
            $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
            $this->data['xmlspec'] = PF::xmlDoc('spec/Board'.$this->data['kind'].'.xml');

            $this->pm = new PM($this->data, $this->data['xmldoc'], $this->data['xmlspec']);
            //共用的hidden變數
            $this->data['hiddens'][] = 'kind';
            if ('' != $this->data['akindid']) {
                $this->data['hiddens'][] = 'akindid';
            }

            //導覽列
            $this->data['nav'] = PT::nav($this->data['xmldoc'], $this->data['kind']);

            $this->fieldnicknames = null;
            $xPath = "//table/Field[@edit='Y']";
            $v = $this->data['xmlspec']->xpath($xPath);
            for ($i = 0; $i < count($v); ++$i) {
                $this->fieldnicknames[strval($v[$i]['name'])] = $v[$i]['title'];
            }
        } catch (\Exception $e) {
            //var_dump($e);
            die($e->getMessage());
        }
    }

    /**
     * 資料列表.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->data['fieldsearchname'] = $this->pm->search();
        $this->data['fieldsearchdatename'] = $this->pm->searchdate();
        $this->data['fieldth'] = $this->pm->th();

        // 開啟記錄sqlcmd顯示
        //DB::enableQueryLog();
        $rows = DB::table('board')->select('board.*', 'kind.kindtitle');
        $rows->leftjoin('kind', 'kind.kindid', '=', 'board.kindid');
        if ('' != $request->input('akindid')) {
            $rows->Where('board.kindid', '=', $request->input('akindid'));
        }
        $rows->myWhere('board.kind|S', $this->data['kind'], '種類', 'Y');
        $rows->myWhere($this->data['searchname'], $this->data['search'], $this->fieldnicknames, 'N');
        //依條件時間搜尋資料的SQL語法

        $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|>=', $request->input('searchstartdate'), $this->fieldnicknames, 'N');
        $rows->myWhere('convert('.$request->input('searchdatename').',DATE)|<=', $request->input('searchenddate'), $this->fieldnicknames, 'N');

        if ($this->data['sortname']) {
            $rows->orderBy($request->input('sortname'), $request->input('sorttype')=="desc"  ? $request->input('sorttype') : "asc"  );
        } else {
            $rows->orderBy('id', 'desc');
        }
//PF::dbSqlPrint($rows);
        $rows = $rows->paginate(10);
        // 顯示sqlcmd

        $this->data['rows'] = $rows;

        return view('admin/board.index', [
                'data' => $this->data,
                'pm' => $this->pm,
            ]);
    }

    /**
     * 資料建立顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $rows = DB::table('board')->selectRaw('max(boardsort) as boardsort');

        if ($rows->count() > 0) {
            $rs = $rows->first();
            $this->data['boardsort'] = $rs->boardsort + 1;
        }
        $this->data['created_at'] = date('Y-m-d');

        $this->data['pmmodify'] = $this->pm->modify($this->data);

        return view('admin/board.edit', [
                'data' => $this->data,
            ]);
    }

    /**
     * 資料單一詳細頁
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
    }

    /**
     * 資料編輯顯示.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        // 啟用 Query Log 功能
        //DB::connection()->enableQueryLog();
        $rows = DB::table('board');
        $rows->select('board.*', 'kind.kindtitle');
        $rows->leftjoin('kind', 'kind.kindid', '=', 'board.kindid');

        if ('' != $request->input('edit')) {
            $rows->Where('id', '=', $request->input('edit'));
        }

        $rows->orderBy('id', 'desc');
        //$row = $row->get();
        //dd($row);
        // 顯示sqlcmd
        //print_r(DB::getQueryLog()); // Show results of log

        if ($rows->count() > 0) {
            foreach ($rows->first() as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            die('No Data');
        }
        $this->data['pmmodify'] = $this->pm->modify($this->data);

        return view('admin/board.edit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料新增編輯寫入.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $edit = $request->input('edit');

        $validators = null;
        $xPath = "//table/Field[@edit='Y']";

        $v = $this->data['xmlspec']->xpath($xPath);
        for ($i = 0; $i < count($v); ++$i) {
            if ('Y' == $v[$i]['必填']) {
                $validators[strval($v[$i]['name'])] = ['required'];
            }
        }
        $validators['kind'] = ['required'];

        $validator = Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->fieldnicknames);
        if ($validator->fails()) {
            return view('errors.validatorback')->withErrors($validator);
        }
        $inputs = null;

        $xPath = "//table/Field[@edit='Y']";
        $v = $this->data['xmlspec']->xpath($xPath);
        $inputs['kind'] = $request->input('kind');
        $uploadfolder = '';
        for ($i = 0; $i < count($v); ++$i) {
            if ('' == $uploadfolder) {
                $uploadfolder = strval($v[$i]['上傳目錄']);
            }
            $inputs[strval($v[$i]['name'])] = $this->data[strval($v[$i]['name'])];
        }
        if ('' != $this->data['kindid']) {
            $inputs['kindid'] = $this->data['kindid'];
        }
        if ('' != $this->data['akindid']) {
            $inputs['kindid'] = $this->data['akindid'];
        }
        $inputs['userid'] = \Auth::guard('admin')->user()->userid; /*編輯人員-*/
        $inputs['useraccount'] = \Auth::guard('admin')->user()->account; /*編輯人員-*/
        // $inputs['created_at'] = $request->input('created_at');
        if ('' != $uploadfolder) {
            try {
                /*檔案上傳*/
                $upload = new UploadFile();
                $upload->request = $request;
                $upload->inputs = $inputs;
                $upload->folder = $uploadfolder;
                //$upload->width = '800';
                //$upload->height = '600';
                //$upload->limitext = config('app.FileLimit');
                $inputs = $upload->execute();
            } catch (\Exception $e) {
                //var_dump($e);
                die($e->getMessage());
            }
        }

        if ('' == $edit) {
            $inputs['hits'] = 0;
            if($inputs['created_at']==""){
                $inputs['created_at'] = date('Y-m-d H:i:s');
            }
            $this->db->create($inputs);
            $this->data['alert'] = '新增成功';
        } else {
            $this->data['alert'] = '更新成功';
            $this->db::find($edit)->update($inputs);
        }
        \Cache::forget($request->input('kind'));
        //return back()->with('success','You have successfully upload image.');

        return view('admin/layouts/postsubmit', [
            'data' => $this->data,
        ]);
    }

    /**
     * 資料刪除.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $rows = $this->db->myWhere('id|ININT', $this->data['del'], 'del', 'Y');
        //PF::dbSqlPrint($rows);
        $rows->get()->each(function ($row) {
            $row->delete();
        });

        return view('admin/layouts/postsubmit', [
            'data' => $this->data,
        ]);
    }
}
