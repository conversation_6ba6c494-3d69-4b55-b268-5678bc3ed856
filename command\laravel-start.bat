chcp 65001

set "current_dir=%~dp0"
echo Current directory: %current_dir%
set "directoryvue=%current_dir%..\resources\js\views\"
echo %directoryvue%
if exist "%directoryvue%" (
  echo "=============================================================="
  echo "關閉xampp"
  C:\xampp82\xampp_stop.exe
  echo "修改xampp目錄 start "
  python C:\pythoncode\filewatch\apache.py --path %current_dir%
  echo "修改xampp目錄 end "
  echo "啟動xampp"
  C:\xampp82\xampp_start.exe
  echo "OK......."
  echo "=============================================================="
  REM php artisan serve
) else (
  echo "非vue3專案不執行mp run dev"
)

cmd /k
