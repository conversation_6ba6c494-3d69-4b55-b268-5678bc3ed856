<?php

namespace App\Http\Controllers\membercenter;

use DB;
use PF;
use Config;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\View;
use App\Repositories\city1Repository;
use App\Repositories\memberRepository;

class editController extends Controller {
    private $data;

    private $memberRepo;

    private $city1Repo;


    public function __construct(memberRepository $memberRepo, city1Repository $city1Repo) {
        //$this->limit="xx";
        parent::__construct();
        $this->memberRepo = $memberRepo;
        $this->city1Repo = $city1Repo;
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->data['displaynames'] = $this->memberRepo->getFieldTitleArray();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        $rows = DB::table('member');
        $rows->selectRaw('member.*');
        $rows->where('id', \Auth::guard('member')->id());
        $rows->limit(1);
        //$rows = $rows->get();
        //dd($row);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('No data');
        }
        $rows = $this->city1Repo->selectRaw('*');
        $rows->myWhere('online|N', 1, "kind", 'N');
        $rows->orderByRaw('sortnum desc');
        $rows = $rows->get();
        $this->data['city1rows'] = $rows;
        // Config::set('config.title', config('config.title'));
        // Config::set('config.keyword', '');
        // Config::set('config.description', '');
        //PF::printr(\Auth::guard('member')->user()->status);
        return view(
            'member.create.index',
            [
                'data' => $this->data,
            ]
        );
    }

    public function store(Request $request) {
        $validators = null;

        //$validators['company'] = ['required']; //任職公司
        $validators['name'] = ['required']; //姓名
        $validators['email'] = ['required']; //電子信箱

        $validator = \Validator::make($request->all(), $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        $inputs = $request->all();

        unset($inputs['password']); //密碼-

        $this->memberRepo->update($inputs, \Auth::guard('member')->id());

        return back()->with('js', "_toast('更新成功',500)");
    }
}
