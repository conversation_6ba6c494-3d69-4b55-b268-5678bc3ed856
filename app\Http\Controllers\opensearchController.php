<?php

namespace App\Http\Controllers;

use PF;
use DB;

//use Illuminate\Support\Facades\DB;

class opensearchController extends Controller
{
    private $data;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();
        
    }

    public function index()
    {

        $body = '';
        try {
            
            $xml = new \SimpleXMLElement('<OpenSearchDescription xmlns="http://a9.com/-/spec/opensearch/1.1/"></OpenSearchDescription>');
            
            $xml->AddChild('ShortName', PF::getConfig('title'));
            $xml->AddChild('Description', PF::getConfig('description'));
            $xml->AddChild('Tags', PF::getConfig('keyword'));
            $xml->AddChild('Contact', PF::getConfig('email'));
            $url=$xml->AddChild('Url', "");
            $url->addAttribute('type', 'text/html');
            
            $url->addAttribute('amotypeunt', url('/').'/news/search?search={searchTerms}');
            


            $body = $xml->asXML();
        } catch (\Exception $e) {
            $body = $e->getMessage();
        }

        return  response($body)->header('Content-Type', 'text/xml');
    }
    
}
