<!doctype html>
<html lang="{{ app()->getLocale() }}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="content-Language" content="{{ app()->getLocale() }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>@yield('title', PF::getConfig('title'))</title>


    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="private" />
    <meta http-equiv="Expires" content="0" />
    <script type="text/javascript" src="{{ asset('Scripts/jquery.js') }}"></script>
    <style>
        /* 在手機版隱藏左側框架 */
        @media (max-width: 768px) {
            #left {
                width: 0px;
            }
        }
    </style>
    <script type="text/javascript">
        function adjustTextInputPosition() {
            var width = $(window).width();
            if (width < 768) {
                $("#frameset").attr('cols', '0,*')
            } else {
                $("#frameset").attr('cols', '200,*')
            }
        }
        window.addEventListener('resize', adjustTextInputPosition);
        document.addEventListener("DOMContentLoaded", () => {

            adjustTextInputPosition();

        });
    </script>


    <frameset cols="200,*" id="frameset" border=0>

        <frame src="{{ \request()->middlewareurl }}frame/left" name="left" id="left" border="0"
            scrolling="auto" />


        <frameset rows="45,*" border="0" id="framesetright">
            <frame src="{{ \request()->middlewareurl }}frame/top" name="top" border="0">
                <frame src="{{ \request()->middlewareurl }}adminuserloginlog" name="maincontent" border="0"
                    id="maincontent" scrolling="auto">
        </frameset>

    </frameset>

<body>

    <script language=JavaScript>
        document.addEventListener("DOMContentLoaded", () => {
            mytimer = setInterval(function() {
                var dict = {
                    url: "{{ \request()->middlewareurl }}frame/updateusetime",
                    data: {
                        'id': "{{ Session::get('adminuserlogin_id') }}"

                    },
                    noloading: false,
                    //data:jQuery("#oForm").serialize(),
                    //data:JSON.stringify(this.inputs),
                    dataType: 'json',
                    //debug:true,
                }
                PF_ajax(dict).done(function(obj) {
                    //console.log('logout');
                }).fail(function(resp) {
                    //    _alert(resp.statusText);
                }).then(function() {

                });
            }, 60 * 1000);
            //$('.content-wrapper').IFrame('createTab', 'adminuserloginlog', 'adminuserloginlog', 'adminuserloginlog11', true);
            //$("[href='adminuserloginlog']").eq(0).trigger("click");
        });
    </script>


</body>

</html>
