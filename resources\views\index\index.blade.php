@extends('layouts.master')
@section('css')
@endsection

@section('js')
    <link type="application/opensearchdescription+xml" rel="search" href="{{ url('/') }}/opensearch" />
    <script type="application/ld+json">
    [{
            "@context": "http://schema.org",
            "@type": "WebSite",
            "name": "{{config('config.title')}}?>",
            "alternateName": ["{{config('config.keyword')}}"],
            "url": "{{ asset('/') }}",
            "image": "{{ asset('/') }}/images/logo.png",
            "potentialAction": {
                "@type": "SearchAction",
                "target": "{{ asset('/') }}news?search={search_term_string}",
                "query-input": "required name=search_term_string"
            }
        },
        {
            "@context": "http://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [{
                    "@type": "ListItem",
                    "position": 1,
                    "item": {
                        "@id": "{{ asset('/') }}",
                        "name": "首頁"
                    }
                }

            ]
        }

    ]
</script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [{
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "@id": "#about",
                "name": "關於我們",
                "url": "{{ asset('/about') }}"
            },
            {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "@id": "#faq",
                "name": "常見問題",
                "url": "{{ asset('/faq') }}"
            },
            {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "@id": "#news",
                "name": "房訊新聞",
                "url": "{{ asset('/news') }}"
            },
            {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "@id": "#contact",
                "name": "與我聯絡",
                "url": "{{ asset('/contact') }}"
            },
            {
                "@context": "https://schema.org",
                "@type": "SiteNavigationElement",
                "@id": "#place",
                "name": "投標地點",
                "url": "{{ asset('/place') }}"
            }
        ]

    }
</script>
@endsection

@section('content')
    <section class="hero swiper-container">
        <div class="swiper-wrapper">
            @foreach ($data['rowsbanner'] as $key => $rs)
                @php
                    $url = $rs->memo == '' ? '#' : $rs->memo;
                @endphp

                <a href="{{ $url }}" class="swiper-slide" title="{{ $rs->title }}">
                    <img src="{{ url('/') }}/images/banner/{{ $rs->field1 }}">
                </a>
            @endforeach

        </div>
        <div class="swiper-pagination"></div>
    </section>


    <div class="row justify-content-center ml-0">

        <div class=" text-center col-md-12 col-xs-12 p-0 " style="background: rgba(255, 255, 255, 0.5);">
            {!! $data['viewcount'] !!}
        </div>

        @inject('search', '\App\Http\Controllers\layouts\searchController')
        {!! $search->index($data) !!}


    </div>
    <div class="px-3">
        <div class="theme-container">


            @include('layouts.house')



        </div>
    </div>


    <script src="{{ url('/') }}/js/libs/swiper.min.js"></script>
    <script src="{{ url('/') }}/js/libs/swiper_slider.js"></script>
@endsection
