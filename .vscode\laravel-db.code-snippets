{
    "db_rows_count": {
        "prefix": "db rows count",
        "scope": "php",
        "description": "db count",
        "body": ["\\$rows->count()"]
    },

    "db_json": {
        "prefix": "db json JSON_EXTRACT",
        "scope": "php",
        "body": ["JSON_EXTRACT(`jsonbody`, '\\$.${1:name}') as `${1:name}`,"]
    },
    "db_date_begindate_closedate": {
        "prefix": "db date begindate closedate ",
        "scope": "php",
            "body": [
                "\\$rows->where(function (\\$query) {",
                "                \\$query->whereNull('begindate')",
                "                    ->orWhere('begindate', '<=', date('Y-m-d'));",
                "            });",
                "            \\$rows->where(function (\\$query) {",
                "                \\$query->whereNull('closedate')",
                "                    ->orWhere('closedate', '>=', date('Y-m-d'));",
                "            });",
            ]
    },

    "db_date_begindate_closedate_time": {
        "prefix": "db begin_datetime closedate",
        "scope": "php",
        "body": [
            "\\$rows->where(function (\\$query) {",
            "                \\$query->whereNull('begin_datetime')",
            "                    ->orWhere('begin_datetime', '<=', date('Y-m-d'));",
            "            });",
            "            \\$rows->where(function (\\$query) {",
            "                \\$query->whereNull('close_datetime')",
            "                    ->orWhere('close_datetime', '>=', date('Y-m-d'));",
            "            });",
        ]

    },

    "db_date": {
        "prefix": "db where date today 語法 date 等於今天",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw('convert(${1:created_at},DATE) =convert(now(),DATE)');"
        ]
    },
    "db_today": {
        "prefix": "db where date YYYY/MM/DD 之後的資料",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw(\"convert(created_at, DATE) >= convert('2023-08-14', DATE)\");"
        ]
    },

    "db_date_1": {
        "prefix": "db where x欄位+?日後",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw('convert(now(),DATE)>=convert((DATE_ADD(${1:created_at},INTERVAL 7 DAY)),DATE)');"
        ]
    },
    "db_date_2": {
        "prefix": "db where x欄位+?日內",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw('convert(now(),DATE)<=convert((DATE_ADD(${1:created_at},INTERVAL 7 DAY)),DATE)');"
        ]
    },

    "db_date1": {
        "prefix": "db where date 語法 date 一天後",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw('convert(${1:created_at},DATETIME)<convert((DATE_ADD(convert(now(),DATETIME),INTERVAL ${2:1} DAY)),DATE)');"
        ]
    },
    "db_date2": {
        "prefix": "db where date 語法 date 開始結束時間內",
        "scope": "php",
        "body": [
            "\\$rows->whereDate('begindate', \"<\",date('Y-m-d'));",
            "        \\$rows->whereDate('enddate', \">\",date('Y-m-d'));"
        ]
    },

    "db_whereMonth": {
        "prefix": "db where date whereMonth",
        "scope": "php",
        "body": ["\\$rows->whereMonth('created_at', '12');"]
    },
    "db_whereDay": {
        "prefix": "db where date whereDay",
        "scope": "php",
        "body": ["\\$rows->whereDay('created_at', '31');"]
    },
    "db_whereYear": {
        "prefix": "db where date whereYear",
        "scope": "php",
        "body": ["\\$rows->whereYear('created_at', date('Y'))"]
    },
    "db_whereTime": {
        "prefix": "db where date whereTime",
        "scope": "php",
        "body": ["\\$rows->whereTime('begindate', '=', date('H:i:00'));"]
    },

    "whereRaw_array": {
        "prefix": "db where raw array",
        "scope": "php",
        "description": "rows->whereRaw",
        "body": [
            "\\$rows->whereRaw('(seller=? or seller is null)', [\\Auth::guard('admin')->id()]);"
        ]
    },
    "db Transaction": {
        "prefix": "db transaction",
        "scope": "php",
        "body": ["DB::transaction(function () {", "", "});"]
    },
    "beginTransaction": {
        "prefix": "db transaction + commit + rollback",
        "scope": "php",
        "body": [
            "\\DB::beginTransaction();",
            "try {",
            "",
            "  \\DB::commit();",
            "} catch (\\Exception \\$e) {",
            "  \\DB::rollback();",
            "  throw \\$e;",
            "}"
        ]
    },

    "whereRaw": {
        "prefix": "db where where raw ",
        "scope": "php",
        "description": "rows->whereRaw",
        "body": ["\\$rows->whereRaw('${1:name}');"]
    },
    "db left inner": {
        "prefix": "db where left inner join",
        "scope": "php",
        "body": [
            "\\$rows->leftJoin('${1:kind}', '${1:kind}.id', '=', '${2:board}.${1:kind}_id');"
        ]
    },
    "db inner": {
        "prefix": "db where inner join",
        "scope": "php",
        "body": [
            "\\$rows->join('${1:kind}', '${1:kind}.id', '=', '${2:board}.${1:kind}_id');"
        ]
    },

    "db inner exists": {
        "prefix": "db where left not exists 不適用一對多",
        "scope": "php",
        "body": [
            "\\$rows->leftJoin('${1:kind}', '${1:kind}.id', '=', '${2:board}.${1:kind}_id');",
            "\\$rows->whereNull('${1:kind}.id')"
        ]
    },
    "db inner not exists": {
        "prefix": "db where left exists 不適用一對多",
        "scope": "php",
        "body": [
            "\\$rows->leftJoin('${1:kind}', '${1:kind}.id', '=', '${2:board}.${1:kind}_id');",
            "\\$rows->whereNotNull('${1:kind}.id')"
        ]
    },

    "db inner two": {
        "prefix": "db where left inner join 多個條件",
        "scope": "php",
        "body": [
            "\\$rows->leftJoin('${1:kind}', function (\\$join) {",
            "            \\$join->on('${1:kind}.id', '=', 'clesson.id');",
            "            \\$join->on('${1:kind}.id', '=',DB::raw(\\$this->data['customerid']));",
            "            \\$join->on(\\DB::raw('convert(created_at,DATE)'), '=', \\DB::raw('convert(now(),DATE)'));",
            "            \\$join->where('${1:kind}.kind', '<>', 0);",
            "        });"
        ]
    },
    "db whereNotExists": {
        "prefix": "db where whereNotExists",
        "scope": "php",
        "body": [
            "\\$rows->whereNotExists(function (\\$query) use (\\$request) {",
            "                \\$query->select(DB::raw(1))",
            "                      ->from('${1:kind}')",
            "                      ->whereRaw('${1:kind}.id = membertag.${1:kind}_id')",
            "                      ->myWhere('kind_id|INT', \\$request->input('notagid'), \\$this->data['displaynames'], 'N');",
            "});"
        ]
    },
    "db whereExists": {
        "prefix": "db where whereExists",
        "scope": "php",
        "body": [
            "\\$rows->whereExists(function (\\$query) use (\\$request) {",
            "                \\$query->select(DB::raw(1))",
            "                      ->from('${1:kind}')",
            "                      ->whereRaw('${1:kind}.id = membertag.${1:kind}_id')",
            "                      ->myWhere('kind_id|INT', \\$request->input('notagid'), \\$this->data['displaynames'], 'N');",
            "});"
        ]
    },


    "db_print_sql_command": {
        "prefix": "psql sql command",
        "body": ["\\PF::dbSqlPrint(\\$rows);"]
    },
    "db_return_sql_command": {
        "prefix": "psql return sql command",
        "body": ["\\Log::info(\\PF::dbToSql(\\$rows));"]
    },

    "db foreach": {
        "prefix": "db2 多筆資料 foreach 迴圈",
        "scope": "php",
        "description": "資料庫多筆資料",
        "body": [
            "\\$rows =\\DB::table('${1:board}')->selectRaw('*');",
            "\\$rows->myWhere('${2:kind}|S', \\$request->input('${2:kind}'), \"${2:kind_id}\", 'N');",
            "\\$rows->whereRaw('ifnull(begindate,now())<=now() and ifnull(closedate,now())>=now()');",
            "\\$rows->orderByRaw('id desc');",
            "\\$rows=\\$rows->limit(10);",
            "\\$rows=\\$rows->get();",
            "foreach (\\$rows  as \\$key => \\$rs) {",
            "    PF::printr(\\$rs->${2:title});",
            "}"
        ]
    },

    "db chunk foreach": {
        "prefix": "db2 chunk 緩衝 分批顯示 foreach",
        "scope": "php",
        "body": [
            "\\DB::table('${1:board}')->selectRaw('*')->orderByRaw('${2:id} desc')->chunk(2, function (\\$rows) {",
            "    foreach (\\$rows  as \\$key => \\$rs){",
            "PF::printr(\\$rs->name);",
            "    };   ",
            "    ",
            "});"
        ]
    },
    "db_page": {
        "prefix": "db2 分頁 rows->paginate(10) ",
        "scope": "php",
        "body": ["\\$rows = \\$rows->paginate(10);"]
    },
    "db_simplePaginate": {
        "prefix": "db2 分頁 rows->simplePaginate(10)不提供總筆數 ",
        "scope": "php",
        "body": [
            "\\$rows = \\$rows->simplePaginate(10);"
        ]
    },
    "db2 no foreach": {
        "prefix": "db2 多筆不分頁",
        "scope": "php",
        "description": "資料庫多筆資料不分頁",
        "body": [
            "\\$rows =\\DB::table('${1:board}')->selectRaw('*');",
            "\\$rows->myWhere('${2:kind}|S', \\$request->input('${2:kind}'), \"${2:kind}\", 'N');",
            "\\$rows->whereRaw('ifnull(begindate,now())<=now() and ifnull(closedate,now())>=now()');",
            "\\$rows->orderByRaw('id desc');",
            "\\$rows=\\$rows->limit(10);",
            "//\\PF::dbSqlPrint(\\$rows);",
            "\\$rows=\\$rows->get();",
            "\\$this->data['rows']=\\$rows;"
        ]
    },
    "db_foreach collect": {
        "prefix": "db2 collect foreach",
        "scope": "html,blade,vue",
        "body": [
            "@if(\\$data['${1:menus}'] != null)",
            "@foreach (collect(\\$data['${1:menus}'])->whereIn('select', ['dbselect', 'dbcheckbox', 'dbradio'])->sortBy('sortnum')->all()  as \\$key => \\$rs)   ",
            "{{\\$rs->${1:title}}}",
            "@endforeach",
            "@endif"
        ]
    },
    "db_first collect html": {
        "prefix": "db1 collect if",
        "scope": "html,blade,vue",
        "body": [
            "@if (collect(\\Cache::get('placekind'))->where('kindid', \\Auth::guard('member')->user()->kindid)->first()->isarticle==\"1\")",
            "{{\\$rs->${1:title}}}",
            "@endif"
        ]
    },
    "db_first collect php ": {
        "prefix": "db1 collect if",
        "scope": "php",
        "body": [
            "if (collect(\\Cache::get('placekind'))->where('kindid', \\Auth::guard('member')->user()->kindid)->first()->isarticle==\"1\"){",
            "\\$rs->${1:title};",
            "}"
        ]
    },

    "dbfirst": {
        "prefix": "db1 first",
        "scope": "html,blade,vue",
        "body": [
            "{{DB::table('${1:kind}')->selectRaw('${2:title}')->where(['id'=>\\$data['${3:kind_id}']])->first()->${2:title}}}"
        ]
    },

    "db_foreach collect_unique": {
        "prefix": "db2 collect unique",
        "scope": "html,blade",
        "body": [
            "@foreach (collect(\\$data['rows'])->unique('${1:step}')->all()  as \\$key => \\$rs) ",
            "{{\\$rs->${1:step}}}",
            "@endforeach"
        ]
    },

    "db_foreach": {
        "prefix": "db2 foreach(不分頁)",
        "scope": "html,blade",
        "body": [
            "@if(\\$data['rows${1:}'] != null)",
            "@foreach (\\$data['rows${1:}']  as \\$key => \\$rs)   ",
            "{{\\$rs->${2:title}}}",
            "@endforeach",
            "@endif"
        ]
    },
    "db_table_foreach": {
        "prefix": "db2 DB::table foreach(不分頁)",
        "scope": "html,blade",
        "body": [
            "@foreach (DB::table('${1:board}')->selectRaw('id,title')->where('kind','${2:productkind}')->orderby(\"boardsort\")->get() as \\$key => \\$rs)   ",
            "{{\\$rs->${2:title}}}",
            "@endforeach"
        ]
    },
    "db_display_sqlcommand": {
        "prefix": "db 顯示sqlcommand",
        "scope": "php",
        "description": "$rows->toSql()",
        "body": ["\\$rows->toSql();"]
    },

    "db1 first": {
        "prefix": "db1 first 多條件",
        "scope": "php",
        "body": [
            "\\$rs=\\DB::table('${1:board}')->selectRaw('${2:id}')->where(['${3:id}'=>\\$request->get('${3:id}')])->first();"
        ]
    },
    "db1 exists": {
        "prefix": "db1 exists 判斷是否存在",
        "scope": "php",
        "body": [
            "if(\\DB::table('${1:board}')->select('${2:id}')->where([",
            "'${3:id}'=>\\$this->data['${3:id}'],",
            "'x'=>'y'",
            "])->exists()){",
            "   throw new \\CustomException(\"請勿重覆\");",
            "}"
        ]
    },

    "db myWhere": {
        "prefix": "db myWhere",
        "scope": "php",
        "body": [
            " \\$rows->myWhere('${1:id}|N',  \\$request->input('${1:id}'), \"${1:id}\", 'N');"
        ]
    },

    "one_db_field": {
        "prefix": "db1 一筆資料",
        "scope": "php",
        "description": "foreach key value",
        "body": [
            "\\$rows = \\DB::table('${1:board}')->selectRaw('${1:board}.*');",
            " \\$rows->myWhere('id|N', \\$this->data['id'], \"id\", 'Y');",
            "//\\$rows->orderByRaw('id desc');",
            "\\$rows=\\$rows->limit(1);",
            "//\\$rs=get_object_vars(\\$rs);//to \\$rs['XX'];",
            "//PF::dbSqlPrint(\\$rows);",
            "\\$rows = \\$rows->get();",
            "if (\\$rows->count() > 0) {",
            "    \\$rs=\\$rows->first();",
            "   \\$this->data = array_merge(\\$this->data, get_object_vars(\\$rs));",
            "} else {",
            "    throw new \\CustomException(\"No data\");",
            "}"
        ]
    },
    "資料庫分頁": {
        "prefix": "db2 多筆 分頁",
        "scope": "php",
        "body": [
            "\\$rows = \\DB::table('${1:board}')->selectRaw('${1:board}.*');",
            "\\$rows->myWhere('${2:kind}|S', \\$request->input('${2:kind}'), \"${2:kind}\", 'N');",
            "\\$rows->whereRaw('ifnull(begindate,now())<=now() and ifnull(closedate,now())>=now()');",
            "\\$rows->orderByRaw('id desc');",
            "\\$rows = \\$rows->paginate(10);",
            "\\$this->data['rows']=\\$rows;"
        ]
    },

    "db tablecount": {
        "prefix": "db count where 單一條件",
        "scope": "php",
        "body": [
            "\\$tcount = \\App\\Models\\\\${1:board}::where('${2:kind}', 0)->count();"
        ]
    },
    "db sql count": {
        "prefix": "db count sql ",
        "scope": "php",
        "body": [
            "(select count(*) from ${1:board} where ${2:board}.id=${2:board}_id) as total"
        ]
    },

    "db table count": {
        "prefix": "db count myWhere 多條件",
        "scope": "php",
        "body": [
            "\\$rows = \\DB::table('${1:board}')->selectRaw('count(*) as tcount');",
            "        \\$rows->myWhere('id|N', \\Auth::guard('admin')->id(), 'id', 'N');",
            "        //PF::dbSqlPrint(\\$rows);",
            "        \\$rows = \\$rows->get();",
            "        if (\\$rows->count() > 0) {",
            "            \\$rs = \\$rows->first();",
            "            \\$this->data['${1:board}count'] = \\$rs->tcount;",
            "        }"
        ]
    },

    "db_insert_php": {
        "prefix": "db raw insert sql",
        "scope": "php",
        "description": "DB::insert",
        "body": [
            "\\DB::insert('insert into ${1:board} (id, name) values (?, ?)', array(1, 'Dayle'));"
        ]
    },
    "db_insert inner php": {
        "prefix": "db raw insert inner join",
        "scope": "php",
        "description": "DB::insert",
        "body": [
            "\\DB::insert('insert into ${1:board} (id, body) select member.id,? from ${1:board} inner join membertag a on (member.id=a.member_id) a where a.kind_id in (?) ', array(\\$body, \\$membertag_ids));"
        ]
    },
    "db_update": {
        "prefix": "db raw update sql",
        "scope": "php",
        "description": "DB::update",
        "body": [
            "\\DB::update('update ${1:board} set votes = 100 where name = ?', array('John'));"
        ]
    },

    "db_update+1": {
        "prefix": "db DB::update hits+1",
        "scope": "php",
        "body": [
            "DB::table('${1:board}')->where(['id' => 10000])->increment('${2:hits}');"
        ]
    },
    "db update inner": {
        "prefix": "db raw update inner join ",
        "scope": "php",
        "body": [
            "\\DB::update('update ${1:board} INNER JOIN ${2:member} ON (${2:board}.id=${1:board}.${2:member}_id) SET iscr=1 where a=1');"
        ]
    },
    "db delete inner": {
        "prefix": "db raw delete inner join ",
        "scope": "php",
        "body": [
            "\\DB::delete('delete ${1:board} from  ${1:board} INNER JOIN ${2:member} ON (${2:board}.id=${1:board}.${2:member}_id) where a=1');"
        ]
    },
    "db_delete": {
        "prefix": "db raw delete sql",
        "scope": "php",
        "body": [
            "\\DB::delete('delete from ${1:board} where name = ?', array('John'));"
        ]
    },

    "db_truncate": {
        "prefix": "db truncate",
        "scope": "php",
        "body": ["\\DB::table('${1:board}')->truncate();"]
    },

    "db_epost_html": {
        "prefix": "epost",
        "scope": "html,blade",
        "description": "PT::getEpostBody",
        "body": ["{!!PT::getEpostBody(\"${1:id}\")!!}"]
    },
    "db_epost_php": {
        "prefix": "epost",
        "scope": "php",
        "description": "PT::getEpostBody",
        "body": ["PT::getEpostBody(\"${1:id}\")"]
    },
    "db_group ": {
        "prefix": "db groupby tosql 放到主table inner",
        "scope": "php",
        "body": [
            "\\$subQuery = \\DB::table('${1:member}')->selectRaw('${2:kind_id},count(*) as tcount');",
            "\\$subQuery->groupByRaw('${2:kind_id}');",
            "\\$rows = DB::table(\"productitem\")->selectRaw('tcount,title');",
            "\\$rows->leftJoinSub(\\$subQuery, 'a', function (\\$join) {",
            "   \\$join->on('${1:member}.${2:kind_id}', '=', '${3:kind}.id');",
            "  });",
            "\\$rows->leftJoinSub(\\$subQuery, 'a', '${1:member}.${2:kind_id}', '=', 'a.${3:kind}.id');",
            "\\$rows->orderByRaw('title');"
        ]
    },
    "db_where_date": {
        "prefix": "db where date",
        "scope": "php",
        "description": "db where 搜尋日期 ",
        "body": [
            "\\$rows->myWhere('closedate|D', date(\"Y-m-d\",strtotime(\"0 day\")), \\$this->fieldnicknames, 'Y');    "
        ]
    },
    "db_where_yearmonth": {
        "prefix": "db where 當年月year month",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw('YEAR(startdate) = YEAR(NOW()) AND MONTH(startdate) = MONTH(NOW())');"
        ]
    },
    "db_where_created_at7": {
        "prefix": "db where 當年月left(created_at,7)",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw('left(${1:created_at},7)=left(convert(now(),DATE),7)');"
        ]
    },

    "db_group_concat": {
        "prefix": "db split group concat",
        "scope": "php",
        "description": "DB字串抓資料庫串在一起GROUP_CONCAT(kindtitle SEPARATOR ',')",
        "body": [
            " (select  GROUP_CONCAT(title SEPARATOR ',') from ${1:kind} where FIND_IN_SET(${1:kind}.id,adminuser.id))  as ${1:board}"
        ]
    },
    "dbSub": {
        "prefix": "db 子查詢 sub or",
        "scope": "php",
        "body": [
            "\\$rows =\\DB::table('${1:board}')->selectRaw('*');",
            "    \\$rows->where('kind', 'news');",
            "    \\$field9=\\$rs->field9;",
            "    \\$rows->where(function (\\$query) use (\\$field9) {",
            "\\$items=explode(\",\",\\$field9);",
            "foreach (\\$items as \\$k => \\$v){",
            "   \\$query->orWhere(\"field9\", 'like', '%'.\\$v.'%');",
            "};   ",
            "       ",
            "    });"
        ]
    },
    "orwhere": {
        "prefix": "db or where 二個欄位",
        "scope": "php",
        "body": [
            "\\$rows->where(function (\\$query) {",
            "    \\$query->orWhere('kind', 'news');",
            "    \\$query->orWhere('votes2', '>', 100);",
            "    \\$query->orWhere('manage_ids', 'like', '%'.$v.'%');",
            "    \\$query->orWhereRaw(\"(convert(due_date,DATE)<convert(now(),DATE) )\")",
            "});"
        ]
    },
    "orwhere and ": {
        "prefix": "db 第一層 and + 第二層 or where 二個欄位",
        "scope": "php",
        "body": [
            "\\$rows->where(function (\\$query) use (\\$request) {",
            "                foreach (\\$request->input('${1:kinds}') as \\$k => \\$v) {",
            "                    if (\\$v != \"\") {",
            "                        \\$query->where(function (\\$query) use (\\$v) {",
            "                            \\$a = explode('~', \\$v);",
            "                            \\$query->orwhere('${1:kinds}', '>=', \\$a[0]);",
            "                            \\$query->orwhere('${1:kinds}', '<=', \\$a[1]);",
            "                        });",
            "                    }",
            "                }",
            "});"
        ]
    },

    "db_foreach_empty": {
        "prefix": "db2 for empty",
        "scope": "html,blade",
        "description": "herf search name",
        "body": [
            "@forelse (\\$data['rows']  as \\$key => \\$rs)",
            "<li>{{\\$rs->title}}</li>",
            "@empty",
            "<p>No Data</p>",
            "@endforelse"
        ]
    },
    "db_subwhere": {
        "prefix": "db sql語法 sub where",
        "scope": "php",
        "description": "rows->orWhere",
        "body": [
            "if ('' != \\$this->data['${1:board}']) {",
            "    \\$${1:board} = \\$this->data['${1:board}'];",
            "    \\$rows->where(function (\\$query) use (\\$${1:board}) {",
            "    \\$items = explode(',', \\$${1:board});",
            "     foreach (\\$items as \\$k => \\$v) {",
            "       \\$query->orWhere('${1:board}', 'like', '%'.\\$v.'%');",
            "     }",
            "     return \\$query;",
            "    });",
            "}"
        ]
    },
    "db_for_two_one": {
        "prefix": "db1 父包子(一筆)",
        "scope": "php",
        "description": "db foeach+foreach",
        "body": [
            "\\$rows = \\$rows->get();",
            "foreach (\\$rows as \\$rs) {",
            "    \\$rows1 = DB::table('${1:board}');",
            "    \\$rows1->selectRaw('${1:board}.*');",
            "    \\$rows1->myWhere('${1:board}id|N', \\$rs->${1:board}id, '${1:board}id', 'Y');",
            "    \\$rows1->orderByRaw('id desc');",
            "    \\$rows=\\$rows->limit(10)->get();",
            "    //\\$rows=\\$rows->get();",
            "    if (\\$rows1->count() > 0) {",
            "     \\$rs1 = \\$rows1->first();",
            "    \\$rs->${1:board} = \\$rs1;",
            "    }",
            "}",
            "\\$this->data['rows'] = \\$rows;"
        ]
    },
    "db_for_multi": {
        "prefix": "db2 父包子(多筆)filter",
        "scope": "php",
        "description": "db foeach+filter",
        "body": [
            "\\$rows = \\DB::table('${1:board}')->selectRaw('kind_id,kindtitle');",
            "\\$rows->myWhere('kind|S', 'qakind', 'kind', 'Y');",
            "\\$rows->orderByRaw('kindsortnum');",
            "\\$rows = \\$rows->get();",
            "",
            "\\$rows1 =\\DB::table('${1:board}')->select('*');",
            "\\$rows1->selectRaw('kind_id,title,body');",
            "\\$rows1->myWhere('kind|S', 'qa', 'kind', 'Y');",
            "//\\$rows1->myWhere('kind_id|N', \\$rs->kind_id, 'kind_id', 'Y');",
            "\\$rows1->orderByRaw('boardsort');",
            "\\$rows1 = \\$rows1->get();",
            "",
            "       ",
            "foreach (\\$rows as \\$rs) {",
            "    \\$kind_id=\\$rs->kind_id;",
            "    \\$rs->board = \\$rows1->filter(function (\\$rs) use (\\$kind_id) {",
            "",
            "    if (\\$rs->kind_id==\\$kind_id){",
            "       return true;",
            "    }",
            "    });",
            "}"
        ]
    },
    "db_for_two": {
        "prefix": "db2 父包子(多筆)",
        "scope": "html,blade",
        "description": "db foeach+foreach",
        "body": [
            "@foreach (\\$data['rows']  as \\$key => \\$rs)   ",
            "{{\\$rs->title}}",
            "    @foreach (\\$rs->board as \\$rs1)      ",
            "    {{\\$rs1->title}}",
            "    @endforeach",
            "@endforeach"
        ]
    },
    "db_分頁": {
        "prefix": "db2 分頁",
        "scope": "html,blade",
        "description": "db 分頁 ",
        "body": [
            "@foreach (\\$data['rows'] as \\$key => \\$rs)   ",
            " {{\\$rs->title}}",
            "@endforeach",
            "@if (count(\\$data['rows'] )==0)",
            "  No Data",
            "@endif",
            "{{ method_exists (\\$data['rows'],'links') ? \\$data['rows']->links('layouts.paginate') :'' }}"
        ]
    },
    "db_groupby": {
        "prefix": "db sql語法 groupby",
        "scope": "php",
        "body": ["\\$rows->groupBy(DB::raw('left(created_at,7)'),'kind_id');"]
    },
    "db_table_groupby": {
        "prefix": "db 原sql語法 groupby",
        "scope": "php",
        "body": [
            "\\$rows1 = \\DB::table(\\DB::raw('('.PF::dbToSql(\\$rows).') a'))->selectRaw('${1:key},count(*) as tcount');",
            "\\$rows1->groupBy('${1:key}');",
            "\\$rows1 = \\$rows1->get();        ",
            "\\$this->data['rows${1:key}'] = \\$rows1;"
        ]
    },
    "db_where": {
        "prefix": "db where",
        "scope": "php",
        "description": "rows->where",
        "body": ["\\$rows->where('${1:id}', \\$${2:key});"]
    },

    "del day": {
        "prefix": "db raw delete 超過?天的記錄",
        "scope": "php",
        "body": [
            "\\DB::delete('delete from ${1:adminuserlog} where convert(created_at,DATE) < convert((DATE_ADD(convert(now(),DATETIME), INTERVAL -${2:30} DAY)),DATE)');"
        ]
    },
    "del minute": {
        "prefix": "db raw delete 超過?分鐘的記錄",
        "scope": "php",
        "body": [
            "\\DB::delete('delete from ${1:adminuserlog} where convert(created_at,DATETIME) < convert((DATE_ADD(convert(now(),DATETIME), INTERVAL -${2:30} MINUTE)),DATETIME)');"
        ]
    },

    "udpate day": {
        "prefix": "db raw update 超過?天的記錄",
        "scope": "php",
        "body": [
            "\\DB::update('update ${1:member} set online=0 where convert(created_at,DATE) < convert((DATE_ADD(convert(now(),DATETIME), INTERVAL -${2:30} DAY)),DATE)');"
        ]
    },
    "udpate count": {
        "prefix": "db raw count",
        "scope": "php",
        "body": [
            "\\DB::update('update project set keywords_count = (select count(*) from keywords where project_id = project.id) where id=?', array(\\$id));"
        ]
    },

    "udpate minute": {
        "prefix": "db raw update 超過?分鐘的記錄",
        "scope": "php",
        "body": [
            "\\DB::update('update ${1:member} set online=0 where convert(created_at,DATETIME) < convert((DATE_ADD(convert(now(),DATETIME), INTERVAL -${2:30} MINUTE)),DATETIME)');"
        ]
    },

    "FIND_IN_SET": {
        "prefix": "db where split FIND_IN_SET in",
        "scope": "php",
        "body": ["\\$rows->whereRaw('FIND_IN_SET(?, ${1:kind_ids})>0', ['A']);"]
    },



    "whereBetween": {
        "prefix": "db whereBetween field value1,value2",
        "scope": "php",
        "body": [
            "\\$rows->whereBetween('${1:created_at}', [date('Y-m-d'), date('Y-m-d')]);"
        ]
    },
    "whereRaw Between": {
        "prefix": "db whereRaw between value field1,field2",
        "scope": "php",
        "body": [
            "\\$rows->whereRaw(\"? between left(DATE_FORMAT(begindate, '%Y-%m'), 7) and left(DATE_FORMAT(closedate, '%Y-%m'), 7)\", [\\PF::left(\\$request->input('start'), 7)]);"
        ]
    },

    "order by ": {
        "prefix": "db order by Raw",
        "scope": "php",
        "description": "order by ",
        "body": ["\\$rows->orderByRaw('member_id desc');"]
    },
    "資料庫select欄位": {
        "prefix": "db sql語法 DB::select(DB::raw(select))",
        "scope": "php",
        "body": [
            "\\$rows = DB::select(DB::raw('select * from xx'));//不用加get()"
        ]
    },
    "dbUnique": {
        "prefix": "db dbUnique",
        "scope": "php",
        "body": [
            "\\$validators['${1:email}'] = ['required', new \\App\\Rules\\MyValidatorsUnique(",
            "'${2:member}', ['id' => \\$edit]",
            ")];"
        ]
    },

    "資料unprepared": {
        "prefix": "db 資料unprepared",
        "scope": "php",
        "description": "DB::unprepared",
        "body": ["DB::unprepared(\\$sqlcalculation);"]
    },

    "db request->all": {
        "prefix": "db create request->all Repositories",
        "scope": "php",
        "body": [
            "",
            "\\$this->fieldnicknames = ${1:member}::getFieldTitleArray();",
            "\\$validators = null;",
            "\\$validators['name'] = ['required'];",
            "\\$validator = Validator::make(\\$request->all(), \\$validators);",
            "\\$validator->setAttributeNames(\\$this->data['displaynames']);",
            "if (\\$validator->fails()) {",
            "    return view('errors.validatorback')->withErrors(\\$validator);",
            "}",
            " ",
            "\\$inputs = \\$request->all();",
            "\\$inputs['member_id'] = Auth::guard('member')->id();",
            "\\$inputs['created_at'] = date('Y-m-d H:i:s');",
            "\\\\App\\Models\\\\${1:board}::create(\\$inputs);",
            "return redirect(\"/membercenter/black\")->with('js', '_alert(\"新增成功\")');",
            "",
            ""
        ]
    },




    "sql wherenotin": {
        "prefix": "db where not in",
        "scope": "php",
        "body": ["\\$rows->whereNotIn('select',['banner','ip'])"]
    },

    "sql whereNotNull": {
        "prefix": "db where not null",
        "scope": "php",
        "body": ["\\$rows->whereNotNull('${1:kind}')"]
    },
    "sql whereNull": {
        "prefix": "db where is null",
        "scope": "php",
        "body": ["\\$rows->whereNull('${1:kind}')"]
    },
    "sql wherein": {
        "prefix": "db where in",
        "scope": "php",
        "body": ["\\$rows->whereIn('select',['news','ip'])"]
    },

    "sql wheredate": {
        "prefix": "db where date",
        "scope": "php",
        "body": ["\\$rows->whereDate('created_at', '=', date('Y-m-d'));"]
    },
    "sql whereday": {
        "prefix": "db where day",
        "scope": "php",
        "body": ["\\$q->whereDay('created_at', '=', date('d'));"]
    },
    "sql wheremonth": {
        "prefix": "db where month",
        "scope": "php",
        "body": ["\\$rows->whereMonth('created_at', '=', date('m'));"]
    },
    "sql whereyear": {
        "prefix": "db where year",
        "scope": "php",
        "body": ["\\$rows->whereYear('created_at', '=', date('Y'));"]
    },

    "db get_object_vars": {
        "prefix": "db field 欄位改成['xx'] get_object_vars",
        "scope": "php",
        "body": ["\\$rs = get_object_vars(\\$rs);"]
    },



    "transaction": {
        "prefix": "transaction",
        "scope": "php",

        "body": [
            "\\DB::transaction(function () {",
            "    DB::table('users')->where(\\$query)->first();",
            "    DB::table('users')->where('id', 1)->update(['status' => 1]);",

            "});",
            ""
        ]
    },
    "code": {
        "prefix": "string random code num 取得唯一code",
        "scope": "php",
        "body": ["PF::dbRndCode('${1:member}', '${2:code}'); //認證碼"]
    },
    "next": {
        "prefix": "db next",
        "scope": "php",
        "body": [
            "",
            "            //上一筆",
            "            \\$rows = \\$this->${1:board}Repo->selectRaw('id');",
            "            \\$rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');",
            "            \\$rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');",
            "            \\$rows->myWhere('created_at|<', \\$this->data['created_at'], 'sortnum', 'Y');",
            "            \\$rows->myWhere('id|<>', \\$this->data['id'], 'sortnum', 'Y');",
            "            \\$rows->orderByRaw('created_at desc');",
            "            \\$rows = \\$rows->take(1);",
            "            \\$rows = \\$rows->get();",
            "            if (\\$rows->count() > 0) {",
            "                \\$rs = \\$rows->first();",
            "                \\$this->data['p1'] = \\$rs->id;",
            "            }",
            "            //下一筆",
            "            \\$rows = \\$this->${1:board}Repo->selectRaw('id');",
            "            \\$rows->myWhere('(ifnull(begindate,curdate()))|<=', date('Y-m-d'), 'begindate', 'N');",
            "            \\$rows->myWhere('(ifnull(closedate,curdate()))|>=', date('Y-m-d'), 'closedate', 'N');",
            "            \\$rows->myWhere('created_at|>', \\$this->data['created_at'], 'sortnum', 'Y');",
            "            \\$rows->myWhere('id|<>', \\$this->data['id'], 'sortnum', 'Y');",
            "            \\$rows->orderByRaw('created_at');",
            "",
            "            \\$rows = \\$rows->take(1);",
            "//            PF::dbSqlPrint(\\$rows);",
            "            \\$rows = \\$rows->get();",
            "            if (\\$rows->count() > 0) {",
            "                \\$rs = \\$rows->first();",
            "                \\$this->data['p2'] = \\$rs->id;",
            "            }"
        ]
    },

    "copy": {
        "prefix": "db copy",
        "scope": "php",
        "body": ["\\$rows1 = clone \\$rows;"]
    },
    "dbjson": {
        "prefix": "db 解析jsonbody ",
        "scope": "php",
        "body": [
            "\\$rs = \\App\\Models\\\\${1:board}::where(['id' => \\$this->data['${2:id}']])->firstOrFail();",
            "            if ('' != \\$rs->jsonbody) {",
            "                \\$json = \\PF::json_decode(\\$rs->jsonbody, true); //ture=>可以用\\$json['yyy'];false=>可以直接update",
            "                foreach (\\$json as \\$k => \\$v) {",
            "                    if(\\$v!=\"\"){",
            "                    \\$this->data[\\$k] = \\$v;",
            "                    }",
            "                }",
            "            }"
        ]
    },
    "delete not exists": {
        "prefix": "db raw delete not exists 爸爸沒有就把小孩刪除",
        "scope": "php",
        "body": [
            "\\DB::delete('DELETE from ${1:ordergroup} where not exists (select null from ${2:member} where ${1:ordergroup}.${2:member}_id=${2:member}.id)');"
        ]
    },
    "delete left join": {
        "prefix": "db raw delete left join 爸爸沒有就把小孩刪除",
        "scope": "php",
        "body": [
            "\\DB::delete('DELETE ${1:ordergroup} FROM ${1:ordergroup} left join ${2:member} on (${1:ordergroup}.${2:member}_id = ${2:member}.id) where ${2:member}.id is null ');"
        ]
    },

    "statement": {
        "prefix": "db raw statement insert update delete 不返回rows",
        "scope": "php",
        "body": [
            "\\DB::statement(\"INSERT INTO users (name, email, password) VALUES (\")"
        ]
    },
    "unprepared": {
        "prefix": "db raw update del",
        "scope": "php",
        "body": ["\\$rows=\\DB::unprepared(\"SELECT * FROM ${1:ordergroup}\")"]
    },
    "db limit": {
        "prefix": "db limit",
        "scope": "php",
        "body": ["\\$rows = \\$rows->limit(${1:1});"]
    },

    "db select raw": {
        "prefix": "db raw select=>rows",
        "scope": "php",
        "body": ["\\$rows = \\DB::select(\\$sql);"]
    },

    "db all fields": {
        "prefix": "db show table all fields 顯示全部資料表全部欄位",
        "body": [
            "\\$tableInfos = [];",
            "        \\$tables = \\DB::getSchemaBuilder()->getAllTables();",
            "",
            "        foreach (\\$tables as \\$k => \\$v) {",
            "\\$name = head(\\$v);",
            "            \\$comments = DB::select(\"SHOW TABLE STATUS LIKE '\" . \\$name . \"'\");",
            "",
            "            \\$tableComment = \"\";",
            "            if (isset(\\$comments[0]->Comment)) {",
            "                \\$tableComment = \\$comments[0]->Comment;",
            "            }",
            "            \\$tableInfos[\\$name] = \\$tableComment;",
            "        }",
            ""
        ]
    },
    "json_rows": {
        "prefix": "db json string 解 rs",
        "scope": "php",
        "body": ["\\$rs=\\PF::jsonToRs(\\$rs, \\$rs->jsonbody);"]
    },
    "db model foreach": {
        "prefix": "model2 多筆資料 foreach 迴圈",
        "scope": "php",
        "description": "資料庫多筆資料",
        "body": [
            "\\$rows =\\App\\Models\\\\${1:board}::selectRaw('*');",
            "\\$rows->myWhere('${2:kind}|S', \\$request->input('${2:kind}'), \"${2:kind_id}\", 'N');",
            "\\$rows->whereRaw('ifnull(begindate,now())<=now() and ifnull(closedate,now())>=now()');",
            "\\$rows->orderByRaw('id desc');",
            "\\$rows=\\$rows->limit(10);",
            "\\$rows=\\$rows->get();",
            "foreach (\\$rows  as \\$key => \\$rs) {",
            "    PF::printr(\\$rs->${2:title});",
            "}"
        ]
    },
    "db model join foreach": {
        "prefix": "model2 多筆資料 foreach query join 迴圈",
        "scope": "php",
        "body": [
            "\\$rows =\\App\\Models\\\\${1:board}::query()",
            "->join('orderdetail', 'ordergroup.id', '=', '${1:board}_id')",
            "->selectRaw('${1:board}.*');",
            "\\$rows->myWhere('${2:kind}|S', \\$request->input('${2:kind}'), \"${2:kind_id}\", 'N');",
            "\\$rows->whereRaw('ifnull(begindate,now())<=now() and ifnull(closedate,now())>=now()');",
            "\\$rows->orderByRaw('id desc');",
            "\\$rows=\\$rows->limit(10);",
            "\\$rows=\\$rows->get();",
            "foreach (\\$rows  as \\$key => \\$rs) {",
            "    PF::printr(\\$rs->${2:title});",
            "}"
        ]
    },
    "model 分頁": {
        "prefix": "model2 分頁",
        "scope": "php",
        "description": "資料庫多筆資料",
        "body": [
            "\\$rows =\\App\\Models\\\\${1:board}::selectRaw('*');",
            "\\$rows->myWhere('${2:kind}|S', \\$request->input('${2:kind}'), \"${2:kind_id}\", 'N');",
            "\\$rows->whereRaw('ifnull(begindate,now())<=now() and ifnull(closedate,now())>=now()');",
            "\\$rows->orderByRaw('id desc');",
            "\\$rows = \\$rows->paginate(10);",
            "\\$this->data['rows'] = \\$rows;"
        ]
    },
    "model inner": {
        "prefix": "model 加一行 with join",
        "scope": "php",
        "body": [
            "\\$rows->with('${1:boardkind}');"
        ]
    },
    "model has": {
        "prefix": "model has 不回傳關係數據",
        "scope": "php",
        "body": [
            "\\$rows->has('${1:boardkind}');"
        ]
    },
    "model inner selectRaw": {
        "prefix": "model with 二層",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:boardkind}::selectRaw('*')",
            "->with(['${2:member}' => function (\\$query) {",
            "     \\$query->selectRaw('id,title');//一般要有父層的KEY",
            "     \\$query->where('online', 1);",
            "}]);",
        ]
    },
    "model inner with selectRaw": {
        "prefix": "model join with 二層",
        "scope": "php",
        "body": ["\\$rows = \\App\\Models\\\\${1:boardkind}::selectRaw('${1:boardkind}.*')",
            "            ->join('${2:member}', function (\\$join) use (\\$request) {",
            "                \\$join->on('${2:member}.id', '=', '${1:boardkind}.${2:member}_id');",
            "            })",
            "            ->with(['${2:member}' => function (\\$query) {",
            "                \\$query->selectRaw('*');",
            "            }])->where('mname', '=', '嘉義管理處')",
            "            ->get();",
        ]
    },
    "model inner selectRaw3": {
        "prefix": "model with 三層",
        "scope": "php",
        "body": ["\\$rows = \\App\\Models\\\\${1:boardkind}::selectRaw('*')",
            "            ->with(['${2:member}' => function (\\$query) {",
            "                \\$query->selectRaw('id,keyword,${3:member}_id')// 一定要有父層的KEY",
            "                    ->with(['${3:member}' => function (\\$query) {",
            "                        \\$query->selectRaw('id,title'); // 一定要有父層的KEY",
            "                    }]); ",
            "            }]);",
        ]
    },
    "model isDirty": {
        "prefix": "model 判斷欄位是否更動 isDirty getOriginal",
        "scope": "php",
        "body": [
            "if (\\$model->isDirty('${1:boardkind}')) {",
            "\\$model->getOriginal('${2:boardkind}')",
            "}",
        ]
    },
    "model inner whereHas": {
        "prefix": "model whereHas 增加查詢條件,不回傳關係數據",
        "scope": "php",
        "body": [
            "::whereHas('orderItems', function(\\$query) {",
            "   \\$query->where('status', 1);",
            "})->get();",
        ]
    },
    "model inner whereHas+with": {
        "prefix": "model with + whereHas 增加查詢條件,會回傳關係數據",
        "scope": "php",
        "body": [
            "whereHas('${1:member}', function(\\$q){",
            "    \\$q->where('online', 1);",
            "})->with(['${1:member}' => function(\\$q) {",
            "    \\$q->where('online', 1);",
            "}])",
        ]
    },
    "Model insert": {
        "prefix": "create",
        "scope": "php",
        "description": "model::create",
        "body": [
            "\\$inputs=[];",
            "\\$inputs['${2:title}'] ='${2:title}'; ",
            "\\$id=\\\\App\\Models\\\\${1:board}::create(\\$inputs)->id;"
        ]
    },
    "model rand": {
        "prefix": "model1 單一欄位 隨機值",
        "scope": "php",
        "body": [
            "\\App\\Models\\\\${1:board}::inRandomOrder()->first()->${2:id}"
        ]
    },
    "model first": {
        "prefix": "model1 第一筆 first()" ,
        "scope": "php",
        "body": [
            "\\$rs=\\App\\Models\\\\${1:board}::first();"
        ]
    },
    "model where first": {
        "prefix": "model1 where first() 判斷沒有 CustomException ",
        "scope": "php",
        "body": [
            "\\$rows=\\App\\Models\\\\${1:board}::selectRaw('${1:board}.*');",
            "\\$rows->where('${2:id}', '=', \\$request->input('${2:id}'));",
            "\\$rs = \\$rows->first();",
            "if (\\$rs == null) {",
            "throw new \\CustomException(\"找不到\");",
            "}"
        ]
    },
    "db1 model last": {
        "prefix": "model1 最後一筆 orderBy('id', 'desc')",
        "scope": "php",
        "body": [
            "\\$rs = \\App\\Models\\\\${1:board}::where([",
            "'ddate' => date('Y-m-d'),",
            "])->orderBy('id', 'desc')->firstOrFail();"
        ]
    },
    "db1 model all last": {
        "prefix": "model1 最後一筆 last",
        "scope": "php",
        "body": [
            "\\$rs = \\App\\Models\\\\${1:board}::all()->last();"
        ]
    },
    "model array_merge data": {
        "prefix": "model rs=>this->data",
        "scope": "php",
        "body": [
            "\\$this->data = PF::stdClassToArray(\\$this->data, \\$rs);"
        ]
    },
    "model db findOrFail": {
        "prefix": "model1 with firstOrFail where",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:ordergroup}::selectRaw('*');",
            "\\$rows->myWhere('account|S', \"allen\", \"account\", 'Y');",
            "\\$rs = \\$rows->firstOrFail();",
            "\\$this->data = PF::stdClassToArray(\\$this->data, \\$rs);"
        ]
    },
    "model db with findOrFail": {
        "prefix": "model1 with firstOrFail where",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:ordergroup}::with('${2:orderdetail}');",
            "\\$rows->myWhere('account|S', \"allen\", \"account\", 'Y');",
            "\\$rs = \\$rows->firstOrFail();",
            "\\$this->data = PF::stdClassToArray(\\$this->data, \\$rs);"
        ]
    },
    "model db with firstOrFail by id": {
        "prefix": "model1 with firstOrFail by id",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:ordergroup}::with('${2:orderdetail}');",
            "\\$rs = \\$rows->findOrFail(\\$id);",
            "\\$this->data = PF::stdClassToArray(\\$this->data, \\$rs);"
        ]
    },
    "findOrFail": {
        "prefix": "model1 findOrFail id",
        "scope": "php",
        "body": [
            "\\$rs=\\App\\Models\\\\${1:board}::findOrFail(\\$id);",
            "\\$this->data = PF::stdClassToArray(\\$this->data, \\$rs);"
        ]
    },
    "find": {
        "prefix": "model1 find id",
        "scope": "php",
        "body": [
            "\\$rs=\\App\\Models\\\\${1:board}::find(\\$id);",
            "\\$this->data = PF::stdClassToArray(\\$this->data, \\$rs);"
        ]
    },
    "findOrFail where ": {
        "prefix": "model1 firstOrFail where ",
        "scope": "php",
        "body": ["\\$rows = \\App\\Models\\\\${1:board}::selectRaw('*');",
            "\\$rows->myWhere('id|N',  \\$request->input('id'), \"id\", 'Y');",
            "\\$rs = \\$rows->firstOrFail();",
        ]
    },
    "update model where": {
        "prefix": "update 多條件多筆 foreach",
        "scope": "php",
        "body": [
            "\\$inputs = [];",
            "\\$inputs['status'] = 4;",
            "\\$rows = \\App\\Models\\\\${1:board}::selectRaw('id');",
            "\\$rows->myWhere('id|N', \\$edit, 'id', 'Y');",
            "\\$rows->chunk(200, function (\\$rows) use (\\$inputs) {",
            "      foreach (\\$rows as \\$rs) {",
            "        \\$rs->update(\\$inputs);",
            "      }",
            "});"
        ]
    },
    "update findOrFail model": {
        "prefix": "update by id findOrFail 找不到會失敗",
        "scope": "php",
        "body": [
            "\\$inputs=[];",
            "\\$inputs['${2:title}'] ='${2:title}'; ",
            "\\$rs=\\App\\Models\\\\${1:member}::findOrFail(\\$edit);",
            "\\$rs->update(\\$inputs);"
        ]
    },
    "update find model": {
        "prefix": "update by id find 找不到不會失敗",
        "scope": "php",
        "body": ["\\$rs = \\App\\Models\\\\${1:member}::find(\\$edirt);",
            "if (\\$rs != null) {",
            "                \\$rs->update(\\$inputs);",
            "}",
        ]
    },
    "update add count": {
        "prefix": "update count +1",
        "scope": "php",
        "body": [
            "\\$inputs=[];",
            "\\$inputs['${2:title}'] ='${2:title}'; ",
            "\\$rs=\\App\\Models\\\\${1:member}::findOrFail(\\$edit);",
            "\\$rs->update(\\$inputs);",
            "\\$rs->increment('article_count');"
        ]
    },

    "db1 repo update": {
        "prefix": "udpate mywhere 一筆",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:member}::selectRaw('${1:member}.*');",
            "\\$rows->myWhere('id|N', \\$edit, 'id', 'Y');",
            "\\$rs = \\$rows->firstOrFail();",
            "\\$inputs = [];",
            "\\$inputs['${2:title}'] = '${2:title}';",
            "\\$rs->update(\\$inputs);"
        ]
    },
    "db1 update": {
        "prefix": "update 必須符合fillable 不會觸發updated 多筆",
        "scope": "php",
        "body": [
            "\\$inputs=[];",
            "\\$inputs['${2:title}'] ='${2:title}'; ",
            "\\App\\Models\\\\${1:member}::where('id', \\$edit)->update(\\$inputs);"
        ]
    },
    "update rs model where": {
        "prefix": "update model rs->udpate",
        "scope": "php",
        "body": [
            "\\$rs->update(\\$inputs);",
        ]
    },
    "updateOrCreate ": {
        "prefix": "update updateOrCreate 資料有存在已更新沒有建立",
        "scope": "php",
        "body": [
            "\\$inputs=[];",
            "\\$inputs['${2:title}'] ='${2:title}'; ",
            "\\$rows=\\App\\Models\\\\${1:board}::updateOrCreate(",
            "  [",
            "   'id'=>1",
            "  ]",
            ",\\$inputs);",
            "    if (\\$rows->wasRecentlyCreated) {",
            "            \\$this->jsondata['resultmessage'] = '新增成功';",
            "    } else {",
            "            \\$this->jsondata['resultmessage'] = '更新成功';",
            "    }",
            "        \\$edit = \\$rows->id;"
        ]
    },
    "updateOrCreate1 ": {
        "prefix": "update updateOrCreate where first null",
        "scope": "php",
        "body": ["\\$rows = \\App\\Models\\\\${1:board}::selectRaw('id');",
            "        \\$rows->where('number', '=', \\$request->input('number'));",
            "        \\$rs = \\$rows->first();",
            "        if (\\$rs != null) {",
            "            \\$id = \\$rs->id;",
            "            \\$rs->update(\\$inputs);",
            "        } else {",
            "            \\$id = \\App\\Models\\\\${1:board}::create(\\$inputs)->id;",
            "        }",
        ]
    },
    "firstOrCreate ": {
        "prefix": "create firstOrCreate 資料有存在不新增",
        "scope": "php",
        "body": [
            "\\$rows=\\App\\Models\\\\${1:board}::firstOrCreate([",
            " 'activity_id' => \\$request->input('activity_id'),",
            " ], \\$inputs);",
            " if(\\$rows->wasRecentlyCreated){",
            "       \\$this->jsondata['resultmessage'] = '新增成功';",
            " }else{",
            "       \\$this->jsondata['resultmessage'] = '已存在';",
            " }"
        ]
    },
    "insert create": {
        "prefix": "create 多筆資料建立 insert",
        "scope": "php",
        "body": [
            "\\$datas=[]; ",
            "  \\$datas[] = [",
            "            'kind' => 'news',",
            "            'begindate' => date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month')))),",
            "];",
            "\\App\\Models\\\\${1:board}::insert(\\$datas);"
        ]
    },
    "spilt insert": {
        "prefix": "create insert split 整枇 insert",
        "scope": "php",
        "body": [
            "DB::beginTransaction();",
            "try {",
            "        \\$rows = \\App\\Models\\\\${1:board}::selectRaw('id');",
            "        \\$rows->myWhere('adminuser_id|N', \\$edit, 'del', 'Y');",
            "        \\$rows->delete();",
            "        \\$datas = [];",
            "        \\$items = explode(\",\", \\$request->input('member_ids'));",
            "        foreach (\\$items as \\$k => \\$v) {",
            "            \\$datas[] = [",
            "                'adminuser_id' => \\$edit,",
            "                'member_id' => \\$v,",
            "            ];",
            "        }",
            "",
            "\\App\\Models\\\\${1:board}::insert(\\$datas);",
            "DB::commit();",
        "} catch (\\Exception \\$e) {",
        "  DB::rollback();",
        "  throw \\$e;",
        "}",
        ]
    },
    "foreach insert": {
        "prefix": "create insert split 整枇 insert",
        "scope": "php",
        "body": [
            "DB::beginTransaction();",
            "try {",
            "        \\$rows = \\App\\Models\\\\${1:board}::selectRaw('id');",
            "        \\$rows->myWhere('adminuser_id|N', \\$edit, 'del', 'Y');",
            "        \\$rows->delete();",
            "        \\$datas = [];",

            "        foreach (\\$request->input('product_items') as \\$k => \\$v) {",
            "            \\$datas[] = [",
            "                'adminuser_id' => \\$edit,",
            "                'member_id' => \\$v,",
            "            ];",
            "        }",
            "",
            "        \\App\\Models\\\\${1:board}::insert(\\$datas);",
            "        DB::commit();",
            "} catch (\\Exception \\$e) {",
            "  DB::rollback();",
            "  throw \\$e;",
            "}",
        ]
    },
    "delete delete findOrFail": {
        "prefix": "delete findOrFail id 一筆 資料刪除(會觸發deleted)",
        "scope": "php",
        "body": [
            "\\App\\Models\\\\${1:member}::findOrFail(\\$id)->delete();"
        ]
    },
    "db delete no deleted": {
        "prefix": "delete 多條件 多筆 不會觸發deleted",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:board}::selectRaw('id');",
            "\\$rows->myWhere('id|ININT', \\$this->data['del'], 'del', 'Y');",
            "\\$rows->delete();"
        ]
    },
    "db delete": {
        "prefix": "delete 多條件 foreach 觸發deleted",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:board}::selectRaw('${1:board}.*');",
            "\\$rows->myWhere('id|ININT', \\$this->data['del'], 'del', 'Y');",
            "\\$rows->chunk(200, function (\\$rows) {",
            "      foreach (\\$rows as \\$rs) {",
            "        \\$rs->delete();",
            "      }",
            "});"
        ]
    },
    "belongsTo": {
        "prefix": "model table belongsTo 子對父(多對一) orderdetail -> ordergroup",
        "scope": "php",
        "body": [
            "// 子對父(多對一)foregin key 在自己身上，那自己就使用 belongsTo。",
            "    public function ${1:ordergroup}()",
            "    {",
            "        return \\$this->belongsTo(${1:ordergroup}::class);",
            "      //return \\$this->belongsTo(${1:ordergroup}::class, '自己_id', '對方id')",
            "    }"
        ]
    },
    "hasMany": {
        "prefix": "model table hasMany 父對子(一對多) ordergroup -> orderdetail",
        "scope": "php",
        "body": [
            "// 父對子(一對多)",
            "    public function ${1:orderdetail}()",
            "    {",
            "return \\$this->hasMany(${1:orderdetail}::class)->orderBy('id');",
            "return \\$this->hasMany(${1:orderdetail}::class, 'sn', 'sn')->orderBy('id');"
            "    }"
        ]
    },
    "hasOne": {
        "prefix": "model table hasOne 父對子(一對一) orderdetail -> product",
        "scope": "php",
        "body": [
            "// 父對子(一對一)foreign key 在別人身上，那自己就是使用 hasOne",
            "    public function ${1:productitem}()",
            "    {",
            "       return \\$this->hasOne(${1:productitem}::class,'${2:id}','${1:productitem}_id')->orderBy('id');",
            "    }"
        ]
    },
    "model db foreach": {
        "prefix": "db1 一筆資料 model getAttributes 欄位 foreach",
        "scope": "php",
        "body": [
            "foreach (\\$rs->getAttributes() as \\$field => \\$value) {",
            "  \\$this->data[\\$field] = \\$value;",
            "}"
        ]
    },
    "model db foreach1": {
        "prefix": "model1 with 指定欄位select (一層)",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:ordergroup}::with(['${2:orderdetail}' => function (\\$query) {",
            " \\$query->selectRaw('id,kindtitle');//一定要有父層的KEY",
            "}]);",
            "\\$rows->where('id', '10028');",
            "\\$rows = \\$rows->get();",
            "foreach (\\$rows as \\$rs) {",
            "",
            "}",
        ]
    },
    "model db2 with foreach": {
        "prefix": "model2 with foreach",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:ordergroup}::with('${2:orderdetail}');",
            "\\$rows->myWhere('kind|S', '${3:news}', \"kind\", 'Y');",
            "\\$rows = \\$rows->get();",
            "foreach (\\$rows as \\$rs) {",
            "",
            "}",
        ]
    },
    "model db with foreach": {
        "prefix": "model2 with foreach 資料表指定select欄位(一層)",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:ordergroup}::with(['${2:orderdetail}' => function (\\$query) {",
            " \\$query->selectRaw('id,kindtitle');//一定要有父層的KEY",
            "}]);",
            "\\$rows->myWhere('kind|S', '${3:news}', \"kind\", 'Y');",
            "\\$rows = \\$rows->get();",
            "foreach (\\$rows as \\$rs) {",
            "",
            "}",
        ]
    },
    "model db with foreach2": {
        "prefix": "model2 with foreach 資料表指定欄位(二層)",
        "scope": "php",
        "body": [
            "\\$rows = \\App\\Models\\\\${1:ordergroup}::with(['${2:orderdetail}' => function (\\$query) {",
            " \\$query->selectRaw('id,kindtitle');//一定要有父層的KEY",
            "}, '${2:orderdetail}.${3:product}' => function (\\$query) {",
            "            \\$query->select('id', 'imgs');",
            " }]);",
            "\\$rows->where('id', '10028');",
            "\\$rows = \\$rows->get();",
            "foreach (\\$rows as \\$rs) {",
            "    foreach (\\$rs->orderdetail as \\$rs1) {",
            "    }",
            "}",
        ]
    },
    "db sub table": {
        "prefix": "db sub table",
        "scope": "php",
        "body": [
            "\\$subQuery = \\DB::table('member')->selectRaw('",
            "member.*,",
            "(select sum(rbuycount) from ordergroup where member_id=member.id and moneystatus=1) as tcount,",
            "(select max(created_at) from ordergroup where member_id=member.id and moneystatus=1) as last_pay_date",
            "')->havingRaw('tcount > 0');",
            "\\$rows = \\DB: :query()->fromSub(\\$subQuery, 'member');",
        ]
    },


}
