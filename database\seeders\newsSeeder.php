<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use DB;
use App\Repositories\boardRepository;

class newsSeeder extends Seeder
{
    private $boardRepo;

    public function __construct(boardRepository $boardRepo)
    {
        
        $this->boardRepo = $boardRepo;
    }

    /**
     * Run the database seeds.
     */
    public function run()
    {
        $this->boardRepo->select()
        ->myWhere('kind|S', 'news', "del", 'Y')
        ->delete();
        
        $faker = \Faker\Factory::create('zh_TW');

        for ($i = 0; $i < 10; ++$i) {
            $data = [
                'kind' => 'news',
                //'title' =>  $faker->sentence,
                'title' => '金雞母不要了！西門町16間億級店面求售-'.$i,
                'body' =>  $faker->text(2000),

                //'field1' => $faker->file('C:\AppServ\laravel\1\Pictures\banner.jpg', 'C:\AppServ\laravel\e\storage\app\public\images\news', false),
                //'field1' => $faker->image(public_path('images/place'), 800, 800, 'cats',false),

                // 'field2' => '(02)2314-6871',
                // 'field3' => '(02)2331-8047',
                //'body' => $faker->randomHtml(2, 3),

                'hits' => 0,

                'begindate' => date('Y-m-d', strtotime(date('Y-m-d', strtotime('-1 month')))),
            ];
            $this->boardRepo->updateOrCreate($data);
        }
        
        

        

        // $this->call(UsersTableSeeder::class);
    }

}
