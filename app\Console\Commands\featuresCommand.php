<?php

namespace App\Console\Commands;

use DB;
use PF;
use Illuminate\Console\Command;

//command php artisan features:create
class featuresCommand extends Command {
    public $isnuxt;
    public $isapi;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'features:create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'COPY PROGRAM';
    public $onlyLaravelFolders;
    public function __construct() {
        parent::__construct();
        $this->isnuxt = false;
        $this->isapi = false;
        if (\File::exists(base_path('vite.config.js')) || \File::exists(base_path('resources/js/nuxt.config.ts'))) {
            $this->isnuxt = true;
        }
        if (\File::exists(base_path('public/swagger/index.html'))) {
            $this->isapi = true;
        }
        $this->onlyLaravelFolders = ["/app/Http/Controllers", "/tests/Feature", "/tests/Feature/admin"];
    }

    public function tree($sourceDir, $directory, $destinationDir) {
        //\PF::printr(["this->isnuxt", $this->isnuxt]);
        // \PF::printr(["directory1", $directory]);

        // \PF::printr(["directory2", $directory]);
        $mydir = dir($directory);


        while ($file = $mydir->read()) {
            if ((is_dir("$directory/$file")) and ('.' != $file) and ('..' != $file)) {
                if ('doc' == $file) {
                    continue;
                }
                $x = new featuresCommand();

                $x->tree($sourceDir, "$directory/$file", $destinationDir);
            } elseif ('.' != $file && '..' != $file) {
                $sourcefile = $directory . '/' . $file;

                if ($this->isapi == false) {

                    if (\Str::contains($directory, ['/controllers/api/', '/tests/Feature/api/'])) {
                        return;
                    }
                }
                $tFolder = str_replace($sourceDir, "", $directory);
                if ($this->isnuxt) {
                    if (\Str::contains($tFolder, ['/Controllers/admin', '/Controllers/member', '/Scripts', '/Controllers/membercenter', '/resources/views', 'Feature/membercenter', 'Feature/member', 'Feature/admin'])) {
                        return;
                    }
                    if (in_array($tFolder, $this->onlyLaravelFolders)) {
                        return;
                    }
                } else if ($this->isapi) {
                    if (in_array($tFolder, $this->onlyLaravelFolders)) {
                        return;
                    }
                } else {
                    if (in_array($tFolder, ["/resources/js"])) {
                        return;
                    }
                }

                $tofolder = base_path() . str_replace($sourceDir, '', $directory);
                $destinationfile = $tofolder . '/' . $file;
                if (false == \File::isDirectory($tofolder)) {
                    \File::makeDirectory($tofolder, 0777, true, true);
                }
                echo $destinationfile . ' > ';
                if ('.sql' == PF::right($file, 4)) {
                    try {
                        $sql = \File::get($sourcefile);
                        DB::update($sql);
                        echo ' command OK ';
                    } catch (\Exception $e) {
                        echo $e->getMessage();
                    }
                } elseif ('Setup.xml' == $file) {
                    //SETUP.XML處理
                    //sapi_windows_cp_set(65001);

                    $directory = str_replace('\\\\', '\\', $directory);
                    echo $directory;
                    $xml = simplexml_load_file($directory . '/Setup.xml', null, LIBXML_NOCDATA);
                    $children = $xml->children();
                    $xmlDoc = PF::xmlDoc('Setup.xml');
                    $xmlfile = storage_path('app/Setup.xml');

                    foreach ($xml->xpath('//參數設定檔/*') as $item) {
                        echo ' XML Node ';
                        echo $item->getName();
                        echo ' -> ';
                        $xpath = '//參數設定檔/' . $item->getName();
                        //echo $xpath;

                        $objxml = $xmlDoc->xpath($xpath);
                        if (empty($objxml)) {
                            //print_r($item->asXML());
                            try {
                                $ebody = \File::get($xmlfile);
                                $ebody = str_replace('<參數設定檔>', "<參數設定檔>\r\n   " . $item->asXML(), $ebody);
                                //echo $ebody;
                                \File::put($xmlfile, $ebody);
                            } catch (\Exception $e) {
                                die($e->getMessage());
                            }
                            echo '寫入成功';
                        } else {
                            echo ' exist';
                        }
                        echo "\r\n";
                    }
                } elseif (mb_substr_count($file, 'install-') > 0) {
                    //install.bat 安裝套件
                    system($sourcefile);
                } elseif (mb_substr_count($file, '.env') > 0) {
                    echo 'make:' . $file . PHP_EOL;
                    try {
                        $ebody1 = \File::get($sourcefile);
                        $items = explode("\r\n", $ebody1);
                        foreach ($items as $k => $v) {
                            $ebody2 = \File::get($destinationfile);

                            if (0 == substr_count($ebody2, $v)) {
                                $ebody2 .= "\r\n" . $v;
                                //echo $destinationfile;
                                //echo base_path()."\\".$file;
                                $myfile = fopen(base_path() . '\\' . $file, "a+") or die("Unable to open file!");
                                fwrite($myfile, $v . PHP_EOL);
                                fclose($myfile);
                                \File::put(base_path() . '\\' . $file, $ebody2);
                                echo $v . ' -> 新增成功';
                            }
                        }
                    } catch (\Exception $e) {
                        echo $e->getMessage(); //throw e;
                    }
                } else {

                    if (false == \File::exists($destinationfile)) {
                        $success = \File::copy($sourcefile, $destinationfile);
                        echo '1' == $success ? ' OK' : $success;
                        // if (mb_substr_count($directory,"database/migrations")>0) {
                        //     \Artisan::call('migrate', [
                        //         '--force'     => true,
                        //     ]);
                        //     echo ' migrate ';
                        // }elseif (mb_substr_count($directory,"database/seeders")>0) {
                        //     $class=str_replace(".php","",$file);
                        //     \Artisan::call('db:seed', [

                        //         '--class'     => $class,
                        //     ]);
                        //     echo ' migrate ';
                        // }
                    } else {
                        echo ' exist ';
                    }
                }
                echo "\r\n";
            }
        }

        $mydir->close();
    }

    public function handle() {
        //try {
        $sourceDir = 'C:\\AppServ\\laravel\\1\\範本\\';
        $mydir = dir($sourceDir);
        $folders = null;
        while ($file = $mydir->read()) {
            if ((is_dir("$sourceDir/$file")) && ('.' != $file) && ('..' != $file)) {
                $folders[] = $file;
            }
        }
        $folder = $this->choice('選擇您要的功能?', $folders);
        //$kind = $this->ask($body);
        sapi_windows_cp_set(65001);
        //echo $dic[$kind];
        $sourceDir .= '\\' . $folder;

        $destinationDir = base_path();
        echo '來源:' . $sourceDir;
        echo "\r\n";
        echo '目的:' . $destinationDir;
        echo "\r\n";

        $x = new featuresCommand();
        $x->tree($sourceDir, $sourceDir, $destinationDir);

        // $cdir = scandir($sourceDir);

        // foreach ($cdir as $key => $value)
        // {
        //    if (!in_array($value,array(".","..")))
        //    {
        //         echo "\r\n";
        //         echo $sourceDir."\\".$value." > ".$destinationDir;
        //         $success = \File::copyDirectory($sourceDir."\\".$value, $destinationDir, true);
        //         echo " = ";
        //         echo ($success=="1") ? "OK" : "ERROR";
        //    }

        // }
        echo "\r\n";

        $msg .= \Artisan::output();

        $this->call('view:clear');
        //  } catch (\Exception $e) {
        //      $msg .= $e->getMessage();
        // }
        echo $msg;
        // echo $this->argument('name');
    }
}
