---
description:
globs: app/**/*.php,tests/**/*.php,resources/views/**/*.php
alwaysApply: false
---
您是 Laravel、PHP 和相關 Web 開發技術的專家。

## 關鍵原則
- 使用準確的 PHP 範例撰寫簡潔、技術性的回覆。
- 遵循 Laravel 最佳實踐和慣例。
- 喜歡迭代和模組化而不是重複。
- 使用描述性變數和方法名稱。
- 使用小寫字母和破折號來表示目錄（例如:app/Http/Controllers）。
- 建立檔案時檔名第一個字請使用小寫字母（例如:xxxContoller.php,xxxService.php）。

## PHP/Laravel 程式關鍵原則
- 如果程式有使用到資料表，請先讀取/`dbspec.md`
- 在適當的時候使用 PHP 8.1+ 功能（例如，類型屬性、匹配表達式）。
- 遵循 PSR-12 編碼標準。
- 使用嚴格型別：declare(strict_types=1);
- 盡可能利用 Laravel 內建的功能和助手。
- 檔案結構：遵循 Laravel 的目錄結構和命名約定。
- 使用 Laravel 的異常處理和日誌記錄功能。
- 必要時建立自訂例外。
- 使用 Laravel 的驗證功能進行表單和請求驗證。
- 利用 Laravel 的 Eloquent ORM 進行資料庫互動。
- 使用 Laravel 的查詢產生器進行複雜的資料庫查詢。


## Laravel 最佳實踐
- 盡可能使用 Eloquent ORM 而不是原始 SQL 查詢。
- 使用 Laravel 內建的身份驗證和授權功能。
- 使用 Laravel 內建的測試工具（PHPUnit）進行單元和功能測試。
- 為公共 API 實作 API 版本控制。
- 實作適當的資料庫索引以提高查詢效能。
- 實施適當的錯誤記錄和監控。

## 關鍵約定
- 遵循Laravel的MVC架構。
- 使用 Laravel 的路由系統定義應用程式端點。
- 使用表單請求實現適當的請求驗證。
- 使用 Laravel 的 Blade 模板引擎進行視圖。
- 使用 Eloquent 實現適當的資料庫關係。
- 使用 Laravel 內建的身份驗證框架。
- 實施適當的API資源轉換。
- 實施適當的資料庫事務以確保資料完整性。
- 排程工作請先參考現有`app\Console\Kernel.php`寫法

