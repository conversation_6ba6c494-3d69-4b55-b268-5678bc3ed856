<?php

namespace App\Http\Controllers;

use PF;
use Config;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\boardRepository;

class successController extends Controller {
    private $data;
    private $boardRepo;

    public function __construct(boardRepository $boardRepo) {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);
        $this->boardRepo = $boardRepo;
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        Config::set('config.title', '成交分享 | ' . config('config.title'));

        $rows = $this->boardRepo->selectRaw('*');
        $rows->myWhere('kind|S', 'success', 'successid', 'Y');
        $rows->orderByRaw('boardsort');
        $rows = $rows->paginate(24);
        $this->data['rows'] = $rows;

        return view(
            'success.index',
            [
                'data' => $this->data,
            ]
        );
    }

    public function show($id) {
        $rs = \App\Models\board::findOrFail($id);
        $this->data = PF::stdClassToArray($this->data, $rs);

        \DB::update('update board set hits=hits+1 where id = ?', [$id]);

        Config::set('config.title', $this->data['title'] . '| 成功案例 | ' . config('config.title'));
        // Config::set('config.keyword', '');
        // Config::set('config.description', '');

        return view(
            'success.show',
            [
                'data' => $this->data,
            ]
        );
    }
}
