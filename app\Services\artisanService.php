<?php

namespace App\Services;

use DB;
use ZipArchive;

/***
"功能名稱":"服務層 - laravel -artisan 操作",
"資料表":"",
"備註":" ",
"建立時間":"2022-01-18 16:57:34",
 ***/
class artisanService {
    public function __construct() {
    }

    public function migrate() {
        //if(! defined('STDIN')) define('STDIN', fopen("php://stdin","r"));
        try {
            echo 'init migrate:install...' . PHP_EOL;
            \Artisan::call('migrate:install');
            echo 'done migrate:install' . PHP_EOL;
        } catch (\Throwable $e) {
            echo 'allready installed' . PHP_EOL;
        }

        echo 'init with tables migrations' . PHP_EOL;

        \Artisan::call('migrate', ['--force' => true]);
        echo 'done with migrations' . PHP_EOL;

        if ('dev' == \config('app.env')) {
            echo 'init with synchronize:model' . PHP_EOL;
            try {
                \Artisan::call('synchronize:model', []);
                echo 'done synchronize:model' . PHP_EOL;
            } catch (\Exception $e) {
                return response($e->getMessage());
            }
        }
    }

    public function migraterefresh($tablename) {
        try {
            $mydir = dir(base_path('database/migrations/'));
            while ($file1 = $mydir->read()) {
                if (('.' != $file1) and ('..' != $file1)) {
                    if (mb_substr_count($file1, 'create_' . $tablename . '_table.php') > 0) {
                        $file = $file1;
                    }
                }
            }

            if ('' == $file) {
                throw new \CustomException($file . ' not found file');
            }
            $temp_fields_key = [];
            if (false == \Schema::hasTable($tablename) && \Schema::hasTable('temp_' . $tablename)) {
                \DB::statement('RENAME TABLE `temp_' . $tablename . '` TO `' . $tablename . '`');
            }
            if (\Schema::hasTable($tablename) && false == \Schema::hasTable('temp_' . $tablename)) {
                echo  $file . ' done with migrations' . PHP_EOL;
                if (\Schema::hasTable($tablename) && false == \Schema::hasTable('temp_' . $tablename)) {
                    \DB::statement('RENAME TABLE  `' . $tablename . '` TO  `temp_' . $tablename . '`');
                }
            }
            if (\Schema::hasTable('temp_' . $tablename)) {
                $temp_fields = DB::select('SHOW FULL COLUMNS FROM temp_' . $tablename);

                foreach ($temp_fields as $k => $v) {
                    $temp_fields_key[$v->Field] = 1;
                }
            }

            //  echo 2;
            \Artisan::call('migrate:refresh', [
                '--path' => '/database/migrations/' . $file,
                '--force' => true,
            ]);

            if (count($temp_fields_key) > 0) {
                $fields = DB::select('SHOW FULL COLUMNS FROM ' . $tablename);

                $sql = 'insert into `' . $tablename . '` (';
                foreach ($fields as $k => $v) {
                    $sql .= '`' . $v->Field . '`,';
                }
                $sql = ltrim(rtrim($sql, ','), ',');
                $sql .= ') select ';

                foreach ($fields as $k => $v) {
                    if (null != $temp_fields_key[$v->Field]) {
                        $sql .= '`' . $v->Field . '`,';
                    } else {
                        $sql .= 'null,';
                    }
                }
                $sql = ltrim(rtrim($sql, ','), ',');
                $sql .= ' from `temp_' . $tablename . '`';

                if (\Schema::hasTable('temp_' . $tablename)) {
                    \DB::statement($sql);
                    \DB::statement('drop TABLE `temp_' . $tablename . '`');
                }
            }
            //echo 'COPY temp_'.$tablename.' -> '.$tablename.' OK<br>';
            if ('dev' == \config('app.env')) {
                // if ('production' != \config('app.env')) {
                echo  'init with synchronize:model' . PHP_EOL;
                try {
                    \Artisan::call('synchronize:model', []);
                    echo  PHP_EOL;
                    echo  'done synchronize:model' . PHP_EOL . " OK";
                } catch (\Exception $e) {
                    echo  'ERROR : ' . $e->getMessage();
                }
                echo '';
            }
        } catch (\Exception $e) {
            echo  'ERROR : ' . $e->getMessage() . PHP_EOL;

            if (\Schema::hasTable('temp_' . $tablename)) {
                if (\Schema::hasTable($tablename)) {
                    \DB::statement('drop TABLE `' . $tablename . '`');
                }
                \DB::statement('RENAME TABLE `temp_' . $tablename . '` TO `' . $tablename . '`');
            }
            echo  '已將 `temp_' . $tablename . '` 還原至 `' . $tablename . '`' . PHP_EOL;
        } finally {
        }
    }

    public function seed($file) {
        if (false == \File::exists(base_path('database/seeders/' . $file . 'Seeder.php'))) {
            throw new \CustomException('No file :' . $file . 'Seeder.php');
        }

        if ('' != $file) {
            \Artisan::call('db:seed', [
                '--class' => $file . 'Seeder',
                '--force' => true,
            ]);
            echo $file . ' done with seed';
        }
        //  else {
        //     \Artisan::call('db:seed', [
        //             '--force' => true,
        //         ]);
        //     echo 'Seeded!';
        // }
    }

    // public function ftp($ftpname)
    // {
    //     \Artisan::call('ftp:upload', [
    //         'ftpname' => $ftpname,
    //     ]);

    //     return response('ok');
    // }

    public function clear() {
        \Artisan::call('optimize:clear');
        if (\config('app.env') != 'dev') {
            \Artisan::call('config:cache');
        }

        $items = [storage_path('framework/laravel-excel'), storage_path('framework/cache/laravel-excel')];

        foreach ($items as $k1 => $v1) {
            if (\File::isDirectory($v1)) {
                $files = \File::files($v1);
                foreach ($files as $k2 => $v2) {
                    $path = $v2->getRealpath();
                    if (\File::exists($path)) {
                        \File::delete($path);
                    }
                };
            }
        }
        //\Artisan::call('optimize');
        return 'Cleared!';
    }
    public function vue() {
        $zip = new ZipArchive();
        \File::deleteDirectory(public_path('build/assets'));
        echo "del build/assets OK | ";
        \File::copy(public_path("build/assets.zip"), public_path("build/assets1.zip"));
        echo "copy assets.zip OK | ";
        if (true === $zip->open(public_path("build/assets.zip"))) {
            $zip->extractTo(public_path("build/assets")); //避免覆蓋，將解壓縮資料放進該資料夾
            $zip->close();
        }
        return 'extract ok';
    }
    public function nuxt() {
        $zip = new ZipArchive();
        \File::deleteDirectory(public_path('_nuxt'));
        echo "del _nuxt OK | ";

        if (true === $zip->open(public_path("public.zip"))) {
            $zip->extractTo(public_path("/")); //避免覆蓋，將解壓縮資料放進該資料夾
            $zip->close();

            return response('ok');
        } else {
            return 'not public.zip';
        }
    }
}
