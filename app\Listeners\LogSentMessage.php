<?php

namespace App\Listeners;

/***
"功能名稱":"LOG函式",
"資料表":"member",
"備註":"寄信時寫一個LOG到檔案",
"建立時間":"2022-01-18 13:22:16",
 ***/
class LogSentMessage {
    /**
     * Handle the event.
     *
     * @param Illuminate\Mail\Events\MessageSending $event
     */
    public function handle($event) {
        //發信時寫一筆LOG
        if (null == $event->message->getTo()) {
            return;
        }


        $version = app()->version();
        if (version_compare($version, '10.0.0', '>=')) {
            foreach ($event->message->getTo() as $key => $rs) {
                \Log::info('Send Mail To :' . $event->message->getSubject() . ' > ' . $rs->getAddress());
            }
        } else {
            $aray = array_keys($event->message->getTo());
            for ($i = 0; $i < count($aray); ++$i) {
                \Log::info('Send Mail Title :' . $event->message->getSubject() . " | Form : " . implode(',', $event->message->getFrom())  . ' | To : ' . $aray[$i]);
            }
        }
    }
}
