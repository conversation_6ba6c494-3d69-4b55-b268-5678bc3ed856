<?php

namespace App\Console\Commands;

use DB;
use PF;
use Illuminate\Console\Command;

//command "php artisan synchronize:model"
class synchronizemodelCommand extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'synchronize:model ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步model';


    public function __construct() {
        parent::__construct();
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }



    public function handle() {

        $Database = \config('database.connections.mysql.database');

        //$table_name = $this->option('table_name');

        $dsn = 'mysql:host=127.0.0.1;port=3306;dbname=sample';
        $username = 'root';
        $password = '123456';
        $connsample = new \PDO($dsn, $username, $password);
        //                echo "select * from dbstr where xmlfile like '%laravel/".$Database."/%'";
        $rows = $connsample->query("select * from dbstr where dbstr like '%mysql_" . $Database . "%'");
        $dbstr_id = "";
        $rs = $rows->fetch();

        if (null != $rs) {
            $dbstr_id = $rs['id'];
            echo 'dbstr_id:' . $dbstr_id . PHP_EOL;
        }

        $x = new synchronizemodelCommand();
        //取得全部資料表欄位
        $tableColumns = [];
        $readme_new_bodys = [];
        $tables = \DB::getSchemaBuilder()->getAllTables();
        // if ($table_name != "") {
        //     $tables = collect($tables)->where('Tables_in_sign', $table_name)->all();
        // }
        foreach ($tables as $table) {
            $tableName = head($table);
            $columns = \Schema::getColumnListing($tableName); // 用你自己的数据库名来替換 "your_database"
            foreach ($columns as $key => $name) {
                $item = new \stdClass();
                $item->tableName = $tableName;
                $item->columnName = $name;
                $tableColumns[] = $item;
            }
        }

        foreach ($tables as $k => $v1) {

            $DbTable = head($v1);
            echo $DbTable;
            $comments = DB::select("SHOW TABLE STATUS LIKE '" . $DbTable . "'");
            $table_comment = "";
            if (isset($comments[0]->Comment)) {
                $table_comment = $comments[0]->Comment;
            }

            echo " " . $table_comment;
            $readme_new_bodys[] = PHP_EOL . "## " . $DbTable . " (" . $table_comment . ")";

            $fields = DB::select('SHOW FULL COLUMNS FROM ' . $DbTable);

            $rows = \DB::table($DbTable)->selectRaw('*');
            $rows = $rows->limit(5);
            $rows = $rows->get();
            $swaggerexamples = [];
            foreach ($rows  as $key => $rs) {
                foreach ($rs as $key1 => $value) {
                    $swaggerexamples[$key1] = "";
                    if (in_array($key1, ['password', 'remember_token', 'api_token', 'jsonbody', 'vuefor']) == false) {
                        $fn = collect($fields)->where('Field', $key1)->first(); //->value();for json
                        if ($fn->Type == 'text' || $fn->Type == 'mediumtext') {
                            continue;
                        }

                        if ($swaggerexamples[$key1] == "") {
                            $swaggerexamples[$key1] = $value;
                        }
                    }
                }
            }
            //\PF::printr(["swaggerexamples", $swaggerexamples]);
            $vscode_fields = "";
            $fieldInfoStr = "";
            $swaggerNewStr = "";
            $dates = "";
            $c1 = "";
            $c2 = "";
            $fillable = "";

            if ($dbstr_id != "") {
                $json = [];
                $jsonfile = "C:\\AppServ\\laravel\\sample\\storage\\app\dbstr\\" . $dbstr_id . '\\' . $DbTable . '.json';
                if (\File::exists($jsonfile)) {
                    $jsonbody = \File::get($jsonfile);
                    $json = PF::json_decode($jsonbody, true); //加ture可以用json['yyy']

                }
            }
            $fieldFiles = "";
            //$relatedNewStr = "";
            //\PF::printr(["fields", $fields]);
            foreach ($fields as $k2 => $v2) {
                if ('PRI' == $v2->Key) {
                    $primaryKey = $v2->Field;
                }


                if ('datetime' == $v2->Type || 'date' == $v2->Type || 'timestamp' == $v2->Type) {
                    $dates .= $c1 . "'" . $v2->Field . "'";
                    $c1 = ',';
                }
                if ($primaryKey != $v2->Field) {
                    $fillable .= $c2 . "'" . $v2->Field . "'";
                    $c2 = ',';
                }

                $memo = '';
                $title = $v2->Comment;

                $vscode_fields .= $v2->Field . "//" . $title . PHP_EOL;

                $swaggerexample = '';

                $memos = $x->xmlmemo($title);
                if (null != $json) {
                    $jsonf = collect($json['datas'])->where('name', $v2->Field)->first();

                    if (in_array($jsonf['select'], ['uploadimg', 'uploaddoc', 'uiuploadmutlifile'])) {
                        $fieldFiles .= "'" . $v2->Field . "' => 'public/images/" . $DbTable . "'," . PHP_EOL;
                    }

                    //$memo = $jsonf['xmlmemo'];
                    $swaggerexample = $jsonf['swaggerexample'];
                    if ($swaggerexample == "") {
                        $swaggerexample = $swaggerexamples[$v2->Field];
                    }
                }
                $swaggertype = $this->getSwaggerType($v2->Type);


                // }
                if ('' == $title) {
                    switch ($v2->Field) {
                        case 'id':
                            $title = '自動編號';
                            break;
                        case 'created_at':
                            $title = '建立時間';
                            break;
                        case 'updated_at':
                            $title = '編輯時間';
                            break;
                    }
                }
                if ('' == $title && 'PRI' == $v2->Key) {
                    $title = '編號';
                }
                $type = "";
                if (\Str::contains($v2->Type, ['enum'])) {
                    $type = "varchar(190)";
                } else {
                    $type = $v2->Type;
                }
                //$fieldInfoStr .= "'".$v2->Field."'=>['title'=>'".$fieldtitles[$v2->Field]."', 'type' => '".$v2->Type."'],//".$memo.PHP_EOL;
                $fieldInfoStr .= "'" . $v2->Field . "'=>['title'=>'" . $title . "','type'=>'" . $type . "'],//" . $memos[0] . PHP_EOL;

                $swaggerNewStr .= '*         @OA\Schema( @OA\Property(property="' . $v2->Field . '", type="' . $swaggertype . '",description="' . $title . $memo . '", example="' . $swaggerexample . '"  )),' . PHP_EOL;


                //定義vscodes
                $memo = "";
                // 繳費項目 (關聯到 cash_kind 表)
                if (PF::right($v2->Field, 3) == '_id') {
                    $tablename1 = \Str::before($v2->Field, '_id');
                    $memo = "關聯 => " . $tablename1 . " 表";
                } else {
                    $memo = $memos[1];
                }
                $readme_new_bodys[] = " - `" . $v2->Field . "` : " . $title .  $memo;
            }

            // $vscodes[$DbTable . '_fields']['prefix'] = 'name ' . $DbTable . " " . $table_comment;
            // $vscodes[$DbTable . '_fields']['scope'] = 'php,html,blade';
            // $vscodes[$DbTable . '_fields']['body'] = [$vscode_fields];

            $model_file = base_path('app/models/' . $DbTable . '.php');

            if (\File::exists($model_file)) {
                //PF::printr($model_file);
                $old_body = \File::get($model_file);
                $body = $old_body;
            } else {
                $body = \File::get('C:/AppServ/laravel/1/sample/models/model.php');
            }
            //replace swagger api document start
            $swaggerstartstr = '/*swagger api document start*/';
            $swaggerendstr = '/*swagger api document end*/';

            if ('' != $swaggerNewStr && mb_substr_count($body, $swaggerstartstr) > 0 && mb_substr_count($body, $swaggerendstr) > 0) {
                $swaggerNewStr = <<<EOF
                    /**
                     * @OA\Schema(
                     *   schema="{$DbTable}",
                     *      allOf={
                     {$swaggerNewStr}
                     *      }
                     *)
                     */

                    EOF;
                $body = $this->oldNewReplace($body, $swaggerNewStr, $swaggerstartstr, $swaggerendstr);
            }
            //replace swagger api document end

            if (mb_substr_count($body, 'fieldInfo = [') > 0) {
                $oldfieldInfodata = PF::getStr($body, 'fieldInfo = [', '];', false);

                if ($oldfieldInfodata == PHP_EOL . $fieldInfoStr) {
                    //echo "資料一致 不更新";
                    //continue;
                } else {
                    $body = str_replace($oldfieldInfodata, PHP_EOL . $fieldInfoStr, $body);
                }
            } else {

                echo ' no fieldInfo'; //.PHP_EOL;
                //continue;
            }

            if (mb_substr_count($body, 'fillable = [') > 0) {
                $oldfillabledata = PF::getStr($body, 'fillable = [', '];', false);
                if ($oldfillabledata != $fillable) {
                    $body = str_replace($oldfillabledata, '', $body);
                    $body = str_replace('protected $fillable = [];', 'protected $fillable = [' . $fillable . '];', $body);
                }
            }

            if (mb_substr_count($body, 'dates = [') > 0) {
                $olddatesdata = PF::getStr($body, 'dates = [', '];', false);
                //echo $olddatesdata;
                if ($olddatesdata !=  $dates) {
                    $body = str_replace($olddatesdata, '', $body);
                    $body = str_replace('dates = [];', 'dates = [' . $dates . '];', $body);
                }
            }

            if ($old_body != $body) {

                $body = str_replace('[+fieldFiles+]', $fieldFiles, $body);
                $body = str_replace('[+tabletitle+]', $table_comment, $body);
                $body = str_replace('[+name+]', $DbTable, $body);
                $body = str_replace('[+fieldInfo+]', $fieldInfoStr, $body);
                $body = str_replace('[+primaryKey+]', $primaryKey, $body);
                $body = str_replace('[+dates+]', $dates, $body);
                \File::put($model_file, $body);
                echo ' model 更新成功';
            }




            // $seederfile = base_path('/database/seeders/'.$DbTable.'Seeder.php');
            // if (false == \File::exists($seederfile)) {
            //     $body = \File::get('C:/AppServ/laravel/1/sample/seeders/seeder.php');
            //     $body = str_replace('[+name+]', $DbTable, $body);
            //     \File::put($seederfile, $body);
            //     echo  ' seeders 建立成功'.PHP_EOL;
            // }

            //exit();

        }


        // 將 $readme_new_bodys 陣列轉成文字並用換行符號隔開
        $readme_new_bodys_str = implode("\n", $readme_new_bodys);
        $readme_new_bodys_str = "# 資料表規格" . PHP_EOL . $readme_new_bodys_str . PHP_EOL;
        //echo $body;
        \File::put(base_path('dbspec.md'), $readme_new_bodys_str);
        //vscode tasks.json start folders
        //$getAllDirs = \File::directories(base_path('app/Http/Controllers'));
        // $vscode_tasks_folders = '"/",'.PHP_EOL;
        // foreach ($getAllDirs as $dir) {
        //     $vscode_tasks_folders .= '"'.basename($dir).'/",'.PHP_EOL;
        //     // echo $dir;
        //     // echo '...'.base_path('app/Http/Controllers/').'...';
        //     $getAllDirs2 = \File::directories($dir);
        //     //print_r($getAllDirs2);
        //     foreach ($getAllDirs2 as $dir2) {
        //         $vscode_tasks_folders .= '"'.basename($dir).'/'.basename($dir2).'/",'.PHP_EOL;
        //         //echo $dir2;
        //     }
        // }

        // $tasksfolders = \Str::between($tasksbody, '//folders start', '//folders end');
        // $tasksbody = str_replace($tasksfolders, "\n\"options\": [\n".$vscode_tasks_folders."],\n", $tasksbody);
        // \File::put(base_path('.vscode/tasks.json'), $tasksbody);
        //vscode tasks.json end


        //$vscodes = json_encode($vscodes, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        // \File::put(base_path('.vscode/table.code-snippets'), $vscodes);
        print_r("<BR>");

        print_r("generateTableSnippets start");
        $this->generateTableSnippets();
        print_r("generateTableSnippets end");


        print_r("generateModelRelations start");
        $this->generateModelRelations();
        print_r("generateModelRelations end");


        if (is_dir(base_path('resources/js/types'))) {
            print_r("generateTypescriptTypes start");
            $this->generateTypescriptTypes();
            print_r("generateTypescriptTypes end");
        }

        $this->call('view:clear');


        // echo $this->argument('name');
    }
    public function getSwaggerType($str) {
        $type = 'string';
        if (\Str::contains($str, ['int'])) {
            $type = 'integer';
        }

        return $type;
    }
    public function vscodes($vscodes, $prefix, $body, $scope = 'php') {
        $vscodes[$prefix]['prefix'] = $prefix;
        $vscodes[$prefix]['scope'] = $scope;
        $vscodes[$prefix]['body'] = $body;

        return $vscodes;
    }
    public function oldNewReplace($body, $newStr, $startstr, $endstr) {
        $oldStr = \Str::between($body, $startstr, $endstr);


        if ('' != $oldStr) {
            $body = str_replace($oldStr, PHP_EOL, $body);
        }
        if ($newStr != $oldStr) {
            if (mb_substr_count($body, $startstr . PHP_EOL . $endstr) > 0) {
                $body = str_replace($startstr . PHP_EOL . $endstr, $startstr . PHP_EOL . $newStr . $endstr, $body);
            }
        }
        return $body;
    }
    public function xmlmemo($title) {
        try {


            if ('' == $title) {
                return;
            }
            $title = str_replace(' ', '', $title);
            $title = str_replace('(', '', $title);
            $title = str_replace(')', '', $title);
            $title = str_replace('_', '', $title);
            $title = str_replace('[', '', $title);
            $title = str_replace(']', '', $title);
            $title = str_replace('、', '', $title);

            $tmp1 = '';
            $tmp2 = '';

            foreach ($this->data['xmldoc']->xpath('//參數設定檔/' . $title . '/KIND') as $v) {
                $tmp1 .= $v->資料;
                $tmp2 .= "    ";
                if (null != $v->傳回值 && '' != strval($v->傳回值)) {
                    $tmp1 .= '[' . strval($v->傳回值) . ']';
                    $tmp2 .= "- " . strval($v->傳回值) . " : ";
                }
                $tmp2 .=  $v->資料;
                $tmp1 .= ',';
                $tmp2 .= PHP_EOL;
            }
            // 去掉最後一筆的換行符號
            if ('' != $tmp2) {
                $tmp2 = rtrim($tmp2, PHP_EOL);
            }

            if ('' != $tmp1) {
                $tmp1 = ' //' . $tmp1;
            }
            if ('' != $tmp2) {
                $tmp2 = PHP_EOL . $tmp2;
            }
        } catch (\Exception $e) {
            echo 'xmlmemo Error:' . $title . ' > ' . $e->getMessage();
        }

        return [$tmp1, $tmp2];
    }
    /**
     * 產生 VS Code table snippets
     */
    public function generateTableSnippets() {
        try {
            // 執行 Artisan 命令
            \Artisan::call('generate:table-snippets');

            // 取得命令輸出
            $output = \Artisan::output();
        } catch (\Exception $e) {
            throw new \CustomException($e->getMessage());
        }
    }
    /**
     * 產生 Model 關聯
     */
    public function generateModelRelations() {
        try {
            // 執行 Artisan 命令
            \Artisan::call('generate:model-relations');

            // 取得命令輸出
            $output = \Artisan::output();
        } catch (\Exception $e) {
            throw new \CustomException($e->getMessage());
        }
    }

    /**
     * 產生 TypeScript 類型定義檔案
     */
    public function generateTypescriptTypes() {
        try {
            // 執行 Artisan 命令
            \Artisan::call('generate:typescript-types');

            // 取得命令輸出
            $output = \Artisan::output();
        } catch (\Exception $e) {
            throw new \CustomException($e->getMessage());
        }
    }
}
