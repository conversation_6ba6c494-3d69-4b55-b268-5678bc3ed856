{
    "1": {
        "prefix": "pp print",
        "scope": "php",
        "body": [
            "\\PF::printr([\"${CLIPBOARD/['$']//gi}\",${1:${CLIPBOARD}}]);"
        ]
    },
    "print": {
        "prefix": "print log",
        "scope": "php",
        "body": [
            "\\PF::printr(${1:});"
        ]
    },
    "consolelog": {
        "prefix": "pc consolelog",
        "scope": "php",
        "body": [
            "\\PF::consolelog([\"${CLIPBOARD/['$']//gi}\",${1:${CLIPBOARD}}]);"
        ]
    },

    "echo": {
        "prefix": "echo",
        "scope": "php",
        "body": [
            "print_r(${1:${CLIPBOARD}});"
        ]
    },
    "exit": {
        "prefix": "ex exit();",
        "scope": "php",
        "body": [
            "exit();"
        ]
    },
    "public": {
        "prefix": "pu public",
        "scope": "php",
        "body": [
            "public \\$${1:created_at};"
        ]
    },
    "print_r_html": {
        "prefix": "pp print_r",
        "scope": "html,blade",
        "body": [
            "{!!print_r([\"${CLIPBOARD/['$']//gi}\",${1:${CLIPBOARD}}])!!}"
        ]
    },
    "print_html data": {
        "prefix": "data",
        "scope": "html,blade,javascript,js",
        "body": [
            "{{\\$data[\"${1:created_at}\"]}}"
        ]
    },
    "print_html pp ": {
        "prefix": "pp",
        "scope": "html,blade",
        "body": [
            "{{\\$data[\"${1:created_at}\"]}}"
        ]
    },
    "data_php": {
        "prefix": "data",
        "scope": "php",
        "body": [
            "\\$this->data['${1:${CLIPBOARD}}']"
        ]
    },
    "pr": {
        "prefix": "pr print",
        "scope": "html,blade",
        "body": [
            "{{\\$rs->${1:title}}}"
        ]
    },
    "fd": {
        "prefix": "fd formatdate",
        "scope": "php",
        "body": [
            "PF::formatDate(\\$data['${1:created_at}']);"
        ]
    },
    "fd_rs": {
        "prefix": "fd rs formatdate",
        "scope": "php",
        "body": [
            "PF::formatDate(\\$rs->${1:created_at});"
        ]
    },
    "fd_html": {
        "prefix": "fd formatdate",
        "scope": "html,blade",
        "body": [
            "{{PF::formatDate(\\$data['${1:created_at}'])}}"
        ]
    },
    "fd_html_rs": {
        "prefix": "fd rs formatdate",
        "scope": "html,blade",
        "body": [
            "{{PF::formatDate(\\$rs->${1:created_at})}}"
        ]
    },
    "data toDateString": {
        "prefix": "date Carbon data toDateString yyyy-MM-dd",
        "scope": "html,blade",
        "body": [
            "{{Carbon::parse(\\$data['${1:created_at}'])->toDateString() }}"
        ]
    },
    "rs toDateString": {
        "prefix": "date Carbon rs toDateString yyyy-MM-dd",
        "scope": "html,blade",
        "body": [
            "{{Carbon::parse(\\$rs->${1:created_at})->toDateString() }}"
        ]
    },
    "data toDateTimeString": {
        "prefix": "date Carbon data toDateTimeString yyyy-MM-dd hh:mm:ss",
        "scope": "html,blade",
        "body": [
            "{{Carbon::parse(\\$data['${1:created_at}'])->toDateTimeString() }}"
        ]
    },
    "rs toDateTimeString": {
        "prefix": "date Carbon rs toDateTimeString yyyy-MM-dd hh:mm:ss",
        "scope": "html,blade",
        "body": [
            "{{Carbon::parse(\\$rs->${1:created_at}])->toDateTimeString() }}"
        ]
    },
    "fn": {
        "prefix": "fn formatnumber data",
        "scope": "php",
        "body": [
            "PF::formatNumber(\\$data['${1:price}'],-1);"
        ]
    },
    "fn_rs": {
        "prefix": "fn formatnumber rs",
        "scope": "php",
        "body": [
            "PF::formatNumber(\\$rs->${1:price},-1);"
        ]
    },
    "fn_html": {
        "prefix": "fn formatnumber data",
        "scope": "html,blade",
        "body": [
            "{{PF::formatNumber(\\$data['${1:price}'],-1)}}"
        ]
    },
    "fn_rs_html": {
        "prefix": "fn formatnumber rs",
        "scope": "html,blade",
        "body": [
            "{{PF::formatNumber(\\$rs->${1:price},-1)}}"
        ]
    },
    "rs_php": {
        "prefix": "rs",
        "scope": "php",
        "body": [
            "\\$rs->${1:title}"
        ]
    },
    "rs_php_html": {
        "prefix": "rs",
        "scope": "html,blade",
        "body": [
            "{{\\$rs->${1:title}}}"
        ]
    },
    "環境_多語系_html": {
        "prefix": "lg 環境_多語系__('')",
        "scope": "html,blade",
        "body": [
            "{{__('${1:${CLIPBOARD}}')}}"
        ],
        "description": "@lang"
    },
    "環境_多語系_php": {
        "prefix": "lg 環境_多語系__('')",
        "scope": "php",
        "body": [
            "__('${1:${CLIPBOARD}}')"
        ],
        "description": "@lang"
    },
    "folder": {
        "prefix": "folder",
        "scope": "html,blade",
        "body": [
            "${1:${TM_FILENAME_BASE}}"
        ],
        "description": "@lang"
    },
    "use": {
        "prefix": "use",
        "scope": "php",
        "body": [
            "use App\\Libraries\\\\${1:PF};",
            "use DB;",
            "use Session;"
        ]
    },
    "date_1_html": {
        "prefix": "date 超過1天 rs",
        "scope": "html,blade",
        "body": [
            "@if (date('Y-m-d')>=date(\"Y-m-d\",strtotime(\"1 day\",strtotime(\\$rs->${1:created_at}))))",
            "",
            "@endif"
        ]
    },
    "date_1_php": {
        "prefix": "date 超過於1天 rs",
        "scope": "php",
        "body": [
            "if (date('Y-m-d')>=date(\"Y-m-d\",strtotime(\"1 day\",strtotime(\\$rs->${1:created_at})))){",
            "",
            "}"
        ]
    },
    "date_1_rs_html": {
        "prefix": "date 超過1天 data",
        "scope": "html,blade",
        "body": [
            "@if (date('Y-m-d')>=date(\"Y-m-d\",strtotime(\"1 day\",strtotime(\\$data['${1:created_at}']))))",
            "",
            "@endif"
        ]
    },
    "date_1_rs_php": {
        "prefix": "date 超過於1天 data",
        "scope": "php",
        "body": [
            "if (date('Y-m-d')>=date(\"Y-m-d\",strtotime(\"1 day\",strtotime(\\$data['${1:created_at}'])))){",
            "",
            "}"
        ]
    },
    "date_now_rs_php": {
        "prefix": "date now 現在時間 內",
        "scope": "php",
        "body": [
            "if (strtotime(\\Carbon::now())>=strtotime(\\$rs->${1:created_at})){",
            "",
            "}"
        ]
    },
    "date_now": {
        "prefix": "date 大於現在時間",
        "scope": "html,blade",
        "body": [
            "@if(strtotime(\\$data['sale_datetime']) >= strtotime(\\Carbon::now()))",
            "",
            "@endif"
        ]
    },
    "date_0": {
        "prefix": "date 1天內 data",
        "scope": "html,blade",
        "body": [
            "@if (date('Y-m-d')<=date(\"Y-m-d\",strtotime(\"1 day\",strtotime(\\$date['${1:created_at}']))))",
            "",
            "@endif"
        ]
    },
    "date_data_0": {
        "prefix": "date 1天內 data",
        "scope": "php",
        "body": [
            "if (date('Y-m-d')<=date(\"Y-m-d\",strtotime(\"1 day\",strtotime(\\$date['created_at'])))){",
            "",
            "}"
        ]
    },
    "date_minutes_0": {
        "prefix": "date 1分內 data",
        "scope": "php",
        "body": [
            "if (date('Y-m-d H:i:s') <= date(\"Y-m-d H:i:s\", strtotime(\"1 minutes \", strtotime($member->created_at)))) {",
            "",
            "}"
        ]
    },
    "date_rs_0_html": {
        "prefix": "date 1天內 rs",
        "scope": "html,blade",
        "body": [
            "@if (date('Y-m-d')<=date(\"Y-m-d\",strtotime(\"15 day\",strtotime(\\$rs->created_at))))",
            "",
            "@endif"
        ]
    },
    "date_rs_0_php": {
        "prefix": "date ?天內 rs",
        "scope": "php",
        "body": [
            "if (date('Y-m-d')<=date(\"Y-m-d\",strtotime(\"15 day\",strtotime(\\$rs->created_at)))){",
            "",
            "}"
        ]
    },
    "date_add": {
        "prefix": "date 加減日期 data",
        "scope": "html,blade",
        "body": [
            "{{date(\"Y-m-d\",strtotime(\"-1 day\",strtotime(\\$data['${1:created_at}'])))}}"
        ]
    },
    "date_add_php": {
        "prefix": "date 加減日期 data",
        "scope": "php",
        "body": [
            "date('Y-m-d',strtotime(\"-1 day\",strtotime(\\$data['${1:created_at}'])))"
        ]
    },
    "date_add_rs_html": {
        "prefix": "date 加減日期 rs",
        "scope": "html,blade",
        "body": [
            "{{date('Y-m-d',strtotime(\"1 day\",strtotime(\\$rs->${1:created_at})))}}"
        ]
    },
    "date_add_date_html": {
        "prefix": "date 加減日期 系統時間",
        "scope": "html,blade",
        "body": [
            "{{date('Y-m-d',strtotime(\"1 day\"))}}"
        ]
    },
    "date_add_rs_php": {
        "prefix": "date 加減日期 rs",
        "scope": "php",
        "body": [
            "date('Y-m-d',strtotime(\"1 day\",strtotime(\\$rs->${1:created_at})))"
        ]
    },
    "date+系統日期加幾天": {
        "prefix": "date 加減 系統時間",
        "scope": "php",
        "body": [
            "date('Y-m-d',strtotime(\"${1:5} day\"))"
        ]
    },
    "date+顯示年月日時分秒": {
        "prefix": "date now Y-m-d H:i:s",
        "scope": "php",
        "body": [
            "date('Y-m-d H:i:s')"
        ]
    },
    "date+顯示年月日": {
        "prefix": "date now Y-m-d",
        "scope": "php",
        "body": [
            "date('Y-m-d')"
        ]
    },
    "date+YmdHis+毫秒": {
        "prefix": "date now YmdHis+毫秒",
        "scope": "php",
        "body": [
            "date('YmdHis').floor(microtime()*1000)"
        ]
    },
    "date+Y-m-d H:i:s+毫秒": {
        "prefix": "date now YmdHis+毫秒",
        "scope": "php",
        "body": [
            "date('Y-m-d H:i:s').floor(microtime()*1000)"
        ]
    },
    "date YmdHis": {
        "prefix": "date now YmdHis",
        "scope": "html,blade",
        "body": [
            "{{date('YmdHis')}}"
        ]
    },
    "date Y-m-d": {
        "prefix": "date now Y-m-d",
        "scope": "html,blade",
        "body": [
            "{{date('Y-m-d')}}"
        ]
    },
    "now": {
        "prefix": "date now",
        "scope": "php",
        "body": [
            "\\Carbon::now()"
        ]
    },
    "date_1_phpbeginend": {
        "prefix": "date 開始結束日(內) rs",
        "scope": "php",
        "body": [
            "if ((null == \\$rs->begindate || date('Y-m-d', strtotime(\\$rs->begindate)) <= date('Y-m-d')) && (null == \\$rs->enddate || date('Y-m-d') <= date('Y-m-d', strtotime(\\$rs->enddate)))) {",
            "        return true;",
            "    }"
        ]
    },
    "date_0_phpbeginend": {
        "prefix": "date 開始結束日(外) rs",
        "scope": "php",
        "body": [
            "if ((null != \\$rs->begindate && date('Y-m-d', strtotime(\\$rs->begindate)) >= date('Y-m-d')) || (null != \\$rs->enddate && date('Y-m-d') >= date('Y-m-d', strtotime(\\$rs->enddate)))) {",
            "        return true;",
            "    }"
        ]
    },
    "session set": {
        "prefix": "session set",
        "scope": "php",
        "body": [
            "\\Session::put('${1:member_id}', \\$this->data['${1:member_id}']);",
            "\\Session::save();"
        ]
    },
    "session get": {
        "prefix": "session get",
        "scope": "php",
        "body": [
            "\\Session::get('${1:member_id}')"
        ]
    },
    "session_get_flash": {
        "prefix": "session get flash 一次性",
        "scope": "php",
        "body": [
            "\\Session::flash('msg', '建議請使用chrome瀏覽器');"
        ]
    },
    "cookies get": {
        "prefix": "cookies get ",
        "scope": "php",
        "body": [
            "\\Cookie::get('${1:member_id}');"
        ]
    },
    "cookies set": {
        "prefix": "cookies set ",
        "scope": "php",
        "body": [
            "retrun \\Cookie::queue(\\Cookie::make('${1:member_id}', $rs->id, 60 * 24 * 10));"
        ]
    },
    "throw": {
        "prefix": "throw",
        "scope": "php",
        "body": [
            " //throw \\$e;"
        ]
    },
    "try": {
        "prefix": "try",
        "scope": "php",
        "body": [
            "try {",
            "//throw new \\Exception('${1:no data}');",
            "} catch (\\CustomException \\$e) {",
            " echo \\$e->getMessage();",
            "} catch (\\Exception \\$e) {",
            " echo \\$e->getMessage();",
            " //throw \\$e;",
            "} finally {",
            "",
            "}"
        ]
    },
    "Request::is": {
        "prefix": "request is url 判斷指定目錄 ",
        "scope": "html,blade",
        "body": [
            "@if (Request::is('${1:store}/*') ) ",
            "",
            "@endif"
        ]
    },
    "Request::is php": {
        "prefix": "request is url 判斷指定目錄 ",
        "scope": "php",
        "body": [
            "if (\\Request::is('${1:store}/*') ){",
            "",
            "}"
        ]
    },
    "Request::raw": {
        "prefix": "request get json",
        "scope": "html,blade",
        "body": [
            "\\$request->getContent()"
        ]
    },
    "透過request取得Middleware": {
        "prefix": "get request input Middleware ",
        "scope": "php",
        "body": [
            "\\$request->route('${1:kind}')"
        ]
    },
    "request input get1": {
        "prefix": "get request input",
        "scope": "php",
        "body": [
            "\\$request->input('${1:id}')"
        ]
    },
    "request input get2": {
        "prefix": "get request input (不用加$)",
        "scope": "php",
        "body": [
            "request()->get('${1:id}')"
        ]
    },

    "request set ": {
        "prefix": "get request set input value",
        "scope": "php",
        "body": [
            "\\$request->merge(['${1:kind}' => ${2:kind}]);"
        ]
    },
    "request->all()": {
        "prefix": "get request input all",
        "scope": "php",
        "body": [
            "\\$request->all()"
        ]
    },
    "input_html": {
        "prefix": "get request input get",
        "scope": "html,blade",
        "description": "Input",
        "body": [
            "{{\\$request->input('${1:kind}')}}"
        ]
    },
    "Validator回傳錯誤內容": {
        "prefix": "validators api 傳用 回傳錯誤訊息純文字",
        "scope": "php",
        "body": [
            "throw new \\CustomException(implode(',', \\$validator->messages()->all()));"
        ]
    },
    "Validator comfirmed1": {
        "prefix": "validators password comfirmed",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}'] = ['required', 'confirmed'];//input name:password_confirmation "
        ]
    },
    "Validator comfirmed2": {
        "prefix": "validators password 密碼強度(規定必須包含至少3個字母、至少1個數字和至少一個特殊字符)",
        "scope": "php",
        "body": [
            "\\$validators['password'] = ['required', 'min:8', 'regex:/^(?=.*[A-Z])(?=.*[a-z])(?=.*[\\d!@#$%^&*])[A-Za-z\\d!@#$%^&*]{8,}$/']; //密碼 ",
            "\\$messages = [",
            "            'password.regex' => '密碼應包含英文大寫、小寫、特殊符號或數字三種',",
            "        ];",
            "\\$validator = \\Validator::make(\\$request->all(), \\$validators, \\$messages);"
        ]
    },
    "Validator min": {
        "prefix": "validators min ",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}'] = ['required', 'min:5'];//長度最小"
        ]
    },
    "Validator max": {
        "prefix": "validators max ",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}'] = ['required', 'max:20'];//長度最大"
        ]
    },
    "Validator image": {
        "prefix": "validators image",
        "scope": "php",
        "body": [
            "\\$validators['${1:img}'] = ['required', 'image']; "
        ]
    },
    "Validator requiredIf": {
        "prefix": "validators 客製requiredIf",
        "scope": "php",
        "body": [
            "\\$validators['${1:kind}'] = [",
            "          \\Rule::requiredIf(function () use (\\$request) {",
            "              return '' != \\$request->get('${2:kind}');",
            "          }),",
            "];"
        ]
    },
    "Validator mobile": {
        "prefix": "validators mobile",
        "scope": "php",
        "body": [
            "\\$validators['mobile'] = 'required|regex:/^09\\d{8}\\$/';"
        ]
    },
    "Validator email": {
        "prefix": "validators email",
        "scope": "php",
        "body": [
            "\\$validators['email'] = ['email', 'required'];"
        ]
    },
    "Validator email": {
        "prefix": "validators unique",
        "scope": "php",
        "body": [
            "\\$validators['${1:email}'] = [",
            "            'required',",
            "            'email'",
            "            \\Illuminate\\Validation\\Rule::unique('${2:member}')->ignore(\\Auth::guard('${2:member}')->id()),",
            "        ]; //電子信箱"
        ]
    },
    "Validator required_with": {
        "prefix": "validators required_with 某欄位不為空,才要檢查",
        "scope": "php",
        "body": [
            "\\$validators['begindate'] = ['required_with:isauto']; //編輯人員"
        ]
    },
    "Validator required_without1": {
        "prefix": "validators required_without 某欄位為空,才要檢查",
        "scope": "php",
        "body": [
            "\\$validators['begindate'] = ['required_without:isauto']; //編輯人員"
        ]
    },
    "Validator required_without2": {
        "prefix": "validators required_if_accepted 某欄位為true,才要檢查",
        "scope": "php",
        "body": [
            "\\$validators['companyname'] = ['required_if_accepted:iscompany']; //編輯人員; //編輯人員"
        ]
    },
    "Validator min1": {
        "prefix": "validators min",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}'] = ['required', 'min:8']; "
        ]
    },
    "Validator after": {
        "prefix": "validators after:YYYY-MM-DD",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}']=['after:${2:begin_date}']; "
        ]
    },
    "Validator before2": {
        "prefix": "validators before:YYYY-MM-DD",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}']=['before:${2:end_date}']; "
        ]
    },
    "Validator after:today": {
        "prefix": "validators after:today必須要今天之後(不含今天)",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}'] = ['after:today']; "
        ]
    },
    "Validator after:yesterday": {
        "prefix": "validators after:yesterday必須要昨天之後(含今天)",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}'] = ['after:yesterday']; "
        ]
    },
    "Validator date": {
        "prefix": "validators date",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}']=['date']; "
        ]
    },
    "Validator image1": {
        "prefix": "validators image",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}']=['image']; "
        ]
    },
    "Validator exists": {
        "prefix": "validators exists",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}']=['exists:${2:member},${1:name}']; "
        ]
    },
    "Validator url": {
        "prefix": "validators url",
        "scope": "php",
        "body": [
            "\\$validators['${1:name}']=['url']; "
        ]
    },
    "Validator": {
        "prefix": "validators check $request->all()",
        "scope": "php",
        "description": "Validator::make+after",
        "body": [
            "\\$validators = [];",
            "        \\$validators['${1:id}'] = 'required';        ",
            "         //\\$this->data['displaynames']['${1:id}'] = '請輸入資料';",
            "         \\$validator = \\Validator::make(\\$request->all(), \\$validators);",
            "         \\$validator->setAttributeNames(\\$this->data['displaynames'] == null ? [] : \\$this->data['displaynames']);",
            "         if (\\$validator->fails()) {",
            "         //return response()->view('errors.validatorback',['errors' => \\$validator->errors()],512);",
            "         throw new \\CustomException(implode(',', \\$validator->messages()->all()));",
            "         }"
        ]
    },
    "ip": {
        "prefix": "ip",
        "scope": "php",
        "body": [
            "\\Request::ip();"
        ]
    },
    "Validator_back": {
        "prefix": "validators back ",
        "scope": "php",
        "body": [
            "\\$validator = \\Validator::make([], []);",
            "\\$validator->errors()->add('field', '${1:此時段已被人預約}');",
            " return response()->view('errors.validatorback',['errors' => \\$validator->errors()],512);"
        ]
    },
    "is empty": {
        "prefix": "is empty",
        "scope": "html,blade",
        "body": [
            "{{ isset(\\${1:field}) ? \"N\" : \"Y\" }}"
        ]
    },
    "is_date": {
        "prefix": "is date",
        "scope": "html,blade",
        "body": [
            "{{ PF::isDate(\\${1:field}) ? \"N\" : \"Y\" }}"
        ]
    },
    "href": {
        "prefix": "href",
        "scope": "html,blade",
        "body": [
            "<a href=\"{{ url('/')}}/${1:key}\" target=\"${2:self}\" title=\"{{\\$rs->title}}\">${3:key}</a>"
        ]
    },
    "numcdoe": {
        "prefix": "numcdoe",
        "scope": "html,blade",
        "description": "captcha 認證碼",
        "body": [
            "<div class=\"form-group\">",
            "                <label>認證碼</label>",
            "                <input type=\"text\" name=\"captcha\" class=\"form-control\" size=\"12\" title=\"認證碼\"",
            "                class=\"required[1,TEXT]\" required autocomplete=\"off\">",
            "                <img src=\"{{captcha_src('flat')}}\" align=\"absmiddle\" style=\"cursor: pointer\"",
            "  onclick=\"this.src='{{captcha_src()}}'+Math.random()\">",
            "            </div>"
        ]
    },
    "img_src": {
        "prefix": "img src",
        "scope": "html,blade",
        "body": [
            " <img src=\"{{ url('/') }}/images/${1:body}.png\" border=\"0\" align=\"absmiddle\" class=\"img-fluid\">"
        ]
    },
    "字串隱碼": {
        "prefix": "string 字串隱碼",
        "scope": "php",
        "body": [
            "PF::getStrX(\\$data['${1:txt}'],[1,3,5],\"*\")"
        ]
    },
    "getStrX_html": {
        "prefix": "string 字串隱碼",
        "scope": "html,blade",
        "body": [
            "{{PF::getStrX(\\$data['${1:txt}'],[1,3,5],\"*\")}}"
        ]
    },
    "字串隨機": {
        "prefix": "string random 字串隨機",
        "scope": "php",
        "body": [
            "\\Str::random(32);"
        ]
    },
    "刪除字串中的最後一個字元": {
        "prefix": "string rtrim 刪除字串中的最後一個字元",
        "scope": "php",
        "body": [
            "rtrim(\\$${1:txt},\".\");",
        ]
    },
    "字串長度": {
        "prefix": "string length  字串長度",
        "scope": "php",
        "body": [
            "\\Str::length(${1:txt});"
        ]
    },
    "lower": {
        "prefix": "string lower 小寫",
        "scope": "php",
        "body": [
            "\\Str::lower(${1:txt});"
        ]
    },
    "upper": {
        "prefix": "string upper 大寫",
        "scope": "php",
        "body": [
            "\\Str::upper(${1:txt});"
        ]
    },
    "startsWith": {
        "prefix": "string startsWith 開頭是否符合",
        "scope": "php",
        "body": [
            "\\Str::startsWith('${1:txt}', '${2:txt}');"
        ]
    },
    "swith": {
        "prefix": "switch",
        "scope": "html,blade",
        "description": "herf search name",
        "body": [
            "@switch(\\$${1:txt})",
            "    @case(1)",
            "        @break",
            "    @default",
            "        // 默认",
            "@endswitch"
        ]
    },
    "contains": {
        "prefix": "string contains 有部份符合",
        "scope": "php",
        "body": [
            "if (\\Str::contains(${1:txt}, ['${2:txt}'])){",
            "",
            "}"
        ]
    },
    "containsAll": {
        "prefix": "string containsAll 多筆都要符合",
        "scope": "php",
        "body": [
            "if (\\Str::containsAll(${1:txt}, ['${2:a}','${3:b}'])){",
            "",
            "}"
        ]
    },
    "contains html": {
        "prefix": "string contains 有部份符合",
        "scope": "html,blade",
        "body": [
            "@if (Str::contains(${1:txt}, ['${2:txt}']))",
            "",
            "@endif"
        ]
    },
    "padRight": {
        "prefix": "string padRight 後補0",
        "scope": "php",
        "body": [
            "\\Str::padRight('James', 10, '0');"
        ]
    },
    "padLeft": {
        "prefix": "string padLeft 前補0",
        "scope": "php",
        "body": [
            "\\Str::padRight('James', 10, '0');"
        ]
    },
    "string remove": {
        "prefix": "string remove 刪除文字",
        "scope": "php",
        "body": [
            "\\Str::remove(\\$this->data['${1:txt}'], '${2:txt}');"
        ]
    },
    "string replace": {
        "prefix": "string replace 替代文字",
        "scope": "php",
        "body": [
            "\\$this->data['${1:name}']=\\Str::replace('${2:txt}', '', \\$this->data['${1:name}']);"
        ]
    },
    "string substr": {
        "prefix": "string substr 左取",
        "scope": "php",
        "body": [
            "\\$this->data['${1:name}']=\\Str::substr(\\$this->data['${1:name}'], 0, ${2:10});"
        ]
    },
    "between": {
        "prefix": "string between 取二個文字中間的文字",
        "scope": "php",
        "body": [
            "\\$msg=\\Str::between(\\$body, '${1:start}', '${2:end}')"
        ]
    },
    "between html": {
        "prefix": "string between 取二個文字中間的文字",
        "scope": "html,blade",
        "body": [
            "Str::between(\\$body, '${1:start}', '${2:end}')"
        ]
    },
    "after": {
        "prefix": "string after 取某文字之後的文字",
        "scope": "php",
        "body": [
            "\\$msg=\\Str::after(\\$body, '${1:start}')"
        ]
    },
    "afterLast": {
        "prefix": "string afterLast 往後取某文字之後的文字",
        "scope": "php",
        "body": [
            "\\$msg=$slice = \\Str::afterLast('App\\Http\\Controllers\\Controller', '\\');"
        ]
    },
    "after htrml": {
        "prefix": "string after 取某文字之後的文字",
        "scope": "html,blade",
        "body": [
            "Str::after(\\$body, '${1:start}')"
        ]
    },
    "before": {
        "prefix": "string before 取某文字之前的文字",
        "scope": "php",
        "body": [
            "\\$msg=\\Str::before(\\$body, '${1:start}')"
        ]
    },
    "beforeLast": {
        "prefix": "string beforeLast 往前取某文字之前的文字",
        "scope": "php",
        "body": [
            "\\$msg=$slice = \\Str::beforeLast('App\\Http\\Controllers\\Controller', '\\');"
        ]
    },
    "before html": {
        "prefix": "string before 取某文字之前的文字",
        "scope": "html,blade",
        "body": [
            "Str::before(\\$body, '${1:start}')"
        ]
    },
    "left": {
        "prefix": "string left",
        "scope": "php",
        "body": [
            "\\Str::limit('${1:txt}',20);"
        ]
    },
    "right": {
        "prefix": "string right",
        "scope": "php",
        "body": [
            "substr($this->data['${1:txt}'], -${2:4})"
        ]
    },
    "hidden_Form::hidden": {
        "prefix": "hidden Form::hidden",
        "scope": "html,blade",
        "body": [
            "{{ Form::hidden(\"${1:id}\", \\$data[\"${1:id}\"] ) }}"
        ]
    },
    "hidden_include": {
        "prefix": "include hidden",
        "scope": "html,blade",
        "body": [
            "@include('admin.layouts.hiddenall')"
        ]
    },
    "hidden_include_add": {
        "prefix": "include add",
        "scope": "html,blade",
        "body": [
            "<div class=\"float-right noprint\">",
            "        @include('admin.layouts.add')",
            "</div>"
        ]
    },
    "hidden_include_delall": {
        "prefix": "include delall",
        "scope": "html,blade",
        "body": [
            "@if (Gate::check('isAdminRole', ['999']))",
            "        <div class=\"float-left noprint\">",
            "            @include('admin.layouts.delall')",
            "        </div>",
            "@endif"
        ]
    },
    "hidden_includewhen": {
        "prefix": "include when",
        "scope": "html,blade",
        "body": [
            "@includeWhen(\\$boolean,'layouts.hiddenall'.['data'=>\\$data])"
        ]
    },
    "checkbox_html_Form": {
        "prefix": "checkbox html onclick in_array",
        "scope": "html,blade",
        "description": "Form::checkbox",
        "body": [
            "{{Form::checkbox('${1:id}',1,in_array(\\$rs->${1:id},\\$data['${1:id}id']),",
            "    ['onClick' => 'set${1:id}('.\\$rs->${1:id}id.',this.checked)'])",
            "}}"
        ]
    },
    "jsondata": {
        "prefix": "json data",
        "scope": "php",
        "body": [
            " \\$this->jsondata['data']['${1:online}']=\\${1:online};"
        ]
    },
    "if": {
        "prefix": "if data",
        "scope": "html,blade",
        "body": [
            "@if (\\$data['${1:index}']!=\"\")",
            " ",
            "@elseif (\\$data['${1:index}']==\"2\")",
            " ",
            "@else",
            " ",
            "@endif"
        ]
    },
    "if one line": {
        "prefix": "if data one line",
        "scope": "html,blade",
        "body": [
            "{{\\$data['${1:index}']!='' ? '-' : ''}}"
        ]
    },
    "if one rs line": {
        "prefix": "if rs one line",
        "scope": "html,blade",
        "body": [
            "{{\\$rs->${1:index}!='' ? '-'.\\$rs->${1:index} : ''}}"
        ]
    },
    "ifrs": {
        "prefix": "if rs",
        "scope": "html,blade",
        "body": [
            "@if (\\$rs->${1:index}==\"\")",
            " ",
            "@elseif (\\$rs->${2:index}==\"\")",
            " ",
            "@else",
            " ",
            "@endif"
        ]
    },
    "if Request::getUri()": {
        "prefix": "if url Request::getUri()",
        "scope": "html,blade",
        "body": [
            "@if (Str::contains(Request::getUri(),\"${1:product}\"))",
            " ",
            "@else",
            " ",
            "@endif"
        ]
    },
    "if_line": {
        "prefix": "if line",
        "scope": "html,blade",
        "body": [
            "@{{  \\$i == '1' ? 'true' : 'false' }}"
        ]
    },
    "select form number": {
        "prefix": "select form number",
        "scope": "html,blade",
        "body": [
            "{{ Form::selectRange('qty', 01, 100, \\$rs->qty, array('class' => 'form-control')) }}"
        ]
    },
    "select": {
        "prefix": "select for number",
        "scope": "html,blade",
        "body": [
            "<select class='form-control' v-model=\"inputs.expiration_date1\" required>",
            "                            <option value=\"\">請選擇月份</option>",
            "                            @for (\\$i = 1; \\$i < 12; \\$i++) <option value=\"{{ \\$i }}\">{{ \\$i }}</option>",
            "                                @endfor",
            "</select>"
        ]
    },
    "select自刻": {
        "prefix": "select foreach arrays",
        "scope": "html,blade",
        "body": [
            "<select name=\"${1:item}s\" class=\"form-control\">",
            "     @foreach (\\$data['${1:item}s'] as \\$key => \\$rs )",
            "       <option value=\"{{\\$rs['value']}}\" {{\\$data['${1:item}']==\\$rs['value'] ? \"selected\" : ''}}>{{\\$rs['title']}}</option>",
            "     @endforeach",
            "</select>"
        ]
    },
    "xml foreach": {
        "prefix": "xml foreach",
        "scope": "php",
        "body": [
            "foreach (\\$this->data['xmldoc']->xpath(\"//參數設定檔/${1:index}/KIND\") as \\$v){",
            "  strval(\\$v->傳回值)",
            "  \\$v->資料",
            "}"
        ]
    },
    "xml select": {
        "prefix": "xml select",
        "scope": "html,blade",
        "body": [
            "{{Form::myUIXml([",
            "  'xmldoc' => \\$data['xmldoc'],",
            "  'type' =>'select',",
            "  'title' =>'${1:index}',",
            "  'node' => '//參數設定檔/${1:index}/KIND',",
            "  'name' => '${2:index}',",
            "  //'v-model' => 'inputs.${2:index}',",
            "  'value' => \\$data['${2:index}'],",
            "  'linecount' => 4,",
            "  'required'=>true,",
            "  //'onChange'=>\"PF_AjaxblockUI('\".url('/').\"/admin/misson/setonline','id=\".\\$rs->id.\"&value=' +this.options[this.selectedIndex].value,null);\"",
            "])",
            "}}"
        ]
    },
    "xml radio": {
        "prefix": "xml radio",
        "scope": "html,blade",
        "body": [
            "{{Form::myUIXml([",
            "  'xmldoc' => \\$data['xmldoc'],",
            "  'type' =>'radio',",
            "  'title' =>'${1:index}',",
            "  'node' => '//參數設定檔/${1:index}/KIND',",
            "  'name' => '${2:index}',",
            "  //'v-model' => 'inputs.${2:index}',",
            "  'value' => \\$data['${2:index}'],",
            "  //'@change'=>\"onCart($event)\"",
            "  'linecount' => 4,",
            "  'required'=>true,",
            "                ])",
            "}}"
        ]
    },
    "xml checkbox": {
        "prefix": "xml checkbox",
        "scope": "html,blade",
        "body": [
            "{{Form::myUIXml([",
            "  'xmldoc' => \\$data['xmldoc'],",
            "  'type' =>'checkbox',",
            "  'title' =>'${1:index}',",
            "  'node' => '//參數設定檔/${1:index}/KIND',",
            "  'name' => '${2:index}',",
            "  'value' => \\$data['${2:index}'],",
            "  'linecount' => 4,",
            "  'required'=>true,",
            "  //'v-model' => 'inputs.${2:index}',",
            "  //'@change'=>\"onCart($event)\"",
            "  ])",
            "}}"
        ]
    },
    "db_checkbox": {
        "prefix": "checkbox db",
        "scope": "html,blade",
        "body": [
            "{{ ",
            "Form::myUIDb([",
            "   'type' => 'checkbox',",
            "   'title' =>'${1:index}',",
            "   'sql' => \"select id,kindtitle from kind where kind='${1:index}' order by kindsortnum\",",
            "   'name' => '${2:index}',",
            "   'value' => \\$data['${2:index}'],",
            "   'linecount' => 4,",
            "   'requiredclass' => '[1,TEXT]',",
            "  //'v-model' => 'inputs.${2:index}',",
            "  //'@change'=>\"onCart($event)\"",
            "])",
            "}}"
        ]
    },
    "db_radio": {
        "prefix": "radio db",
        "scope": "html,blade",
        "body": [
            "{{ ",
            "Form::myUIDb([",
            "  'type' => 'radio',",
            "  'title' =>'${1:index}',",
            "  'sql' => \"select id,kindtitle from kind where kind='${1:index}' order by kindsortnum\",",
            "  'name' => '${2:index}',",
            "  'value' => \\$data['${2:index}'],",
            "  'linecount' => 4,",
            "   'required'=>true,",
            "  //'v-model' => 'inputs.${2:index}',",
            "  //'@change'=>\"onCart($event)\"",
            "]);"
        ]
    },
    "db_select": {
        "prefix": "select db",
        "scope": "html,blade",
        "description": "Form::myUIDb",
        "body": [
            "",
            "{{",
            "Form::myUIDb([",
            "    'type' => 'select',",
            "    'title' =>'${1:index}',",
            "    'sql' => \"select id,kindtitle from kind where kind='${1:index}' order by kindsortnum\",",
            "    'name' => '${2:index}',",
            "    'value' => \\$data['${2:index}'],",
            "    'multipleWidth'=>200,",
            "    'required'=>true,",
            "  //'v-model' => 'inputs.${2:index}',",
            "  //'@change'=>\"onCart($event)\"",
            " ])",
            "}}"
        ]
    },
    "db_selectautocomplete": {
        "prefix": "select autocomplete db",
        "scope": "html,blade",
        "description": "Form::myUIDb",
        "body": [
            "",
            "{{",
            "Form::myUIDb([",
            "    'type' => 'select2',",
            "    'title' =>'${1:index}',",
            "    'sql' => \"select kindmain.id,concat( case alg when 'zh' then '繁中' when 'en' then '英文' end ,'>',title,'>',title) from kindmain inner join kindhead on (kindmain.kindhead_id=kindhead.id)  order by alg,sortnum,sortnum\"",
            "    'name' => '${2:index}',",
            "    'value' => \\$data['${2:index}'],",
            "    'required'=>true,",
            " ])",
            "}}"
        ]
    },
    "session_html": {
        "prefix": "session",
        "scope": "html,blade",
        "body": [
            "{{Session::get('${1:name}')}}"
        ]
    },
    "file_read": {
        "prefix": "file read ",
        "scope": "php",
        "body": [
            "\\$body =\\File::get(storage_path('images/'));"
        ]
    },
    "file_ext": {
        "prefix": "file get ext ",
        "scope": "php",
        "body": [
            "\\$ext = substr(\\$f, strrpos(\\$f, '.') + 1);"
        ]
    },

    "file width": {
        "prefix": "file image width height",
        "scope": "php",
        "body": [
            "\\$image_details = getimagesize(public_path(\"images/project/\" . \\$rs->${1:name}));",
            "\\$this->data['${1:name}_width'] = \\$image_details[0];",
            "\\$this->data['${1:name}_height'] = \\$image_details[1];"
        ]
    },
    "file base64_decode": {
        "prefix": "file base64_decode to file",
        "scope": "php",
        "body": [
            "if (strpos(\\$base64String, 'data:image') !== false) {",
            "   \\$base64String = explode(',', \\$base64String)[1];",
            "}",
            "\\$fileData = base64_decode(\\$base64String);",
            "\\File::put(storage_public_pathpath(\"images/member/\".\\$id.\".pdf\"), \\$fileData);"
        ]
    },
    "file_append": {
        "prefix": "file append 添加 ",
        "scope": "php",
        "body": [
            "\\File::append(storage_path(${1:name}),\\$${2:data});"
        ]
    },
    "file_foreach1": {
        "prefix": "file foreach 檔案+目錄",
        "scope": "php",
        "body": [
            "\\$directory = base_path('${1:resources}');",
            "\\$files = File::allFiles(\\$directory, \\$hidden = false);",
            "",
            "foreach (\\$files as \\$k => \\$v) {",
            "\\$v->getRealpath();",
            "//date('Y-m-d H:i:s', \\File::lastmodified($item));//時間",
            "//PF::formatNumber($v->getSize()/1024/1024,2).M;//大小",
            "}"
        ]
    },
    "file_foreach2": {
        "prefix": "file foreach 檔案",
        "scope": "php",
        "body": [
            "\\$directory = base_path('${1:resources}');",
            "\\$files = File::files(\\$directory, \\$hidden = false);",
            "",
            "foreach (\\$files as \\$k => \\$v) {",
            "\\$v->getRealpath();",
            "//date('Y-m-d H:i:s', \\File::lastmodified($item));//時間",
            "//PF::formatNumber($v->getSize()/1024/1024,2).M;//大小",
            "}"
        ]
    },
    "file_foreach3": {
        "prefix": "file foreach 目錄",
        "scope": "php",
        "body": [
            "\\$directory = base_path('${1:resources}');",
            "\\$files = File::directories(\\$directory);",
            "",
            "foreach (\\$files as \\$k => \\$v) {",
            "   \\$v->getRealpath();",
            "}"
        ]
    },
    "del folder file": {
        "prefix": "file del 刪目錄下的全部檔案",
        "scope": "php",
        "body": [
            "\\File::deleteDirectory(public_path('build/assets'));"
        ]
    },
    "file_type": {
        "prefix": "file type 類型 ",
        "scope": "php",
        "body": [
            "\\File::type(storage_path(${1:name}));"
        ]
    },
    "file_lastModified": {
        "prefix": "file lastModified 最後修改時間 ",
        "scope": "php",
        "body": [
            "\\File::lastModified(storage_path(${1:name}));"
        ]
    },
    "file_files": {
        "prefix": "file files 取得目錄下所有文件 ",
        "scope": "php",
        "body": [
            "\\$files=\\File::files(storage_path(${1:name}));"
        ]
    },
    "file_copy": {
        "prefix": "file copy 複製檔案 ",
        "scope": "php",
        "body": [
            "\\File::copy(storage_path(${1:name}),storage_path(${2:name}));"
        ]
    },
    "file_move": {
        "prefix": "file move 移動檔案 ",
        "scope": "php",
        "body": [
            "\\File::move(storage_path(${1:name}),storage_path(${2:name}));"
        ]
    },
    "檔案是否存在": {
        "prefix": "file exist 檔案是否存在",
        "scope": "php",
        "body": [
            "if (false == \\File::exists(storage_path('images/'))) {",
            "",
            "}"
        ]
    },
    "檔案刪除": {
        "prefix": "file del 檔案刪除",
        "scope": "php",
        "body": [
            "\\$path=public_path('images/${1:member}/'.\\$rs->${2:img});",
            "if (\\File::exists(\\$path)) {",
            "   \\File::delete(\\$path);",
            "}"
        ]
    },
    "檔案寫入": {
        "prefix": "file write 檔案寫入",
        "scope": "php",
        "body": [
            " \\File::put(storage_path('images/'), \\$request->input('${1:body}'));"
        ]
    },
    "env get value": {
        "prefix": "evn",
        "scope": "php",
        "body": [
            "env('${1:name}','')"
        ]
    },
    "config_PF_getConfig": {
        "prefix": "config PF::getConfig",
        "scope": "html,blade",
        "body": [
            "{{PF::getConfig('${1:name}')}}"
        ]
    },
    "config_html": {
        "prefix": "config get ",
        "scope": "html,blade",
        "body": [
            "{{config('${1:config}.${2:name}')}}"
        ]
    },
    "config_set_php": {
        "prefix": "config PF::setConfig",
        "scope": "php",
        "body": [
            "\\PF::setConfig('${1:title}', \"${1:title}\");"
        ]
    },
    "Config::set": {
        "prefix": "config Config::set",
        "scope": "php",
        "body": [
            "\\Config::set('mypay.URL',\"xx\");"
        ]
    },
    "config_get_pf": {
        "prefix": "config PF::getConfig",
        "scope": "php",
        "body": [
            "\\PF::getConfig('${1:name}')"
        ]
    },
    "config_php": {
        "prefix": "config get ",
        "scope": "php",
        "body": [
            "\\config('${1:config}.${2:name}')"
        ]
    },
    "folder_resource_php": {
        "prefix": "folder resource",
        "scope": "php",
        "body": [
            "resource_path('views/email/${1:name}.blade.php')"
        ]
    },
    "folder_public_php": {
        "prefix": "folder public",
        "scope": "php",
        "body": [
            "public_path('images/building/'.\\$rs->photomap)"
        ]
    },
    "create folder": {
        "prefix": "folder create 建立目錄",
        "scope": "php",
        "body": [
            "\\$folder = public_path('images/businesscardself/'.request()->member['id'].'/');",
            "       // PF::printr(\\$folder);",
            "        if (false == \\File::isDirectory(\\$folder)) {",
            "    \\File::makeDirectory(\\$folder);",
            "        }"
        ]
    },
    "deleteDirectory": {
        "prefix": "folder delete 刪除目錄",
        "scope": "php",
        "body": [
            "\\$folder = public_path('images/businesscardself/'.request()->member['id'].'/');",
            "       // PF::printr(\\$folder);",
            "        \\File::deleteDirectory(\\$folder));"
        ]
    },
    "cleanDirectory": {
        "prefix": "folder clean 清空目錄含檔案",
        "scope": "php",
        "body": [
            "\\$folder = public_path('images/businesscardself/'.request()->member['id'].'/');",
            "       // PF::printr(\\$folder);",
            "        \\File::cleanDirectory(\\$folder));"
        ]
    },
    "folder_storage_path_php": {
        "prefix": "folder storage",
        "scope": "php",
        "body": [
            "storage_path('images/')"
        ]
    },
    "folder_html": {
        "prefix": "folder resource",
        "scope": "html,blade",
        "body": [
            "{{resource_path('views/email/${1:name}.blade.php')}}"
        ]
    },
    "folder_base_path_php": {
        "prefix": "folder base_path",
        "scope": "php",
        "body": [
            "base_path('resource/views/email/${1:name}.blade.php')"
        ]
    },
    "folder_base_path_html": {
        "prefix": "folder base_path",
        "scope": "html,blade",
        "body": [
            "{{base_path('resource/views/email/${1:name}.blade.php')}}"
        ]
    },
    "data_php_rows": {
        "prefix": "data rows",
        "scope": "php",
        "body": [
            "\\$this->data['${1:rows}']=\\$${1:rows};"
        ]
    },
    "email_php": {
        "prefix": "send mail",
        "scope": "php",
        "body": [
            "\\$sendMail=new \\App\\Mails\\\\${1:feedback}Mail(\\$rs->id);",
            "//echo \\$sendMail->render();",
            "\\Mail::send(\\$sendMail);",
        ]
    },
    "log info": {
        "prefix": "log info",
        "scope": "php",
        "body": [
            "\\Log::info(${1:ebody});"
        ]
    },
    "log error": {
        "prefix": "log error",
        "scope": "php",
        "body": [
            "\\Log::error(${1:ebody});"
        ]
    },
    "is_empty": {
        "prefix": "is empty",
        "scope": "php",
        "body": [
            "if (PF::isEmpty(\\$${1:ebody})) {",
            "",
            "}"
        ]
    },
    "is_date_php": {
        "prefix": "is date",
        "scope": "php",
        "body": [
            "if (PF::isDate(\\$${1:ebody})) {",
            "",
            "}"
        ]
    },
    "xml_html_foreach": {
        "prefix": "xml foreach",
        "scope": "html,blade",
        "body": [
            "@foreach (\\$data['xmldoc']->xpath(\"//參數設定檔/${1:key}/KIND\")  as \\$key => \\$v) ",
            "        {{\\$v->資料}}",
            "        {{strval(\\$v->傳回值)}}",
            "@endforeach"
        ]
    },
    "xml_php_foreach": {
        "prefix": "xml foreach",
        "scope": "php",
        "body": [
            "foreach (\\$this->data['xmldoc']->xpath('//參數設定檔/${1:key}/KIND')  as \\$key => \\$v) {",
            "\\$v->資料",
            "strval(\\$v->傳回值)",
            "}"
        ]
    },
    "return_back_toast整個頁面會重新load資料會不見": {
        "prefix": "back alert _toast success",
        "scope": "php",
        "body": [
            "return back()->with('js', \"_toast('${1:更新成功}',1000,'success')\");"
        ]
    },
    "return_back_toast warning": {
        "prefix": "back alert _toast warning",
        "scope": "php",
        "body": [
            "return back()->with('js', \"_toast('${1:更新成功}',1000,'warning')\");"
        ]
    },
    "return_back_alert整個頁面會重新load資料會不見": {
        "prefix": "back alert success ",
        "scope": "php",
        "body": [
            "return back()->with('js', '_alert(\\'更新成功\\',\\'success\\')');"
        ]
    },
    "return_back_warning": {
        "prefix": "back alert warning ",
        "scope": "php",
        "body": [
            "return back()->with('js', '_alert(\\'更新成功\\',\\'warning\\')');"
        ]
    },
    "return_back_alert Swal.fire": {
        "prefix": "back alert modal close 可以自己加事件",
        "scope": "php",
        "body": [
            "return back()->with('js', \"Swal.fire({text: '更新成功',icon: \\\"success\\\",dangerMode: true,}).then((willDelete) => {\\\\$(\\\"[data-dismiss='modal']\\\",parent.document).last().trigger('click');return false;});\");"
        ]
    },
    "redirect _toast": {
        "prefix": "redirect to url js _toast",
        "scope": "php",
        "body": [
            "return redirect(\\request()->middlewareurl . \"topic/show?topic_id=\" . \\$this->data['topic_id'])->with('js', \"_toast('更新成功', 3000)\");"
        ]
    },
    "redirect session": {
        "prefix": "redirect to url session",
        "scope": "php",
        "body": [
            "return redirect()->to(\\request()->middlewareurl . \".cart/payfail')->with('message', \"fail\");"
        ]
    },
    "redirect _alert": {
        "prefix": "redirect to url js _alert",
        "scope": "php",
        "body": [
            "return redirect(\\request()->middlewareurl . \"topic/show?topic_id=\" . \\$this->data['topic_id'])->with('js', \"_alert('更新成功','success')\");"
        ]
    },
    "postsubmit": {
        "prefix": "alert redirect postsubmit",
        "scope": "php",
        "body": [
            "\\$this->data['action'] = \\$loginurl;",
            " \\$this->data['alert'] = '登入成功';",
            " //\\$this->data['icon'] = 'warning';",
            "                return view('admin.layouts.postsubmit', [",
            "                    'data' => \\$this->data,",
            "                ]);"
        ]
    },
    "url": {
        "prefix": "url",
        "scope": "php",
        "description": "url",
        "body": [
            "url('/').'/${1:admin}'"
        ]
    },
    "url_目前網址": {
        "prefix": "url request",
        "scope": "html,blade",
        "description": "url request",
        "body": [
            "{{request()->url()}}/"
        ]
    },
    "php url 目前網址": {
        "prefix": "url request ",
        "scope": "php",
        "body": [
            "request()->url()"
        ]
    },
    "php url domain": {
        "prefix": "url domain ",
        "scope": "php",
        "body": [
            "request()->getHost()"
        ]
    },
    "request()->url": {
        "prefix": "url request 抓Middleware自設的url",
        "scope": "html,blade",
        "body": [
            "{{\\request()->middlewareurl}}"
        ]
    },
    "request()->url": {
        "prefix": "url 返回當前頁面的完整路徑url: http://xx.com/aa/bb",
        "scope": "html,blade",
        "body": [
            "{{ request()->getRequestUri() }}"
        ]
    },
    "request()->url": {
        "prefix": "url request 抓Middleware自設的KEY VALUE",
        "scope": "html,blade",
        "body": [
            "{{\\request()->${1:manage}['${2:mname}']} }"
        ]
    },
    "request()->url php": {
        "prefix": "url request 抓Middleware自設的url",
        "scope": "php",
        "body": [
            "\\request()->middlewareurl"
        ]
    },
    "request()->url html": {
        "prefix": "url request 抓Middleware自設的url",
        "scope": "html,blade",
        "body": [
            "{{\\request()->middlewareurl}}"
        ]
    },
    "環境_多語系_圖": {
        "prefix": "url lg img 圖",
        "scope": "html,blade",
        "description": "img url(app()->getLocale().",
        "body": [
            "{{ url(app()->getLocale().'/assets/images/${1:board}') }}"
        ]
    },
    "環境_多語系_網址html": {
        "prefix": "url lg 網址",
        "scope": "html,blade",
        "description": "url(app()->getLocale().",
        "body": [
            "{{ url(app()->getLocale()) }}/"
        ]
    },
    "環境_多語系_網址php": {
        "prefix": "url lg",
        "scope": "php",
        "description": "url(app()->getLocale().",
        "body": [
            " url(app()->getLocale().'/')"
        ]
    },
    "return_response": {
        "prefix": "return response ok",
        "scope": "php",
        "body": [
            "return response('${1:ok}');"
        ]
    },
    "return_response noContent": {
        "prefix": "return response noContent",
        "scope": "php",
        "body": [
            "return response()->noContent()->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');"
        ]
    },
    "is debug": {
        "prefix": "is debug dev",
        "scope": "php",
        "body": [
            "if (\\config('app.env') == 'dev') {",
            "",
            "}"
        ]
    },
    "is testing": {
        "prefix": "is testing",
        "scope": "php",
        "body": [
            "if (\\config('app.env') == 'testing') {",
            "",
            "}"
        ]
    },


    "is production": {
        "prefix": "is debug production",
        "scope": "php",
        "body": [
            "if (\\config('app.env') == 'production') {",
            "",
            "}"
        ]
    },
    "debug view": {
        "prefix": "debug view",
        "scope": "html,blade",
        "body": [
            "@include('layouts.debug')"
        ]
    },
    "json response": {
        "prefix": "json response",
        "scope": "php",
        "body": [
            "return \\$this->apiResponse(\\$this->jsondata);"
        ]
    },
    "json_update": {
        "prefix": "json api update",
        "scope": "php",
        "body": [
            "public function setchecked(Request \\$request)",
            "    {",
            "        \\$this->jsondata['resultcode'] = 0;",
            "        \\$this->jsondata['resultmessage'] = '';",
            "        try {",
            "    \\$validators['id'] = 'required';",
            "    \\$validator = Validator::make(\\$request->all(), \\$validators);",
            "    \\$validator->setAttributeNames(\\$this->data['displaynames'] == null ? [] : \\$this->data['displaynames']);",
            "    if (\\$validator->fails()) {",
            "        return view('api.errors.validator')->withErrors(\\$validator);",
            "    }",
            "    \\$inputs['online'] = (\\$request->input('value')==\"true\") ? 1 : 0;",
            "    ",
            "    \\$this->diversionurlRepo->update(\\$inputs, \\$request->input('id'));",
            "        } catch (Exception \\$e) {",
            "    \\$this->jsondata['resultcode'] = 999;",
            "    \\$this->jsondata['resultmessage'] = \\$e->getMessage();",
            "        }",
            "",
            "        return response()->json(\\$this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);",
            "    }"
        ]
    },
    "資料庫永久cache": {
        "prefix": "cache 資料庫 永久 rememberForever",
        "scope": "php",
        "description": "cache rows",
        "body": [
            "\\$rows=\\Cache::rememberForever('kind',function () use (\\$kind_id)  {",
            "    \\$rows = DB::table('kind')->selectRaw('*');",
            "    //\\$rows->myWhere('kind|S', 'newskind', 'kind', 'Y');        ",
            "    \\$rows->orderByRaw('kindsortnum');",
            "    //PF::dbSqlPrint(\\$rows);",
            "    \\$rows = \\$rows->get();",
            "    //\\$echo \\$ebody;",
            "    return \\$rows;",
            "})->where('kind', 'productkind');"
        ]
    },
    "資料庫幾分cache": {
        "prefix": "cache 資料庫 幾分 remember",
        "scope": "php",
        "description": "cache rows",
        "body": [
            "\\$rows=\\Cache::remember('newskind', 5*60, function(\\$kind_id)  {",
            "    \\$rows = DB::table('kind')->selectRaw('*');",
            "    \\$rows->myWhere('kind|S', 'newskind', 'kind', 'Y');        ",
            "    \\$rows->orderByRaw('kindsortnum');",
            "    //PF::dbSqlPrint(\\$rows);",
            "    \\$rows = \\$rows->get();",
            "    //\\$echo \\$ebody;",
            "    return \\$rows;",
            "});"
        ]
    },
    "cache儲存": {
        "prefix": "cache save 儲存",
        "scope": "php",
        "body": [
            "\\\\Cache::forever('${1:table}', $body);"
        ]
    },
    "cache刪除 key": {
        "prefix": "cache 刪除 key",
        "scope": "php",
        "body": [
            "\\\\Cache::forget('${1:table}_');"
        ]
    },
    "cache刪除全部": {
        "prefix": "cache 刪除全部",
        "scope": "php",
        "body": [
            "\\\\Cache::flush();"
        ]
    },
    "cache讀取": {
        "prefix": "cache read 讀取",
        "scope": "php",
        "body": [
            "\\\\Cache::get('${1:table}');"
        ]
    },
    "cache讀取 html": {
        "prefix": "cache read 讀取",
        "scope": "html,blade",
        "body": [
            "{{Cache::get('epost_${1:table}')}}"
        ]
    },
    "cache explode": {
        "prefix": "cache read collect explode",
        "scope": "html,blade",
        "body": [
            "@foreach (collect(\\Cache::get('clesson'))->whereIn('id', explode(\",\",\\$rs->clesson_ids))  as \\$key => \\$rs1)",
            "{\\$rs1->id}}",
            "@endforeach"
        ]
    },
    "取得adminuser id php": {
        "prefix": "auth admin id",
        "scope": "php",
        "body": [
            "\\Auth::guard('admin')->id()"
        ]
    },
    "取得adminuser id html": {
        "prefix": "auth admin id",
        "scope": "html,blade",
        "body": [
            "{{Auth::guard('admin')->id()}}"
        ]
    },
    "取得member_id php": {
        "prefix": "auth member member_id",
        "scope": "php",
        "description": "",
        "body": [
            "\\Auth::guard('${1:member}')->id()"
        ]
    },
    "取得member_id html": {
        "prefix": "auth member member_id",
        "scope": "html,blade",
        "description": "",
        "body": [
            "{{Auth::guard('${1:member}')->id()}}"
        ]
    },
    "取得member xxx": {
        "prefix": "auth member xxx",
        "scope": "php",
        "body": [
            "\\Auth::guard('${2:member}')->user()->${1:role}"
        ]
    },
    "switch role php": {
        "prefix": "auth switch role",
        "scope": "php",
        "body": [
            "switch (\\Auth::guard('admin')->user()->role) {",
            "            case '999'://管理者",
            " ",
            "                break;",
            "            case '998':",
            " ",
            "                break;",
            "            default",
            " ",
            "                break;",
            "        }"
        ]
    },
    "switch role html": {
        "prefix": "auth switch role",
        "scope": "html,blade",
        "body": [
            "@switch(Auth::guard('admin')->user()->role)",
            "      @case(999)",
            "",
            "      @break",
            "      @case(900)",
            "",
            "      @break",
            "      @default",
            "",
            "      @endswitch"
        ]
    },
    "取得admin xxx": {
        "prefix": "auth admin xxx",
        "scope": "php",
        "body": [
            "\\Auth::guard('admin')->user()->${1:role}"
        ]
    },
    "判斷是否為管理者role php": {
        "prefix": "auth admin role 判斷角色",
        "scope": "php",
        "body": [
            "if (\\Gate::check('isAdminRole',['999'])){",
            "${1:}",
            "}"
        ]
    },
    "判斷是否為管理者role false php": {
        "prefix": "auth admin role 判斷角色==false",
        "scope": "php",
        "body": [
            "if (\\Gate::check('isAdminRole',['999'])==false){",
            "${1:}",
            "}"
        ]
    },
    "判斷是否為管理者role switch": {
        "prefix": "auth role switch",
        "scope": "php",
        "body": [
            "switch (\\Auth::guard('admin')->user()->role) {",
            "            case '998'://管理處",
            "                \\$rows->myWhere('school.adminuser_ids', \\Auth::guard('admin')->id(), 'id', 'N');",
            "                break;",
            "            case '900'://老師",
            "                \\$rows->myWhere('classt.adminuser_id|N', \\Auth::guard('admin')->id(), 'id', 'N');",
            "                break;",
            "            }"
        ]
    },
    "判斷是否為管理者role html": {
        "prefix": "auth admin role 判斷角色",
        "scope": "html,blade",
        "body": [
            "@if (Gate::check('isAdminRole',['999']) )",
            "${1:}",
            "@endif"
        ]
    },
    "判斷是否為管理者role false html": {
        "prefix": "auth admin role 判斷角色==false",
        "scope": "html,blade",
        "body": [
            "@if (Gate::check('isAdminRole',['999'])==false )",
            "${1:}",
            "@endif"
        ]
    },
    "判斷功能權限 php": {
        "prefix": "auth role+limit 判斷功能角色+權限",
        "scope": "php",
        "body": [
            "if (\\Gate::check('isAdminLimit',['${1:member}']) || \\Gate::check('isAdminRole',['999','${2:998}']) ){",
            "${1:}",
            "}"
        ]
    },
    "判斷功能權限 html": {
        "prefix": "auth role+limit 判斷功能角色+權限",
        "scope": "html,blade",
        "body": [
            "@if (Gate::check('isAdminLimit',['${1:member}']) || Gate::check('isAdminRole',['999','${2:998}']) )",
            "",
            "@endif"
        ]
    },
    "判斷會員是否登入 php": {
        "prefix": "auth member 判斷是否登入",
        "scope": "php",
        "body": [
            "if ( \\Auth::guard('${1:member}')->check()) {",
            "",
            "}"
        ]
    },
    "判斷會員角色 html": {
        "prefix": "auth member role 判斷角色",
        "scope": "html,blade,js,javascript",
        "body": [
            "@if (in_array(\\Auth::guard('${1:member}')->user()->role, ['1']))",
            "",
            "@endif"
        ]
    },
    "in_array html": {
        "prefix": "array exist in_array ",
        "scope": "html,blade,js,javascript",
        "body": [
            "@if(in_array('${1:kind}',['${2:kind}']))",
            "",
            "@endif"
        ]
    },
    "array explode": {
        "prefix": "array exist in_array explode spilt",
        "scope": "php",
        "body": [
            "if((in_array('${1:kind}',explode(\",\", ${1:kind}s)))){",
            "",
            "}"
        ]
    },
    "array explode last": {
        "prefix": "array spilt last",
        "scope": "php",
        "body": [
            "collect(explode(',', $rs->img))->last()"
        ]
    },

    "判斷會員是否登入 html": {
        "prefix": "auth member 判斷是否登入",
        "scope": "html,blade",
        "description": "判斷會員是否登入",
        "body": [
            "@if (Auth::guard('${1:member}')->check())",
            "",
            "@else",
            "",
            "@endif"
        ]
    },
    "判斷管理者是否登入 php": {
        "prefix": "auth admin 判斷是否登入",
        "scope": "php",
        "body": [
            "if (\\Auth::guard('admin')->check()) {",
            "",
            "}"
        ]
    },
    "判斷管理者是否登入 html": {
        "prefix": "auth admin 判斷是否登入",
        "scope": "html,blade",
        "body": [
            "@if (Auth::guard('admin')->check())",
            "",
            "@else",
            "",
            "@endif"
        ]
    },
    "管理者取得帳號 html": {
        "prefix": "auth admin xxxx",
        "scope": "html,blade",
        "body": [
            "{{Auth::guard('admin')->user()->${1:account}}}"
        ]
    },
    "會員取得姓名 html": {
        "prefix": "auth member xxx",
        "scope": "html,blade",
        "description": "取得member姓名",
        "body": [
            "{{Auth::guard('${2:member}')->user()->${1:account}}}"
        ]
    },
    "rs": {
        "prefix": "rs",
        "scope": "html,blade",
        "body": [
            "{{\\$rs->${1:title}}}"
        ]
    },
    "model": {
        "prefix": "model",
        "scope": "php",
        "body": [
            "\\App\\Models\\\\${1:board}"
        ]
    },
    "image_for": {
        "prefix": "img for 多筆圖",
        "scope": "html,blade",
        "body": [
            "@foreach (explode(\",\",\\$data['${1:field1}'])  as \\$key => \\$value)",
            "@if (\\$value!=\"\" && File::exists(public_path('images/${2:news}/'.\\$value)))",
            "<a href=\"{{url('/')}}/images/${2:news}/{{\\$value}}\" target=\"_blank\">",
            "{{",
            "    Html::myUIImage([",
            "    'folder' => \"images/${2:news}\",",
            "    'filename' => \\$value,",
            "    'width' => 100,",
            "    'height' => 100,",
            "    //'noimg' => 'no-picture.gif',",
            "    ])",
            "}}",
            "</a>",
            "@endif",
            "@endforeach"
        ]
    },
    "foreach split explode": {
        "prefix": "foreach split explode(,) ",
        "scope": "html,blade",
        "body": [
            "@foreach (explode(\",\",\\$data['${1:field1}']) as \\$key => \\$rs)",
            "{{\\$rs}}",
            "@endforeach"
        ]
    },
    "foreach split explode PHP_EOL": {
        "prefix": "foreach split explode(PHP_EOL) ",
        "scope": "html,blade",
        "body": [
            "@foreach (explode(PHP_EOL,\\$data['${1:field1}'])  as \\$key => \\$rs)",
            "<li>{{\\$rs}}</li>",
            "@endforeach"
        ]
    },
    "foreach_key_value": {
        "prefix": "foreach data key value",
        "scope": "html,blade",
        "body": [
            "@if (\\$data['${1:field1}'] != null && count(\\$data['${1:field1}'])>0)",
            "      @foreach (\\$data['${1:field1}'] as \\$key => \\$rs )",
            "       {{\\$key}}",
            "       {{\\$rs['a']}}",
            "      @endforeach",
            "@endif"
        ]
    },
    "新增陣列1": {
        "prefix": "array array_push 新增一筆陣列",
        "scope": "php",
        "body": [
            "array_push(${1:emails}, \\$rs->${2:id});"
        ]
    },
    "新增陣列2": {
        "prefix": "array array_push 新增多筆陣列",
        "scope": "php",
        "body": [
            "\\$yearkinds = [];",
            "for (\\$i = date('Y') - 1911; \\$i > 105; --\\$i) {",
            "   \\$item = ['value' => \\$i, 'title' => \\$i];",
            "   array_push(\\$yearkinds, \\$item);",
            "}",
            "array_push(\\$yearkinds, ['value' => 'file', 'title' => '資料檔案']);"
        ]
    },
    "collect where all": {
        "prefix": "collect db where",
        "scope": "php",
        "body": [
            "\\$json['datas']=collect('${1:name}')->where('${2:id}', 10000)->all();//->value();for json"
        ]
    },
    "collect where all 只顯示部份欄位": {
        "prefix": "collect db where only 只顯示部份欄位",
        "scope": "php",
        "body": [
            "\\$rows = \\Cache::get('${1:name}')->where('${2:id}', 10000)->map(function (\\$item) {",
            "            return collect(\\$item)->only(['${3:id}']);",
            "});"
        ]
    },
    "collect reject": {
        "prefix": "collect del reject 刪除",
        "scope": "php",
        "body": [
            "\\$collection = collect(explode(\",\", \\$myproducts));",
            " \\$filtered = \\$collection->reject(function (\\$value) use (\\$product_id) {",
            "     return \\$value == \\$product_id; // 刪除值為 2 的元素",
            "});"
        ]
    },
    "collect contains": {
        "prefix": "collect 一階 array like ",
        "scope": "php",
        "body": [
            "\\$collection = collect(explode(\",\", \\$myproducts));",
            "if (\\$collection->contains(\\$product_id)) {",
            "  throw new \\CustomException(\"已存在\");",
            "}"
        ]
    },
    "collect first": {
        "prefix": "collect where first 某第一筆",
        "scope": "php",
        "body": [
            "\\$json['datas']=collect(${1:name})->where('${1:name}', \\$rs->${1:name})->first();"
        ]
    },
    "collect sortdesc": {
        "prefix": "collect sortByDesc first 排序",
        "scope": "php",
        "body": [
            "collect(${1:name})->sortBy([['Market_and_Exchange_Names', 'asc'],['date', 'asc']])->first();"
        ]
    },
    "collect sort": {
        "prefix": "collect sortBy first 排序",
        "scope": "php",
        "body": [
            "collect(${1:name})->sortBy([['Market_and_Exchange_Names', 'asc'],['date', 'asc']])->first();"
        ]
    },
    "collect take": {
        "prefix": "collect take 拿前幾筆",
        "scope": "php",
        "body": [
            "collect(${1:name})->take(3);"
        ]
    },
    "db filter_html": {
        "prefix": "collect where",
        "scope": "html,blade",
        "body": [
            "@foreach (collect(\\$data['rows'])->where('${1:name}',1)->all() as \\$key => \\$rs) ",
            "{{\\$rs->title}}",
            "@endforeach"
        ]
    },
    "foreach_key_value_rs": {
        "prefix": "foreach rs key value",
        "scope": "html,blade",
        "body": [
            "@if (\\$rs->${1:field1} != null)",
            "      @foreach (\\$rs->${1:field1} as \\$key => \\$rs )",
            "         {{\\$key}}",
            "         {{\\$rs->a]}}",
            "      @endforeach",
            "@endif"
        ]
    },
    "image_rs": {
        "prefix": "image data",
        "scope": "html,blade",
        "body": [
            "",
            "<a href=\"{{url('/')}}/images/${1:news}/{{\\$data['${2:news}']}}\" target=\"_blank\">",
            "{{",
            "        Html::myUIImage([",
            "    'folder' => \"images/${1:news}\",",
            "    'filename' => \\$data['${2:news}'],",
            "    'width' => 300,",
            "    'height' => 300,",
            "    //'noimg' => 'no-picture.gif',",
            "    //'class' => 'card-img',",
            "        ])",
            "}}",
            "</a>"
        ]
    },
    "image_data": {
        "prefix": "image rs",
        "scope": "html,blade",
        "body": [
            "<a href=\"{{url('/')}}/${1:news}/show/{{\\$rs->id}}\" title=\"{{\\$rs->${3:title}}}\">",
            "{{",
            "        Html::myUIImage([",
            "    'folder' => \"images/${1:news}\",",
            "    'filename' => \\$rs->${2:img},",
            "    'alt' => \\$rs->${3:title},",
            "    'width' => 300,",
            "    'height' => 300,",
            "    'noimg' => 'no-picture.gif',",
            "        ])",
            "}}",
            "</a>"
        ]
    },
    "xml_search": {
        "prefix": "xml search",
        "scope": "html,blade",
        "description": "PF::xmlSearch",
        "body": [
            "{{PF::xmlSearch(\\$data['xmldoc'],'//參數設定檔/${1:角色}/KIND/傳回值','資料',\\$rs->${2:table})}}"
        ]
    },
    "xml_傳回值": {
        "prefix": "xml 傳回值",
        "scope": "html,blade",
        "body": [
            "{{strval(\\$v->傳回值)}}"
        ]
    },
    "xml_傳回值 php": {
        "prefix": "xml 傳回值",
        "scope": "php",
        "body": [
            "strval(\\$v->傳回值)"
        ]
    },
    "xml_資料": {
        "prefix": "xml 資料",
        "scope": "html,blade",
        "body": [
            "{{\\$v->資料}}"
        ]
    },
    "xml_資料 php": {
        "prefix": "xml 資料",
        "scope": "php",
        "body": [
            "\\$v->資料"
        ]
    },
    "CustomException": {
        "prefix": "throw CustomException",
        "scope": "php",
        "body": [
            "throw new \\CustomException(\"${1:No data}\");"
        ]
    },
    "if_data": {
        "prefix": "if data",
        "scope": "php",
        "body": [
            "if (\\$this->data['${1:board}']!=\"\") {",
            "",
            "}"
        ]
    },
    "if_request": {
        "prefix": "if request",
        "scope": "php",
        "body": [
            "if (\\$request->input('${1:board}')!=\"\") {",
            "",
            "}"
        ]
    },
    "if_rs_php": {
        "prefix": "if rs",
        "scope": "php",
        "body": [
            "if (\\$rs->${1:board}==\"1\") {",
            "",
            "}"
        ]
    },
    "file_exists_html": {
        "prefix": "file exists",
        "scope": "html,blade",
        "body": [
            "@if (File::exists(public_path('images/ordergroup/'.\\$rs->id.'.pdf')))",
            "",
            "",
            "@endif"
        ]
    },
    "xml_search_php": {
        "prefix": "xml search",
        "scope": "php",
        "description": "xml PF::xmlSearch",
        "body": [
            "PF::xmlSearch(\\$this->data['xmldoc'], '//參數設定檔/${1:field1}/KIND/傳回值', '資料', \\$this->data['${2:field1}']);"
        ]
    },
    "url_html_admin": {
        "prefix": "url admin",
        "scope": "html,blade",
        "description": "url admin",
        "body": [
            "{{ url('admin/${1:board}/store') }}"
        ]
    },
    "url_html_": {
        "prefix": "url",
        "scope": "html,blade",
        "description": "url",
        "body": [
            "{{ url('/') }}/"
        ]
    },
    "try_throw": {
        "prefix": "try throw",
        "scope": "php",
        "description": "throw",
        "body": [
            "throw new \\Exception('${1:no data}');"
        ]
    },
    "viewtohtml": {
        "prefix": "view to html",
        "scope": "php",
        "description": "view render html",
        "body": [
            "\\$ebody = \\view('email.contract',\\$data)->render();"
        ]
    },
    "string to view to string": {
        "prefix": "view string to view to string Blade::render",
        "scope": "php",
        "body": [
            "\\$postData = \\Blade::render(\\PT::getManage(10009)->wechat_template_taskoutadd, \\$rs);"
        ]
    },
    "環境_多語系_get_php": {
        "prefix": "lg get",
        "scope": "php",
        "description": "app()->getLocale()",
        "body": [
            "app()->getLocale()"
        ]
    },
    "include_html": {
        "prefix": "include no controller",
        "scope": "html,blade",
        "description": "@include('layouts')",
        "body": [
            "@include('layouts.${1:banner}')"
        ]
    },
    "include_hmtl_inject": {
        "prefix": "include add controller  ",
        "scope": "html,blade",
        "description": "controller inject",
        "body": [
            "@inject('${1:banner}', '\\App\\Http\\Controllers\\layouts\\\\${1:banner}Controller')",
            "{!!\\$${1:banner}->index('banner')!!}"
        ]
    },
    "config_web_title": {
        "prefix": "config PF::setConfig web title",
        "scope": "php",
        "description": "Config::set('config.title",
        "body": [
            "\\PF::setConfig('title',__(\"${1:最新消息}\") . ' | ' . \\PF::getConfig('title'));"
        ]
    },
    "日期格式_strtotime_php_rs": {
        "prefix": "date formate rs ",
        "scope": "php",
        "body": [
            "date(\"Y-m-d H:i:s\",strtotime(\\$rs->${1:created_at}))"
        ]
    },
    "文字 to timestamp": {
        "prefix": "date 文字 2025-3-10 02:40:00 to timestamp ",
        "scope": "php",
        "body": [
            "\\$date = new \\DateTime(\\$timestamp, new \\DateTimeZone('Asia/Taipei'));",
            "\\$timestampMillis = \\$date->getTimestamp() * 1000;",
        ]
    },
    "date 超過1小時 ": {
        "prefix": "date 超過1小時 throw",
        "scope": "php",
        "body": [
            "if (strtotime(\\Carbon::now()) > strtotime('1 hour', strtotime(\\$this->data['datetime']))) {",
            "   throw new \\CustomException('逾時請重新QRCODE');",
            "}"
        ]
    },
    "日期格式_strtotime_html_rs": {
        "prefix": "date formate rs ",
        "scope": "html,blade",
        "body": [
            "{{date('Y-m-d H:i:s',strtotime(\\$rs->${1:created_at}))}}"
        ]
    },
    "日期格式_strtotime_php_data": {
        "prefix": "date formate data ",
        "scope": "php",
        "body": [
            "date('Y-m-d H:i:s',strtotime(\\$this->data['${1:created_at}']))"
        ]
    },
    "日期格式_strtotime_html_data": {
        "prefix": "date formate data ",
        "scope": "html,blade",
        "body": [
            "{{date('Y-m-d H:i:s',strtotime(\\$data['${1:created_at}']))}}"
        ]
    },
    "日期格式_Carbon": {
        "prefix": "date formate",
        "scope": "php",
        "description": "Carbon parse format",
        "body": [
            "\\Carbon::createFromFormat('Ymd', \\$rs->${1:created_at})->format('Y-m-d H:i:s');"
        ]
    },
    "日期格式_Carbon_data": {
        "prefix": "date formate data",
        "scope": "php",
        "body": [
            "\\Carbon::createFromFormat('Ymd', \\$this->data[\"${1:created_at}\"])->format('Y-m-d H:i:s');"
        ]
    },
    "字串取固定長度str_limit": {
        "prefix": "str left ",
        "scope": "html,blade",
        "body": [
            "{{Str::limit(\\$rs->${1:title},100,'...')}} "
        ]
    },
    "title": {
        "prefix": "title",
        "scope": "html,blade",
        "body": [
            " title=\"${1:首頁}\""
        ]
    },
    "title_rs": {
        "prefix": "title rs",
        "scope": "html,blade",
        "body": [
            " title=\"{{\\$$rs->${1:title}}}\""
        ]
    },
    "轉址有加參數的redirect()->away post parma": {
        "prefix": "redirect 轉址有加參數",
        "scope": "php",
        "body": [
            "return redirect()->away(url('/admin/contact/sign?signid='.\\$rs->signid));"
        ]
    },
    "轉址沒有加參數的redirect()->to": {
        "prefix": "redirect 轉址沒有加參數",
        "scope": "php",
        "body": [
            "return redirect()->to('/${1:membercenter}');"
        ]
    },
    "函式回傳一個VIEWreturn view": {
        "prefix": "view 回傳view",
        "scope": "php",
        "body": [
            "        return view('${1:x}.index', [",
            "        'data' => \\$this->data,",
            "    ]);"
        ]
    },
    "css_include": {
        "prefix": "css",
        "scope": "html,blade",
        "description": "css link href",
        "body": [
            "<link href=\"{{ asset('css/${1:css}.css') }}\" rel=\"stylesheet\" type=\"text/css\" />"
        ]
    },
    "js_include": {
        "prefix": "js script",
        "scope": "html,blade",
        "description": "js script",
        "body": [
            "<script  type=\"text/javascript\" src=\"{{ asset('Scripts/jquery.js') }}\"></script>"
        ]
    },
    "for1_10": {
        "prefix": "for number",
        "scope": "html,blade",
        "description": "for number",
        "body": [
            "@for (\\$i = 0; \\$i < 10; \\$i++)",
            "{{ \\$i }}",
            "@endfor",
            ""
        ]
    },
    "validator_custom": {
        "prefix": "validator 客製訊息(不檢查)只回錯誤",
        "scope": "php",
        "body": [
            "\\$validator = \\Validator::make([], []);",
            "\\$validator->errors()->add('field', '此時段已被人預約');",
            "return view('errors.validatorback')->withErrors(\\$validator);",
            "//throw new \\CustomException(implode(',', $validator->messages()->all()));"
        ]
    },
    "字串轉陣列foreach": {
        "prefix": "foreach split explode 字串轉陣列",
        "scope": "php",
        "body": [
            "//\\$items=explode(\"chr(13).chr(10)\",\\$${1:s});",
            "\\$items=explode(\";\",\\$${1:s});",
            "foreach (\\$items as \\$k => \\$v){",
            "   \\$v;",
            "};   "
        ]
    },
    "split 最後一個": {
        "prefix": "split 最後一個",
        "scope": "php",
        "body": [
            "end(explode(\"/\", \\$${1:names}));"
        ]
    },
    "array like": {
        "prefix": "array like",
        "scope": "php",
        "body": [
            "if (\\Str::contains(\\$this->data['${1:names}'], ['A', 'B'])) {   ",
            "};   "
        ]
    },
    "foreach array": {
        "prefix": "foreach array",
        "scope": "php",
        "body": [
            "\\$items=['${1:items}','${2:items}'];",
            "foreach (\\$items as \\$k => \\$v){",
            "   \\$v;",
            "};   "
        ]
    },
    "foreach_key_value_1": {
        "prefix": "foreach key value",
        "scope": "php",
        "body": [
            "foreach (\\$${1:items} as \\$k => \\$v){",
            "   \\$k;",
            "   \\$v;",
            "};   "
        ]
    },
    "php_html": {
        "prefix": "php",
        "scope": "html,blade",
        "description": "@php",
        "body": [
            " @php",
            "    \\$i++",
            " @endphp"
        ]
    },
    "reponseraw": {
        "prefix": "response raw",
        "scope": "php",
        "description": "view raw Swal.fire",
        "body": [
            "\\$this->data['raw']=\"<script>Swal.fire({title: '刪除成功',icon: \"success\",}).then((willDelete) => {PF_ColorBoxClose()});</script>\";",
            "    return view('layouts.empty', [",
            "        'data' => \\$this->data,",
            "    ]);"
        ]
    },
    "created_at": {
        "prefix": "created_at",
        "scope": "php,html",
        "description": "created_at",
        "body": [
            "created_at"
        ]
    },
    "vbcrlf斷行": {
        "prefix": "vbcrlf",
        "scope": "html,blade",
        "body": [
            "{!!PF::vbcrlf(\\$data['${1:body}'])!!}"
        ]
    },
    "vbcrlf rs斷行": {
        "prefix": "vbcrlf rs",
        "scope": "html,blade",
        "body": [
            "{!!PF::vbcrlf(\\$rs->${1:body})!!}"
        ]
    },
    "function": {
        "prefix": "fun create function",
        "scope": "php",
        "description": "create public function",
        "body": [
            "public function ${1:index}(Request \\$request)",
            "{",
            "        //return response('OK');              ",
            "        return view('${2:admin}.${1:index}', [",
            "        'data' => \\$this->data,",
            "        ]);",
            "}"
        ]
    },
    "function api": {
        "prefix": "fun create function api",
        "scope": "php",
        "description": "create public function",
        "body": [
            "public function ${1:index}(Request \\$request)",
            "{",
            "",
            "",
            "",
            "        return response()->json(\\$this->jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);",
            "}"
        ]
    },
    "移除 Session 中指定的項目": {
        "prefix": "session del 移除",
        "scope": "php",
        "body": [
            "\\Session::forget('${1:member_id}');"
        ]
    },
    "formquerylogService": {
        "prefix": "formquerylogService",
        "scope": "php",
        "description": "form query log Service",
        "body": [
            "\\$formquerylogService= new \\App\\Services\\formquerylogService(\\$request);",
            "\\$formquerylogService->pagename=\"${1:金流回傳}\";",
            "\\$formquerylogService->run();"
        ]
    },
    "Service": {
        "prefix": "add Service",
        "scope": "php",
        "body": [
            "\\$${1:xx}= new \\App\\Services\\\\${1:xx}();",
            "\\$${1:xx}->execute(\\$request);"
        ]
    },

    "banner": {
        "prefix": "banner",
        "scope": "html,blade",
        "description": "banner",
        "body": [
            "@foreach (\\$data['rowsbanner'] as \\$rs)",
            "    @php",
            "    \\$url=\\$rs->memo==\"\" ? \"#\" : \\$rs->memo;",
            "    @endphp",
            "    <a href=\"{{\\$url}}\" target=\"{{\\$rs->field9}}\" title=\"{{\\$rs->title}}\"",
            "        onclick=\"PF_Hits('board','id','{{\\$rs->id}}');\">",
            "        {{",
            "        Html::myUIImage([",
            "            'folder' => \"images/banner\",",
            "            'filename' => \\$rs->field1,",
            "            'alt' => \\$rs->title,",
            "            //'width' => 230,",
            "            ",
            "            'class' => 'banner',",
            "        ])",
            "        }}",
            "",
            "    </a>",
            "    <br>",
            "    @endforeach"
        ]
    },
    "news": {
        "prefix": "news",
        "scope": "html,blade",
        "description": "news",
        "body": [
            "@foreach (\\$data['rows']  as \\$key => \\$rs)   ",
            "            {{",
            "  Html::myUIImage([",
            "      'folder' => \"images/news\",",
            "      'filename' => \\$rs->field1,",
            "      'alt' => \\$rs->title,",
            "      'width' => 270,",
            "      'height' => 162,",
            "      'noimg' => 'no-picture.gif',",
            "  ])",
            "            }}",
            "        <a href=\"{{ url('/') }}/news/show/{{\\$rs->id}}\" class=\"detail\" title=\"{{\\$rs->title}}\">",
            "            <div class=\"title\">{{\\$rs->title}}</div>",
            "            <small class=\"date\">{{PF::formatDate(\\$rs->created_at)}}</small>",
            "            <div class=\"text\">{{\\$rs->memo}}</div>",
            "        </a>",
            "    @endforeach"
        ]
    },
    "sms": {
        "prefix": "sms",
        "scope": "php",
        "description": "sms簡訊",
        "body": [
            "\\$ebody = view('sms.sncheck', \\$this->data)->render();            ",
            "    \\$sms = new AppServicessmsService();",
            "    \\$sms->push(\\$inputs['mobile'],\\$ebody);",
            ""
        ]
    },
    "frame": {
        "prefix": "frame",
        "scope": "php",
        "description": "php frame",
        "body": [
            "public function frame(Request \\$request)",
            "    {",
            "        ",
            "        return view('admin.${1:member}.frame', [",
            "        'data' => \\$this->data,",
            "    ]);",
            "    }",
            "    public function top(Request \\$request)",
            "    {",
            "        return view('admin.${1:member}.top', [",
            "    'data' => \\$this->data,",
            "        ]);",
            "    }"
        ]
    },
    "view_back": {
        "prefix": "back view",
        "scope": "php",
        "description": "backview",
        "body": [
            "return view('layouts.backalert', [",
            "        'alert' => \\$e->getMessage(),",
            "    ]);"
        ]
    },
    "Http 同步 8 post": {
        "prefix": "http curl net post + raw 同步 ",
        "scope": "php",
        "body": [
            "try {",
            "    \\$headers = [",
            "        'Authorization' => 'Bearer '.\\$accesstoken,",
            "        'Content-Type' => 'application/json; charset=utf-8',",
            "        'Cache-Control' => 'no-cache, no-store, must-revalidate',",
            "    ];",
            "    \\$response = \\Http::withHeaders(\\$headers)->withOptions([",
            "            'connect_timeout' => 15,",
            "            'timeout' => 20,",
            "             'verify' => false,//// 忽略 SSL 憑證驗證",
            "    ])->send('POST', 'http:///api/core/me/subobject', [",
            "        'form_params' => [//post",
            "            'oid' => 1,",
            "         ],",
            "        'body' => json_encode([//raw",
            "            'oid' => 1,",
            "            ])",
            "    ]);",
            "    if (\\$response->ok()==false){",
            "      throw new \\CustomException('Http Error:'.\\$response->body().\"(\".\\$response->status().\")\");",
            "     }",
            "        } catch (\\Exception \\$e) {",
            "    throw new \\CustomException(\\$e->getMessage());",
            "        }",
            "        \\$json = PF::json_decode(\\$response->body());",
            "        print_r(\\$json);"
        ]
    },
    "Http 非同步 8 pool": {
        "prefix": "http curl net pool 非同步 ",
        "scope": "php",
        "body": [
            "\\$this->requestids = [];",
            "\\$responses = \\\\Http::pool(function (\\$http) use (\\$rows) {",
            "    \\foreach (\\$rows as \\$rs) {",
            "      \\$this->requestids[\\$rs->id] = \\$rs;",
            "      \\$http->as(\\$rs->id)->withBasicAuth('api', 'xxx')->withOptions([",
            "        'connect_timeout' => 5,",
            "        'timeout' => 5,",
            "        'verify' => false,",
            "       ])->send('POST', 'https://api.mailgun.net/v3//messages', [",
            "        'form_params' => [",
            "            'a' => 1,",
            "            'b' => 2,",
            "        ],",
            "      ]);",
            "    }  ",
            "});",
            "foreach (\\$this->requestids as \\$key => \\$rs) {",
            "    try {",
            "        if (\\$responses[\\$rs->id] == null) {",
            "             continue;",
            "        }",
            "        \\$response = \\$responses[\\$rs->id];",
            "        if (false == \\$response->ok()) {",
            "            throw new \\CustomException('Http Error:' . \\$response->body() . '(' . \\$response->status() . ')');",
            "        }",
            "        \\$json = PF::json_decode(\\$response->body());",
            "    } catch (\\\\Exception \\$e) {",
            "        echo \\$e->getMessage();",
            "    } finally {",
            "    }",
            "}"
        ]
    },
    "Http 同步 8 get": {
        "prefix": "http curl net get 8 同步",
        "scope": "php",
        "body": [
            "try {",
            "    \\$headers = [",
            "      'Authorization' => 'Bearer '.\\$accesstoken,",
            "      'Content-Type' => 'application/json; charset=utf-8',",
            "      'Cache-Control' => 'no-cache, no-store, must-revalidate',",
            "    ];",
            "    \\$response = \\Http::withHeaders(\\$headers)->withOptions([",
            "    'connect_timeout' => 15,",
            "    'timeout' => 20,",
            "    'verify' => false,// 忽略 SSL 憑證驗證",
            "    ])->get('https://');",
            "    if (\\$response->ok()==false){",
            "       throw new \\CustomException('Http Error:'.\\$response->body().\"(\".\\$response->status().\")\");",
            "    }",
            "} catch (\\Exception \\$e) {",
            "    throw new \\CustomException(\\$e->getMessage());",
            "}",
            "\\$json = PF::json_decode(\\$response->body());",
            "print_r(\\$json);"
        ]
    },
    "json_decode": {
        "prefix": "json decode 解 string ->object",
        "scope": "php",
        "description": "json_decode",
        "body": [
            "if(\\$jsonbody!=\"\"){",
            "\\$json = \\PF::json_decode(\\$jsonbody, true);//ture=>可以用\\$json['yyy'];false=>可以直接update",
            " foreach (\\$json as \\$k => \\$v) {",
            "   \\$this->data[\\$k] = \\$v;",
            " }",
            "}"
        ]
    },
    "myUISelectDate": {
        "prefix": "date myUISelectDate",
        "scope": "html,blade",
        "description": "myUISelectDate",
        "body": [
            "{{",
            "  Form::myUISelectDate([",
            "  'name'=>'${1:startdate}',",
            "  'type'=>'date',        ",
            "  'title'=>'開始日期',        ",
            "  'value'=>\\$data['${1:startdate}'],        ",
            "  ])",
            "}}"
        ]
    },
    "判斷request->route()->middleware_php": {
        "prefix": "auth request middleware if array_search",
        "scope": "php",
        "body": [
            "if (array_search(\"Front\",\\$request->route()->middleware())) {",
            "",
            "}"
        ]
    },
    "判斷request->route()->middleware_html": {
        "prefix": "auth request middleware if array_search",
        "scope": "html,blade",
        "body": [
            "@if (array_search(\"Front\",Request::route()->middleware())) ",
            "",
            "@endif"
        ]
    },
    "inputs加$this->data['xx']": {
        "prefix": "inputs ",
        "scope": "php",
        "description": "inputs",
        "body": [
            "\\$inputs['${1:userid}'] = \\$$request->input('${1:startdate}');"
        ]
    },
    "foreach第一筆最後一筆": {
        "prefix": "db foreach first last 第一筆最後一筆",
        "scope": "html,blade",
        "body": [
            "{{ \\$loop->index }}",
            "@if (\\$loop->first)",
            "                 ",
            "@endif",
            "@if (\\$loop->last)",
            "                 ",
            "@endif"
        ]
    },
    "Repository init": {
        "prefix": "repo 多行初始",
        "scope": "php",
        "body": [
            "use App\\Repositories\\\\${1:board}Repository;",
            "private \\$${1:board}Repo;",
            "${1:board}Repository \\$${1:board}Repo",
            "\\$this->${1:board}Repo=\\$${1:board}Repo;"
        ]
    },
    "service init": {
        "prefix": "Service 多行初始",
        "scope": "php",
        "body": [
            "use App\\Services\\\\${1:board}Service;",
            "private \\$${1:board}Service;",
            "${1:board}Service \\$${1:board}Service",
            "\\$this->${1:board}Service=\\$${1:board}Service;"
        ]
    },
    "Repository create": {
        "prefix": "repo 一行初始",
        "scope": "php",
        "body": [
            "\\$this->${1:board}Repo = app(\\App\\Repositories\\\\${1:board}Repository::class);"
        ]
    },
    "unique": {
        "prefix": "unique",
        "scope": "html,blade",
        "body": [
            "name=\"${1:board}\" title=\"xx\" unique=\"{{\\$data['${1:email}']}}\" table=\"${2:member}\""
        ]
    },
    "字串解密 aes decrypt ": {
        "prefix": "string 字串解密 Crypt decrypt",
        "scope": "php",
        "body": [
            "\\$${1:baord}=\\Crypt::decrypt(\\$${1:baord});"
        ]
    },
    "字串加密 aes encrypt ": {
        "prefix": "string 字串加密 Crypt encrypt",
        "scope": "php",
        "body": [
            "\\$${1:baord}=\\Crypt::encrypt(\\$${1:baord});"
        ]
    },
    "send email raw": {
        "prefix": "send mail raw",
        "scope": "php",
        "body": [

            "\\$sendMail = new \\App\\Mails\\sendMail([",
            "            'subject' => \"test\",",
            "            'raw' => \"test\",",
            "        ]);",
            "        //echo \\$sendMail->render();",
            "\\Mail::to(\"<EMAIL>\") //收件人",
            "            // ->cc() //副本",
            "            // ->bcc() //密件副本",
            "            ->send(\\$sendMail);",

        ]
    },
    "upload_file": {
        "prefix": "upload file",
        "scope": "php",
        "body": [
            "try {",
            "    //FIXME 檔案上傳取得值",
            "    \\$upload = new \\App\\Libraries\\UploadFile();",
            "    \\$upload->request = \\$request;",
            "    \\$upload->inputs = \\$inputs;",
            "    \\$upload->folder = 'images/${1:created_at}/';",
            "    \\$upload->width = '234';",
            "    \\$upload->height = '145';",
            "    //\\$upload->limitext = config('app.FileLimit');",
            "    \\$inputs = \\$upload->execute();",
            "} catch (Exception \\$e) {",
            "    die(\\$e->getMessage());",
            "}"
        ]
    },
    "trim": {
        "prefix": "trim",
        "scope": "php",
        "body": [
            "\\$${1:id}=ltrim(rtrim(\\$${1:id}, \",\"),\",\");"
        ]
    },
    "urlfull": {
        "prefix": "url 回傳瀏覽器網址+後方的參數",
        "scope": "php",
        "body": [
            "\\URL::full()"
        ]
    },
    "urlcurrent": {
        "prefix": "url 回傳瀏覽器網址+不含後方的參數",
        "scope": "php",
        "body": [
            "\\URL::current()"
        ]
    },
    "getHttpHost": {
        "prefix": "url 回傳網域 allennb.com.tw",
        "scope": "php",
        "body": [
            "\\Request::getHttpHost()"
        ]
    },
    "urlprevious": {
        "prefix": "url 回傳瀏覽器上一層網址",
        "scope": "php",
        "body": [
            "\\URL::previous()"
        ]
    },
    "request-url": {
        "prefix": "URL 獲取請求http://xx.com/aa/bb",
        "scope": "php",
        "body": [
            "\\Request::url();"
        ]
    },
    "request-path": {
        "prefix": "url 獲取路徑: /aa/bb",
        "scope": "php",
        "body": [
            "\\Request::path();"
        ]
    },
    "request-getRequestUri": {
        "prefix": "url 獲取請求/aa/bb/?c=d",
        "scope": "php",
        "body": [
            "\\Request::getRequestUri();"
        ]
    },
    "request-getQueryString": {
        "prefix": "request getQueryString() 獲取查詢字串: c=d",
        "scope": "php",
        "body": [
            "\\Request::getQueryString();"
        ]
    },
    "href search": {
        "prefix": "href search",
        "scope": "html,blade",
        "body": [
            "<a href=\"javascript:;\" onclick=\"PF_formSearch('${1:member}.${2:kind_id}|N','{{\\$rs->${2:kind_id}}}')\">",
            "",
            "</a>"
        ]
    },
    "number": {
        "prefix": "text number",
        "scope": "html,blade",
        "body": [
            "<input type=\"number\" pattern=\"\\d*\"  required class=\"form-control\" name=\"${1:account}\"",
            " v-model=\"inputs.${1:account}\"",
            " value=\"{{\\$data['${1:account}']}}\">"
        ]
    },
    "float": {
        "prefix": "text float",
        "scope": "html,blade",
        "body": [
            "<input type=\"number\" step=\"0.01\" inputmode=\"decimal\" required class=\"form-control\" name=\"${1:account}\"",
            " v-model=\"inputs.${1:account}\"",
            " value=\"{{\\$data['${1:account}']}}\">"
        ]
    },
    "checkLimit": {
        "prefix": "limit",
        "scope": "php",
        "body": [
            "\\$adminuserService = new \\App\\Services\\adminuserService();",
            "\\$adminuserService->checkLimits([",
            "request()->route()->parameters['controller'] => [],",
            "'' => ['index', 'show'],",
            "]);//權限編號(沒有代表沒有權限編號都可以使用),可以使用的functions(沒有代表都可以使用)"
        ]
    },
    "upload": {
        "prefix": "upload 多筆",
        "scope": "html,blade",
        "body": [
            "",
            "    {{",
            "    Form::myUIMultiUpload([",
            "    'name'=>'files',",
            "    'folder'=>'images/${1:title}',",
            "    'filename'=>\\$data[\"${1:imgs}\"],",
            "    'title'=>'檔案',",
            "    // 'width'=>800,",
            "    // 'height'=>800,",
            "        'accept' => '.jpg,.png,.gif,.jpeg,.bmp,.webp',",
            "    ])",
            "    }}"
        ]
    },
    "upoload": {
        "prefix": "upload 單一檔案",
        "scope": "html,blade",
        "body": [
            "{{",
            "  Form::myUIUpload([",
            "  'name' => 'file',",
            "  'folder' => 'images/${1:title}',",
            "  'filename' => \\$data['${2:title}'],",
            "  'title' => \\$data['title'],",
            "  'width' => 800,",
            "  'height' => 800,",
            "  //'required' => 1",
            "  'accept' => '.png,.jpg,*.gif',",
            "  ])",
            "  }}"
        ]
    },
    "modal_close": {
        "prefix": "close modal",
        "scope": "php",
        "body": [
            "$(\"[data-dismiss='modal']\",parent.document).last().trigger('click');"
        ]
    },
    "sum": {
        "prefix": "db collection sum",
        "scope": "html,blade",
        "body": [
            "{{\\$data['rows']->sum('${1:qty}')}}"
        ]
    },
    "sum php": {
        "prefix": "json to collection sum",
        "scope": "php",
        "body": [
            "\\$qtys=collect(\\$json['qty1s'])->sum('qty');"
        ]
    },
    "collect count": {
        "prefix": "collection count exist",
        "scope": "php",
        "body": [
            "if (collect(\\$items)->where('sku', 'xx')->count() > 0) {",
            "",
            "}"
        ]
    },

    "collection filter like": {
        "prefix": "collection filter like",
        "scope": "php",
        "body": [
            "\\$rs->onsale=\\Cache::get('onsale')->filter(function (\\$rs) use (\\$kind_id) {",
            "    if (\\$rs->kind_id==\\$kind_id){",
            "       return true;",
            "    }",
            "    });",
        ]
    },
    "json remove index": {
        "prefix": "json remove index",
        "scope": "php",
        "body": [
            "\\$qtys=collect($json['qty1s'])->values();"
        ]
    },
    "sum_collection": {
        "prefix": "json to collection sum",
        "scope": "html,blade",
        "body": [
            "{{collect(\\$data['json'])->sum('${1:score}')}}"
        ]
    },
    "sum_collection if count": {
        "prefix": "db collection where count",
        "scope": "html,blade",
        "body": [
            "@if (count(collect(\\$data['${1:rows}'])->where('${2:score_id}',\\$rs->id))>0)",
            "",
            "@endif"
        ]
    },
    "created_at_html": {
        "prefix": "created_at",
        "scope": "html,blade",
        "body": [
            "created_at"
        ]
    },
    "flush": {
        "prefix": "flush",
        "scope": "php",
        "body": [
            "ob_implicit_flush(true);",
            "ob_flush();"
        ]
    },
    "login": {
        "prefix": "login",
        "scope": "php",
        "body": [
            "\\Auth::guard('${1:member}')->loginUsingId(\\$${2:id});",
            "\\$inputs = [];",
            "\\$inputs['api_token'] =hash('sha256', \\Str::random(80));",
            "\\App\\Models\\\\${1:member}::findOrFail(\\$${2:id})->update(\\$inputs);"
        ]
    },
    "logout": {
        "prefix": "logout",
        "scope": "php",
        "body": [
            "\\Auth::guard('${1:admin}')->logout();"
        ]
    },
    "ckeditor": {
        "prefix": "ckeditor",
        "scope": "html,blade",
        "body": [
            "@include('layouts.ckeditor', ['name' => '${1:body}', 'data' => \\$data])",
        ]
    },
    "collect unique explode": {
        "prefix": "collect unique explode 只留下不重覆",
        "scope": "php",
        "body": [
            "\\$${1:tag_id}s = explode(',', \\$${1:tag_id});",
            "\\$${1:tag_id}s = collect(\\$${1:tag_id}s);",
            "\\$${1:tag_id} = \\$${1:tag_id}s->unique();",
            "\\$${1:tag_id} = \\$${1:tag_id}->implode(',');",
            "\\$inputs['${1:tag_id}'] = \\$${1:tag_id};"
        ]
    },
    "collect unique rows ": {
        "prefix": "collect unique rows 只留下某一個欄位",
        "scope": "php",
        "body": [
            "\\$${1:tag_id}s = collect($rows)->pluck('${1:tag_id}')->unique();",
            "        foreach (\\$${1:tag_id}s as \\$key => \\$v) {",
            "            \\PF::printr([\\$v]);",
            "        }"
        ]
    },
    "collect unique ": {
        "prefix": "collect unique map 字串只留下不重覆",
        "scope": "php",
        "body": [
            "\\$${1:name}s = collect(\\$json['${1:name}s'])->unique('${2:name}');",
            "        foreach (\\$${1:tag_id}s as \\$key => \\$rs) {",
            "            \\PF::printr(['rs', \\$rs['${1:name}']]);",
            "        }"
        ]
    },
    "collect implode": {
        "prefix": "collect str implode 轉 字串",
        "scope": "php",
        "body": [
            "\\$${1:name}s = collect(\\$json['${2:datas}'])->pluck('${1:number}')->implode(',');"
        ]
    },
    "array implode": {
        "prefix": "collect key to implode 轉 字串",
        "scope": "php",
        "body": [
            "collect(array_keys(\\$${1:ids}))->implode(',')"
        ]
    },
    "array implode fields 二層": {
        "prefix": "collect 二層 to implode 轉 字串",
        "scope": "php",
        "body": [
            "collect($rows)->implode('${1:ids}', ',');"
        ]
    },
    "array implode fields 一層": {
        "prefix": "collect 一層 to implode 轉 字串",
        "scope": "php",
        "body": [
            "collect(\\$rows)->implode(',');"
        ]
    },
    "collect pop ": {
        "prefix": "collect pop 最後一個",
        "scope": "php",
        "body": [
            "collect(\\$rows)->pop();"
        ]
    },
    "collect push": {
        "prefix": "collect push 一層 加一個 ",
        "scope": "php",
        "body": [
            "collect(\\$rows)->push(5);"
        ]
    },
    "collect put": {
        "prefix": "collect put 二層 加一個",
        "scope": "php",
        "body": [
            "collect(\\$rows)->put('price', 100);"
        ]
    },
    "collect random": {
        "prefix": "collect random 隨機",
        "scope": "php",
        "body": [
            "collect(\\$rows)->random();"
        ]
    },
    "google recaptcha": {
        "prefix": "google recaptcha",
        "scope": "html,blade",
        "body": [
            "<script",
            "src=\"https://www.google.com/recaptcha/api.js?render={{config('recaptcha.id')}}\"></script>",
            "<script>",
            "\\$(\"button\").each(function(index) {",
            "  \\$(this).prop('disabled', true);",
            "});",
            "grecaptcha.ready(function() {",
            "grecaptcha.execute(\"{{config('recaptcha.id')}}\", {action: 'homepage'}).then(function(token) {",
            "var recaptchaResponse = document.getElementById('recaptchaResponse');",
            "recaptchaResponse.value = token;",
            "\\$(\"button\").each(function(index) {",
            "    \\$(this).prop('disabled', false);",
            "});",
            "});",
            "});",
            "</script> <input type=\"hidden\" value=\"\" name=\"google_recaptcha_token\" id=\"recaptchaResponse\">",
            "<style>",
            ".grecaptcha-badge {",
            "    visibility: hidden;",
            "}",
            "</style>"
        ]
    },
    "google recaptcha php": {
        "prefix": "google recaptcha validators",
        "scope": "php",
        "body": [
            "if (\\config('app.env') == 'production') {",
            "\\$validators['google_recaptcha_token'] = ['required', 'string', new \\App\\Rules\\MyValidatorsGoogleRecapchaV3()];",
            "}"
        ]

    },
    "Carbon diff php": {
        "prefix": "date 時間差 Carbon",
        "scope": "php",
        "body": [
            "\\$start = \\Carbon::parse(\\$rs->${1:closedate});",
            "//\\$diffHours = \\Carbon::now()->diffInHours(\\$start, false);",
            "\\$this->data['diff'] = \\$start->diff(\\Carbon::parse(now()))->format('%d天 %h小時');"
        ]
    },
    "Carbon diff html": {
        "prefix": "date 時間差 Carbon",
        "scope": "html,blade",
        "body": [
            "{{Carbon::parse($rs->${1:closedate})->diff(Carbon::parse(now()))->format('%d天 %h小時')}}"
        ]
    },
    "Carbon addSeconds ": {
        "prefix": "date 系統時間加多少秒",
        "scope": "html,blade",
        "body": [
            "\\$now = \\Carbon::now();",
            "\\$nowPlus = \\$now->addSeconds(50);"
        ]
    },
    "if env debug ": {
        "prefix": "is debug env local",
        "scope": "html,blade",
        "body": [
            "@if (config('app.env') === 'testing' || config('app.env') === 'dev')",
            "",
            "@endif"
        ]
    },
    "Form::select": {
        "prefix": "Form select",
        "scope": "html,blade",
        "body": [
            "{!! Form::select('${1:limit}[]', \\$data['${1:limit}s'],\\$data['${1:lmit}'], ['style'=>\"width:100%\",'multiple'=>true,'class' => 'form-control','select2'=>\"true\"]) !!}"
        ]
    },
    "view exist": {
        "prefix": "view exist",
        "scope": "html,blade",
        "body": [
            "@if(view()->exists('p/'.\\$data['kind']))",
            "@include('p/'.$data['kind'])",
            "@endif"
        ]
    },
    "style css": {
        "prefix": "style css link href",
        "scope": "html,blade",
        "body": [
            "<link href=\"{{asset('css/${1:css}.css')}}\" rel=\"stylesheet\" type=\"text/css\" />"
        ]
    },
    "html_Ceil": {
        "prefix": "math Ceil 無條件去進",
        "scope": "html,blade",
        "body": [
            "{{Ceil(${1:123})}}"
        ]
    },
    "html_floor": {
        "prefix": "math floor 無條件去小數",
        "scope": "html,blade",
        "body": [
            "{{floor(${1:123})}}"
        ]
    },
    "html_round": {
        "prefix": "math round 四捨五入",
        "scope": "html,blade",
        "body": [
            "{{round(${1:123})}}"
        ]
    },
    "mobile": {
        "prefix": "mobile",
        "scope": "html,blade",
        "body": [
            "<input type=\"tel\" required  class=\"form-control\" name=\"${1:mobile}\"  ",
            "value=\"{{\\$data['${1:mobile}']}}\"  placeholder=\"ex 09123456789\" pattern=\"09[1-8][0-9]([-|s]?)[0-9]{3}\\1[0-9]{3}\"/>"
        ]
    },
    "email": {
        "prefix": "email",
        "scope": "html,blade",
        "body": [
            "<input type=\"email\" class=\"form-control\" name=\"${1:email}\" value=\"{{\\$data['${1:email}']}}\" required requiredclass=\"[1,EMAIL]\" placeholder=\"ex <EMAIL>\" />"
        ]
    },
    "code": {
        "prefix": "code all",
        "scope": "php",
        "body": [
            "if ('code' != $v && ('' != \\$this->data['${1:kind_id}'] && (\\$this->data['code'] != \\PT::code(\\$this->data['${1:kind_id}'])))) {",
            "     throw new \\CustomException(__('檢核碼錯誤'));",
            "}"
        ]
    },
    "codeid": {
        "prefix": "code id ",
        "scope": "php",
        "body": [
            "if (($request->input('idcode') != \\PT::code(\\$edit))) {",
            "     throw new \\CustomException(__('檢核碼錯誤'));",
            "}"
        ]
    },
    "codeidhtml": {
        "prefix": "code id",
        "scope": "html,blade",
        "body": [
            "&idcode={{PT::code(\\$rs->${1:id})}}"
        ]
    },
    "codephp": {
        "prefix": "code",
        "scope": "html,blade",
        "body": [
            "&code={{PT::code(\\$rs->${1:id})}}"
        ]
    },
    "v-model": {
        "prefix": "v-model",
        "scope": "php",
        "body": [
            "'v-model'=>'inputs.${1:id}',"
        ]
    },
    "select open window": {
        "prefix": "select  db open window",
        "scope": "html,blade",
        "body": [
            "<input type=\"text\" readonly class=\"form-control\" name=\"${1:member}_id_xxx\" value=\"{{\\$data['${1:member}_id']!='' ? DB::table(\"${1:member}\")->select('name')->where(['id'=>\\$data['${1:member}_id']])->first()->name : ''}}\" required v-model=\"inputs.${1:member_id}_id\" placeholder=\"\" onclick=\"PF_WindowOpen('請選擇','{{ url('/')}}/admin/select?action=${1:member}&idf=${1:member}_id',500,500)\" />",
            "{{ Form::hidden(\"${1:member}_id\", \\$data[\"${1:member}_id\"] ) }}"
        ]
    },
    "abort": {
        "prefix": "abort 403",
        "scope": "php",
        "body": [
            "abort(${1:403});"
        ]
    },
    "date week": {
        "prefix": "date week",
        "scope": "php",
        "body": [
            "\\$weekdayMap = [",
            "    0 => '日',",
            "    1 => '一',",
            "    2 => '二',",
            "    3 => '三',",
            "    4 => '四',",
            "    5 => '五',",
            "    6 => '六',",
            "];",
            "",
            "\\$date = \\Carbon::parse(\\$${1:date1});",
            "\\$weekday = \\$date->dayOfWeek; // 取得星期幾，回傳值為 0~6，分別代表星期日到星期六",
            "\\$weekdayInChinese = \\$weekdayMap[\\$weekday];// 輸出：星期X"
        ]
    },
    "image": {
        "prefix": "img photo fancybox 多照片幻燈片",
        "scope": "html,blade",
        "body": [
            "<link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.css\" />",
            "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js\"></script>",
            "  <a href=\"{{ url('/') }}/images/taskout/{{\\$data['${1:img}']}}\" data-fancybox=\"group_name\" data-caption=\"Caption #1\">",
            "            {{",
            "               Html::myUIImage([",
            "           'folder' => \"images/${2:product}\",",
            "           'filename' => \\$data['${1:img}'],",
            "           'width' =>100,",
            "           'height' => 100,",
            "               ])",
            "       }}",
            "        </a>"
        ]
    },
    "array": {
        "prefix": "array in exist",
        "scope": "html,blade",
        "body": [
            "@if(in_array(\\$data['status'], ['1', '2']))",
            "@endif"
        ]
    },
    "rows>map>implode": {
        "prefix": "collect db implode 某一個欄位值串在一起1,2,3,4 ",
        "scope": "php",
        "body": [
            "\\$ids = \\$rows->map(function (\\$rs) {",
            "  return \\$rs->${1:id};",
            "})->implode(',');"
        ]
    },
    "header": {
        "prefix": "header",
        "scope": "php",
        "body": [
            "\\$headers = [",
            "            'Content-Type' => 'application/zip',",
            "            'Cache-Control' => 'no-cache, no-store, must-revalidate',",
            "            'Pragma' => 'no-cache',",
            "            'Expires' => '0',",
            "            'Content-Disposition' => 'attachment; filename='.basename(\\$zipFilePath),",
            "];"
        ]
    },
    "raw": {
        "prefix": "raw body",
        "scope": "php",
        "body": [
            "\\$this->data['body'] = \"${1:ok}\"",
            " return view('raw.index', [",
            "     'data' => \\$this->data,",
            " ]);"
        ]
    },
    "d role check": {
        "prefix": "check auth role function-checkRoleRows+$this->checkRoleRows",
        "scope": "php",
        "body": [
            "\\$rows = \\$this->checkRoleRows(\\$rows);",
            "   public function checkRoleRows(\\$rows)",
            "   {",
            "        //role管理者[999],",
            "        if (\\Gate::check('isAdminRole', ['999']) == false) {",
            "            \\$rows->myWhere('${1:kind}_id|ININT', \\Auth::guard('admin')->user()->${1:kind}_ids, '管理權限', 'Y');",
            "        }",
            "        return \\$rows;",
            "    }"
        ]
    },
    "role": {
        "prefix": "check role $this->checkRoleRows",
        "scope": "php",
        "body": [
            "\\$rows = \\$this->checkRoleRows(\\$rows);"
        ]
    },
    "validators XX": {
        "prefix": "validators ['XX']",
        "scope": "php",
        "body": [
            "\\$validators['${1:kind}'] = ['required']; "
        ]
    },
    "collect db": {
        "prefix": "db collect where first ",
        "scope": "html,blade",
        "body": [
            "{{collect(\\$data['${1:member}rows'])->where('${2:member_id}', $rs->id)->first()->${3:id}}}"
        ]
    },
    "download": {
        "prefix": "download response()->streamDownload",
        "scope": "php",
        "body": [
            "\\$f = \\$file_path . '/' . \\$zipFileName;",
            "return response()->streamDownload(function () use (\\$f) {",
            "    echo \\File::get(\\$f);",
            "}, \\$ratingproject_id . \"-\" . \\Str::random(32) . \".zip\");"
        ]
    },
    "onchange": {
        "prefix": "select onChange",
        "scope": "php",
        "body": [
            "'onChange'=>\"PF_AjaxblockUI('\".url('/').\"/admin/misson/setonline','id=\".\\$rs->id.\"&value=' +this.options[this.selectedIndex].value,null);\","
        ]
    },
    "wire:model": {
        "prefix": "live wire:mode",
        "scope": "php",
        "body": [
            "'wire:model' => '${1:kindhead}_id'"
        ]
    },
    "wire include file": {
        "prefix": "live include file",
        "scope": "php",
        "body": [
            "@livewire('${1:kindhead}'', ['manage_id' => '10002', 'school_id' => '10002', 'kind_ids' => '10034'])"
        ]
    },
    "file upload": {
        "prefix": "file upload 原生",
        "scope": "php",
        "body": [
            "foreach (\\$request->file('files') as \\$k => \\$file) {            ",
            "    \\$file->move(public_path('assets/' . \\$request->input('folder') . \"/\"), \\$file->getClientOriginalName());",
            "}"
        ]
    },
    "mobile1": {
        "prefix": "mobile",
        "scope": "php",
        "body": [
            "if(PF::isMobile()){",
            "",
            "}"
        ]
    },
    "job": {
        "prefix": "job",
        "scope": "php",
        "body": [
            "\\$job = (new \\App\\Jobs\\\\${1:sign}Job(\\$id));",
            "dispatch(\\$job);"
        ]
    },
    "json html": {
        "prefix": "json 物件轉換成文字 json_encode",
        "scope": "blade",
        "body": [
            "{!! json_encode(\\$item, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) !!}"
        ]
    },
    "class": {
        "prefix": "class 建立stdClass",
        "scope": "php",
        "body": ["\\$${1:member} = new \\stdClass();",
            "",
            " \\$${1:member}->id = \"10024\";",
            " \\$${1:member}->title = \"test\";",
            " \\$${1:member}->onsale = \\$onsale;",
        ]
    },
    "viewstring": {
        "prefix": "view config view to string",
        "scope": "php",
        "body": [
            "use Illuminate\\Support\\Facades\\Blade;",
            "\\$rs->count = \\$count;",
            "\\$prompt = Blade::render(\\config('config.prompt_title'), get_object_vars(\\$rs));",
        ]
    },
    "ai validators": {
        "prefix": "ai validators",
        "scope": "php",
        "body": [
            "${1:} $validators['欄位']",
        ]
    },
    "api": {
        "prefix": "api return",
        "scope": "php",
        "body": [
            "\\$this->jsondata['resultcode'] = 0;",
            "\\$this->jsondata['resultmessage'] = '更新成功';",
            "return \\$this->apiResponse(\\$this->jsondata);",
        ]
    },





}