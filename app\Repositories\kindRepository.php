<?php

namespace App\Repositories;

use DB;
use App\Models\kind;

class kindRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(kind $model)
    {
        $this->model = $model;
    }

    public function select($field = '*')
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    public function create($inputs)
    {
        \Cache::flush('kind');

        return parent::create($inputs);
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        \Cache::flush('kind');

        return parent::update($inputs, $id, 'id');
    }

    public function deleteIds($ids)
    {
        \Cache::flush('kind');
        parent::deleteIds($ids);
        // $rows=$this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // $rows->get()->each(function ($rs) {
        //     $path=storage_path($this->model->table.'/'.$rs->img.".json");
        //     //$path=public_path('images/'.$this->model->table.'/'.$rs->img);
        //     if (\File::exists($path)) {
        //        \File::delete($path);
        //     }
        //     $rs->delete();
        // });
    }
}
