<?php

namespace Tests\Feature\api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Product;
use App\Models\adminuser;
use Illuminate\Foundation\Testing\WithFaker;

class houseflowTest extends \Tests\Feature\api\baseTest {
    use RefreshDatabase;
    use WithFaker;

    public $adminuser;
    public $product;

    public function setUp(): void {
        parent::setUp();

        // 建立測試用管理員
        $this->adminuser = \App\Models\adminuser::factory()->admin()->create();

        // 建立測試用產品資料
        $this->product = \App\Models\product::factory()->create([
            'number' => 'TEST123456',
            'productkind' => 2,
            'producttitle' => '測試房屋物件',
            'city1title' => '台北市',
            'city2title' => '大安區',
            'totalupset' => 8000000,
        ]);
    }

    /**
     * 測試新增房屋物件功能
     *
     * @return void
     */
    public function test_新增房屋物件_store() {
        $requestData = [
            'user_code' => 'U12345',
            'branch_code' => 'BR001',
            'commission_date_start' => '2024-01-01',
            'commission_date_end' => '2024-12-31',
            'number' => 'NEW123456',
            'producttitle' => '豪華公寓',
            'city1title' => '台北市',
            'city2title' => '大安區',
            'address' => '忠孝東路四段100號',
            'totalupset' => 8000000,
            'pattern' => '公寓',
            'floor_start' => '3',
            'floor_end' => '5',
            'storey' => '12',
            'houseage' => 15,
            'room_count' => '3',
            'living_room_count' => '1',
            'hall_count' => '2',
            'bathroom_count' => '2',
            'balcony_count' => '1',
            'buildname' => '信義豪宅',
            'parkingmode' => '地下室車位',
            'pingtotalnumberof' => 39,
            'mainlawnestablishment' => '25.5',
            'attachedtolawnestablishment' => '5.2',
            'postulateping' => '8.3',
            'carping' => '5.0',
            'other_ping' => '2.5',
            'stakeholdersfloor' => '10.5',
            'memo' => '採光佳，交通便利',
            'layout_image' => 'layout.jpg',
            'property_image1' => 'image1.jpg',
            'property_image2' => 'image2.jpg',
            'property_image3' => 'image3.jpg',
            'virtual_tour_video' => 'tour_video.mp4'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
        ])->post('/api/houseflow/store', $requestData);

        //echo $response->getContent();
        $this->checkJson($response);
        $response->assertStatus(200);
        $response->assertJsonPath('resultcode', 0);
        $response->assertJsonPath('resultmessage', '新增成功');

        // 檢查資料庫是否正確新增資料
        $this->assertDatabaseHas('product', [
            'number' => 'NEW123456',
            'producttitle' => '豪華公寓',
            'productkind' => 2,
            'city1title' => '台北市',
            'city2title' => '大安區',
            'totalupset' => 8000000,
        ]);
    }

    /**
     * 測試更新房屋物件功能
     *
     * @return void
     */
    public function test_更新房屋物件_store() {
        $requestData = [
            'user_code' => 'U54321',
            'branch_code' => 'BR002',
            'commission_date_start' => '2024-02-01',
            'commission_date_end' => '2024-11-30',
            'number' => 'TEST123456', // 使用現有的編號進行更新
            'producttitle' => '更新後的豪華公寓',
            'city1title' => '新北市',
            'city2title' => '板橋區',
            'address' => '文化路二段200號',
            'totalupset' => 9500000,
            'pattern' => '大樓',
            'floor_start' => '5',
            'floor_end' => '7',
            'storey' => '15',
            'houseage' => 10,
            'room_count' => '4',
            'living_room_count' => '2',
            'hall_count' => '3',
            'bathroom_count' => '3',
            'balcony_count' => '2',
            'buildname' => '板橋豪宅',
            'parkingmode' => '平面車位',
            'pingtotalnumberof' => 45,
            'mainlawnestablishment' => '30.0',
            'attachedtolawnestablishment' => '6.0',
            'postulateping' => '9.0',
            'carping' => '6.0',
            'other_ping' => '3.0',
            'stakeholdersfloor' => '12.0',
            'memo' => '更新後的物件特色',
            'layout_image' => 'new_layout.jpg',
            'property_image1' => 'new_image1.jpg',
            'property_image2' => 'new_image2.jpg',
            'virtual_tour_video' => 'new_tour_video.mp4'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->adminuser->api_token,
        ])->post('/api/houseflow/store', $requestData);

        //echo $response->getContent();
        $this->checkJson($response);
        $response->assertStatus(200);
        $response->assertJsonPath('resultcode', 0);
        $response->assertJsonPath('resultmessage', '更新成功');

        // 檢查資料庫是否正確更新資料
        $this->assertDatabaseHas('product', [
            'number' => 'TEST123456',
            'producttitle' => '更新後的豪華公寓',
            'productkind' => 2,
            'city1title' => '新北市',
            'city2title' => '板橋區',
            'totalupset' => 9500000,
        ]);
    }
}
