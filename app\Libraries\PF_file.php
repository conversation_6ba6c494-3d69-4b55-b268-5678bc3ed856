<?php

namespace App\Libraries;

/***
"功能名稱":"共用類別-檔案函式",
"建立時間":"2022-01-18 13:19:49",
***/
trait PF_file
{
    public static function fileDel($folder, $files)
    {
        $array = explode(',', $files);
        $storage = base_path('public/');
        for ($x = 0; $x < count($array); ++$x) {
            $filename = $storage.$folder.'/'.$array[$x];
            if (\File::exists($filename)) {
                \File::delete($filename);
            }
        }
    }
}
