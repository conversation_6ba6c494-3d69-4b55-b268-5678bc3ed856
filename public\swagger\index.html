<!-- HTML for static distribution bundle build -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>API document</title>
    <meta name="robots" content="noarchive,noindex,nofollow" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="private" />
    <meta http-equiv="Expires" content="0" />
    <link rel="stylesheet" type="text/css" href="./swagger-ui.css" />
    <link rel="icon" type="image/png" href="./favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="./favicon-16x16.png" sizes="16x16" />
    <style>
      html
      {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }

      *,
      *:before,
      *:after
      {
        box-sizing: inherit;
      }

      body
      {
        margin:0;
        background: #fafafa;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script src="../Scripts/jquery.js" charset="UTF-8"> </script>
    <script src="./swagger-ui-bundle.js" charset="UTF-8"> </script>
    <script src="./swagger-ui-standalone-preset.js" charset="UTF-8"> </script>
    <script>
       function gup(name, url) {
            if (!url) url = location.href;
            name = name.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
            var regexS = "[\\?&]" + name + "=([^&#]*)";
            var regex = new RegExp(regexS);
            var results = regex.exec(url);
            return results == null ? null : results[1];
        }

    window.onload = function() {
      // Begin Swagger UI call region
      const ui = SwaggerUIBundle({
        url: "../api/doc",
        dom_id: '#swagger-ui',
        defaultModelsExpandDepth: -1,
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        onComplete: function () {
          let spec = ui.specSelectors.specJson().toJS();
          console.log(spec);
          let servers = spec.servers.filter((item) => {
            console.log(item);
            if (item.url.indexOf(window.location.host)>-1){
              return true;
            }
            return false;
          });
          spec.servers = servers;

          ui.specActions.updateJsonSpec(spec);
        },
        requestInterceptor: (req) => {
          if (! req.loadSpec) {
            const urlParams = new URLSearchParams(window.location.search);
            const Bearer = urlParams.get('Bearer');
            console.log(Bearer);

            if(Bearer!=null && Bearer!="" && Bearer!="null"){
              // var basic= gup('basic', window.top.location);

              req.headers.Authorization = "Bearer "+Bearer;
            }
          }
          return req;
        }
      });
      // End Swagger UI call region

      window.ui = ui;
    };
  </script>



  </body>
</html>
