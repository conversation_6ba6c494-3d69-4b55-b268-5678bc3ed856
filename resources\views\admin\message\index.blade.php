
@extends('admin.layouts.master')


@section('css')
@endsection

@section('js')
@endsection
@section('nav')
{!!$data['nav']!!}
@endsection


@section('content')

{!!Session::get('msg')!!}


<div class="container-fluid p-1">
    <form name="SearchoForm" class="form-inline" method="post" language="javascript" action="{{request()->url()}}" onsubmit="return SearchoForm_onsubmit(this);">
        @include('admin.layouts.search', [])
    </form>
</div>


<div class="table-responsive">
    <table class="table table-striped table-hover  table-bordered table-fixed">
    <!--排序的參數-->
    <form name="SortoForm" method="post">    
    @include('admin.layouts.hidden', ['method'=>'SortoForm','data'=>$data])     
        <thead>
    <tr  valign="top" align="left">
    
        <th  align="center">            
                        @if (Auth::guard('admin')->user()->status=="999")
                        <div class="form-row align-items-center">
                            <div class="col-auto">
                                <button type="button" class="btn btn-danger"
                                    onclick="if (confirm('確定要刪除？')==false){return false;};document.forms['oForm'].action='{{request()->url()}}/destroy';document.forms['oForm'].submit();">刪除</button>
                            </div>
                            <div class="col-auto">
                                <label class="h6 small"
                                    onClick="if (this.innerHTML=='全選'){try{checkAll(jQuery($('input[name=\'del[]\']')))}catch(e){alert('目前無可刪除資料');return false;};this.innerHTML='全不選';}else{try{uncheckAll($('input[name=\'del[]\']'))}catch(e){};this.innerHTML='全選';}">全選</label>
                            </div>
                        </div>
                        @endif
        </th>
        <th width="" id="kind">留言類別</th>
    <th width="" id="name">姓名</th>
    <th width="" id="email">電子信箱</th>
    <th width="" id="sex">性別</th>
    <th width="" id="title">標題</th>
    <th width="" id="created_at">建立時間</th>
    
    </tr>
    </thead>

    </form>


    <!--傳給下一頁的參數-->
    <form  method="post" language="javascript" name="oForm" action="{{request()->url()}}" >
    
    @include('admin.layouts.hidden', ['method'=>'oForm','data'=>$data]) 
        <tbody>            
            @foreach ($data['rows'] as $rs)   

            <tr>

    <td valign="top"  align="center"  width="120">
        <div class="form-row align-items-center">
                
            
                <div class="col-auto">
                  <button  type="submit" class="btn btn-info" onclick="javascript:form.action='{{request()->url()}}/edit?edit={{ $rs->id }}';">
                  編輯
                  </button>
                </div>
            
                    <div class="col-auto">
                    @if (Auth::guard('admin')->user()->status=="999")            
                        <input type="checkbox" name="del[]" class="all" value="{{$rs->id}}">            
                    @endif
                    </div>
                    
        </div>
    </td>

    <td> 
    
        <a href="#" 
        onclick="PF_formSearch('message.kind|S','{{$rs->kind}}');return false;">
        {{PF::xmlSearch($xmlDoc,"//參數設定檔/留言類別/KIND/傳回值","資料",$rs->kind)}}
            </a>
              
     </td>
    
    <td> 
    
    {{$rs->name}}
      
     </td>
    
    <td> 
    	  
    <a href="mailto:{{ $rs->email}}">{{ $rs->email}}</a> 
      
     </td>
    
    <td> 
    
            {{$rs->sex}}		
          
     </td>
    
    <td> 
    
    {{$rs->title}}
      
     </td>
    
    <td> 
    	  
    {{PF::formatDate($rs->created_at)}}
      
     </td>
    
			</tr>
            @endforeach

		</tbody>
	</form>


    </table>

    @if (count($data['rows'] )==0)
        No Data 
    @endif
    {{ method_exists ($data['rows'],'links') ? $data['rows']->links('layouts.paginate') :"" }}
        
        
</div>
@endsection

