<?php

namespace App\Repositories;

use App\Models\failed_jobs;
use DB;
use PF;

class failed_jobsRepository extends Repository
{
    public $model;
    public $data;

    public function __construct(failed_jobs $model)
    {
        $this->model = $model;
    }
    public function select($field="*")
    {
        $rows = DB::table($this->model->table);
        $rows->selectRaw($field);

        return $rows;
    }

    public function view($rows)
    {
        $rows->join('diversionurl', 'diversionurl.id', '=', 'diversionlog.diversionurl_id');
        return $rows;
    }


    public function create($inputs)
    {

        return parent::create($inputs);
    }

    public function update($inputs, $id, $attribute = 'id')
    {
        return  parent::update($inputs,$id,'id') ;      
    }

    public function deleteIds($ids)
    {
    //     $this->failed_jobsxxRepo = app(\App\Repositories\failed_jobsxxRepository::class);
    //     $rows = $this->failed_jobsxxRepo->select(null);
    //     $rows->myWhere('failed_jobs_id|ININT', $ids, 'failed_jobs_id', 'Y');
    //     $rows->delete();

        
    //    \DB::delete('delete failed_jobssign from  failed_jobssign INNER JOIN failed_jobsxx ON (failed_jobsxx.id=failed_jobssign.failed_jobsxx_id) where failed_jobsxx.failed_jobs_id in (?)',[$ids]);
        
        parent::deleteIds($ids);
      

        // $rows=$this->model->whereIn($this->model->primaryKey, explode(",",$ids));
        // $rows->get()->each(function ($rs) {
        //     $path=storage_path($this->model->table.'/'.$rs->img.".json");            
        //     //$path=public_path('images/'.$this->model->table.'/'.$rs->img);
        //     if (\File::exists($path)) {
        //        \File::delete($path);
        //     }            
        //     $rs->delete();
        // });
        
    }
}
