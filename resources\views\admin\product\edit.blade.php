@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
    {!! $data['nav'] !!}
@endsection

@section('content')


    <div class="card">
        <div class="card-body">

            <form name="oForm" id="oForm" class="needs-validation" method="post" language="javascript"
                action="{{ URL::to('admin/product/store') }}" ENCTYPE="multipart/form-data" novalidate>
                <!---->

                <div align="center">
                    <button type="submit" class="btn btn-success">確定</button>
                    <button type="reset" class="btn btn-secondary">取消</button>
                    <!--<button type="submit" class="btn btn-primary" onclick="oForm.edit.value='';">複製一筆</button>-->
                    <button type="reset" class="btn btn-secondary"
                        onClick="javascript:window.history.go(-1)">回上一頁</button>

                </div>
                <script language="JavaScript">
                    $(function() {
                        var ul = $("ul[role='tablist']");
                        $("div[class^=tab-pane]").each(function(i) {
                            var title = $(this).attr('id');
                            var active = $(this).attr('class') == 'tab-pane fade show active' ? 'active' : '';
                            ul.append('<li><a class="nav-item nav-link ' + active + '" id="name-' + title +
                                '"  data-toggle="tab" role="tab"  href="#' + title + '">' + title + '</a></li>');

                        });
                    });
                </script>

                <ul class="nav nav-tabs" id="nav-tab" role="tablist">

                </ul>


                <div class="tab-content">
                    <div id="資訊" class="tab-pane fade show active">

                        <div class="form-group row">
                            <label class="col-md-2">案號：<font class="text-danger">*</font></label>
                            <div class="col-md-10">


                                <input type="text" class="form-control" name="number" title="案號" required
                                    requiredclass="required[1,TEXT]" value="{{ $data['number'] }}" size="40"
                                    unique="{{ $data['number'] }}" table="product" />

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-2">標題<font class="text-danger p-1"></font></label>
                            <div class="col-md-10">
                                <input type="text" class="form-control" name="producttitle"
                                    value="{{ $data['producttitle'] }}" title="標題" requiredclass="required[0,TEXT]"
                                    placeholder="" />
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-2">上下架：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                {{ Form::myUIXml([
                                    'xmldoc' => $data['xmldoc'],
                                    'type' => 'Radio',
                                    'title' => '上下架',
                                    'node' => '//參數設定檔/上下架/KIND',
                                    'name' => 'online',
                                    'value' => $data['online'],
                                    'linecount' => 5,
                                    'requiredclass' => 'required[0,TEXT]',
                                
                                    //'onClick'=>"if (this.value==1){\$('.species').show();}else{\$('.species').hide();}",
                                ]) }}



                            </div>
                        </div>




                        <div class="form-group row">
                            <label class="col-md-2">放置位置：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                {{ Form::hidden('location', '') }}


                                {{ Form::myUIXml([
                                    'xmldoc' => $data['xmldoc'],
                                    'type' => 'checkbox',
                                    'title' => '放置位置',
                                    'node' => '//參數設定檔/放置位置/KIND',
                                    'name' => 'location',
                                    'value' => $data['location'],
                                    'linecount' => 5,
                                    'requiredclass' => 'required[0,TEXT]',
                                
                                    //'onClick'=>"if (this.value==1){\$('.species').show();}else{\$('.species').hide();}",
                                ]) }}
                                {{ Form::hidden('locationadmin', '') }}
                                {{ Form::myUIXml([
                                    'xmldoc' => $data['xmldoc'],
                                    'type' => 'checkbox',
                                    'title' => '管理者放置位置',
                                    'node' => '//參數設定檔/管理者放置位置/KIND',
                                    'name' => 'locationadmin',
                                    'value' => $data['locationadmin'],
                                    'linecount' => 5,
                                    'requiredclass' => 'required[0,TEXT]',
                                
                                    //'onClick'=>"if (this.value==1){\$('.species').show();}else{\$('.species').hide();}",
                                ]) }}
                                <br>
                                <font class='text-danger'>首頁:放置位置:熱門推薦;上下架:上架</font>

                            </div>
                        </div>




                        <div class="form-group row">
                            <label class="col-md-2">主建坪：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="mainlawnestablishment" title="主建坪"
                                    requiredclass="required[0,TEXT]" value="{{ $data['mainlawnestablishment'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">縣市：<font class="text-danger">*</font></label>
                            <div class="col-md-10">

                                <div class="input-group">


                                    {{ Form::myUISelectMulti([
                                        [
                                            'formname' => 'oForm',
                                            'title' => '縣市',
                                            'sql' => 'select city1title, city1title from city1 order by sortnum desc',
                                            'name' => 'city1title',
                                            'value' => $data['city1title'],
                                            'requiredclass' => 'required[0,TEXT]',
                                        ],
                                        [
                                            'formname' => 'oForm',
                                            'title' => '鄉鎮',
                                            'url' => url('/api/dependentdropdown/city2'),
                                            'name' => 'city2title',
                                            'value' => $data['city2title'],
                                            'requiredclass' => 'required[0,TEXT]',
                                        ],
                                    ]) }}
                                </div>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-2">地址：<font class="text-danger">*</font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="address" title="地址" required
                                    requiredclass="required[1,TEXT]" value="{{ $data['address'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">附建坪數：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="attachedtolawnestablishment" title="附建坪數"
                                    requiredclass="required[0,TEXT]" value="{{ $data['attachedtolawnestablishment'] }}"
                                    size="40" />

                            </div>
                        </div>




                        <div class="form-group row">
                            <label class="col-md-2">增建坪：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="additionalping" title="增建坪"
                                    requiredclass="required[0,TEXT]" value="{{ $data['additionalping'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">型態：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="pattern" title="型態"
                                    requiredclass="required[0,TEXT]" value="{{ $data['pattern'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">公設坪：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="postulateping" title="公設坪"
                                    requiredclass="required[0,TEXT]" value="{{ $data['postulateping'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">大樓名稱：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="buildname" title="大樓名稱"
                                    requiredclass="required[0,TEXT]" value="{{ $data['buildname'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">公告建坪：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="noticelawnestablishment" title="公告建坪"
                                    requiredclass="required[0,TEXT]" value="{{ $data['noticelawnestablishment'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">屋齡：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="houseage" title="屋齡"
                                    requiredclass="required[0,TEXT]" value="{{ $data['houseage'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">持分地坪：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="stakeholdersfloor" title="持分地坪"
                                    requiredclass="required[0,TEXT]" value="{{ $data['stakeholdersfloor'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">公設比：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="postulatemorethan" title="公設比"
                                    requiredclass="required[0,TEXT]" value="{{ $data['postulatemorethan'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">總坪數：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="number" class="form-control" step="0.01" name="pingtotalnumberof"
                                    title="總坪數" requiredclass="required[0,FLOAT]"
                                    value="{{ $data['pingtotalnumberof'] }}" maxlength="5" size="5" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">樓高：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="storey" title="樓高"
                                    requiredclass="required[0,TEXT]" value="{{ $data['storey'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">坪單價：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="floorprice" title="坪單價"
                                    requiredclass="required[0,TEXT]" value="{{ $data['floorprice'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">架構：<font class="text-danger"></font></label>
                            <div class="col-md-10">
                                <input type="text" class="form-control" name="architecture"
                                    value="{{ $data['architecture'] }}" title="架構" requiredclass="required[0,TEXT]"
                                    placeholder="" />


                            </div>
                        </div>






                        <div class="form-group row">
                            <label class="col-md-2">管理方式：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="management" title="管理方式"
                                    requiredclass="required[0,TEXT]" value="{{ $data['management'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">停車方式：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="parkingmode" title="停車方式"
                                    requiredclass="required[0,TEXT]" value="{{ $data['parkingmode'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">費用：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="fees" title="費用"
                                    requiredclass="required[0,TEXT]" value="{{ $data['fees'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">交通機能：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="transportfunction" title="交通機能"
                                    requiredclass="required[0,TEXT]" value="{{ $data['transportfunction'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">面臨路寬：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="facingthelaneis" title="面臨路寬"
                                    requiredclass="required[0,TEXT]" value="{{ $data['facingthelaneis'] }}"
                                    size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">學區：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="schooldistrict" title="學區"
                                    requiredclass="required[0,TEXT]" value="{{ $data['schooldistrict'] }}"
                                    size="40" />

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-2">捷運路線：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="mrtland" title="捷運路線"
                                    requiredclass="required[0,TEXT]" value="{{ $data['mrtland'] }}" size="40" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">捷運站名：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="text" class="form-control" name="mrtstation" title="捷運站名"
                                    requiredclass="required[0,TEXT]" value="{{ $data['mrtstation'] }}" size="40" />

                            </div>
                        </div>

                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">法拍屋</h3>
                            </div>
                            <div class="card-body">
                                <di class="form-group row">
                                    <label class="col-md-2">法院：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="court" title="法院"
                                            requiredclass="required[0,TEXT]" value="{{ $data['court'] }}"
                                            size="40" />

                                    </div>
                                </di v>

                                <div class="form-group row">
                                    <label class="col-md-2">校對日：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="proofreadingday" title="校對日"
                                            requiredclass="required[0,TEXT]" value="{{ $data['proofreadingday'] }}"
                                            size="40" />

                                    </div>
                                </div>





                                <div class="form-group row">
                                    <label class="col-md-2">投標日：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="tenderdate"
                                            value="{{ $data['tenderdate'] }}" title="title"
                                            requiredclass="required[0,TEXT]" placeholder="" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">債務人：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="debtor" title="債務人"
                                            requiredclass="required[0,TEXT]" value="{{ $data['debtor'] }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">拍次：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="beattime" title="拍次"
                                            requiredclass="required[0,TEXT]" value="{{ $data['beattime'] }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">總底價：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="number" class="form-control" step="0.01" name="totalupset"
                                            title="總底價" requiredclass="required[0,FLOAT]"
                                            value="{{ $data['totalupset'] }}" maxlength="5" size="5" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">點交否：<font class="text-danger"></font></label>
                                    <div class="col-md-10">
                                        <input type="text" class="form-control" name="nocross_point"
                                            value="{{ $data['nocross_point'] }}" placeholder="" />



                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">保證金：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="margin" title="保證金"
                                            requiredclass="required[0,TEXT]" value="{{ $data['margin'] }}"
                                            size="40" />

                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-2">拍後增值：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="aftermakingvalue_added"
                                            title="拍後增值" requiredclass="required[0,TEXT]"
                                            value="{{ $data['aftermakingvalue_added'] }}" size="40" />

                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-md-2">查封人：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="sealedhuman" title="查封人"
                                            requiredclass="required[0,TEXT]" value="{{ $data['sealedhuman'] }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">投標人數：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="thenumberofbidders"
                                            title="投標人數" requiredclass="required[0,TEXT]"
                                            value="{{ $data['thenumberofbidders'] }}" size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">得標價格：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="thebidprice" title="得標價格"
                                            requiredclass="required[0,TEXT]" value="{{ $data['thebidprice'] }}"
                                            size="40" />

                                    </div>
                                </div>





                                <div class="form-group row">
                                    <label class="col-md-2">土地公現：<font class="text-danger"></font></label>
                                    <div class="col-md-10">
                                        <input type="text" class="form-control" name="landprice"
                                            value="{{ $data['landprice'] }}" title="土地公現"
                                            requiredclass="required[0,TEXT]" placeholder="" />


                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">加價金額：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="increasetheamountof"
                                            title="加價金額" value="{{ $data['increasetheamountof'] }}" size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">土地地址：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="landaddress" title="土地地址"
                                            requiredclass="required[0,TEXT]" value="{{ $data['landaddress'] }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">公告現值：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="currentvalues" title="公告現值"
                                            requiredclass="required[0,TEXT]" value="{{ $data['currentvalues'] }}"
                                            size="40" />

                                    </div>
                                </div>




                                <div class="form-group row">
                                    <label class="col-md-2">土地增值金額1：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="landvalue1" title="土地增值金額1"
                                            requiredclass="required[0,TEXT]" value="{{ $data['landvalue1'] }}"
                                            size="40" />

                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-2">土地增值金額2：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="landvalue2" title="土地增值金額2"
                                            requiredclass="required[0,TEXT]" value="{{ $data['landvalue2'] }}"
                                            size="40" />

                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-2">得標人：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="bidwere" title="得標人"
                                            requiredclass="required[0,TEXT]" value="{{ $data['bidwere'] }}"
                                            size="40" />

                                    </div>
                                </div>

                            </div>
                        </div>













                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">中古屋</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group row">
                                    <label class="col-md-2">用戶代碼：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="user_code" title="用戶代碼"
                                            requiredclass="required[0,TEXT]" value="{{ $data['user_code'] ?? '' }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">分店代碼：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="branch_code" title="分店代碼"
                                            requiredclass="required[0,TEXT]" value="{{ $data['branch_code'] ?? '' }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">委託日期(起)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="date" class="form-control" name="commission_date_start"
                                            title="委託日期(起)" requiredclass="required[0,TEXT]"
                                            value="{{ $data['commission_date_start'] ?? '' }}" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">委託日期(迄)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="date" class="form-control" name="commission_date_end"
                                            title="委託日期(迄)" requiredclass="required[0,TEXT]"
                                            value="{{ $data['commission_date_end'] ?? '' }}" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">樓層(起)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="floor_start" title="樓層(起)"
                                            requiredclass="required[0,TEXT]" value="{{ $data['floor_start'] ?? '' }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">樓層(迄)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="text" class="form-control" name="floor_end" title="樓層(迄)"
                                            requiredclass="required[0,TEXT]" value="{{ $data['floor_end'] ?? '' }}"
                                            size="40" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">格局(房)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="number" class="form-control" name="room_count" title="格局(房)"
                                            requiredclass="required[0,INT]" value="{{ $data['room_count'] ?? 0 }}"
                                            min="0" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">格局(廳)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="number" class="form-control" name="living_room_count"
                                            title="格局(廳)" requiredclass="required[0,INT]"
                                            value="{{ $data['living_room_count'] ?? 0 }}" min="0" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">格局(室)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="number" class="form-control" name="hall_count" title="格局(室)"
                                            requiredclass="required[0,INT]" value="{{ $data['hall_count'] ?? 0 }}"
                                            min="0" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">格局(衛)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="number" class="form-control" name="bathroom_count" title="格局(衛)"
                                            requiredclass="required[0,INT]" value="{{ $data['bathroom_count'] ?? 0 }}"
                                            min="0" />

                                    </div>
                                </div>


                                <div class="form-group row">
                                    <label class="col-md-2">格局(陽台)：<font class="text-danger"></font></label>
                                    <div class="col-md-10">

                                        <input type="number" class="form-control" name="balcony_count" title="格局(陽台)"
                                            requiredclass="required[0,INT]" value="{{ $data['balcony_count'] ?? 0 }}"
                                            min="0" />

                                    </div>
                                </div>


                            </div>
                        </div>



                    </div>
                    <div id="其他" class="tab-pane fade">

                        <div class="form-group row">
                            <label class="col-md-2">流標記錄：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <textarea name="bidsrecords" cols="50" rows="5" class="form-control" title="流標記錄"
                                    requiredclass="required[0,TEXT]">{{ $data['bidsrecords'] }}</textarea>

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">法院筆錄：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <textarea name="courttranscript" cols="50" rows="5" class="form-control" title="法院筆錄"
                                    requiredclass="required[0,TEXT]">{{ $data['courttranscript'] }}</textarea>

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">建物：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <textarea name="buildings" cols="50" rows="5" class="form-control" title="建物"
                                    requiredclass="required[0,TEXT]">{{ $data['buildings'] }}</textarea>

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">他項權利：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <textarea name="hisrightis" cols="50" rows="5" class="form-control" title="他項權利"
                                    requiredclass="required[0,TEXT]">{{ $data['hisrightis'] }}</textarea>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-2">謄本資料：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <textarea name="transcriptinformation" cols="50" rows="5" class="form-control" title="謄本資料"
                                    requiredclass="required[0,TEXT]">{{ $data['transcriptinformation'] }}</textarea>

                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-2">線上賞屋影片：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                <input type="url" class="form-control" name="virtual_tour_video" title="線上賞屋影片"
                                    requiredclass="required[0,TEXT]" value="{{ $data['virtual_tour_video'] ?? '' }}"
                                    placeholder="請輸入影片網址 (例如: https://www.youtube.com/watch?v=...)" />

                            </div>
                        </div>


                        <div class="form-group row">
                            <label class="col-md-2">案件簡介：<font class="text-danger"></font></label>
                            <div class="col-md-10">
                                <textarea name="memo" class="form-control" cols="40" rows="10" title="內容" style="width:100%">{!! $data['memo'] !!}</textarea>

                                <script src="{{ URL::to('ckeditor/ckeditor.js') }}"></script>
                                <script>
                                    CKEDITOR.replace('memo');
                                </script>

                            </div>
                        </div>
                    </div>

                    <div id="照片" class="tab-pane fade">

                        <div class="form-group row">
                            <label class="col-md-2">連結照片：<font class="text-danger"></font></label>
                            <div class="col-md-10">

                                {{ Form::myUIMultiUpload([
                                    'name' => 'img',
                                    'folder' => 'https://www.ebayhouse.com.tw/images/product',
                                    'filename' => $data['img'],
                                    'title' => '連結照片',
                                    'width' => 800,
                                    'height' => 800,
                                    'limitfile' => 'jpg,gif,bmp',
                                    'ismove' => true,
                                ]) }}
                                <!-最大可上傳筆數:5-->

                            </div>
                        </div>

                    </div>
                </div>






                <div align="center">
                    <button type="submit" class="btn btn-success">確定</button>
                    <button type="reset" class="btn btn-secondary">取消</button>
                    <!--<button type="submit" class="btn btn-primary" onclick="oForm.edit.value='';">複製一筆</button>-->
                    <button type="reset" class="btn btn-secondary"
                        onClick="javascript:window.history.go(-1)">回上一頁</button>

                </div>

                @include('admin.layouts.hidden', ['method' => 'EditoForm'])

                {!! Form::hidden('edit', $data['edit']) !!}
                {!! Form::hidden('gobackurl', $data['gobackurl']) !!}


            </form>
        </div>
    </div>

@stop
