<?php

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use DB;
use PF;

class feedbackMail extends Mailable
{
    public $tries = 3;
    use Queueable;
    use SerializesModels;
    public $id;

    // 讓外部可以將參數指定進來
    public function __construct($id)
    {
        $this->id = $id;
    }

    public function build()
    {
        $rows = DB::table('feedback');
        $rows->selectRaw('feedback.*');
        $rows->myWhere('id|N', $this->id, 'key', 'N');
        $rows->orderBy('id', 'desc');
        $rows->limit(1);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }

            $this->from(PF::getFromEmail(), config('config.name'));
            $this->subject(config('config.name').'-聯絡我們');
            $this->view('email.feedback');
            $this->with($this->data);

            $this->to(explode(';', config('config.email')));
        //$this->cc(config('config.email'));
            //$this->bcc(config('config.email'));
        } else {
            throw new \CustomException('No data');
        }

        return $this;
    }

  
}
