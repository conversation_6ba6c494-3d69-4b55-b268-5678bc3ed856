<?php

namespace App\Http\Controllers\layouts;

use DB;
use PF;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
//use Illuminate\Support\Facades\DB;

use App\Repositories\boardRepository;
use App\Repositories\city1Repository;
use App\Repositories\city2Repository;

class searchController extends Controller {
    private $data;

    private $boardRepo;
    private $city1Repo;
    private $city2Repo;

    /**
     *建構子.
     */
    public function __construct(boardRepository $boardRepo, city1Repository $city1Repo, city2Repository $city2Repo) {
        //$this->limit="xx";
        //parent::__construct();
        //將request全部導入到$this->data變數中
        //$this->data = PF::requestAll($this->data);

        $this->boardRepo = $boardRepo;
        $this->city1Repo = $city1Repo;
        $this->city2Repo = $city2Repo;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($data) {

        $this->data = $data;
        $rows = $this->city1Repo->selectRaw('*');
        $rows->myWhere('online|N', 1, "kind", 'N');
        $rows->orderByRaw('sortnum desc');
        $rows = $rows->get();
        $this->data['city1rows'] = $rows;

        if (\Cache::get('city2rows') == null) {
            $rows = $this->city2Repo->selectRaw('Postal as postal,city2title,city1title');
            $rows->orderByRaw('city2title');
            $rows = $rows->get();
            \Cache::forever('city2rows', $rows);
        }




        // $this->data['rowscity1'] = $rowscity1;
        // //$city1title=PF::request("city1title");

        // if ($this->data['city1title']) {
        //     $rowscity2 = $this->city2Repo->selectRaw('*');
        //     $rowscity2->myWhere('city1title|S', $this->data['city1title'], 'city1title', 'Y');
        //     $rowscity2->orderByRaw('city2title');
        //     $rowscity2 = $rowscity2->get();

        //     $this->data['rowscity2'] = $rowscity2;
        // }

        if ($this->data['productkind'] == "") {
            $this->data['productkind'] = "1";
        }



        return view(
            'layouts.search',
            [
                'data' => $this->data,
            ]
        );
    }
}
