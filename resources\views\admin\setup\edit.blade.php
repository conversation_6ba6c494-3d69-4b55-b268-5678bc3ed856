@extends('admin.layouts.master')

@section('css')
@stop

@section('js')
@stop

@section('nav')
{!!$data['nav']!!}
@endsection

@section('content')

<SCRIPT language=JavaScript>
    function oForm_onsubmit(form)
    {
        if (PF_FormMultiAll(form)==false){return false};
   PF_FieldDisabled(form)//將全部button Disabled
   return true;
}
</SCRIPT>
<div class="card">
    <div class="card-body">

<h2>內容不支援單雙引號</h2>
<!--// TODO : 前端資料填寫-->
<form name="oForm"  id="oForm"  method="post" language="javascript" action="{{ URL::to('admin/setup/store') }}"  onsubmit="return oForm_onsubmit(this);"><!--novalidate-->



        @if (PF::xmlSearch($xmlDoc,"//參數設定檔/語系/KIND/傳回值","資料","zh")!="zh")
        <div class="form-group row">
            <label class="col-md-3">語系：<font class="text-danger">*</font></label>
            <div class="col-md-9">
                {{Form::myUIXml([
                    'xmldoc' => $data['xmldoc'],
                    'type' =>'select',
                    'title' =>'語系',
                    'node' => '//參數設定檔/語系/KIND',
                    'name' => 'alg',
                    'value' => $data['alg'],
                    'linecount' => 4,
                    'requiredclass' => 'required[1,TEXT]',
                    'onChange'=>"location.href='?edit=".$data['edit']."&alg='+ this.options[this.selectedIndex].value;" 
                ])
}}請先選擇你要異動的語系內容
            </div>
        </div>
        @endif

        

@foreach ($data['arrs'] as $rs)
<div class="form-group row">
    <label class="col-md-3">{{$rs['title']}}：
        @if ($rs['key']=="name" || $rs['key']=="title" || $rs['key']=="email")
        <font class="text-danger">*</font>
        @endif
        </label>
    <div class="col-md-9">
        <input type="text" name="{{$rs['key']}}" 
    @if ($rs['key']=="name" || $rs['key']=="title" || $rs['key']=="email")
    required
    @endif
    class="form-control" 
    value="{{$rs['value']}}" title="{{$rs['title']}}" requiredclass="required[0,TEXT]" placeholder="" style="width:90%"/>
 	
    </div>
</div>


@endforeach



    

    <div align="center">
        <button type="submit" class="btn btn-success">確定</button>        
        
        {!!Session::get('success')!!}
    </div>
    
</form>

</div>
</div>


@stop

