@extends('admin.layouts.master')
@section('css')
@endsection

@section('js')
@endsection
@section('nav')
{!!$data['nav']!!}
@endsection


@section('content')
<div class="container-fluid p-1">
    <form name="SearchoForm" class="form-inline" method="post" language="javascript" action="{{request()->getRequestUri()}}" onsubmit="return SearchoForm_onsubmit(this);">
        @include('admin.layouts.search', [])
    </form>
</div>
<div align="right" style=''>
    <form name="AddoForm" method="post" language="javascript" action="{{url('admin/kind')}}/create">
        <button type="submit" class="btn btn-info">新增</button>
        
        @include('admin.layouts.hidden', ['method'=>'AddoForm','data'=>$data])
    </form>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover  table-bordered table-fixed">
        <!--排序的參數-->
        <form name="SortoForm" method="post">
            @include('admin.layouts.hidden', ['method'=>'SortoForm','data'=>$data])
            <thead>
                <tr valign="top" align="left">

                    <th>
                        @if (Auth::guard('admin')->user()->status=="999")
                    <div class="form-row align-items-center">
                       
                        <div class="col-auto">
                            <button type="button" class="btn btn-danger"
                                onclick="if (confirm('確定要刪除？')==false){return false;};document.forms['oForm'].action='{{request()->url()}}/destroy';document.forms['oForm'].submit();">刪除</button>
                        </div>
                        
                        <div class="col-auto">
                            <label class="h6 small"
                                onClick="if (this.innerHTML=='全選'){try{checkAll(jQuery($('input[name=\'del[]\']')))}catch(e){alert('目前無可刪除資料');return false;};this.innerHTML='全不選';}else{try{uncheckAll($('input[name=\'del[]\']'))}catch(e){};this.innerHTML='全選';}">全選</label>
                        </div>
                
                    </div>
                    @endif    
                    </th>

                    {{$data['fieldth']}}

                </tr>
            </thead>

        </form>


        <!--傳給下一頁的參數-->
        <form method="post" language="javascript" name="oForm" action="{{url('admin/kind')}}">
        
            @include('admin.layouts.hidden', ['method'=>'oForm','data'=>$data])
            <tbody>
                @foreach ($data['rows'] as $rs)

                <tr>

                    <td valign="top" title="編輯" align="center"  width="120">

                        <div class="form-row align-items-center">
                            <div class="col-auto">
                            <button type="submit" class="btn btn-info"
                                onclick="javascript:form.action='{{url('admin/kind')}}/edit?edit={{ $rs->kindid }}';">
                                編輯
                            </button>
                            </div>
                            @if (Auth::guard('admin')->user()->status=="999")
                            <div class="col-auto">                                
                                <input type="checkbox" name="del[]" value="{{$rs->kindid}}">                                
                            </div>
                            @endif
                        </div>

                    </td>


                    {{$pm->list($rs)}}


                </tr>
                @endforeach

            </tbody>
        </form>



    </table>

    {{ $data['rows']->links('layouts.paginate') }}
</div>
@endsection