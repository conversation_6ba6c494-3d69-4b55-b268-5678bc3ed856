# Swiper.js 圖片輪播功能使用說明

## 功能概述

本系統使用 Swiper.js 實現房屋詳情頁面的圖片輪播功能，提供流暢的圖片瀏覽體驗。

## 功能特色

### 主要功能

-   **主圖片輪播**: 大尺寸圖片展示，完全填滿容器，支援循環播放
-   **縮圖導航**: 側邊縮圖快速切換，與主圖聯動
-   **分頁指示器**: 底部圓點指示器，顯示當前圖片位置
-   **圖片數量標籤**: 右上角顯示總圖片數量
-   **箭頭導航**: 美觀的三角形箭頭按鈕，支援上下導航

### 操作方式

-   **滑鼠操作**: 點擊縮圖切換、拖拽主圖滑動
-   **鍵盤操作**:
    -   `←` 左箭頭：上一張圖片
    -   `→` 右箭頭：下一張圖片
    -   `ESC` 鍵：關閉全螢幕模式（預留功能）
-   **觸控操作**: 支援手機、平板的滑動手勢

### 響應式設計

-   **桌面版本**: 垂直縮圖導航，主圖片 400px 高度
-   **平板版本**: 優化的間距和尺寸
-   **手機版本**: 水平縮圖導航，主圖片 250px 高度

## 檔案結構

```
public/
├── Scripts/
│   └── house-gallery-swiper.js     # 輪播功能 JavaScript 配置
├── css/
│   └── house-gallery-swiper.css    # 輪播樣式檔案
└── js/libs/
    └── swiper.min.js               # Swiper.js 核心檔案
```

## 使用方法

### 1. 載入資源檔案

在 Blade 模板中載入必要的 CSS 和 JavaScript：

```php
@section('css')
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="{{ asset('css/libs/swiper.min.css') }}">
    <!-- 自定義圖片輪播樣式 -->
    <link rel="stylesheet" href="{{ asset('css/house-gallery-swiper.css') }}">
@endsection

@section('js')
    <!-- Swiper JavaScript -->
    <script src="{{ asset('js/libs/swiper.min.js') }}"></script>
    <!-- 房屋圖片輪播配置 -->
    <script src="{{ asset('Scripts/house-gallery-swiper.js') }}"></script>
@endsection
```

### 2. HTML 結構

```html
<section class="slider__wrap">
    <div class="slider__flex">
        <!-- 主圖片區域 -->
        <div class="sliders">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    @foreach ($images as $image)
                    <div class="swiper-slide">
                        <div class="imgBox">
                            <img
                                src="{{ $image }}"
                                alt="房屋圖片"
                                loading="lazy"
                            />
                        </div>
                    </div>
                    @endforeach
                </div>
                <!-- 分頁指示器 -->
                <div class="swiper-pagination"></div>
                <!-- 圖片數量標籤 -->
                <div class="image-count-badge">{{ count($images) }} 張圖片</div>
            </div>
        </div>

        <!-- 縮圖導航區域 -->
        <div class="slider__col">
            <div class="slider__prev" title="上一張"></div>
            <div class="slider__thumbs">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        @foreach ($images as $key => $image)
                        <div class="swiper-slide">
                            <div class="imgBox">
                                <img
                                    src="{{ $image }}"
                                    alt="縮圖 {{ $key + 1 }}"
                                    loading="lazy"
                                />
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="slider__next" title="下一張"></div>
        </div>
    </div>
</section>
```

## 自定義配置

### JavaScript 配置選項

可以在 `house-gallery-swiper.js` 中修改以下配置：

```javascript
const swiperMainConfig = {
    spaceBetween: 10, // 圖片間距
    slidesPerView: 1, // 同時顯示圖片數量
    loop: true, // 循環播放
    speed: 300, // 切換速度（毫秒）
    effect: "slide", // 切換效果：slide, fade, cube, flip
    grabCursor: true, // 顯示抓取游標
    // ... 其他配置
};
```

### CSS 樣式自定義

主要樣式變數位於 `house-gallery-swiper.css`：

```css
/* 主圖片容器高度 */
.sliders .swiper-container {
    height: 400px;
}

/* 縮圖容器寬度 */
.slider__col {
    width: 120px;
}

/* 分頁指示器顏色 */
.sliders .swiper-pagination-bullet-active {
    background: #007bff;
}

/* 導航箭頭按鈕樣式 */
.slider__prev,
.slider__next {
    width: 40px;
    height: 40px;
    background: rgba(0, 123, 255, 0.8);
    border-radius: 50%;
}

/* 箭頭圖示尺寸調整 */
.slider__prev:before {
    border-bottom: 10px solid white; /* 上箭頭 */
}

.slider__next:before {
    border-top: 10px solid white; /* 下箭頭 */
}
```

## 擴展功能

### 全螢幕查看

系統預留了全螢幕查看功能，可以通過以下方式啟用：

```javascript
// 在圖片點擊事件中啟用全螢幕
document
    .querySelectorAll(".sliders .swiper-slide img")
    .forEach((img, index) => {
        img.addEventListener("click", function () {
            openFullscreen(this.src);
        });
    });
```

### 自動播放

如需啟用自動播放，可在配置中添加：

```javascript
autoplay: {
    delay: 5000,                    // 自動播放間隔（毫秒）
    disableOnInteraction: false,    // 用戶操作後是否停止自動播放
    pauseOnMouseEnter: true,        // 滑鼠懸停時暫停
}
```

## 瀏覽器支援

-   Chrome 60+
-   Firefox 55+
-   Safari 12+
-   Edge 79+
-   iOS Safari 12+
-   Android Chrome 60+

## 效能優化建議

1. **圖片優化**: 使用適當的圖片格式和尺寸
2. **懶載入**: 已啟用 `loading="lazy"` 屬性
3. **預載入**: 可調整 `preloadImages` 和 `lazy.loadPrevNext` 設定
4. **響應式圖片**: 考慮使用 `srcset` 屬性提供不同尺寸的圖片

## 常見問題

### Q: 圖片無法顯示？

A: 檢查圖片路徑是否正確，確保圖片檔案存在於 `public/images/product/` 目錄中。

### Q: 輪播功能無法正常運作？

A: 確認已正確載入 Swiper.js 檔案，檢查瀏覽器控制台是否有 JavaScript 錯誤。

### Q: 在移動設備上操作不順暢？

A: 檢查是否有其他 CSS 或 JavaScript 衝突，確保觸控事件沒有被阻止。

### Q: 如何調整縮圖數量？

A: 修改 `breakpoints` 配置中的 `slidesPerView` 值。

## 維護說明

-   定期檢查 Swiper.js 版本更新
-   測試不同瀏覽器和設備的兼容性
-   監控圖片載入效能
-   根據用戶反饋調整使用者體驗
