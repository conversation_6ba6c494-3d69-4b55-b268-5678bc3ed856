{"id": 3, "title": "Comfortable family house", "desc": "Lorem ipsum dolor sit amet consectetur adipisicing elit. Praesentium magnam veniam sit reprehenderit deserunt ad voluptates id aperiam veritatis! Nobis saepe quos eveniet numquam vitae quis, tenetur consectetur impedit dolore.", "propertyType": "House", "propertyStatus": ["For Sale", "Open House"], "city": "Seattle", "zipCode": "98104", "neighborhood": ["Belltown"], "street": ["Belltown Street #1", "Belltown Street #2"], "location": {"lat": 47.60276, "lng": -122.33144}, "formattedAddress": "3rd Ave & James St, Seattle, WA 98104, USA", "features": ["Air Conditioning", "Barbeque", "Dryer", "Microwave", "Gym", "Fireplace", "Sauna", "TV Cable", "WiFi"], "featured": false, "priceDollar": {"sale": 2550000, "rent": null}, "priceEuro": {"sale": 2280000, "rent": null}, "bedrooms": 3, "bathrooms": 1, "garages": 1, "area": {"value": 2100, "unit": "ft²"}, "yearBuilt": 2010, "ratingsCount": 7, "ratingsValue": 560, "additionalFeatures": [{"name": "Heat", "value": "Natural Gas"}, {"name": "<PERSON><PERSON>", "value": "Composition/Shingle"}, {"name": "Floors", "value": "Wall-to-Wall Carpet"}, {"name": "Water", "value": "District/Public"}, {"name": "Cross Streets", "value": "Orangethorpe-<PERSON>"}, {"name": "Windows", "value": "Skylights"}, {"name": "Flat", "value": "5"}, {"name": "Childroom", "value": "2"}], "gallery": [{"small": "assets/images/props/house-1/1-small.jpg", "medium": "assets/images/props/house-1/1-medium.jpg", "big": "assets/images/props/house-1/1-big.jpg"}, {"small": "assets/images/props/house-1/2-small.jpg", "medium": "assets/images/props/house-1/2-medium.jpg", "big": "assets/images/props/house-1/2-big.jpg"}, {"small": "assets/images/props/house-1/3-small.jpg", "medium": "assets/images/props/house-1/3-medium.jpg", "big": "assets/images/props/house-1/3-big.jpg"}, {"small": "assets/images/props/house-1/4-small.jpg", "medium": "assets/images/props/house-1/4-medium.jpg", "big": "assets/images/props/house-1/4-big.jpg"}, {"small": "assets/images/props/house-1/5-small.jpg", "medium": "assets/images/props/house-1/5-medium.jpg", "big": "assets/images/props/house-1/5-big.jpg"}, {"small": "assets/images/props/house-1/6-small.jpg", "medium": "assets/images/props/house-1/6-medium.jpg", "big": "assets/images/props/house-1/6-big.jpg"}], "plans": [{"name": "First floor", "desc": "Plan description. Lorem ipsum dolor sit amet consectetur adipisicing elit. Praesentium magnam veniam sit reprehenderit deserunt ad voluptates id aperiam veritatis! Nobis saepe quos eveniet numquam vitae quis, tenetur consectetur impedit dolore.", "area": {"value": 1180, "unit": "ft²"}, "rooms": 3, "baths": 1, "image": "assets/images/others/plan-1.jpg"}, {"name": "Second floor", "desc": "Plan description. Lorem ipsum dolor sit amet consectetur adipisicing elit. Praesentium magnam veniam sit reprehenderit deserunt ad voluptates id aperiam veritatis! Nobis saepe quos eveniet numquam vitae quis, tenetur consectetur impedit dolore.", "area": {"value": 1200, "unit": "ft²"}, "rooms": 5, "baths": 2, "image": "assets/images/others/plan-2.jpg"}], "videos": [{"name": "Video with 'mat-video' plugin", "link": "http://themeseason.com/data/videos/video-1.mp4"}, {"name": "Video with 'ngx-embed-video' plugin", "link": "https://www.youtube.com/watch?v=-NInBEdSvp8"}], "published": "2011-05-12 18:20:00", "lastUpdate": "2019-06-18 14:20:00", "views": 587}