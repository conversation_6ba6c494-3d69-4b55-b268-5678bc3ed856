<?php

namespace App\Macros;

use PF;

/***
"功能名稱":"共用類別-Html 日期時間",
"備註":" ",
"建立時間":"2022-01-18 13:26:42",
***/
class MyFormUISelectDate
{
    protected $arr;

    public function __construct($form, $arr)
    {
        $this->arr = $arr;
    }

    public function _options($start, $end, $selected, $Stype, $name, $LineCount)
    {
        $list = '';
        if ('SELECT' == strtoupper($Stype)) {
            for ($i = $start; $i <= $end; ++$i) {
                $list .= '<option value="'.sprintf('%02d', $i).'"';
                if ($i == $selected) {
                    $list .= ' selected';
                }
                $list .= '>'.sprintf('%02d', $i).'</option>';
                $list .= "\r\n";
            }
        } else {
            for ($i = $start; $i <= $end; ++$i) {
                $list .= '<input ';
                $list .= 'type="'.$Stype.'"';
                $list .= ' name="'.$name;
                if ('CHECKBOX' == strtoupper($Stype)) {
                    $list .= '[]';
                }
                $list .= '" value="'.sprintf('%02d', $i).'"';
                if (PF_SplitCompare($selected, $i)) {
                    $list .= ' checked';
                }
                $list .= '>'.sprintf('%02d', $i);
                if (0 == ($i) % $LineCount) {
                    $list .= '<br>';
                }
                $list .= "\n";
            }
        }

        return $list;
    }

    public function createHtml()
    {
        $html = '';
        try {
            $this->arr['type'] = strtolower($this->arr['type']);
            if ('datetime' == $this->arr['type']) {
                $this->arr['type'] = 'datetime-local';
            }
            switch ($this->arr['type']) {
                case 'date':
                case 'time':
                case 'datetime-local':

                $html .= '<input';
                //PF_print($this->arr['Type);
                $this->arr['value'] = str_replace('/', '-', $this->arr['value']);
                $html .= ' type="'.$this->arr['type'].'"';
                $html .= ' name="'.$this->arr['name'].'" ';

                if ('datetime-local' == strtolower($this->arr['type']) && '' != $this->arr['value']) {
                    $html .= ' value="'.date('Y-m-d\TH:i:s', strtotime($this->arr['value'])).'"';
                } elseif ('time' == $this->arr['type'] && '' != $this->arr['value']) {
                    $html .= ' value="'.$this->arr['value'].'"';
                } else {
                    $html .= ' value="'.str_replace('/', '-', PF::formatdate($this->arr['value'])).'"';
                }
                if ($this->arr['required'] || substr_count($this->arr['requiredclass'], 'required[1,') > 0 || substr_count($this->arr['classmame'], 'required[1,') > 0) {
                    $html .= ' required ';
                }

                foreach ($this->arr as $_key => $_value) {
                    if (false == in_array($_key, ['required', 'name', 'selectfirsttxt', 'xmldoc', 'node', 'type', 'linecount', 'sql', 'value', 'firsttext', 'name'])) {
                        $html .= ' '.$_key.'="'.$_value.'"';
                    }
                }

                if ('datetime-local' == $this->arr['type']) {
                    $html .= " pattern=\"\d{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}\"";
                } elseif ('date' == $this->arr['type']) {
                    $html .= ' pattern="(?:19|20)[0-9]{2}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-9])|(?:(?!02)(?:0[1-9]|1[0-2])-(?:30))|(?:(?:0[13578]|1[02])-31))"';
                    $html .= ' placeholder="yyyy-mm-dd"';
                }
                $html .= ' class="form-control" ';
                $html .= '/>';

                break;
                case 'yymmdd':
                    $yyyyy = '';
                    $mm = '';
                    $dd = '';

                    if (PF::isDate($this->arr['value'])) {
                        $yyyy = date('Y', strtotime($this->arr['value']));
                        $mm = date('m', strtotime($this->arr['value']));
                        $dd = date('d', strtotime($this->arr['value']));
                    }

                    if ('' == $this->arr['yyyystart']) {
                        $this->arr['yyyystart'] = date('Y') - 100;
                    }
                    if ('' == $this->arr['yyyyend']) {
                        $this->arr['yyyyend'] = date('Y');
                    }

                    $html .= '<div class="form-inline">';
                    $html .= '<input type="hidden" name="'.$this->arr['name'].'">';
                    $html .= '<select name="'.$this->arr['name'].'_yy" ';
                    $html .= " v-model='inputs.".$this->arr['name']."_yy' ";
                    $html .= ' class="form-control"'.PHP_EOL;
                    if (substr_count($this->arr['requiredclass'], 'required[1,') > 0 || substr_count($this->arr['classmame'], 'required[1,') > 0) {
                        $html .= ' required ';
                    }
                    foreach ($this->arr as $_key => $_value) {
                        if (false == in_array($_key, ['name', 'selectfirsttxt', 'xmldoc', 'node', 'type', 'linecount', 'sql', 'value', 'firsttext', 'name'])) {
                            $html .= ' '.$_key.'="'.$_value.'"';
                        }
                    }
                    $html .= '>'.PHP_EOL;
                    $html .= '<option value="">'._('請選擇')."</option>\n";

                    for ($i = intval($this->arr['yyyystart']); $i <= intval($this->arr['yyyyend']); ++$i) {
                        $html .= '<option value="'.sprintf('%02d', $i).'"';
                        if ($i == $yyyy) {
                            $html .= ' selected';
                        }
                        $html .= '>'.sprintf('%02d', $i - 1911).'</option>';
                        $html .= PHP_EOL;
                    }
                    $html .= '</select>'._('年');

                    $html .= '<select name="'.$this->arr['name'].'_mm"';
                    $html .= " v-model='inputs.".$this->arr['name']."_mm' ";
                    $html .= ' class="form-control"'.PHP_EOL;
                    if ($this->arr['required'] || substr_count($this->arr['requiredclass'], 'required[1,') > 0 || substr_count($this->arr['classmame'], 'required[1,') > 0) {
                        $html .= ' required ';
                    }
                    foreach ($this->arr as $_key => $_value) {
                        if (false == in_array($_key, ['name', 'selectfirsttxt', 'xmldoc', 'node', 'type', 'linecount', 'sql', 'value', 'firsttext', 'name'])) {
                            $html .= ' '.$_key.'="'.$_value.'"';
                        }
                    }
                    $html .= '>'.PHP_EOL;
                    $html .= '<option value="">'._('請選擇')."</option>\n";
                    $html .= $this->_options(1, 12, $mm, 'select', '', '');
                    $html .= '</select>'._('月');

                    $html .= '<select name="'.$this->arr['name'].'_dd"';
                    $html .= " v-model='inputs.".$this->arr['name']."_dd' ";
                    $html .= ' class="form-control"'.PHP_EOL;
                    if (substr_count($this->arr['requiredclass'], 'required[1,') > 0 || substr_count($this->arr['classmame'], 'required[1,') > 0) {
                        $html .= ' required ';
                    }
                    foreach ($this->arr as $_key => $_value) {
                        if (false == in_array($_key, ['required', 'name', 'selectfirsttxt', 'xmldoc', 'node', 'type', 'linecount', 'sql', 'value', 'firsttext', 'name'])) {
                            $html .= ' '.$_key.'="'.$_value.'"';
                        }
                    }
                    $html .= '>'.PHP_EOL;
                    $html .= '<option value="">'._('請選擇')."</option>\n";
                    $html .= $this->_options(1, 31, $dd, 'select', '', '');
                    $html .= '</select>'._('日');
                    $html .= '</div>';

                break;
                default:

                    break;
            }
        } catch (\Exception $e) {
            $html .= $e->getMessage();
        }

        return $html;
    }
}
