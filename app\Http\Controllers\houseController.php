<?php

namespace App\Http\Controllers;

use PF;
use Config;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use App\Repositories\productRepository;
use App\Repositories\feedbackRepository;

class houseController extends Controller {
    private $data;
    private $productRepo;
    private $feedbackRepo;



    public function __construct(productRepository $productRepo, feedbackRepository $feedbackRepo) {
        //$this->limit="xx";
        parent::__construct();
        $this->data = PF::requestAll($this->data);

        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->productRepo = $productRepo;
        $this->feedbackRepo = $feedbackRepo;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request) {
        Config::set('config.title', '法拍物件 | ' . config('config.title'));
        $rows = $this->productRepo->select('product.*');
        $rows->leftJoin('city2', function ($join) {
            $join->on('city2.city2title', '=', 'product.city2title');
            $join->on('city2.city1title', '=', 'product.city1title');
        });


        if ($this->data['city1title'] != "") {
            $rows->myWhere('product.city1title|INS', $this->data['city1title'], 'kindid', 'N');
            Config::set('config.title', $this->data['city1title'] . ' | ' . config('config.title'));
        }

        if ('不限' != $this->data['city2title']) {
            $rows->myWhere('product.city2title|INS', $this->data['city2title'], 'kindid', 'N');
            Config::set('config.title', $this->data['city2title'] . ' | ' . config('config.title'));
        }


        $rows->myWhere('postal|INS', $this->data['postal'], 'kindid', 'N');
        // if ('不限' != $this->data['pattern']) {
        //     $rows->myWhere('pattern|S', $this->data['pattern'], 'kindid', 'N');
        // }
        $rows->myWhere('pattern|INS', $this->data['pattern'], 'kindid', 'N');
        $rows->myWhere('beattime|INS', $this->data['beattime'], 'beattime', 'N');
        $rows->myWhere('online|N', 1, 'online', 'N');

        if ($request->input('totalupset') != null) {
            $rows->where(function ($query) use ($request) {
                foreach ($request->input('totalupset') as $k => $v) {
                    if ($v != "") {
                        $query->orwhere(function ($query) use ($v) {
                            $a = explode('~', $v);
                            $query->where('totalupset', '>=', $a[0]);
                            $query->where('totalupset', '<=', $a[1]);
                        });
                    }
                }
            });
        }

        if ($request->input('houseage') != null) {
            $rows->where(function ($query) use ($request) {
                foreach ($request->input('houseage') as $k => $v) {
                    if ($v != "") {
                        $query->orwhere(function ($query) use ($v) {
                            $a = explode('~', $v);
                            $query->where('houseage', '>=', $a[0]);
                            $query->where('houseage', '<=', $a[1]);
                        });
                    }
                }
            });
        }




        if ($request->input('pingtotalnumberof') != null) {
            $rows->where(function ($query) use ($request) {
                foreach ($request->input('pingtotalnumberof') as $k => $v) {
                    if ($v != "") {
                        $query->orwhere(function ($query) use ($v) {
                            $a = explode('~', $v);
                            $query->where('pingtotalnumberof', '>=', $a[0]);
                            $query->where('pingtotalnumberof', '<=', $a[1]);
                        });
                    }
                }
            });
        }



        $rows->myWhere('product.city1title^product.city2title^address^producttitle^mrtstation^mrtland', $this->data['search'], 'kindid', 'N');
        //\PF::dbSqlPrint($rows);
        switch ($request->input('location')) {
            case 'beattime':
                $rows->whereIn('beattime', ['3拍', '4拍', '5拍', '6拍', '應買', '特拍']);
                Config::set('config.title', '低價必賣區' . config('config.title'));

                break;

            default:
                $rows->myWhere('location', $request->input('location'), 'kindid', 'N');
                break;
        }
        $rows->myWhere('productkind|N', $request->input('productkind'), 'kindid', 'N');
        // switch ($request->input('productkind')) {

        //     case '2':
        //         $rows->where('productkind', 2);
        //         break;
        //     case '3':
        //         $rows->where('productkind', 3);
        //         # code...
        //         break;

        //     default:
        //         $rows->where('productkind', 1);
        //         # code...
        //         break;
        // }

        if ($this->data['sortname']) {
            $rows->orderBy($request->input('sortname'), 'desc' == $request->input('sorttype') ? $request->input('sorttype') : 'asc');
        } else {
            $rows->orderByRaw('tenderdate desc ,productid desc');
        }
        //PF::dbSqlPrint($rows);

        $rows = $rows->paginate(24);
        $this->data['rows'] = $rows;

        return view(
            'house.index',
            [
                'data' => $this->data,
            ]
        );
    }

    public function show(Request $request, $productid = '') {
        $rows = $this->productRepo->selectRaw('product.*');
        $rows->myWhere('productid|N', $productid, 'productid', 'Y');
        $rows->orderByRaw('productid desc');

        $rows = $rows->take(1)->get();
        //$rows = $rows->get();
        //dd($row);
        //PF::dbSqlPrint($rows);

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
            if ($this->data['img'] == "") {
                $this->data['img'] = "no-picture.gif";
            }
            Config::set('config.title', $rs->city1title . ' | ' . $rs->city2title . ' | ' . $rs->address . ' | 法拍物件 | ' . config('config.title'));
            Config::set('config.keyword', $rs->city1title . ',' . $rs->city2title . ',法拍物件');

            if ('' != $rs->img) {
                $images = explode(',', $rs->img);
                $image = $images[0];
                $this->data['ob:image'] = url('/') . '/images/product/' . $image;
            }
        } else {
            return redirect()->to('/');
            //            throw new \CustomException('No data');
        }
        $rows = $this->productRepo->select('*');
        $rows->myWhere('online|N', 1, 'online', 'N');
        $rows->where('productkind', $this->data['productkind']);
        $rows->myWhere('city1title|S', $this->data['city1title'], 'kindid', 'N');

        $rows->myWhere('city2title|S', $this->data['city2title'], 'kindid', 'N');
        $rows->myWhere('pattern|S', $this->data['pattern'], 'kindid', 'N');
        $rows->myWhere('productid|<>', $productid, 'kindid', 'N');
        //$rows->whereRaw('img<>""');

        // if ('' != $this->data['totalupset']) {
        //     $totalupsetSplit = explode('~', $this->data['totalupset']);
        //     $rows->myWhere('totalupset|>=', $totalupsetSplit[0], 'kindid', 'N');
        //     $rows->myWhere('totalupset|<=', $totalupsetSplit[1], 'kindid', 'N');
        // }
        $rows->orderByRaw('auctionssortnum ,productid desc');
        $rows = $rows->take(12);
        //PF::dbSqlPrint($rows);
        $rows = $rows->get();
        $this->data['rows'] = $rows;
        //Config::set('config.title', $this->data['city1title'].$this->data['city2title'].' | '.$this->data['producttitle'].' | '.$this->data['pattern'].' | '.config('config.title'));
        \DB::update('update product set hits=hits+1 where productid = ?', [$productid]);
        // Config::set('config.keyword', '');
        //Config::set('config.description', $this->data['producttitle'].' , '.$this->data['city1title'].$this->data['city2title'].$this->data['address']);
        //return response($productid);
        return view(
            'house.show',
            [
                'data' => $this->data,
            ]
        );
    }
    public function print(Request $request) {

        $rows = \App\Models\product::select('*');
        $rows->myWhere('productid|N',  $request->input('productid'), "id", 'Y');
        $rows->myWhere('online|N',  1, "id", 'Y');
        $rs = $rows->firstOrFail();
        if ($rs->img != "") {

            $images = explode(',', $rs->img);
            $images = array_merge(array_slice($images, -2), [reset($images)]);
            $this->data['images'] = $images;
        }
        $this->data = PF::stdClassToArray($this->data, $rs);



        return view(
            'house.print' . $this->data['productkind'],
            [
                'data' => $this->data,
            ]
        );
    }

    public function store(Request $request) {



        /*欄位規格與必填判斷*/
        $validators = null;
        $validators['name'] = 'required';
        $validators['memo'] = 'required';
        $validators['email'] = 'required|email';
        //        $validators['title'] = 'required';
        $validators['google_recaptcha_token'] = ['required', 'string', new \App\Rules\MyValidatorsGoogleRecapchaV3()];
        $validator = \Validator::make($request->all(), $validators);

        $validator->setAttributeNames($this->feedbackRepo->getFieldTitleArray());
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        $inputs = $request->all();

        //PF::printr($inputs);exit();
        $this->feedbackRepo->create($inputs);

        $jsondata['resultmessage'] = '我們將儘快與您聯絡';
        return back()->with('js', "_toast('我們將儘快與您聯絡',1000,'success')");
    }

    public function courttranscript(Request $request) {
        $rows = $this->productRepo->selectRaw('courttranscript');
        $rows->myWhere('productid|N', $this->data['productid'], 'productid', 'Y');
        //$rows->orderByRaw('id desc');
        $rows = $rows->limit(1);
        //$rs=get_object_vars(rs);//to rs['XX'];
        //PF::dbSqlPrint($rows);
        $rows = $rows->get();

        if ($rows->count() > 0) {
            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
        } else {
            throw new \CustomException('No data');
        }

        return view('house.courttranscript', [
            'data' => $this->data,
        ]);
    }
}
