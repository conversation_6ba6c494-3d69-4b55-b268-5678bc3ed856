<?php
namespace App\Providers;

use App\Libraries\PF; //要服务的Container
use Illuminate\Support\ServiceProvider;
use App;

class PFServiceProvider extends ServiceProvider
{
    public function boot(){}
    //注册到容器中
    public function register()
    {
        //可以这么绑定,这需要use App;
        App::bind("PF",function(){
            return new PF();
        });
        //也可以这么绑定
        // $this->app->bind("myfoo", function(){
        //     return new MyFoo();
        // });
    }
}
