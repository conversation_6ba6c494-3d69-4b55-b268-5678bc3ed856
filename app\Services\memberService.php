<?php

namespace App\Services;

use PF;

class memberService {
    public function __construct() {
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
        $this->memberRepo = app(\App\Repositories\memberRepository::class);
        $this->data['displaynames'] = $this->memberRepo->getFieldTitleArray();
    }

    public function create($request, $data) {
        $this->data = $data;
        //FIXME 那些欄位為必填判斷
        $validators = null;

        $validators['password'] = ['required', 'min:8']; //密碼

        $validators['mobile'] = ['required']; //任職公司
        $validators['name'] = ['required']; //姓名
        $validators['email'] = ['required', 'email']; //電子信箱
        //$validators['account'] = 'required|unique:member,account';

        $validator = \Validator::make($request->all(), $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        $inputs = $request->all();

        //$this->data['password'] = date('YmdHis').floor(microtime() * 1000);
        $inputs['password'] = \Hash::make($this->data['password']); //密碼-
        $inputs['api_token'] = hash('sha256', \Str::random(80));
        $inputs['online'] = 1;
        //$inputs['country']=$this->data['country'];//國家別-
        //$inputs['companyname']=$this->data['companyname'];//任職公司-
        //$inputs['name']=$this->data['name'];//姓名-
        //$inputs['sex']=$this->data['sex'];//性別-[:先生 ; :女士 ; ]
        //$inputs['title']=$this->data['title'];//職稱-
        //$inputs['tel']=$this->data['tel'];//電話-
        //$inputs['email']=$this->data['email'];//電子信箱-
        //$inputs['workurl']=$this->data['workurl'];//工作網址-
        //$inputs['industry']=$this->data['industry'];//從事產業類別-
        //$inputs['memo']=$this->data['memo'];//個人簡介-
        //$inputs['online']=$this->data['online'];//會員狀態-[1:正常 ; 2:停權 ; ]

        //$inputs['alg'] = app()->getLocale();;//語系-[zh:繁體中文 ; en:英文 ; ]

        $member = $this->memberRepo->create($inputs);
        //$api_token = $member->api_token;
        $id = $member->id;

        \Mail::queue(new \App\Mails\memberaddMail($id, $this->data['password']));

        return $id;
    }

    public function edit($request, $data) {
        $this->data = $data;
        $validators = null;
        //   $validators['account']=['required'];//帳號
        //$validators['password']=['min:8'];//密碼

        $validators['company'] = ['required']; //任職公司
        $validators['name'] = ['required']; //姓名
        $validators['email'] = ['required']; //電子信箱

        $validator = \Validator::make($request->all(), $validators);

        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }
        PF::dbUnique('member', $this->data['displaynames'], ['id' => $edit], [
            'email' => $this->data['email'],
        ]);
        $inputs = $request->all();

        unset($inputs['account']); //密碼-

        //PF::printr($inputs); exit();
        $this->memberRepo->update($inputs, \request()->member['id']);

        return '更新成功';
    }

    public function changePassword($request, $data) {
        $this->data = $data;
        $validators = null;
        $validators['password'] = ['required', 'confirmed', 'min:8']; //密碼
        $this->data['displaynames']['password'] = '密碼';
        $validator = \Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        $inputs = null;
        $inputs['password'] = \Hash::make($this->data['password']);
        $this->memberRepo->update($inputs, \Auth::guard('member')->id());

        return '更新成功';
    }

    public function forgetPassword($request, $data) {
        $this->data = $data;
        $validators = null;
        $validators['email'] = 'required';
        //  if ('@DEBUG' != $request->input('captcha')) {
        //     $validators['captcha'] = 'required|captcha';
        //}
        $validators['google_recaptcha_token'] = ['required', 'string', new \App\Rules\MyValidatorsGoogleRecapchaV3()];
        $validator = \Validator::make($request->all(), $validators);
        $validator->setAttributeNames($this->data['displaynames']);
        if ($validator->fails()) {
            throw new \CustomException(implode(',', $validator->messages()->all()));
        }

        \Mail::queue(new \App\Mails\memberforgetpwdMail($this->data['email']));

        return '請至您的電子信箱收取密碼函通知書';
    }
}
