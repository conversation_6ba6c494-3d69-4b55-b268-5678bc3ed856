<?php

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use DB;
use PF;

class memberforgetpwdMail extends Mailable
{
    public $tries = 3;
    use Queueable;
    use SerializesModels;
    public $email;
    public $password;

    // 讓外部可以將參數指定進來
    public function __construct($email)
    {
        $this->email = $email;
    }

    public function build()
    {
        $rows = DB::table('member');
        $rows->selectRaw('email,name,id');
        $rows->myWhere('email|S', $this->email, 'email', 'Y');
        $rows->limit(1);
        if ($rows->count() > 0) {
            $rs = $rows->first();
            $pwd = substr(md5(uniqid(rand(), true)), 0, 9);
            $inputs = null;
            $inputs['password'] = \Hash::make($pwd);
            
            \App\Models\member::myWhere('id', $rs->id, 'id', 'Y')->update($inputs);

            $rs = $rows->first();
            foreach ($rs as $key => $value) {
                $this->data[$key] = $value;
            }
            $this->data['pwd']=$pwd;
            
            
            
            //\App::setLocale($rs->alg);

            $this->from(PF::getFromEmail(), config('config.name'));

            $this->subject(config('config.name')."-忘記密碼");
/*
            if (PF::isEmpty($rs->alg)) {
                $this->view('email.zh.memberforgetpwd');
            } else {
                $this->view('email.'.$rs->alg.'.memberforgetpwd');
            }
*/
            $this->view('email.memberforgetpwd');
            $this->with($this->data);
            $this->to($rs->email);
            //PF::printr($this->data);

        //    $this->cc(explode(';', config('config.email')));
        //$this->bcc(config('config.'.$rs->alg.'.email'));
        } else {
            throw new \CustomException('查無此記錄');
        }

        //PF::dbSqlPrint($rows);

        return $this;
    }
}
