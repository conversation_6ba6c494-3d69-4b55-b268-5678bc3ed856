@extends('admin.layouts.master')
@section('css')
@endsection

@section('js')
@endsection
@section('nav')
{!!$data['nav']!!}
@endsection


@section('content')


<div class="container-fluid p-1">
    <form name="SearchoForm" class="form-inline" method="post" language="javascript"
        action="{{request()->getRequestUri()}}" onsubmit="return SearchoForm_onsubmit(this);">
        @include('admin.layouts.search', [])
    </form>
</div>
<div class="table-responsive">
    <table class="table table-striped table-hover  table-bordered table-fixed">
        <!--排序的參數-->
        <form name="SortoForm" method="post">
            @include('admin.layouts.hidden', ['method'=>'SortoForm'])
            <thead>
                <tr valign="top" align="left">
                    <th width="" id="account">帳號</th>
                    <th width="" id="clientip">Clientip</th>
                    <th width="" id="created_at">登入時間</th>
                    <th width="" id="loginstatus">登入狀態</th>
                    <th width="" id="logouttime">登出時間</th>

                </tr>
            </thead>

        </form>


        <!--傳給下一頁的參數-->
        <form method="post" language="javascript" name="oForm" action="{{url('admin/adminuserloginlog')}}">
            
            @include('admin.layouts.hidden', ['method'=>'oForm'])
            <tbody>
                @foreach ($data['rows'] as $rs)

                <tr>

                    </td>

                    <td>

                        {{$rs->account}}
                         </td>

                    <td>

                        {{$rs->clientip}}
                         </td>

                    <td>

                        {{$rs->created_at}}
                         </td>

                    <td>

                        {{$rs->loginstatus}}
                         </td>

                    <td>

                        {{$rs->logouttime}}
                         </td>

                </tr>
                @endforeach

            </tbody>
        </form>


    </table>
</div>
{{ $data['rows']->links('layouts.paginate') }}
@endsection