document.addEventListener("DOMContentLoaded", function () {
  const tableView = document.getElementById("tableView");
  const gridView = document.getElementById("propertyContainer");
  const btnGrid = document.getElementById("btn-grid");
  const btnTable = document.getElementById("btn-table");

  // 加入安全檢查，如果元素不存在就不執行
  if (!tableView || !gridView || !btnGrid || !btnTable) {
    console.log('findings.js: 缺少必要的元素，跳過執行');
    return;
  }

  btnGrid.addEventListener("click", function () {
    gridView.style.display = "grid";
    tableView.style.display = "none";
    btnGrid.classList.add("active");
    btnTable.classList.remove("active");
  });

  btnTable.addEventListener("click", function () {
    gridView.style.display = "none";
    tableView.style.display = "block";
    btnTable.classList.add("active");
    btnGrid.classList.remove("active");

    if (!tableView.dataset.filled) {
      const cards = document.querySelectorAll(".property-card");

      cards.forEach((card) => {
        const imgSrc = card.querySelector("img")?.src || "";
        const status = card.dataset.status || "待標";
        const time = card.dataset.time || "114/07/23";
        const address = card.querySelector("h2")?.textContent.trim() || "";

        // 用 querySelector 精準抓各個欄位
        const price = card.querySelector(".price")?.textContent.trim() + "萬" || "";
        const type = card.querySelector(".info-row:nth-of-type(1) p:nth-of-type(2) span")?.textContent.trim() || "";
        const total = card.querySelector(".info-row:nth-of-type(2) p:nth-of-type(1) span")?.textContent.trim() + "坪" || "";
        const unit = card.querySelector(".info-row:nth-of-type(2) p:nth-of-type(2) span")?.textContent.trim() + "萬" || "";

        const a = document.createElement("a");
        a.href = card.getAttribute("href") || "#";
        //a.target = "_blank"; // 新開視窗
        a.style.textDecoration = "none";
        a.style.color = "inherit";

        const row = document.createElement("div");
        row.className = "table-row";
        row.innerHTML = `
          <div><img src="${imgSrc}" alt="" /></div>
          <div>${status}</div>
          <div>${time}</div>
          <div>${address}</div>
          <div>${type}</div>
          <div>${price}</div>
          <div>${total}</div>
          <div>${unit}</div>
        `;
        a.appendChild(row);
        tableView.appendChild(a);
      });

      tableView.dataset.filled = "true";
    }
  });
});