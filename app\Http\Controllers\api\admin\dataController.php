<?php

namespace App\Http\Controllers\api\admin;

use DB;
use PF;
use Illuminate\Http\Request;

//use Illuminate\Support\Facades\DB;

class dataController extends adminController
{
    private $data;
    private $db;

    /**
     *建構子.
     */
    public function __construct()
    {
        //$this->limit="xx";
        parent::__construct();

        //將request全部導入到$this->data變數中
        $this->data = PF::requestAll($this->data);
        $this->data['xmldoc'] = PF::xmlDoc('Setup.xml');
    }

    public function index(Request $request)
    {
        $jsondata['resultcode'] = 0;
        $jsondata['resultmessage'] = '';

        try {
            switch ($this->data['str']) {
        case 'city1':
            $rows = DB::table('city1')->select('city1title', 'city1title');
            $rows->myWhere('partid|N', $key, 'key', 'N');
            $rows->orderBy('sortnum', 'desc');

            break;
        case 'city2':
            //DB::enableQueryLog();
            // 顯示sqlcmd
            $rows = DB::table('city2')->select(DB::raw('concat(city2title,postal) as t1'), DB::raw('concat(city2title,postal) as t2'));
            $rows->myWhere('city1title|S', $key, 'key', 'Y');
            $rows->orderBy('city2title');

            break;
        case 'kindhead':

            $rows = DB::table('kindhead')->select('kindheadid', 'kindheadtitle');

            $rows->orderBy('kindheadsort');

            break;
        case 'kindmain':

            $rows = DB::table('kindmain')->select('kindmainid', 'kindmaintitle');
            $rows->myWhere('kindheadid|N', $this->data['kindheadid'], 'kindheadid', 'Y');
            $rows->orderBy('kindmainsort');
            break;

        case 'kinditem':
                $rows = DB::table('kinditem')->select('kinditemid', 'kinditemtitle');

                $rows->myWhere('kindmainid|N', $this->data['kindmainid'], 'kindmainid', 'Y');
                $rows->orderBy('kinditemtitle');

                break;
        case 'periods':
            $rows = DB::table('periodsrate')->selectRaw('distinct(periods)');
            $rows->myWhere('kinditemid|N', $this->data['kinditemid'], 'kinditemid', 'Y');

            $rows->orderBy('periods');

            break;
        case 'rate':
            $rows = DB::table('periodsrate')->selectRaw('periodsrateid,rate');
            $rows->myWhere('kinditemid|N', $this->data['kinditemid'], 'kinditemid', 'Y');
            $rows->myWhere('periods|N', $this->data['periods'], 'periods', 'Y');
            $rows->orderBy('rate');

                        break;
        case 'productkindSQLCmd':
            $rows = DB::table('board')->select('id', 'title');
            $rows->myWhere('kind|S', $key, 'key', 'Y');
            $rows->orderBy('boardsort');
            break;
        default:
            throw new \CustomException('no str');
            break;
        }
            $rows = $rows->get();
            $data = [];
            foreach ($rows as $rs) {
                $rs = get_object_vars($rs);
                $arrkey = array_keys($rs);
                $vtitle = 1;
                if (1 == count($arrkey)) {
                    $vtitle = 0;
                }
                $data[] = [
                    'value' => $rs[$arrkey[0]],
                    'title' => $rs[$arrkey[$vtitle]],
                ];
            }

            $jsondata['data'] = $data;
            //throw new \Exception('no data');
        } catch (\CustomException $e) {
            $jsondata['resultcode'] = 999;
            $jsondata['resultmessage'] = $e->getMessage();
        }
        //json = json_encode(body,JSON_UNESCAPED_UNICODE);
        return response()->json($jsondata, 200, ['Content-Type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }
}
